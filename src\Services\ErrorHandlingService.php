<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Utils\ErrorClassifier;
use Skpassegna\GuardgeoApi\Utils\ErrorHandler;
use Skpassegna\GuardgeoApi\Utils\ProductionErrorHandler;
use Skpassegna\GuardgeoApi\Utils\ThreatResponseSystem;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;
use Skpassegna\GuardgeoApi\Utils\SecurityAuditLogger;
use Skpassegna\GuardgeoApi\Utils\RateLimiter;

/**
 * Error Handling Service
 * 
 * Central service that coordinates all error handling components
 * and provides a unified interface for error management.
 */
class ErrorHandlingService
{
    private ErrorClassifier $classifier;
    private ErrorHandler $errorHandler;
    private ProductionErrorHandler $productionHandler;
    private ThreatResponseSystem $threatResponse;
    private ResponseFormatter $responseFormatter;
    private ErrorLogger $errorLogger;
    private LoggingService $logger;
    private bool $isProduction;

    public function __construct(
        ErrorClassifier $classifier,
        ErrorHandler $errorHandler,
        ProductionErrorHandler $productionHandler,
        ThreatResponseSystem $threatResponse,
        ResponseFormatter $responseFormatter,
        ErrorLogger $errorLogger,
        LoggingService $logger
    ) {
        $this->classifier = $classifier;
        $this->errorHandler = $errorHandler;
        $this->productionHandler = $productionHandler;
        $this->threatResponse = $threatResponse;
        $this->responseFormatter = $responseFormatter;
        $this->errorLogger = $errorLogger;
        $this->logger = $logger;
        $this->isProduction = ($_ENV['APP_ENV'] ?? 'production') === 'production';
    }

    /**
     * Handle any error with comprehensive processing
     */
    public function handleError(string $errorCode, string $message, array $context = [], ?\Throwable $exception = null): void
    {
        try {
            // Classify the error
            $classification = $this->classifier->classifyError($errorCode);
            
            // Check if this is a security-related error
            if ($classification['category'] === ErrorClassifier::CATEGORY_SECURITY) {
                $this->handleSecurityError($errorCode, $message, $context, $exception);
                return;
            }
            
            // Use production handler for safe error responses
            if ($this->isProduction) {
                $this->productionHandler->handleProductionError($errorCode, $message, $context, $exception);
            } else {
                // Use detailed error handler for development
                $errorResponse = $this->errorHandler->handleError($errorCode, $message, $context, $exception);
                $this->sendErrorResponse($errorResponse);
            }
            
        } catch (\Throwable $criticalError) {
            // If error handling itself fails, use emergency response
            $this->handleCriticalFailure($criticalError);
        }
    }

    /**
     * Handle exceptions with automatic classification
     */
    public function handleException(\Throwable $exception, array $additionalContext = []): void
    {
        try {
            $classification = $this->classifier->classifyException($exception);
            $errorCode = $this->getErrorCodeFromException($exception);
            
            $context = array_merge($additionalContext, [
                'exception_class' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine()
            ]);
            
            $this->handleError($errorCode, $exception->getMessage(), $context, $exception);
            
        } catch (\Throwable $criticalError) {
            $this->handleCriticalFailure($criticalError);
        }
    }

    /**
     * Handle security-related errors
     */
    public function handleSecurityError(string $errorCode, string $message, array $context = [], ?\Throwable $exception = null): void
    {
        // Analyze and respond to security threat
        $threatResponse = $this->threatResponse->analyzeSecurityEvent($errorCode, array_merge($context, [
            'message' => $message,
            'exception' => $exception ? get_class($exception) : null
        ]));
        
        // If IP is blocked, send immediate block response
        $ip = $context['ip'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        if ($threatResponse['ip_blocked']) {
            $this->sendBlockedResponse($ip, $threatResponse);
            return;
        }
        
        // Send appropriate security error response
        $this->productionHandler->handleProductionError($errorCode, $message, $context, $exception);
    }

    /**
     * Handle database errors with special processing
     */
    public function handleDatabaseError(\Throwable $exception, array $context = []): void
    {
        $this->productionHandler->handleDatabaseError($exception, $context);
    }

    /**
     * Handle external API errors
     */
    public function handleExternalApiError(string $service, \Throwable $exception, array $context = []): void
    {
        $this->productionHandler->handleExternalApiError($service, $exception, $context);
    }

    /**
     * Handle validation errors with detailed field information
     */
    public function handleValidationError(array $validationResult): void
    {
        $context = [
            'field_errors' => $validationResult['errors'] ?? [],
            'field_warnings' => $validationResult['warnings'] ?? [],
            'field_count' => count($validationResult['errors'] ?? [])
        ];
        
        $this->handleError('VALIDATION_ERROR', 'Request validation failed', $context);
    }

    /**
     * Handle rate limiting errors
     */
    public function handleRateLimitError(string $identifier, int $retryAfter = null): void
    {
        $context = [
            'identifier' => $identifier,
            'retry_after' => $retryAfter ?? 60
        ];
        
        $this->handleError('RATE_LIMIT_EXCEEDED', 'Rate limit exceeded', $context);
    }

    /**
     * Handle authentication errors
     */
    public function handleAuthenticationError(string $reason, array $context = []): void
    {
        $context['reason'] = $reason;
        $this->handleError('AUTHENTICATION_ERROR', 'Authentication failed', $context);
    }

    /**
     * Handle authorization errors
     */
    public function handleAuthorizationError(string $resource, string $action, array $context = []): void
    {
        $context = array_merge($context, [
            'resource' => $resource,
            'action' => $action
        ]);
        
        $this->handleError('AUTHORIZATION_ERROR', 'Access denied', $context);
    }

    /**
     * Get system health status
     */
    public function getSystemHealthStatus(): array
    {
        return $this->errorHandler->getSystemHealthStatus();
    }

    /**
     * Get error statistics
     */
    public function getErrorStatistics(int $timeWindow = 3600): array
    {
        return [
            'system_health' => $this->getSystemHealthStatus(),
            'security_stats' => $this->threatResponse->getSecurityStatistics($timeWindow),
            'error_rates' => $this->getErrorRates($timeWindow),
            'recovery_success_rate' => $this->getRecoverySuccessRate($timeWindow)
        ];
    }

    /**
     * Check if IP is blocked
     */
    public function isIpBlocked(string $ip): bool
    {
        return $this->threatResponse->isIpBlocked($ip);
    }

    /**
     * Get IP reputation
     */
    public function getIpReputation(string $ip): array
    {
        return $this->threatResponse->getIpReputationScore($ip);
    }

    /**
     * Handle SQL injection attempts
     */
    public function handleSqlInjectionAttempt(string $payload, string $field, array $context = []): void
    {
        $response = $this->threatResponse->handleSqlInjectionAttempt($payload, $field, $context);
        
        if ($response['ip_blocked']) {
            $ip = $context['ip'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $this->sendBlockedResponse($ip, $response);
        } else {
            $this->handleError('SQL_INJECTION_ATTEMPT', 'Request blocked for security reasons', $context);
        }
    }

    /**
     * Handle XSS attempts
     */
    public function handleXssAttempt(string $payload, string $field, array $context = []): void
    {
        $response = $this->threatResponse->handleXssAttempt($payload, $field, $context);
        
        if ($response['ip_blocked']) {
            $ip = $context['ip'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $this->sendBlockedResponse($ip, $response);
        } else {
            $this->handleError('XSS_ATTEMPT', 'Request blocked for security reasons', $context);
        }
    }

    /**
     * Handle suspicious activity
     */
    public function handleSuspiciousActivity(string $activityType, array $context = []): void
    {
        $response = $this->threatResponse->handleSuspiciousActivity($activityType, $context);
        
        if ($response['ip_blocked']) {
            $ip = $context['ip'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $this->sendBlockedResponse($ip, $response);
        } else {
            $this->handleError('SUSPICIOUS_ACTIVITY', 'Suspicious activity detected', $context);
        }
    }

    /**
     * Get fallback response when systems fail
     */
    public function getFallbackResponse(string $operation, array $context = []): array
    {
        return $this->errorHandler->getFallbackResponse($operation, $context);
    }

    /**
     * Handle critical system failures
     */
    private function handleCriticalFailure(\Throwable $criticalError): void
    {
        try {
            // Log the critical failure
            $this->logger->critical('Critical error handling failure', [
                'error' => $criticalError->getMessage(),
                'file' => $criticalError->getFile(),
                'line' => $criticalError->getLine(),
                'trace' => $criticalError->getTraceAsString()
            ]);
            
            // Send emergency response
            if (!headers_sent()) {
                echo $this->productionHandler->getCriticalFailureResponse();
            }
            
        } catch (\Throwable $emergencyError) {
            // Last resort - basic error response
            if (!headers_sent()) {
                http_response_code(500);
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => [
                        'code' => 'CRITICAL_FAILURE',
                        'message' => 'A critical system error occurred'
                    ]
                ]);
            }
        }
    }

    /**
     * Send error response
     */
    private function sendErrorResponse(array $errorResponse): void
    {
        http_response_code($errorResponse['http_code']);
        
        $this->responseFormatter->setJsonHeaders();
        $this->responseFormatter->setSecurityHeaders();
        
        echo $this->responseFormatter->formatError(
            $errorResponse['error_code'],
            $errorResponse['message'],
            $errorResponse['details'],
            $errorResponse['http_code']
        );
    }

    /**
     * Send blocked IP response
     */
    private function sendBlockedResponse(string $ip, array $threatResponse): void
    {
        http_response_code(403);
        
        $this->responseFormatter->setJsonHeaders();
        $this->responseFormatter->setSecurityHeaders();
        
        echo $this->responseFormatter->formatError(
            'IP_BLOCKED',
            'Access denied - IP address blocked due to security violations',
            [
                'ip' => $ip,
                'threat_level' => $threatResponse['threat_level'],
                'block_duration' => 'varies',
                'contact_support' => true
            ],
            403
        );
    }

    /**
     * Get error code from exception
     */
    private function getErrorCodeFromException(\Throwable $exception): string
    {
        $class = get_class($exception);
        
        if (strpos($class, 'Database') !== false || strpos($class, 'PDO') !== false) {
            return 'DATABASE_ERROR';
        }
        
        if (strpos($class, 'FreemiusApi') !== false) {
            return 'FREEMIUS_API_ERROR';
        }
        
        if (strpos($class, 'IpRegistryApi') !== false) {
            return 'IP_INTELLIGENCE_ERROR';
        }
        
        if (strpos($class, 'InvalidArgument') !== false) {
            return 'VALIDATION_ERROR';
        }
        
        return 'INTERNAL_ERROR';
    }

    /**
     * Get error rates for monitoring
     */
    private function getErrorRates(int $timeWindow): array
    {
        // This would typically query the database for error statistics
        // For now, return placeholder data
        return [
            'total_errors' => 0,
            'error_rate' => 0.0,
            'most_common_errors' => [],
            'time_window' => $timeWindow
        ];
    }

    /**
     * Get recovery success rate
     */
    private function getRecoverySuccessRate(int $timeWindow): float
    {
        // This would calculate actual recovery success rate
        // For now, return placeholder
        return 0.85; // 85% success rate
    }
}