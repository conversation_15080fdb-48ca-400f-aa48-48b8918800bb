<?php

namespace Skpassegna\GuardgeoApi\Views\Components;

/**
 * Modal Component
 * 
 * Reusable modal component with various sizes and configurations
 * following the design system guidelines.
 */
class Modal extends BaseComponent
{
    protected function getDefaultClasses(): string
    {
        return 'fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center';
    }

    public function render(): string
    {
        $id = $this->prop('id', 'modal');
        $title = $this->prop('title');
        $content = $this->prop('content', '');
        $footer = $this->prop('footer');
        $size = $this->prop('size', 'md');
        $closable = $this->prop('closable', true);
        $visible = $this->prop('visible', false);

        // Size classes
        $sizeClasses = match ($size) {
            'xs' => 'max-w-xs',
            'sm' => 'max-w-sm',
            'md' => 'max-w-md',
            'lg' => 'max-w-lg',
            'xl' => 'max-w-xl',
            '2xl' => 'max-w-2xl',
            '3xl' => 'max-w-3xl',
            '4xl' => 'max-w-4xl',
            'full' => 'max-w-full mx-4',
            default => 'max-w-md'
        };

        $html = '<div id="' . $this->escape($id) . '" class="' . $this->getClasses() . ($visible ? '' : ' hidden') . '"';
        
        if ($this->attributes) {
            $html .= ' ' . $this->renderAttributes();
        }
        
        $html .= '>';

        // Modal content
        $html .= '<div class="bg-white rounded-lg shadow-xl ' . $sizeClasses . ' w-full max-h-screen overflow-y-auto">';

        // Header
        if ($title || $closable) {
            $html .= '<div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">';
            
            if ($title) {
                $html .= '<h3 class="text-lg font-medium text-gray-900">' . $this->escape($title) . '</h3>';
            }
            
            if ($closable) {
                $html .= '<button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="closeModal(\'' . $this->escape($id) . '\')">';
                $html .= '<i class="fas fa-times"></i>';
                $html .= '</button>';
            }
            
            $html .= '</div>';
        }

        // Content
        if ($content) {
            $html .= '<div class="p-6">' . $content . '</div>';
        }

        // Footer
        if ($footer) {
            $html .= '<div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">' . $footer . '</div>';
        }

        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Create a confirmation modal
     */
    public static function confirm(string $title, string $message, array $props = []): self
    {
        $confirmText = $props['confirmText'] ?? 'Confirm';
        $cancelText = $props['cancelText'] ?? 'Cancel';
        $confirmAction = $props['confirmAction'] ?? '';
        $cancelAction = $props['cancelAction'] ?? '';
        $variant = $props['variant'] ?? 'danger';

        $content = '<p class="text-gray-600">' . htmlspecialchars($message, ENT_QUOTES, 'UTF-8') . '</p>';
        
        $footer = '';
        $footer .= (new Button(['text' => $cancelText, 'variant' => 'outline', 'onclick' => $cancelAction]))->render();
        $footer .= (new Button(['text' => $confirmText, 'variant' => $variant, 'onclick' => $confirmAction]))->render();

        return new self(array_merge([
            'title' => $title,
            'content' => $content,
            'footer' => $footer,
            'size' => 'sm'
        ], $props));
    }

    /**
     * Create an info modal
     */
    public static function info(string $title, string $message, array $props = []): self
    {
        $content = '<div class="flex items-start">';
        $content .= '<div class="flex-shrink-0">';
        $content .= '<div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">';
        $content .= '<i class="fas fa-info text-blue-600"></i>';
        $content .= '</div>';
        $content .= '</div>';
        $content .= '<div class="ml-3">';
        $content .= '<p class="text-gray-600">' . htmlspecialchars($message, ENT_QUOTES, 'UTF-8') . '</p>';
        $content .= '</div>';
        $content .= '</div>';

        $footer = (new Button(['text' => 'OK', 'variant' => 'primary', 'onclick' => 'closeModal()']))->render();

        return new self(array_merge([
            'title' => $title,
            'content' => $content,
            'footer' => $footer,
            'size' => 'sm'
        ], $props));
    }
}