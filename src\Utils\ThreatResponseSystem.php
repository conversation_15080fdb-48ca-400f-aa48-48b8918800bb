<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Utils\SecurityAuditLogger;

/**
 * Threat Response System
 * 
 * Automated threat detection and response system for handling
 * security incidents with appropriate escalation and mitigation.
 */
class ThreatResponseSystem
{
    private SecurityAuditLogger $securityLogger;
    private LoggingService $logger;
    private RateLimiter $rateLimiter;
    
    // Threat levels
    public const THREAT_LOW = 'low';
    public const THREAT_MEDIUM = 'medium';
    public const THREAT_HIGH = 'high';
    public const THREAT_CRITICAL = 'critical';
    
    // Response actions
    public const ACTION_LOG = 'log';
    public const ACTION_RATE_LIMIT = 'rate_limit';
    public const ACTION_BLOCK_IP = 'block_ip';
    public const ACTION_ALERT_ADMIN = 'alert_admin';
    public const ACTION_EMERGENCY_BLOCK = 'emergency_block';
    
    // Threat patterns
    private array $threatPatterns = [];
    
    // IP reputation tracking
    private array $ipReputation = [];
    
    // Active blocks
    private array $activeBlocks = [];

    public function __construct(
        SecurityAuditLogger $securityLogger,
        LoggingService $logger,
        RateLimiter $rateLimiter
    ) {
        $this->securityLogger = $securityLogger;
        $this->logger = $logger;
        $this->rateLimiter = $rateLimiter;
        
        $this->initializeThreatPatterns();
    }

    /**
     * Analyze and respond to security event
     */
    public function analyzeSecurityEvent(string $eventType, array $eventData): array
    {
        $ip = $eventData['ip'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $eventData['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        // Analyze threat level
        $threatLevel = $this->analyzeThreatLevel($eventType, $eventData);
        
        // Update IP reputation
        $this->updateIpReputation($ip, $eventType, $threatLevel);
        
        // Determine response actions
        $responseActions = $this->determineResponseActions($threatLevel, $eventType, $ip);
        
        // Execute response actions
        $executionResults = $this->executeResponseActions($responseActions, $ip, $eventData);
        
        // Log the security event and response
        $this->logSecurityResponse($eventType, $eventData, $threatLevel, $responseActions, $executionResults);
        
        return [
            'threat_level' => $threatLevel,
            'actions_taken' => $responseActions,
            'execution_results' => $executionResults,
            'ip_blocked' => in_array(self::ACTION_BLOCK_IP, $responseActions) || in_array(self::ACTION_EMERGENCY_BLOCK, $responseActions),
            'requires_manual_review' => $threatLevel === self::THREAT_CRITICAL
        ];
    }

    /**
     * Check if IP is currently blocked
     */
    public function isIpBlocked(string $ip): bool
    {
        if (!isset($this->activeBlocks[$ip])) {
            return false;
        }
        
        $block = $this->activeBlocks[$ip];
        
        // Check if block has expired
        if ($block['expires_at'] && time() > $block['expires_at']) {
            unset($this->activeBlocks[$ip]);
            return false;
        }
        
        return true;
    }

    /**
     * Get IP reputation score
     */
    public function getIpReputationScore(string $ip): array
    {
        if (!isset($this->ipReputation[$ip])) {
            return [
                'score' => 100, // Default good reputation
                'events' => 0,
                'last_incident' => null,
                'risk_level' => 'low'
            ];
        }
        
        $reputation = $this->ipReputation[$ip];
        
        // Calculate risk level based on score
        $riskLevel = 'low';
        if ($reputation['score'] < 30) {
            $riskLevel = 'critical';
        } elseif ($reputation['score'] < 50) {
            $riskLevel = 'high';
        } elseif ($reputation['score'] < 70) {
            $riskLevel = 'medium';
        }
        
        return array_merge($reputation, ['risk_level' => $riskLevel]);
    }

    /**
     * Handle SQL injection attempt
     */
    public function handleSqlInjectionAttempt(string $payload, string $field, array $context = []): array
    {
        $eventData = array_merge($context, [
            'payload' => substr($payload, 0, 200), // Limit payload size in logs
            'field' => $field,
            'payload_length' => strlen($payload),
            'detection_patterns' => $this->detectSqlInjectionPatterns($payload)
        ]);
        
        return $this->analyzeSecurityEvent('sql_injection_attempt', $eventData);
    }

    /**
     * Handle XSS attempt
     */
    public function handleXssAttempt(string $payload, string $field, array $context = []): array
    {
        $eventData = array_merge($context, [
            'payload' => substr($payload, 0, 200),
            'field' => $field,
            'payload_length' => strlen($payload),
            'detection_patterns' => $this->detectXssPatterns($payload)
        ]);
        
        return $this->analyzeSecurityEvent('xss_attempt', $eventData);
    }

    /**
     * Handle suspicious request patterns
     */
    public function handleSuspiciousActivity(string $activityType, array $context = []): array
    {
        $eventData = array_merge($context, [
            'activity_type' => $activityType,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
        ]);
        
        return $this->analyzeSecurityEvent('suspicious_activity', $eventData);
    }

    /**
     * Handle brute force attempts
     */
    public function handleBruteForceAttempt(string $target, array $context = []): array
    {
        $eventData = array_merge($context, [
            'target' => $target,
            'attempt_count' => $this->getBruteForceAttemptCount($context['ip'] ?? 'unknown', $target)
        ]);
        
        return $this->analyzeSecurityEvent('brute_force_attempt', $eventData);
    }

    /**
     * Get security statistics
     */
    public function getSecurityStatistics(int $timeWindow = 3600): array
    {
        return [
            'active_blocks' => count($this->activeBlocks),
            'high_risk_ips' => $this->getHighRiskIpCount(),
            'recent_incidents' => $this->getRecentIncidentCount($timeWindow),
            'threat_distribution' => $this->getThreatDistribution($timeWindow),
            'top_attack_types' => $this->getTopAttackTypes($timeWindow),
            'response_effectiveness' => $this->getResponseEffectiveness()
        ];
    }

    /**
     * Initialize threat patterns
     */
    private function initializeThreatPatterns(): void
    {
        $this->threatPatterns = [
            'sql_injection_attempt' => [
                'base_threat_level' => self::THREAT_HIGH,
                'escalation_factors' => [
                    'multiple_attempts' => 2.0,
                    'complex_payload' => 1.5,
                    'known_bad_ip' => 2.0
                ]
            ],
            'xss_attempt' => [
                'base_threat_level' => self::THREAT_MEDIUM,
                'escalation_factors' => [
                    'multiple_attempts' => 2.0,
                    'script_injection' => 1.5,
                    'known_bad_ip' => 2.0
                ]
            ],
            'brute_force_attempt' => [
                'base_threat_level' => self::THREAT_MEDIUM,
                'escalation_factors' => [
                    'high_frequency' => 2.0,
                    'multiple_targets' => 1.5,
                    'distributed_source' => 1.3
                ]
            ],
            'suspicious_activity' => [
                'base_threat_level' => self::THREAT_LOW,
                'escalation_factors' => [
                    'repeated_behavior' => 1.5,
                    'known_bad_ip' => 2.0,
                    'unusual_patterns' => 1.3
                ]
            ]
        ];
    }

    /**
     * Analyze threat level
     */
    private function analyzeThreatLevel(string $eventType, array $eventData): string
    {
        $pattern = $this->threatPatterns[$eventType] ?? null;
        if (!$pattern) {
            return self::THREAT_LOW;
        }
        
        $baseThreatLevel = $pattern['base_threat_level'];
        $escalationFactors = $pattern['escalation_factors'];
        
        $escalationScore = 1.0;
        
        // Check escalation factors
        $ip = $eventData['ip'] ?? 'unknown';
        $ipReputation = $this->getIpReputationScore($ip);
        
        if ($ipReputation['risk_level'] === 'high' || $ipReputation['risk_level'] === 'critical') {
            $escalationScore *= $escalationFactors['known_bad_ip'] ?? 1.0;
        }
        
        // Check for multiple attempts
        if ($this->hasMultipleRecentAttempts($ip, $eventType)) {
            $escalationScore *= $escalationFactors['multiple_attempts'] ?? 1.0;
        }
        
        // Determine final threat level
        if ($escalationScore >= 3.0) {
            return self::THREAT_CRITICAL;
        } elseif ($escalationScore >= 2.0) {
            return self::THREAT_HIGH;
        } elseif ($escalationScore >= 1.5) {
            return self::THREAT_MEDIUM;
        }
        
        return $baseThreatLevel;
    }

    /**
     * Determine response actions based on threat level
     */
    private function determineResponseActions(string $threatLevel, string $eventType, string $ip): array
    {
        $actions = [self::ACTION_LOG]; // Always log
        
        switch ($threatLevel) {
            case self::THREAT_CRITICAL:
                $actions[] = self::ACTION_EMERGENCY_BLOCK;
                $actions[] = self::ACTION_ALERT_ADMIN;
                break;
                
            case self::THREAT_HIGH:
                $actions[] = self::ACTION_BLOCK_IP;
                $actions[] = self::ACTION_ALERT_ADMIN;
                break;
                
            case self::THREAT_MEDIUM:
                $actions[] = self::ACTION_RATE_LIMIT;
                if ($this->hasMultipleRecentAttempts($ip, $eventType)) {
                    $actions[] = self::ACTION_BLOCK_IP;
                }
                break;
                
            case self::THREAT_LOW:
                // Only log for low threats
                break;
        }
        
        return $actions;
    }

    /**
     * Execute response actions
     */
    private function executeResponseActions(array $actions, string $ip, array $eventData): array
    {
        $results = [];
        
        foreach ($actions as $action) {
            try {
                switch ($action) {
                    case self::ACTION_LOG:
                        $results[$action] = $this->executeLogAction($eventData);
                        break;
                        
                    case self::ACTION_RATE_LIMIT:
                        $results[$action] = $this->executeRateLimitAction($ip);
                        break;
                        
                    case self::ACTION_BLOCK_IP:
                        $results[$action] = $this->executeBlockIpAction($ip, 3600); // 1 hour block
                        break;
                        
                    case self::ACTION_EMERGENCY_BLOCK:
                        $results[$action] = $this->executeBlockIpAction($ip, 86400); // 24 hour block
                        break;
                        
                    case self::ACTION_ALERT_ADMIN:
                        $results[$action] = $this->executeAlertAdminAction($eventData);
                        break;
                }
            } catch (\Exception $e) {
                $results[$action] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }

    /**
     * Update IP reputation based on security event
     */
    private function updateIpReputation(string $ip, string $eventType, string $threatLevel): void
    {
        if (!isset($this->ipReputation[$ip])) {
            $this->ipReputation[$ip] = [
                'score' => 100,
                'events' => 0,
                'last_incident' => null,
                'incident_types' => []
            ];
        }
        
        $reputation = &$this->ipReputation[$ip];
        $reputation['events']++;
        $reputation['last_incident'] = time();
        
        if (!isset($reputation['incident_types'][$eventType])) {
            $reputation['incident_types'][$eventType] = 0;
        }
        $reputation['incident_types'][$eventType]++;
        
        // Decrease reputation score based on threat level
        $scoreDecrease = match($threatLevel) {
            self::THREAT_CRITICAL => 50,
            self::THREAT_HIGH => 30,
            self::THREAT_MEDIUM => 15,
            self::THREAT_LOW => 5,
            default => 5
        };
        
        $reputation['score'] = max(0, $reputation['score'] - $scoreDecrease);
    }

    /**
     * Execute individual response actions
     */
    private function executeLogAction(array $eventData): array
    {
        // Already logged by the calling method
        return ['success' => true];
    }

    private function executeRateLimitAction(string $ip): array
    {
        // Implement stricter rate limiting for this IP
        return ['success' => true, 'action' => 'rate_limit_applied'];
    }

    private function executeBlockIpAction(string $ip, int $duration): array
    {
        $this->activeBlocks[$ip] = [
            'blocked_at' => time(),
            'expires_at' => time() + $duration,
            'duration' => $duration,
            'reason' => 'security_threat'
        ];
        
        return [
            'success' => true,
            'action' => 'ip_blocked',
            'duration' => $duration,
            'expires_at' => time() + $duration
        ];
    }

    private function executeAlertAdminAction(array $eventData): array
    {
        // In a real implementation, this would send notifications
        $this->logger->critical('Security alert: Administrator notification required', $eventData);
        
        return ['success' => true, 'action' => 'admin_alerted'];
    }

    /**
     * Check for multiple recent attempts
     */
    private function hasMultipleRecentAttempts(string $ip, string $eventType): bool
    {
        // This would check recent security events from the database
        // For now, return false as placeholder
        return false;
    }

    /**
     * Detect SQL injection patterns
     */
    private function detectSqlInjectionPatterns(string $payload): array
    {
        $patterns = [
            'union_select' => '/union\s+select/i',
            'or_1_equals_1' => '/or\s+1\s*=\s*1/i',
            'drop_table' => '/drop\s+table/i',
            'insert_into' => '/insert\s+into/i',
            'delete_from' => '/delete\s+from/i',
            'update_set' => '/update\s+.*\s+set/i'
        ];
        
        $detected = [];
        foreach ($patterns as $name => $pattern) {
            if (preg_match($pattern, $payload)) {
                $detected[] = $name;
            }
        }
        
        return $detected;
    }

    /**
     * Detect XSS patterns
     */
    private function detectXssPatterns(string $payload): array
    {
        $patterns = [
            'script_tag' => '/<script/i',
            'javascript_protocol' => '/javascript:/i',
            'on_event' => '/on\w+\s*=/i',
            'iframe_tag' => '/<iframe/i',
            'object_tag' => '/<object/i'
        ];
        
        $detected = [];
        foreach ($patterns as $name => $pattern) {
            if (preg_match($pattern, $payload)) {
                $detected[] = $name;
            }
        }
        
        return $detected;
    }

    /**
     * Get brute force attempt count
     */
    private function getBruteForceAttemptCount(string $ip, string $target): int
    {
        // This would query the database for recent attempts
        // For now, return 0 as placeholder
        return 0;
    }

    /**
     * Log security response
     */
    private function logSecurityResponse(string $eventType, array $eventData, string $threatLevel, array $actions, array $results): void
    {
        $this->securityLogger->logSecurityEvent('threat_response', [
            'original_event_type' => $eventType,
            'original_event_data' => $eventData,
            'threat_level' => $threatLevel,
            'response_actions' => $actions,
            'execution_results' => $results,
            'timestamp' => date('c')
        ]);
    }

    /**
     * Placeholder methods for statistics
     */
    private function getHighRiskIpCount(): int
    {
        $count = 0;
        foreach ($this->ipReputation as $reputation) {
            if ($reputation['score'] < 50) {
                $count++;
            }
        }
        return $count;
    }

    private function getRecentIncidentCount(int $timeWindow): int
    {
        return 0; // Placeholder
    }

    private function getThreatDistribution(int $timeWindow): array
    {
        return []; // Placeholder
    }

    private function getTopAttackTypes(int $timeWindow): array
    {
        return []; // Placeholder
    }

    private function getResponseEffectiveness(): float
    {
        return 0.95; // Placeholder - 95% effectiveness
    }
}