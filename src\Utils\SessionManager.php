<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Session Manager
 * 
 * Handles secure session management with proper cookie handling
 * and session security measures.
 */
class SessionManager
{
    private string $sessionName;
    private int $sessionLifetime;
    private bool $sessionStarted = false;
    private ?SessionSecurityManager $securityManager = null;

    public function __construct(
        string $sessionName = 'GUARDGEO_ADMIN_SESSION', 
        int $sessionLifetime = 86400,
        LoggingService $logger = null
    ) {
        $this->sessionName = $sessionName;
        $this->sessionLifetime = $sessionLifetime; // 24 hours default
        
        if ($logger) {
            $this->securityManager = new SessionSecurityManager($logger, [
                'session_timeout' => $sessionLifetime,
                'idle_timeout' => min(1800, $sessionLifetime / 2) // 30 minutes or half session lifetime
            ]);
        }
    }

    /**
     * Start secure session
     *
     * @return void
     */
    public function startSession(): void
    {
        if ($this->sessionStarted || session_status() === PHP_SESSION_ACTIVE) {
            return;
        }

        // Configure secure session settings
        ini_set('session.cookie_httponly', '1');
        ini_set('session.cookie_secure', $this->isHttps() ? '1' : '0');
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.use_strict_mode', '1');
        ini_set('session.cookie_lifetime', (string)$this->sessionLifetime);
        
        // Set session name
        session_name($this->sessionName);
        
        // Start session
        session_start();
        
        $this->sessionStarted = true;

        // Regenerate session ID periodically for security
        if (!isset($_SESSION['last_regeneration'])) {
            $this->regenerateSessionId();
        } elseif (time() - $_SESSION['last_regeneration'] > 1800) { // 30 minutes
            $this->regenerateSessionId();
        }
    }

    /**
     * Create new session with user data
     *
     * @param array $userData
     * @return string Session ID
     */
    public function createSession(array $userData): string
    {
        $this->startSession();
        
        // Clear any existing session data
        session_unset();
        
        // Regenerate session ID for security
        $this->regenerateSessionId();
        
        // Store user data
        $_SESSION['user_data'] = $userData;
        $_SESSION['csrf_token'] = $this->generateCsrfToken();
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? '';
        
        return session_id();
    }

    /**
     * Get current session data
     *
     * @return array|null Session data or null if no valid session
     */
    public function getSession(): ?array
    {
        $this->startSession();
        
        if (!isset($_SESSION['user_data'])) {
            return null;
        }

        // Validate session integrity
        if (!$this->validateSessionIntegrity()) {
            $this->destroySession();
            return null;
        }

        return $_SESSION['user_data'];
    }

    /**
     * Destroy current session
     *
     * @return bool Success status
     */
    public function destroySession(): bool
    {
        $this->startSession();
        
        // Clear session data
        session_unset();
        
        // Destroy session
        $destroyed = session_destroy();
        
        // Clear session cookie
        if (isset($_COOKIE[$this->sessionName])) {
            setcookie(
                $this->sessionName,
                '',
                time() - 3600,
                '/',
                '',
                $this->isHttps(),
                true
            );
        }
        
        $this->sessionStarted = false;
        
        return $destroyed;
    }

    /**
     * Get CSRF token for current session
     *
     * @return string|null CSRF token or null if no session
     */
    public function getCsrfToken(): ?string
    {
        $this->startSession();
        
        return $_SESSION['csrf_token'] ?? null;
    }

    /**
     * Validate CSRF token
     *
     * @param string $token
     * @return bool True if token is valid
     */
    public function validateCsrfToken(string $token): bool
    {
        $sessionToken = $this->getCsrfToken();
        
        if (!$sessionToken) {
            return false;
        }
        
        return hash_equals($sessionToken, $token);
    }

    /**
     * Regenerate session ID for security
     *
     * @return void
     */
    private function regenerateSessionId(): void
    {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }

    /**
     * Generate secure CSRF token
     *
     * @return string CSRF token
     */
    private function generateCsrfToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Validate session integrity
     *
     * @return bool True if session is valid
     */
    private function validateSessionIntegrity(): bool
    {
        // Check user agent consistency
        $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $sessionUserAgent = $_SESSION['user_agent'] ?? '';
        
        if ($currentUserAgent !== $sessionUserAgent) {
            return false;
        }

        // Check IP address consistency (optional, can be disabled for mobile users)
        $currentIp = $_SERVER['REMOTE_ADDR'] ?? '';
        $sessionIp = $_SESSION['ip_address'] ?? '';
        
        // Allow IP changes for now, but log them for monitoring
        if ($currentIp !== $sessionIp) {
            error_log("Session IP change detected: {$sessionIp} -> {$currentIp}");
        }

        return true;
    }

    /**
     * Check if connection is HTTPS
     *
     * @return bool True if HTTPS
     */
    private function isHttps(): bool
    {
        return (
            (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
            $_SERVER['SERVER_PORT'] == 443 ||
            (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
            (!empty($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on')
        );
    }

    /**
     * Get session lifetime in seconds
     *
     * @return int Session lifetime
     */
    public function getSessionLifetime(): int
    {
        return $this->sessionLifetime;
    }

    /**
     * Set session lifetime
     *
     * @param int $lifetime Lifetime in seconds
     * @return void
     */
    public function setSessionLifetime(int $lifetime): void
    {
        $this->sessionLifetime = $lifetime;
    }
}