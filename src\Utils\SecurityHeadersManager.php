<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Security Headers Manager
 * 
 * Comprehensive security headers implementation with production-ready
 * configurations and environment-specific policies.
 */
class SecurityHeadersManager
{
    private LoggingService $logger;
    private array $config;
    private bool $headersSet = false;

    // Default security configuration
    private const DEFAULT_CONFIG = [
        'environment' => 'production',
        'force_https' => true,
        'hsts_max_age' => 31536000, // 1 year
        'hsts_include_subdomains' => true,
        'hsts_preload' => true,
        'csp_report_uri' => null,
        'csp_report_only' => false,
        'frame_options' => 'DENY',
        'content_type_options' => 'nosniff',
        'xss_protection' => '1; mode=block',
        'referrer_policy' => 'strict-origin-when-cross-origin',
        'permissions_policy_enabled' => true,
        'cross_origin_policies_enabled' => true,
        'cache_control_sensitive' => 'no-store, no-cache, must-revalidate, max-age=0',
        'custom_headers' => []
    ];

    // Content Security Policy templates
    private const CSP_TEMPLATES = [
        'strict' => [
            'default-src' => "'self'",
            'script-src' => "'self'",
            'style-src' => "'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com",
            'font-src' => "'self' https://fonts.gstatic.com",
            'img-src' => "'self' data: https:",
            'connect-src' => "'self'",
            'object-src' => "'none'",
            'base-uri' => "'self'",
            'form-action' => "'self'",
            'frame-ancestors' => "'none'",
            'upgrade-insecure-requests' => ''
        ],
        'moderate' => [
            'default-src' => "'self'",
            'script-src' => "'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com",
            'style-src' => "'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com",
            'font-src' => "'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com",
            'img-src' => "'self' data: https:",
            'connect-src' => "'self'",
            'object-src' => "'none'",
            'base-uri' => "'self'",
            'form-action' => "'self'",
            'frame-ancestors' => "'none'",
            'upgrade-insecure-requests' => ''
        ],
        'development' => [
            'default-src' => "'self'",
            'script-src' => "'self' 'unsafe-inline' 'unsafe-eval' https:",
            'style-src' => "'self' 'unsafe-inline' https:",
            'font-src' => "'self' https:",
            'img-src' => "'self' data: https:",
            'connect-src' => "'self' ws: wss:",
            'object-src' => "'none'",
            'base-uri' => "'self'",
            'form-action' => "'self'",
            'frame-ancestors' => "'none'"
        ]
    ];

    public function __construct(LoggingService $logger = null, array $config = [])
    {
        $this->logger = $logger ?? new LoggingService();
        $this->config = array_merge(self::DEFAULT_CONFIG, $config);
        
        // Auto-detect environment if not specified
        if (!isset($config['environment'])) {
            $this->config['environment'] = $this->detectEnvironment();
        }
    }

    /**
     * Set all security headers
     *
     * @param string|null $pageType Type of page (admin, api, public)
     * @return bool Success status
     */
    public function setSecurityHeaders(?string $pageType = null): bool
    {
        if ($this->headersSet) {
            return true; // Headers already set
        }

        try {
            // Basic security headers
            $this->setBasicSecurityHeaders();
            
            // HTTPS enforcement
            if ($this->config['force_https']) {
                $this->setHttpsHeaders();
            }
            
            // Content Security Policy
            $this->setContentSecurityPolicy($pageType);
            
            // Permissions Policy
            if ($this->config['permissions_policy_enabled']) {
                $this->setPermissionsPolicy();
            }
            
            // Cross-Origin policies
            if ($this->config['cross_origin_policies_enabled']) {
                $this->setCrossOriginPolicies();
            }
            
            // Page-specific headers
            $this->setPageSpecificHeaders($pageType);
            
            // Custom headers
            $this->setCustomHeaders();
            
            $this->headersSet = true;
            
            $this->logger->logInfo('Security headers set', [
                'page_type' => $pageType,
                'environment' => $this->config['environment'],
                'https' => $this->isHttps()
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            $this->logger->logError('Failed to set security headers', [
                'error' => $e->getMessage(),
                'page_type' => $pageType
            ]);
            return false;
        }
    }

    /**
     * Set basic security headers
     *
     * @return void
     */
    private function setBasicSecurityHeaders(): void
    {
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: ' . $this->config['content_type_options']);
        
        // Prevent clickjacking
        header('X-Frame-Options: ' . $this->config['frame_options']);
        
        // XSS protection
        header('X-XSS-Protection: ' . $this->config['xss_protection']);
        
        // Referrer policy
        header('Referrer-Policy: ' . $this->config['referrer_policy']);
        
        // Additional security headers
        header('X-Permitted-Cross-Domain-Policies: none');
        header('X-Download-Options: noopen');
        header('X-DNS-Prefetch-Control: off');
    }

    /**
     * Set HTTPS-related headers
     *
     * @return void
     */
    private function setHttpsHeaders(): void
    {
        if (!$this->isHttps() && $this->config['environment'] === 'production') {
            // Redirect to HTTPS in production
            $httpsUrl = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
            header('Location: ' . $httpsUrl, true, 301);
            exit;
        }
        
        if ($this->isHttps()) {
            // HTTP Strict Transport Security
            $hstsValue = 'max-age=' . $this->config['hsts_max_age'];
            
            if ($this->config['hsts_include_subdomains']) {
                $hstsValue .= '; includeSubDomains';
            }
            
            if ($this->config['hsts_preload']) {
                $hstsValue .= '; preload';
            }
            
            header('Strict-Transport-Security: ' . $hstsValue);
        }
    }

    /**
     * Set Content Security Policy
     *
     * @param string|null $pageType
     * @return void
     */
    private function setContentSecurityPolicy(?string $pageType): void
    {
        $cspTemplate = $this->getCspTemplate();
        $csp = $this->buildCspString($cspTemplate, $pageType);
        
        if ($this->config['csp_report_only']) {
            header('Content-Security-Policy-Report-Only: ' . $csp);
        } else {
            header('Content-Security-Policy: ' . $csp);
        }
        
        // Add report URI if configured
        if ($this->config['csp_report_uri']) {
            $reportCsp = $csp . '; report-uri ' . $this->config['csp_report_uri'];
            header('Content-Security-Policy-Report-Only: ' . $reportCsp);
        }
    }

    /**
     * Set Permissions Policy
     *
     * @return void
     */
    private function setPermissionsPolicy(): void
    {
        $policies = [
            'geolocation' => '()',
            'microphone' => '()',
            'camera' => '()',
            'payment' => '()',
            'usb' => '()',
            'magnetometer' => '()',
            'gyroscope' => '()',
            'speaker' => '()',
            'vibrate' => '()',
            'fullscreen' => '(self)',
            'sync-xhr' => '()',
            'autoplay' => '()',
            'encrypted-media' => '()',
            'picture-in-picture' => '()',
            'accelerometer' => '()',
            'ambient-light-sensor' => '()',
            'battery' => '()',
            'display-capture' => '()',
            'document-domain' => '()',
            'execution-while-not-rendered' => '()',
            'execution-while-out-of-viewport' => '()',
            'gamepad' => '()',
            'hid' => '()',
            'idle-detection' => '()',
            'local-fonts' => '()',
            'midi' => '()',
            'navigation-override' => '()',
            'publickey-credentials-get' => '()',
            'screen-wake-lock' => '()',
            'serial' => '()',
            'web-share' => '()',
            'xr-spatial-tracking' => '()'
        ];
        
        $policyString = implode(', ', array_map(
            fn($key, $value) => $key . '=' . $value,
            array_keys($policies),
            $policies
        ));
        
        header('Permissions-Policy: ' . $policyString);
    }

    /**
     * Set Cross-Origin policies
     *
     * @return void
     */
    private function setCrossOriginPolicies(): void
    {
        header('Cross-Origin-Embedder-Policy: require-corp');
        header('Cross-Origin-Opener-Policy: same-origin');
        header('Cross-Origin-Resource-Policy: same-origin');
    }

    /**
     * Set page-specific headers
     *
     * @param string|null $pageType
     * @return void
     */
    private function setPageSpecificHeaders(?string $pageType): void
    {
        switch ($pageType) {
            case 'admin':
                // Strict caching for admin pages
                header('Cache-Control: ' . $this->config['cache_control_sensitive']);
                header('Pragma: no-cache');
                header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
                break;
                
            case 'api':
                // API-specific headers
                header('Cache-Control: no-cache, must-revalidate');
                header('X-Robots-Tag: noindex, nofollow, noarchive, nosnippet');
                break;
                
            case 'public':
                // Public pages can have some caching
                header('Cache-Control: public, max-age=300'); // 5 minutes
                break;
                
            default:
                // Default caching policy
                header('Cache-Control: private, max-age=0');
                break;
        }
    }

    /**
     * Set custom headers
     *
     * @return void
     */
    private function setCustomHeaders(): void
    {
        foreach ($this->config['custom_headers'] as $name => $value) {
            header($name . ': ' . $value);
        }
    }

    /**
     * Get CSP template based on environment
     *
     * @return array
     */
    private function getCspTemplate(): array
    {
        switch ($this->config['environment']) {
            case 'development':
                return self::CSP_TEMPLATES['development'];
            case 'staging':
                return self::CSP_TEMPLATES['moderate'];
            case 'production':
            default:
                return self::CSP_TEMPLATES['strict'];
        }
    }

    /**
     * Build CSP string from template
     *
     * @param array $template
     * @param string|null $pageType
     * @return string
     */
    private function buildCspString(array $template, ?string $pageType): string
    {
        // Modify template based on page type
        if ($pageType === 'admin') {
            // Admin pages might need additional script sources
            $template['script-src'] .= " 'unsafe-inline'";
        }
        
        $cspParts = [];
        foreach ($template as $directive => $value) {
            if ($value !== '') {
                $cspParts[] = $directive . ' ' . $value;
            } else {
                $cspParts[] = $directive;
            }
        }
        
        return implode('; ', $cspParts);
    }

    /**
     * Detect environment
     *
     * @return string
     */
    private function detectEnvironment(): string
    {
        $env = $_ENV['APP_ENV'] ?? $_SERVER['APP_ENV'] ?? 'production';
        return strtolower($env);
    }

    /**
     * Check if connection is HTTPS
     *
     * @return bool
     */
    private function isHttps(): bool
    {
        return (
            (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
            $_SERVER['SERVER_PORT'] == 443 ||
            (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
            (!empty($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on')
        );
    }

    /**
     * Add CSP nonce for inline scripts
     *
     * @return string Generated nonce
     */
    public function generateCspNonce(): string
    {
        if (!isset($_SESSION['csp_nonce'])) {
            $_SESSION['csp_nonce'] = base64_encode(random_bytes(16));
        }
        
        return $_SESSION['csp_nonce'];
    }

    /**
     * Set CSP header with nonce
     *
     * @param string $nonce
     * @param string|null $pageType
     * @return void
     */
    public function setCspWithNonce(string $nonce, ?string $pageType = null): void
    {
        $cspTemplate = $this->getCspTemplate();
        
        // Add nonce to script-src
        $cspTemplate['script-src'] .= " 'nonce-" . $nonce . "'";
        
        $csp = $this->buildCspString($cspTemplate, $pageType);
        
        if ($this->config['csp_report_only']) {
            header('Content-Security-Policy-Report-Only: ' . $csp);
        } else {
            header('Content-Security-Policy: ' . $csp);
        }
    }

    /**
     * Check if headers have been set
     *
     * @return bool
     */
    public function areHeadersSet(): bool
    {
        return $this->headersSet;
    }

    /**
     * Reset headers flag (for testing)
     *
     * @return void
     */
    public function resetHeadersFlag(): void
    {
        $this->headersSet = false;
    }

    /**
     * Get security headers report
     *
     * @return array
     */
    public function getSecurityReport(): array
    {
        return [
            'headers_set' => $this->headersSet,
            'environment' => $this->config['environment'],
            'https_enabled' => $this->isHttps(),
            'hsts_enabled' => $this->config['force_https'] && $this->isHttps(),
            'csp_enabled' => true,
            'permissions_policy_enabled' => $this->config['permissions_policy_enabled'],
            'cross_origin_policies_enabled' => $this->config['cross_origin_policies_enabled'],
            'custom_headers_count' => count($this->config['custom_headers'])
        ];
    }

    /**
     * Update configuration
     *
     * @param array $newConfig
     * @return void
     */
    public function updateConfiguration(array $newConfig): void
    {
        $this->config = array_merge($this->config, $newConfig);
        $this->headersSet = false; // Reset to allow re-setting with new config
    }

    /**
     * Get current configuration
     *
     * @return array
     */
    public function getConfiguration(): array
    {
        return $this->config;
    }

    /**
     * Validate CSP policy
     *
     * @param array $policy
     * @return array Validation result
     */
    public function validateCspPolicy(array $policy): array
    {
        $result = ['valid' => true, 'errors' => [], 'warnings' => []];
        
        $requiredDirectives = ['default-src', 'script-src', 'object-src'];
        foreach ($requiredDirectives as $directive) {
            if (!isset($policy[$directive])) {
                $result['errors'][] = "Missing required directive: $directive";
                $result['valid'] = false;
            }
        }
        
        // Check for unsafe directives
        foreach ($policy as $directive => $value) {
            if (strpos($value, "'unsafe-eval'") !== false) {
                $result['warnings'][] = "Unsafe directive 'unsafe-eval' found in $directive";
            }
            
            if (strpos($value, "'unsafe-inline'") !== false && $directive === 'script-src') {
                $result['warnings'][] = "Consider using nonces instead of 'unsafe-inline' for scripts";
            }
        }
        
        return $result;
    }
}