<?php

namespace Skpassegna\GuardgeoApi\Views\Pages;

use Skpassegna\GuardgeoApi\Views\Templates\BaseTemplate;
use Skpassegna\GuardgeoApi\Views\Components\Card;
use Skpassegna\GuardgeoApi\Views\Components\Button;

/**
 * Freemius Page Template
 * 
 * Freemius integration management page with products and installations
 * using the component-based design system.
 */
class FreemiusPage extends BaseTemplate
{
    public function render(): string
    {
        $canManage = $this->get('canManage', false);
        
        return $this->renderStatisticsCards() . 
               $this->renderFreemiusTabs($canManage) . 
               $this->renderPageScript($canManage);
    }

    private function renderStatisticsCards(): string
    {
        $productsCard = Card::stat([
            'icon' => 'fas fa-plug',
            'iconColor' => 'bg-blue-500',
            'title' => 'Products',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('total-products-card');

        $installationsCard = Card::stat([
            'icon' => 'fas fa-download',
            'iconColor' => 'bg-green-500',
            'title' => 'Installations',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('total-installations-card');

        $premiumCard = Card::stat([
            'icon' => 'fas fa-crown',
            'iconColor' => 'bg-purple-500',
            'title' => 'Premium',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('premium-installations-card');

        $activeCard = Card::stat([
            'icon' => 'fas fa-check-circle',
            'iconColor' => 'bg-yellow-500',
            'title' => 'Active',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('active-installations-card');

        return <<<HTML
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {$productsCard->render()}
            {$installationsCard->render()}
            {$premiumCard->render()}
            {$activeCard->render()}
        </div>
HTML;
    }

    private function renderFreemiusTabs(bool $canManage): string
    {
        $freemiusCard = new Card([
            'content' => $this->getFreemiusTabsContent($canManage)
        ]);

        return $freemiusCard->render();
    }

    private function getFreemiusTabsContent(bool $canManage): string
    {
        $syncProductsBtn = $canManage ? 
            Button::primary('Sync Products', [
                'icon' => 'fas fa-sync',
                'onclick' => 'syncProducts()',
                'id' => 'syncProductsBtn'
            ])->render() : '';

        $syncInstallationsBtn = $canManage ? 
            Button::secondary('Sync Installations', [
                'icon' => 'fas fa-sync',
                'onclick' => 'syncInstallations()',
                'id' => 'syncInstallationsBtn'
            ])->render() : '';

        return <<<HTML
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex">
                <button id="productsTab" class="py-2 px-4 border-b-2 border-blue-500 text-blue-600 font-medium text-sm">
                    Products
                </button>
                <button id="installationsTab" class="py-2 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm ml-8">
                    Installations
                </button>
            </nav>
        </div>

        <!-- Products Tab Content -->
        <div id="productsContent" class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Freemius Products</h3>
                {$syncProductsBtn}
            </div>
            
            <div id="productsTable" class="overflow-x-auto">
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                    <p class="text-gray-500">Loading products...</p>
                </div>
            </div>
        </div>

        <!-- Installations Tab Content -->
        <div id="installationsContent" class="p-6 hidden">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Freemius Installations</h3>
                {$syncInstallationsBtn}
            </div>
            
            <div id="installationsTable" class="overflow-x-auto">
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                    <p class="text-gray-500">Loading installations...</p>
                </div>
            </div>
        </div>
HTML;
    }

    private function renderPageScript(bool $canManage): string
    {
        $canManageJs = $canManage ? 'true' : 'false';
        
        return <<<HTML
        <script>
            class FreemiusManager {
                constructor() {
                    this.currentTab = 'products';
                    this.canManage = {$canManageJs};
                    this.init();
                }

                init() {
                    this.loadFreemiusStatistics();
                    this.loadProducts();
                    this.bindEvents();
                }

                bindEvents() {
                    // Tab switching
                    document.getElementById('productsTab').addEventListener('click', () => {
                        this.showTab('products');
                    });

                    document.getElementById('installationsTab').addEventListener('click', () => {
                        this.showTab('installations');
                        if (this.currentTab !== 'installations') {
                            this.loadInstallations();
                        }
                    });

                    // Sync buttons
                    const syncProductsBtn = document.getElementById('syncProductsBtn');
                    if (syncProductsBtn) {
                        syncProductsBtn.addEventListener('click', () => this.syncProducts());
                    }

                    const syncInstallationsBtn = document.getElementById('syncInstallationsBtn');
                    if (syncInstallationsBtn) {
                        syncInstallationsBtn.addEventListener('click', () => this.syncInstallations());
                    }
                }

                showTab(tab) {
                    this.currentTab = tab;
                    
                    // Update tab buttons
                    document.getElementById('productsTab').className = tab === 'products' ? 
                        'py-2 px-4 border-b-2 border-blue-500 text-blue-600 font-medium text-sm' :
                        'py-2 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm ml-8';
                        
                    document.getElementById('installationsTab').className = tab === 'installations' ? 
                        'py-2 px-4 border-b-2 border-blue-500 text-blue-600 font-medium text-sm ml-8' :
                        'py-2 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm ml-8';
                    
                    // Show/hide content
                    document.getElementById('productsContent').classList.toggle('hidden', tab !== 'products');
                    document.getElementById('installationsContent').classList.toggle('hidden', tab !== 'installations');
                }

                async loadFreemiusStatistics() {
                    try {
                        const response = await fetch('/admin/api/freemius/statistics');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateStatistics(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading Freemius statistics:', error);
                    }
                }

                async loadProducts() {
                    try {
                        const response = await fetch('/admin/api/freemius/products');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateProductsTable(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading products:', error);
                    }
                }

                async loadInstallations() {
                    try {
                        const response = await fetch('/admin/api/freemius/installations');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateInstallationsTable(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading installations:', error);
                    }
                }

                updateStatistics(stats) {
                    this.updateStatCard('.total-products-card', stats.total_products || 0);
                    this.updateStatCard('.total-installations-card', stats.total_installations || 0);
                    this.updateStatCard('.premium-installations-card', stats.premium_installations || 0);
                    this.updateStatCard('.active-installations-card', stats.active_installations || 0);
                }

                updateStatCard(selector, value) {
                    const card = document.querySelector(selector);
                    if (card) {
                        const valueElement = card.querySelector('.text-2xl');
                        if (valueElement) {
                            valueElement.textContent = value;
                        }
                    }
                }

                updateProductsTable(products) {
                    const container = document.getElementById('productsTable');

                    if (products.length === 0) {
                        container.innerHTML = '<p class="text-gray-500 text-center py-8">No products found</p>';
                        return;
                    }

                    let html = `
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Updated</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                    `;

                    products.forEach(product => {
                        const statusClass = product.is_released ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
                        const statusText = product.is_released ? 'Released' : 'Development';

                        html += `
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">\${product.title}</div>
                                    <div class="text-xs text-gray-500">ID: \${product.id}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">\${product.type}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \${statusClass}">
                                        \${statusText}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">\${product.updated}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="freemiusManager.viewProduct(\${product.id})" class="text-blue-600 hover:text-blue-900">View</button>
                                </td>
                            </tr>
                        `;
                    });

                    html += '</tbody></table>';
                    container.innerHTML = html;
                }

                updateInstallationsTable(installations) {
                    const container = document.getElementById('installationsTable');

                    if (installations.length === 0) {
                        container.innerHTML = '<p class="text-gray-500 text-center py-8">No installations found</p>';
                        return;
                    }

                    let html = `
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Site</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Version</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Premium</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                    `;

                    installations.forEach(installation => {
                        const statusClass = installation.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                        const statusText = installation.is_active ? 'Active' : 'Inactive';
                        const premiumClass = installation.is_premium ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800';
                        const premiumText = installation.is_premium ? 'Premium' : 'Free';

                        html += `
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">\${installation.url}</div>
                                    <div class="text-xs text-gray-500">ID: \${installation.id}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">\${installation.version}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \${statusClass}">
                                        \${statusText}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \${premiumClass}">
                                        \${premiumText}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="freemiusManager.viewInstallation(\${installation.id})" class="text-blue-600 hover:text-blue-900">View</button>
                                </td>
                            </tr>
                        `;
                    });

                    html += '</tbody></table>';
                    container.innerHTML = html;
                }

                async syncProducts() {
                    if (!confirm('This will sync all products from Freemius. Continue?')) return;

                    try {
                        const response = await fetch('/admin/api/freemius/sync-product', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                alert('Products synced successfully');
                                this.loadProducts();
                                this.loadFreemiusStatistics();
                            } else {
                                alert('Sync failed: ' + data.error);
                            }
                        }
                    } catch (error) {
                        console.error('Error syncing products:', error);
                        alert('Error syncing products');
                    }
                }

                async syncInstallations() {
                    if (!confirm('This will sync all installations from Freemius. Continue?')) return;

                    try {
                        const response = await fetch('/admin/api/freemius/sync-installation', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                alert('Installations synced successfully');
                                this.loadInstallations();
                                this.loadFreemiusStatistics();
                            } else {
                                alert('Sync failed: ' + data.error);
                            }
                        }
                    } catch (error) {
                        console.error('Error syncing installations:', error);
                        alert('Error syncing installations');
                    }
                }

                viewProduct(productId) {
                    // Implement product details view
                    console.log('View product:', productId);
                }

                viewInstallation(installationId) {
                    // Implement installation details view
                    console.log('View installation:', installationId);
                }
            }

            // Global functions
            function syncProducts() {
                freemiusManager.syncProducts();
            }

            function syncInstallations() {
                freemiusManager.syncInstallations();
            }

            // Initialize Freemius manager when DOM is loaded
            let freemiusManager;
            document.addEventListener('DOMContentLoaded', function() {
                freemiusManager = new FreemiusManager();
            });
        </script>
HTML;
    }
}