<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Config\Environment;

/**
 * Monitoring Service
 * 
 * Comprehensive monitoring and alerting system for production deployment.
 * Monitors system health, performance metrics, and security events.
 */
class MonitoringService
{
    private LoggingService $logger;
    private DatabaseConnection $db;
    private array $config;
    private array $metrics = [];
    
    // Alert thresholds
    private const RESPONSE_TIME_THRESHOLD = 2000; // milliseconds
    private const MEMORY_USAGE_THRESHOLD = 80;    // percentage
    private const ERROR_RATE_THRESHOLD = 5;       // percentage
    private const DISK_USAGE_THRESHOLD = 85;      // percentage
    
    public function __construct(LoggingService $logger)
    {
        $this->logger = $logger;
        $this->db = new DatabaseConnection();
        $this->config = $this->loadMonitoringConfig();
    }

    /**
     * Load monitoring configuration
     */
    private function loadMoni
        $this->loggingService->getLogger(LoggingService::TYPE_API)
            ->info('Request monitoring started', [
                'request_id' => $requestId,
                'endpoint' => $requestData['endpoint'] ?? 'unknown',
                'ip' => $requestData['ip'] ?? null
            ]);
    }
    
    /**
     * Add checkpoint to request monitoring
     */
    public function addCheckpoint(string $requestId, string $checkpoint, array $data = []): void
    {
        if (!isset($this->activeRequests[$requestId])) {
            return;
        }
        
        $currentTime = microtime(true);
        $startTime = $this->activeRequests[$requestId]['start_time'];
        
        $this->activeRequests[$requestId]['checkpoints'][] = [
            'name' => $checkpoint,
            'time' => $currentTime,
            'elapsed_ms' => round(($currentTime - $startTime) * 1000, 2),
            'memory_usage' => memory_get_usage(true),
            'data' => $data
        ];
        
        $this->loggingService->getLogger(LoggingService::TYPE_API)
            ->debug('Request checkpoint', [
                'request_id' => $requestId,
                'checkpoint' => $checkpoint,
                'elapsed_ms' => round(($currentTime - $startTime) * 1000, 2)
            ]);
    }
    
    /**
     * End request monitoring
     */
    public function endRequest(string $requestId, array $responseData = []): array
    {
        if (!isset($this->activeRequests[$requestId])) {
            return [];
        }
        
        $request = $this->activeRequests[$requestId];
        $endTime = microtime(true);
        $totalDuration = $endTime - $request['start_time'];
        $memoryUsed = memory_get_usage(true) - $request['memory_start'];
        
        $metrics = [
            'request_id' => $requestId,
            'total_duration_ms' => round($totalDuration * 1000, 2),
            'memory_used' => $memoryUsed,
            'memory_peak' => memory_get_peak_usage(true),
            'checkpoints' => $request['checkpoints'],
            'response_status' => $responseData['status'] ?? null
        ];
        
        // Log comprehensive request metrics
        $this->loggingService->logApiRequest(
            $request['request_data'],
            $responseData,
            (int)$metrics['total_duration_ms']
        );
        
        // Store performance metrics
        $this->storePerformanceMetrics($requestId, $metrics);
        
        // Clean up active request
        unset($this->activeRequests[$requestId]);
        
        return $metrics;
    }
    
    /**
     * Monitor database query performance
     */
    public function monitorDatabaseQuery(string $operation, string $sql, array $params, callable $queryExecutor): mixed
    {
        $startTime = microtime(true);
        $memoryBefore = memory_get_usage(true);
        
        try {
            $result = $queryExecutor();
            $success = true;
            $error = null;
        } catch (\Exception $e) {
            $success = false;
            $error = $e->getMessage();
            throw $e;
        } finally {
            $duration = microtime(true) - $startTime;
            $memoryAfter = memory_get_usage(true);
            
            $this->logDatabasePerformance($operation, $sql, $params, $duration, $success, $error, [
                'memory_before' => $memoryBefore,
                'memory_after' => $memoryAfter,
                'memory_used' => $memoryAfter - $memoryBefore
            ]);
        }
        
        return $result ?? null;
    }
    
    /**
     * Monitor external API call performance
     */
    public function monitorExternalApiCall(string $service, string $endpoint, callable $apiCall): array
    {
        $startTime = microtime(true);
        $memoryBefore = memory_get_usage(true);
        
        try {
            $result = $apiCall();
            $success = true;
            $error = null;
            $responseCode = $result['http_code'] ?? 200;
            $responseSize = isset($result['response']) ? strlen($result['response']) : 0;
        } catch (\Exception $e) {
            $success = false;
            $error = $e->getMessage();
            $responseCode = 0;
            $responseSize = 0;
            $result = ['error' => $error];
        } finally {
            $duration = microtime(true) - $startTime;
            $memoryAfter = memory_get_usage(true);
            
            $this->logExternalApiPerformance($service, $endpoint, $responseCode, $duration, $responseSize, $success, $error, [
                'memory_before' => $memoryBefore,
                'memory_after' => $memoryAfter,
                'memory_used' => $memoryAfter - $memoryBefore
            ]);
        }
        
        return $result ?? [];
    }
    
    /**
     * Get audit trail for specific entity
     */
    public function getAuditTrail(string $entityType, string $entityId, int $limit = 50): array
    {
        try {
            $sql = "SELECT sl.*, au.email as user_email 
                    FROM system_logs sl
                    LEFT JOIN admin_users au ON sl.user_id = au.id
                    WHERE sl.context::text LIKE ? 
                    ORDER BY sl.created_at DESC 
                    LIMIT ?";
            
            $searchPattern = '%"' . $entityType . '":"' . $entityId . '"%';
            $params = [$searchPattern, $limit];
            
            $statement = DatabaseConnection::execute($sql, $params);
            return $statement->fetchAll();
            
        } catch (DatabaseException $e) {
            $this->loggingService->getLogger(LoggingService::TYPE_ERROR)
                ->error('Failed to retrieve audit trail', [
                    'entity_type' => $entityType,
                    'entity_id' => $entityId,
                    'error' => $e->getMessage()
                ]);
            
            return [];
        }
    }
    
    /**
     * Get user activity audit trail
     */
    public function getUserActivityAudit(int $userId, DateTime $fromDate = null, DateTime $toDate = null, int $limit = 100): array
    {
        try {
            $sql = "SELECT sl.*, au.email as user_email 
                    FROM system_logs sl
                    LEFT JOIN admin_users au ON sl.user_id = au.id
                    WHERE sl.user_id = ?";
            
            $params = [$userId];
            
            if ($fromDate) {
                $sql .= " AND sl.created_at >= ?";
                $params[] = $fromDate->format('Y-m-d H:i:s');
            }
            
            if ($toDate) {
                $sql .= " AND sl.created_at <= ?";
                $params[] = $toDate->format('Y-m-d H:i:s');
            }
            
            $sql .= " ORDER BY sl.created_at DESC LIMIT ?";
            $params[] = $limit;
            
            $statement = DatabaseConnection::execute($sql, $params);
            return $statement->fetchAll();
            
        } catch (DatabaseException $e) {
            $this->loggingService->getLogger(LoggingService::TYPE_ERROR)
                ->error('Failed to retrieve user activity audit', [
                    'user_id' => $userId,
                    'error' => $e->getMessage()
                ]);
            
            return [];
        }
    }
    
    /**
     * Get system performance metrics
     */
    public function getPerformanceMetrics(DateTime $fromDate = null, DateTime $toDate = null): array
    {
        try {
            // API performance metrics
            $apiMetrics = $this->getApiPerformanceMetrics($fromDate, $toDate);
            
            // Database performance metrics
            $dbMetrics = $this->getDatabasePerformanceMetrics($fromDate, $toDate);
            
            // System resource metrics
            $resourceMetrics = $this->getResourceMetrics();
            
            // Error rate metrics
            $errorMetrics = $this->getErrorMetrics($fromDate, $toDate);
            
            return [
                'api' => $apiMetrics,
                'database' => $dbMetrics,
                'resources' => $resourceMetrics,
                'errors' => $errorMetrics,
                'generated_at' => (new DateTime())->format('c')
            ];
            
        } catch (\Exception $e) {
            $this->loggingService->getLogger(LoggingService::TYPE_ERROR)
                ->error('Failed to retrieve performance metrics', [
                    'error' => $e->getMessage()
                ]);
            
            return ['error' => 'Failed to retrieve metrics'];
        }
    }
    
    /**
     * Get API performance metrics
     */
    private function getApiPerformanceMetrics(DateTime $fromDate = null, DateTime $toDate = null): array
    {
        $sql = "SELECT 
                    COUNT(*) as total_requests,
                    AVG(response_time_ms) as avg_response_time,
                    MIN(response_time_ms) as min_response_time,
                    MAX(response_time_ms) as max_response_time,
                    COUNT(CASE WHEN response_status >= 200 AND response_status < 300 THEN 1 END) as success_count,
                    COUNT(CASE WHEN response_status >= 400 THEN 1 END) as error_count,
                    COUNT(CASE WHEN response_time_ms > 1000 THEN 1 END) as slow_requests
                FROM api_requests 
                WHERE 1=1";
        
        $params = [];
        
        if ($fromDate) {
            $sql .= " AND created_at >= ?";
            $params[] = $fromDate->format('Y-m-d H:i:s');
        }
        
        if ($toDate) {
            $sql .= " AND created_at <= ?";
            $params[] = $toDate->format('Y-m-d H:i:s');
        }
        
        $statement = DatabaseConnection::execute($sql, $params);
        $metrics = $statement->fetch();
        
        // Calculate derived metrics
        $totalRequests = (int)$metrics['total_requests'];
        $successCount = (int)$metrics['success_count'];
        $errorCount = (int)$metrics['error_count'];
        
        return [
            'total_requests' => $totalRequests,
            'success_rate' => $totalRequests > 0 ? round(($successCount / $totalRequests) * 100, 2) : 0,
            'error_rate' => $totalRequests > 0 ? round(($errorCount / $totalRequests) * 100, 2) : 0,
            'avg_response_time_ms' => round((float)$metrics['avg_response_time'], 2),
            'min_response_time_ms' => (int)$metrics['min_response_time'],
            'max_response_time_ms' => (int)$metrics['max_response_time'],
            'slow_requests' => (int)$metrics['slow_requests'],
            'slow_request_rate' => $totalRequests > 0 ? round(((int)$metrics['slow_requests'] / $totalRequests) * 100, 2) : 0
        ];
    }
    
    /**
     * Get database performance metrics
     */
    private function getDatabasePerformanceMetrics(DateTime $fromDate = null, DateTime $toDate = null): array
    {
        // This would be enhanced with actual database performance logging
        // For now, return basic connection stats
        try {
            $connectionStats = DatabaseConnection::getConnectionStats();
            
            return [
                'connection_status' => $connectionStats['connection_status'] ?? 'unknown',
                'server_info' => $connectionStats['server_info'] ?? 'unknown',
                'persistent_connection' => $connectionStats['config']['persistent'] ?? false,
                'in_transaction' => $connectionStats['in_transaction'] ?? false
            ];
        } catch (\Exception $e) {
            return ['error' => 'Unable to retrieve database metrics'];
        }
    }
    
    /**
     * Get comprehensive performance metrics with historical data
     */
    public function getComprehensivePerformanceMetrics(DateTime $fromDate = null, DateTime $toDate = null): array
    {
        try {
            $fromDate = $fromDate ?? (new DateTime())->modify('-24 hours');
            $toDate = $toDate ?? new DateTime();
            
            return [
                'time_range' => [
                    'from' => $fromDate->format('c'),
                    'to' => $toDate->format('c')
                ],
                'api_performance' => $this->getApiPerformanceMetrics($fromDate, $toDate),
                'database_performance' => $this->getDatabasePerformanceMetrics($fromDate, $toDate),
                'system_resources' => $this->getResourceMetrics(),
                'error_metrics' => $this->getErrorMetrics($fromDate, $toDate),
                'cache_performance' => $this->getCachePerformanceMetrics($fromDate, $toDate),
                'external_api_performance' => $this->getExternalApiPerformanceMetrics($fromDate, $toDate),
                'performance_trends' => $this->getPerformanceTrends($fromDate, $toDate),
                'generated_at' => (new DateTime())->format('c')
            ];
            
        } catch (\Exception $e) {
            $this->loggingService->getLogger(LoggingService::TYPE_ERROR)
                ->error('Failed to retrieve comprehensive performance metrics', [
                    'error' => $e->getMessage()
                ]);
            
            return ['error' => 'Failed to retrieve comprehensive metrics'];
        }
    }
    
    /**
     * Get cache performance metrics
     */
    private function getCachePerformanceMetrics(DateTime $fromDate, DateTime $toDate): array
    {
        try {
            $cacheManager = new \Skpassegna\GuardgeoApi\Services\CacheManager();
            $cacheStats = $cacheManager->getCacheStatistics();
            $cacheHealth = $cacheManager->getCacheHealth();
            
            return [
                'statistics' => $cacheStats,
                'health' => $cacheHealth,
                'hit_rate' => $this->calculateCacheHitRate($fromDate, $toDate),
                'performance_impact' => $this->calculateCachePerformanceImpact($fromDate, $toDate)
            ];
            
        } catch (\Exception $e) {
            return ['error' => 'Unable to retrieve cache performance metrics'];
        }
    }
    
    /**
     * Get external API performance metrics
     */
    private function getExternalApiPerformanceMetrics(DateTime $fromDate, DateTime $toDate): array
    {
        try {
            // Query system logs for external API calls
            $sql = "SELECT 
                        context->>'service' as service,
                        context->>'endpoint' as endpoint,
                        COUNT(*) as total_calls,
                        AVG((context->>'duration')::float) as avg_duration,
                        MIN((context->>'duration')::float) as min_duration,
                        MAX((context->>'duration')::float) as max_duration,
                        COUNT(CASE WHEN (context->>'response_code')::int >= 200 AND (context->>'response_code')::int < 300 THEN 1 END) as success_count,
                        COUNT(CASE WHEN (context->>'response_code')::int >= 400 THEN 1 END) as error_count
                    FROM system_logs 
                    WHERE message LIKE '%external service call%'
                    AND created_at BETWEEN ? AND ?
                    AND context->>'service' IS NOT NULL
                    GROUP BY context->>'service', context->>'endpoint'
                    ORDER BY total_calls DESC";
            
            $params = [$fromDate->format('Y-m-d H:i:s'), $toDate->format('Y-m-d H:i:s')];
            $statement = \Skpassegna\GuardgeoApi\Database\DatabaseConnection::execute($sql, $params);
            $apiMetrics = $statement->fetchAll();
            
            $summary = [
                'total_services' => 0,
                'total_calls' => 0,
                'overall_success_rate' => 0,
                'overall_avg_duration' => 0,
                'services' => []
            ];
            
            foreach ($apiMetrics as $metric) {
                $service = $metric['service'];
                $totalCalls = (int)$metric['total_calls'];
                $successCount = (int)$metric['success_count'];
                
                if (!isset($summary['services'][$service])) {
                    $summary['services'][$service] = [
                        'total_calls' => 0,
                        'success_count' => 0,
                        'error_count' => 0,
                        'avg_duration' => 0,
                        'endpoints' => []
                    ];
                    $summary['total_services']++;
                }
                
                $summary['services'][$service]['total_calls'] += $totalCalls;
                $summary['services'][$service]['success_count'] += $successCount;
                $summary['services'][$service]['error_count'] += (int)$metric['error_count'];
                $summary['services'][$service]['endpoints'][] = [
                    'endpoint' => $metric['endpoint'],
                    'total_calls' => $totalCalls,
                    'success_rate' => $totalCalls > 0 ? round(($successCount / $totalCalls) * 100, 2) : 0,
                    'avg_duration' => round((float)$metric['avg_duration'], 2),
                    'min_duration' => round((float)$metric['min_duration'], 2),
                    'max_duration' => round((float)$metric['max_duration'], 2)
                ];
                
                $summary['total_calls'] += $totalCalls;
            }
            
            // Calculate overall metrics
            if ($summary['total_calls'] > 0) {
                $totalSuccess = array_sum(array_column($summary['services'], 'success_count'));
                $summary['overall_success_rate'] = round(($totalSuccess / $summary['total_calls']) * 100, 2);
            }
            
            return $summary;
            
        } catch (\Exception $e) {
            return ['error' => 'Unable to retrieve external API performance metrics'];
        }
    }
    
    /**
     * Get performance trends
     */
    private function getPerformanceTrends(DateTime $fromDate, DateTime $toDate): array
    {
        try {
            // Get hourly performance data
            $sql = "SELECT 
                        DATE_TRUNC('hour', created_at) as hour,
                        COUNT(*) as request_count,
                        AVG(response_time_ms) as avg_response_time,
                        COUNT(CASE WHEN response_status >= 200 AND response_status < 300 THEN 1 END) as success_count,
                        COUNT(CASE WHEN response_status >= 400 THEN 1 END) as error_count
                    FROM api_requests 
                    WHERE created_at BETWEEN ? AND ?
                    GROUP BY DATE_TRUNC('hour', created_at)
                    ORDER BY hour";
            
            $params = [$fromDate->format('Y-m-d H:i:s'), $toDate->format('Y-m-d H:i:s')];
            $statement = \Skpassegna\GuardgeoApi\Database\DatabaseConnection::execute($sql, $params);
            $hourlyData = $statement->fetchAll();
            
            $trends = [
                'hourly_data' => [],
                'peak_hour' => null,
                'lowest_performance_hour' => null,
                'average_requests_per_hour' => 0,
                'performance_degradation_periods' => []
            ];
            
            $totalRequests = 0;
            $maxRequests = 0;
            $maxResponseTime = 0;
            $peakHour = null;
            $slowestHour = null;
            
            foreach ($hourlyData as $data) {
                $hour = $data['hour'];
                $requestCount = (int)$data['request_count'];
                $avgResponseTime = round((float)$data['avg_response_time'], 2);
                $successCount = (int)$data['success_count'];
                $errorCount = (int)$data['error_count'];
                
                $successRate = $requestCount > 0 ? round(($successCount / $requestCount) * 100, 2) : 0;
                
                $trends['hourly_data'][] = [
                    'hour' => $hour,
                    'request_count' => $requestCount,
                    'avg_response_time' => $avgResponseTime,
                    'success_rate' => $successRate,
                    'error_count' => $errorCount
                ];
                
                $totalRequests += $requestCount;
                
                if ($requestCount > $maxRequests) {
                    $maxRequests = $requestCount;
                    $peakHour = $hour;
                }
                
                if ($avgResponseTime > $maxResponseTime) {
                    $maxResponseTime = $avgResponseTime;
                    $slowestHour = $hour;
                }
                
                // Identify performance degradation periods (high response time or low success rate)
                if ($avgResponseTime > 2000 || $successRate < 95) {
                    $trends['performance_degradation_periods'][] = [
                        'hour' => $hour,
                        'avg_response_time' => $avgResponseTime,
                        'success_rate' => $successRate,
                        'issue_type' => $avgResponseTime > 2000 ? 'slow_response' : 'high_error_rate'
                    ];
                }
            }
            
            $trends['peak_hour'] = $peakHour;
            $trends['lowest_performance_hour'] = $slowestHour;
            $trends['average_requests_per_hour'] = count($hourlyData) > 0 ? round($totalRequests / count($hourlyData), 2) : 0;
            
            return $trends;
            
        } catch (\Exception $e) {
            return ['error' => 'Unable to retrieve performance trends'];
        }
    }
    
    /**
     * Calculate cache hit rate
     */
    private function calculateCacheHitRate(DateTime $fromDate, DateTime $toDate): array
    {
        try {
            // This would require additional logging of cache hits/misses
            // For now, return estimated data based on IP intelligence queries
            $sql = "SELECT 
                        COUNT(*) as total_requests,
                        COUNT(CASE WHEN context->>'cache_hit' = 'true' THEN 1 END) as cache_hits
                    FROM system_logs 
                    WHERE message LIKE '%IP intelligence%'
                    AND created_at BETWEEN ? AND ?";
            
            $params = [$fromDate->format('Y-m-d H:i:s'), $toDate->format('Y-m-d H:i:s')];
            $statement = \Skpassegna\GuardgeoApi\Database\DatabaseConnection::execute($sql, $params);
            $result = $statement->fetch();
            
            $totalRequests = (int)$result['total_requests'];
            $cacheHits = (int)$result['cache_hits'];
            $hitRate = $totalRequests > 0 ? round(($cacheHits / $totalRequests) * 100, 2) : 0;
            
            return [
                'total_requests' => $totalRequests,
                'cache_hits' => $cacheHits,
                'cache_misses' => $totalRequests - $cacheHits,
                'hit_rate_percentage' => $hitRate
            ];
            
        } catch (\Exception $e) {
            return ['error' => 'Unable to calculate cache hit rate'];
        }
    }
    
    /**
     * Calculate cache performance impact
     */
    private function calculateCachePerformanceImpact(DateTime $fromDate, DateTime $toDate): array
    {
        try {
            // Estimate performance impact of caching
            $sql = "SELECT 
                        AVG(CASE WHEN context->>'cache_hit' = 'true' THEN (context->>'duration')::float END) as avg_cached_duration,
                        AVG(CASE WHEN context->>'cache_hit' = 'false' THEN (context->>'duration')::float END) as avg_uncached_duration
                    FROM system_logs 
                    WHERE message LIKE '%IP intelligence%'
                    AND created_at BETWEEN ? AND ?
                    AND context->>'duration' IS NOT NULL";
            
            $params = [$fromDate->format('Y-m-d H:i:s'), $toDate->format('Y-m-d H:i:s')];
            $statement = \Skpassegna\GuardgeoApi\Database\DatabaseConnection::execute($sql, $params);
            $result = $statement->fetch();
            
            $cachedDuration = (float)$result['avg_cached_duration'];
            $uncachedDuration = (float)$result['avg_uncached_duration'];
            
            $performanceGain = 0;
            if ($uncachedDuration > 0 && $cachedDuration > 0) {
                $performanceGain = round((($uncachedDuration - $cachedDuration) / $uncachedDuration) * 100, 2);
            }
            
            return [
                'avg_cached_response_time' => round($cachedDuration, 2),
                'avg_uncached_response_time' => round($uncachedDuration, 2),
                'performance_gain_percentage' => $performanceGain,
                'estimated_time_saved_per_request' => round($uncachedDuration - $cachedDuration, 2)
            ];
            
        } catch (\Exception $e) {
            return ['error' => 'Unable to calculate cache performance impact'];
        }
    }
    
    /**
     * Get current resource metrics
     */
    private function getResourceMetrics(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        return [
            'memory_usage' => $memoryUsage,
            'memory_usage_formatted' => $this->formatBytes($memoryUsage),
            'memory_peak' => $memoryPeak,
            'memory_peak_formatted' => $this->formatBytes($memoryPeak),
            'memory_limit' => $memoryLimit,
            'memory_limit_formatted' => ini_get('memory_limit'),
            'memory_usage_percent' => $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 2) : null,
            'php_version' => PHP_VERSION,
            'max_execution_time' => ini_get('max_execution_time'),
            'load_average' => function_exists('sys_getloadavg') ? sys_getloadavg() : null
        ];
    }
    
    /**
     * Get error metrics
     */
    private function getErrorMetrics(DateTime $fromDate = null, DateTime $toDate = null): array
    {
        $sql = "SELECT 
                    level,
                    COUNT(*) as count
                FROM system_logs 
                WHERE level IN ('error', 'critical', 'warning')";
        
        $params = [];
        
        if ($fromDate) {
            $sql .= " AND created_at >= ?";
            $params[] = $fromDate->format('Y-m-d H:i:s');
        }
        
        if ($toDate) {
            $sql .= " AND created_at <= ?";
            $params[] = $toDate->format('Y-m-d H:i:s');
        }
        
        $sql .= " GROUP BY level ORDER BY count DESC";
        
        $statement = DatabaseConnection::execute($sql, $params);
        $errorCounts = $statement->fetchAll();
        
        $metrics = [
            'error' => 0,
            'critical' => 0,
            'warning' => 0,
            'total' => 0
        ];
        
        foreach ($errorCounts as $row) {
            $metrics[$row['level']] = (int)$row['count'];
            $metrics['total'] += (int)$row['count'];
        }
        
        return $metrics;
    }
    
    /**
     * Store performance metrics for historical analysis
     */
    private function storePerformanceMetrics(string $requestId, array $metrics): void
    {
        // Store in memory for immediate access
        $this->performanceMetrics[$requestId] = $metrics;
        
        // Keep only last 100 metrics in memory
        if (count($this->performanceMetrics) > 100) {
            $this->performanceMetrics = array_slice($this->performanceMetrics, -100, null, true);
        }
    }
    
    /**
     * Log database performance
     */
    private function logDatabasePerformance(string $operation, string $sql, array $params, float $duration, bool $success, ?string $error, array $metrics): void
    {
        $this->loggingService->getLogger(LoggingService::TYPE_SYSTEM)
            ->logPerformanceMetrics("database_{$operation}", $duration, array_merge($metrics, [
                'sql' => substr($sql, 0, 200) . (strlen($sql) > 200 ? '...' : ''),
                'param_count' => count($params),
                'success' => $success,
                'error' => $error
            ]));
    }
    
    /**
     * Log external API performance
     */
    private function logExternalApiPerformance(string $service, string $endpoint, int $responseCode, float $duration, int $responseSize, bool $success, ?string $error, array $metrics): void
    {
        $this->loggingService->getLogger(LoggingService::TYPE_SYSTEM)
            ->logExternalServiceCall($service, $endpoint, $responseCode, $duration, $responseSize);
    }
    
    /**
     * Parse memory limit string to bytes
     */
    private function parseMemoryLimit(string $limit): int
    {
        if ($limit === '-1') {
            return -1; // Unlimited
        }
        
        $unit = strtolower(substr($limit, -1));
        $value = (int)substr($limit, 0, -1);
        
        return match($unit) {
            'g' => $value * 1024 * 1024 * 1024,
            'm' => $value * 1024 * 1024,
            'k' => $value * 1024,
            default => (int)$limit
        };
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }
    
    /**
     * Generate system health report
     */
    public function generateHealthReport(): array
    {
        $checks = [];
        
        // Database connectivity check
        try {
            DatabaseConnection::getConnection();
            $checks['database'] = ['status' => 'ok', 'message' => 'Database connection successful'];
        } catch (\Exception $e) {
            $checks['database'] = ['status' => 'error', 'message' => 'Database connection failed: ' . $e->getMessage()];
        }
        
        // Memory usage check
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $memoryPercent = $memoryLimit > 0 ? ($memoryUsage / $memoryLimit) * 100 : 0;
        
        if ($memoryPercent > 90) {
            $checks['memory'] = ['status' => 'critical', 'message' => 'Memory usage critical: ' . round($memoryPercent, 1) . '%'];
        } elseif ($memoryPercent > 75) {
            $checks['memory'] = ['status' => 'warning', 'message' => 'Memory usage high: ' . round($memoryPercent, 1) . '%'];
        } else {
            $checks['memory'] = ['status' => 'ok', 'message' => 'Memory usage normal: ' . round($memoryPercent, 1) . '%'];
        }
        
        // Log directory check
        $logDir = dirname(__DIR__, 2) . '/logs';
        if (!is_dir($logDir) || !is_writable($logDir)) {
            $checks['logs'] = ['status' => 'error', 'message' => 'Log directory not writable'];
        } else {
            $checks['logs'] = ['status' => 'ok', 'message' => 'Log directory accessible'];
        }
        
        // Recent error rate check
        $recentErrors = $this->getErrorMetrics((new DateTime())->modify('-1 hour'));
        if ($recentErrors['critical'] > 0) {
            $checks['errors'] = ['status' => 'critical', 'message' => $recentErrors['critical'] . ' critical errors in last hour'];
        } elseif ($recentErrors['error'] > 10) {
            $checks['errors'] = ['status' => 'warning', 'message' => $recentErrors['error'] . ' errors in last hour'];
        } else {
            $checks['errors'] = ['status' => 'ok', 'message' => 'Error rate normal'];
        }
        
        // Log the health check
        $this->loggingService->getLogger(LoggingService::TYPE_SYSTEM)
            ->logHealthCheck($checks);
        
        return [
            'checks' => $checks,
            'overall_status' => $this->calculateOverallHealth($checks),
            'timestamp' => (new DateTime())->format('c')
        ];
    }
    
    /**
     * Calculate overall health status
     */
    private function calculateOverallHealth(array $checks): string
    {
        $statuses = array_column($checks, 'status');
        
        if (in_array('critical', $statuses)) {
            return 'critical';
        }
        
        if (in_array('error', $statuses)) {
            return 'error';
        }
        
        if (in_array('warning', $statuses)) {
            return 'warning';
        }
        
        return 'healthy';
    }
}