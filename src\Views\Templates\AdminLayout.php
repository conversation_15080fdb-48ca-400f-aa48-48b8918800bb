<?php

namespace Skpassegna\GuardgeoApi\Views\Templates;

/**
 * Admin Layout Template
 * 
 * Main layout template for admin interface with sidebar navigation,
 * header, and responsive design.
 */
class AdminLayout extends BaseTemplate
{
    public function render(): string
    {
        $title = $this->get('title', 'GuardGeo Admin');
        $pageTitle = $this->get('pageTitle', $title);
        $pageDescription = $this->get('pageDescription', '');
        $user = $this->get('user', []);
        $navigation = $this->get('navigation', []);
        $currentPage = $this->get('currentPage', '');
        $content = $this->get('content', '');

        return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$this->escape($title)}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .content-transition { transition: margin-left 0.3s ease-in-out; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Mobile menu overlay -->
    <div id="mobileMenuOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 hidden lg:hidden"></div>
    
    <!-- Sidebar -->
    {$this->renderSidebar($navigation, $currentPage, $user)}
    
    <!-- Main content -->
    <div id="mainContent" class="lg:ml-64 content-transition">
        <!-- Top bar -->
        {$this->renderTopBar($pageTitle, $pageDescription, $user)}
        
        <!-- Page content -->
        <main class="p-6">
            <div id="pageContent">
                {$content}
            </div>
        </main>
    </div>
    
    <!-- Loading overlay -->
    {$this->renderLoadingOverlay()}
    
    <script>
        {$this->renderLayoutScript()}
    </script>
    
    <!-- Admin Interface JavaScript -->
    <script>
        {$this->getAdminJavaScript()}
    </script>
</body>
</html>
HTML;
    }

    private function renderSidebar(array $navigation, string $currentPage, array $user): string
    {
        $userInitials = $this->getUserInitials($user['email'] ?? '');
        $userRole = $this->getRoleDisplayName($user['role'] ?? '');
        $navigationHtml = $this->renderNavigation($navigation, $currentPage);

        return <<<HTML
<div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 sidebar-transition">
    <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200">
        <h1 class="text-xl font-bold text-gray-900">GuardGeo Admin</h1>
        <button id="closeSidebar" class="lg:hidden text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <nav class="mt-6">
        {$navigationHtml}
    </nav>
    
    <!-- User info -->
    <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span class="text-white text-sm font-medium">{$userInitials}</span>
                </div>
            </div>
            <div class="ml-3 flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">{$this->escape($user['email'] ?? '')}</p>
                <p class="text-xs text-gray-500">{$userRole}</p>
            </div>
            <div class="ml-2">
                <button id="logoutBtn" class="text-gray-400 hover:text-gray-600" title="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </div>
</div>
HTML;
    }

    private function renderTopBar(string $pageTitle, string $pageDescription, array $user): string
    {
        $userInitials = $this->getUserInitials($user['email'] ?? '');
        $userRole = $this->getRoleDisplayName($user['role'] ?? '');

        return <<<HTML
<div class="bg-white shadow-sm border-b border-gray-200">
    <div class="flex items-center justify-between h-16 px-6">
        <div class="flex items-center">
            <button id="openSidebar" class="lg:hidden text-gray-500 hover:text-gray-700 mr-4">
                <i class="fas fa-bars"></i>
            </button>
            <div>
                <h2 class="text-lg font-semibold text-gray-900">{$this->escape($pageTitle)}</h2>
                {$pageDescription ? "<p class=\"text-sm text-gray-600\">{$this->escape($pageDescription)}</p>" : ''}
            </div>
        </div>
        
        <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <button class="text-gray-400 hover:text-gray-600" title="Notifications">
                <i class="fas fa-bell"></i>
            </button>
            
            <!-- User menu -->
            <div class="relative">
                <button id="userMenuBtn" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">{$userInitials}</span>
                    </div>
                </button>
                
                <div id="userMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                    <div class="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
                        <div class="font-medium">{$this->escape($user['email'] ?? '')}</div>
                        <div class="text-gray-500">{$userRole}</div>
                    </div>
                    <button id="logoutBtnTop" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-sign-out-alt mr-2"></i>Sign out
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
HTML;
    }

    private function renderNavigation(array $navigation, string $currentPage): string
    {
        $html = '';
        
        foreach ($navigation as $item) {
            $isActive = $item['id'] === $currentPage;
            $activeClass = $isActive ? 'bg-blue-50 border-r-2 border-blue-500 text-blue-700' : 'text-gray-700 hover:bg-gray-50';
            $icon = $this->getIconClass($item['icon'] ?? '');
            
            $html .= <<<HTML
            <a href="{$this->escape($item['url'] ?? '#')}" class="flex items-center px-6 py-3 {$activeClass} transition-colors duration-200">
                <i class="{$icon} mr-3"></i>
                <span class="font-medium">{$this->escape($item['title'] ?? '')}</span>
            </a>
HTML;
        }
        
        return $html;
    }

    private function renderLoadingOverlay(): string
    {
        return <<<HTML
<div id="loadingOverlay" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center">
    <div class="bg-white rounded-lg p-6 flex items-center">
        <svg class="animate-spin h-5 w-5 text-blue-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-gray-700">Loading...</span>
    </div>
</div>
HTML;
    }

    private function renderLayoutScript(): string
    {
        return <<<JS
        // Mobile menu functionality
        const sidebar = document.getElementById('sidebar');
        const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
        const openSidebarBtn = document.getElementById('openSidebar');
        const closeSidebarBtn = document.getElementById('closeSidebar');
        
        function openMobileMenu() {
            sidebar.classList.remove('-translate-x-full');
            mobileMenuOverlay.classList.remove('hidden');
        }
        
        function closeMobileMenu() {
            sidebar.classList.add('-translate-x-full');
            mobileMenuOverlay.classList.add('hidden');
        }
        
        openSidebarBtn?.addEventListener('click', openMobileMenu);
        closeSidebarBtn?.addEventListener('click', closeMobileMenu);
        mobileMenuOverlay?.addEventListener('click', closeMobileMenu);
        
        // User menu functionality
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userMenu = document.getElementById('userMenu');
        
        userMenuBtn?.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });
        
        // Close user menu when clicking outside
        document.addEventListener('click', function() {
            userMenu?.classList.add('hidden');
        });
        
        // Logout functionality
        const logoutBtns = [document.getElementById('logoutBtn'), document.getElementById('logoutBtnTop')];
        
        logoutBtns.forEach(btn => {
            btn?.addEventListener('click', async function(e) {
                e.preventDefault();
                
                if (confirm('Are you sure you want to logout?')) {
                    try {
                        const response = await fetch('/admin/logout', {
                            method: 'POST',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            window.location.href = result.redirect || '/admin/login';
                        } else {
                            alert('Logout failed. Please try again.');
                        }
                    } catch (error) {
                        console.error('Logout error:', error);
                        window.location.href = '/admin/logout';
                    }
                }
            });
        });
        
        // Loading overlay functions
        window.showLoading = function() {
            document.getElementById('loadingOverlay')?.classList.remove('hidden');
        };
        
        window.hideLoading = function() {
            document.getElementById('loadingOverlay')?.classList.add('hidden');
        };
        
        // Auto-hide loading on page load
        window.addEventListener('load', function() {
            hideLoading();
        });
JS;
    }

    private function getUserInitials(string $email): string
    {
        $parts = explode('@', $email);
        $username = $parts[0] ?? '';
        
        if (strlen($username) >= 2) {
            return strtoupper(substr($username, 0, 2));
        }
        
        return strtoupper(substr($email, 0, 1));
    }

    private function getRoleDisplayName(string $role): string
    {
        return match ($role) {
            'super_admin' => 'Super Admin',
            'dev' => 'Developer',
            'marketing' => 'Marketing',
            'sales' => 'Sales',
            default => ucfirst($role)
        };
    }

    private function getIconClass(string $icon): string
    {
        return match ($icon) {
            'dashboard' => 'fas fa-tachometer-alt',
            'globe' => 'fas fa-globe',
            'plugin' => 'fas fa-plug',
            'file-text' => 'fas fa-file-alt',
            'users' => 'fas fa-users',
            'settings' => 'fas fa-cog',
            default => 'fas fa-circle'
        };
    }

    /**
     * Get admin JavaScript content
     */
    private function getAdminJavaScript(): string
    {
        $jsFile = __DIR__ . '/../Assets/admin.js';
        if (file_exists($jsFile)) {
            return file_get_contents($jsFile);
        }
        return '// Admin JavaScript not found';
    }
}