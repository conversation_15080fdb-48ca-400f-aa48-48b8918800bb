<?php

namespace Skpassegna\GuardgeoApi\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Skpassegna\GuardgeoApi\Utils\Logger;
use Skpassegna\GuardgeoApi\Config\Environment;

/**
 * Freemius API Client
 * 
 * Handles HTTP communication with the Freemius API using Bearer token authentication.
 * Provides comprehensive error handling for API failures and rate limiting.
 */
class FreemiusApiClient
{
    private Client $httpClient;
    private Logger $logger;
    private string $baseUrl;
    private string $bearerToken;
    private int $timeout;
    private int $retryAttempts;
    private int $retryDelay;
    
    /**
     * Constructor
     */
    public function __construct(
        ?string $bearerToken = null,
        ?string $baseUrl = null,
        int $timeout = 30,
        int $retryAttempts = 3,
        int $retryDelay = 1
    ) {
        $this->bearerToken = $bearerToken ?? Environment::get('FREEMIUS_API_TOKEN');
        $this->baseUrl = $baseUrl ?? Environment::get('FREEMIUS_API_BASE_URL', 'https://api.freemius.com/v1');
        $this->timeout = $timeout;
        $this->retryAttempts = $retryAttempts;
        $this->retryDelay = $retryDelay;
        
        $this->logger = new Logger();
        
        // Initialize Guzzle HTTP client
        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => $this->timeout,
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'User-Agent' => 'GuardGeo-API/1.0'
            ]
        ]);
        
        if (empty($this->bearerToken)) {
            throw new \InvalidArgumentException('Freemius API Bearer token is required');
        }
    }
    
    /**
     * Get product information by ID
     */
    public function getProduct(int $productId): array
    {
        $endpoint = "/products/{$productId}.json";
        
        $this->logger->info("Fetching Freemius product", [
            'product_id' => $productId,
            'endpoint' => $endpoint
        ]);
        
        return $this->makeRequest('GET', $endpoint);
    }
    
    /**
     * Get installation information by product ID and install ID
     */
    public function getInstallation(int $productId, int $installId): array
    {
        $endpoint = "/products/{$productId}/installs/{$installId}.json";
        
        $this->logger->info("Fetching Freemius installation", [
            'product_id' => $productId,
            'install_id' => $installId,
            'endpoint' => $endpoint
        ]);
        
        return $this->makeRequest('GET', $endpoint);
    }
    
    /**
     * List installations for a product
     */
    public function listInstallations(int $productId, array $filters = []): array
    {
        $endpoint = "/products/{$productId}/installs.json";
        
        $this->logger->info("Listing Freemius installations", [
            'product_id' => $productId,
            'filters' => $filters,
            'endpoint' => $endpoint
        ]);
        
        return $this->makeRequest('GET', $endpoint, $filters);
    }
    
    /**
     * Get user information by ID
     */
    public function getUser(int $userId): array
    {
        $endpoint = "/users/{$userId}.json";
        
        $this->logger->info("Fetching Freemius user", [
            'user_id' => $userId,
            'endpoint' => $endpoint
        ]);
        
        return $this->makeRequest('GET', $endpoint);
    }
    
    /**
     * Get subscription information by ID
     */
    public function getSubscription(int $subscriptionId): array
    {
        $endpoint = "/subscriptions/{$subscriptionId}.json";
        
        $this->logger->info("Fetching Freemius subscription", [
            'subscription_id' => $subscriptionId,
            'endpoint' => $endpoint
        ]);
        
        return $this->makeRequest('GET', $endpoint);
    }
    
    /**
     * Get license information by ID
     */
    public function getLicense(int $licenseId): array
    {
        $endpoint = "/licenses/{$licenseId}.json";
        
        $this->logger->info("Fetching Freemius license", [
            'license_id' => $licenseId,
            'endpoint' => $endpoint
        ]);
        
        return $this->makeRequest('GET', $endpoint);
    }
    
    /**
     * List subscriptions for a product
     */
    public function listSubscriptions(int $productId, array $filters = []): array
    {
        $endpoint = "/products/{$productId}/subscriptions.json";
        
        $this->logger->info("Listing Freemius subscriptions", [
            'product_id' => $productId,
            'filters' => $filters,
            'endpoint' => $endpoint
        ]);
        
        return $this->makeRequest('GET', $endpoint, $filters);
    }
    
    /**
     * List licenses for a product
     */
    public function listLicenses(int $productId, array $filters = []): array
    {
        $endpoint = "/products/{$productId}/licenses.json";
        
        $this->logger->info("Listing Freemius licenses", [
            'product_id' => $productId,
            'filters' => $filters,
            'endpoint' => $endpoint
        ]);
        
        return $this->makeRequest('GET', $endpoint, $filters);
    }
    
    /**
     * Get installation subscription details
     */
    public function getInstallationSubscription(int $productId, int $installId): array
    {
        $endpoint = "/products/{$productId}/installs/{$installId}/subscription.json";
        
        $this->logger->info("Fetching installation subscription", [
            'product_id' => $productId,
            'install_id' => $installId,
            'endpoint' => $endpoint
        ]);
        
        return $this->makeRequest('GET', $endpoint);
    }
    
    /**
     * Get installation license details
     */
    public function getInstallationLicense(int $productId, int $installId): array
    {
        $endpoint = "/products/{$productId}/installs/{$installId}/license.json";
        
        $this->logger->info("Fetching installation license", [
            'product_id' => $productId,
            'install_id' => $installId,
            'endpoint' => $endpoint
        ]);
        
        return $this->makeRequest('GET', $endpoint);
    }
    
    /**
     * Batch get multiple installations
     */
    public function getBatchInstallations(int $productId, array $installIds): array
    {
        $this->logger->info("Fetching batch installations", [
            'product_id' => $productId,
            'install_ids' => $installIds,
            'count' => count($installIds)
        ]);
        
        $results = [];
        $errors = [];
        
        // Process in smaller batches to avoid API limits
        $batchSize = 10;
        $batches = array_chunk($installIds, $batchSize);
        
        foreach ($batches as $batchIndex => $batch) {
            $this->logger->debug("Processing installation batch", [
                'batch_index' => $batchIndex + 1,
                'batch_size' => count($batch),
                'total_batches' => count($batches)
            ]);
            
            foreach ($batch as $installId) {
                try {
                    $installation = $this->getInstallation($productId, $installId);
                    $results[$installId] = $installation;
                } catch (FreemiusApiException $e) {
                    $errors[$installId] = $e->getMessage();
                    $this->logger->warning("Failed to fetch installation in batch", [
                        'product_id' => $productId,
                        'install_id' => $installId,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            // Add delay between batches to respect rate limits
            if ($batchIndex < count($batches) - 1) {
                usleep(500000); // 500ms delay
            }
        }
        
        return [
            'results' => $results,
            'errors' => $errors,
            'summary' => [
                'requested' => count($installIds),
                'successful' => count($results),
                'failed' => count($errors)
            ]
        ];
    }
    
    /**
     * Batch get multiple products
     */
    public function getBatchProducts(array $productIds): array
    {
        $this->logger->info("Fetching batch products", [
            'product_ids' => $productIds,
            'count' => count($productIds)
        ]);
        
        $results = [];
        $errors = [];
        
        foreach ($productIds as $productId) {
            try {
                $product = $this->getProduct($productId);
                $results[$productId] = $product;
            } catch (FreemiusApiException $e) {
                $errors[$productId] = $e->getMessage();
                $this->logger->warning("Failed to fetch product in batch", [
                    'product_id' => $productId,
                    'error' => $e->getMessage()
                ]);
            }
            
            // Small delay between requests
            usleep(100000); // 100ms delay
        }
        
        return [
            'results' => $results,
            'errors' => $errors,
            'summary' => [
                'requested' => count($productIds),
                'successful' => count($results),
                'failed' => count($errors)
            ]
        ];
    }
    
    /**
     * Validate installation by checking if it exists and is active
     */
    public function validateInstallation(int $productId, int $installId): array
    {
        try {
            $installation = $this->getInstallation($productId, $installId);
            
            $isValid = isset($installation['is_active']) && $installation['is_active'] === true
                    && (!isset($installation['is_uninstalled']) || $installation['is_uninstalled'] === false);
            
            $this->logger->info("Installation validation result", [
                'product_id' => $productId,
                'install_id' => $installId,
                'is_valid' => $isValid,
                'is_active' => $installation['is_active'] ?? false,
                'is_uninstalled' => $installation['is_uninstalled'] ?? false
            ]);
            
            return [
                'valid' => $isValid,
                'installation' => $installation,
                'reason' => $isValid ? 'Installation is active' : 'Installation is inactive or uninstalled'
            ];
            
        } catch (FreemiusApiException $e) {
            $this->logger->error("Installation validation failed", [
                'product_id' => $productId,
                'install_id' => $installId,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ]);
            
            return [
                'valid' => false,
                'installation' => null,
                'reason' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Make HTTP request to Freemius API with enhanced retry logic
     */
    private function makeRequest(string $method, string $endpoint, array $params = []): array
    {
        $attempt = 0;
        $lastException = null;
        $requestId = uniqid('freemius_', true);
        
        $this->logger->debug("Starting Freemius API request", [
            'request_id' => $requestId,
            'method' => $method,
            'endpoint' => $endpoint,
            'params_count' => count($params),
            'max_attempts' => $this->retryAttempts
        ]);
        
        while ($attempt < $this->retryAttempts) {
            $attempt++;
            
            try {
                $options = [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $this->bearerToken,
                        'X-Request-ID' => $requestId
                    ]
                ];
                
                // Add query parameters for GET requests
                if ($method === 'GET' && !empty($params)) {
                    $options['query'] = $params;
                }
                
                // Add JSON body for POST/PUT requests
                if (in_array($method, ['POST', 'PUT']) && !empty($params)) {
                    $options['json'] = $params;
                }
                
                $startTime = microtime(true);
                $response = $this->httpClient->request($method, $endpoint, $options);
                $responseTime = round((microtime(true) - $startTime) * 1000, 2);
                
                $statusCode = $response->getStatusCode();
                $body = $response->getBody()->getContents();
                
                // Extract rate limit information from headers
                $rateLimitInfo = $this->extractRateLimitInfo($response->getHeaders());
                
                $data = json_decode($body, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new FreemiusApiException(
                        'Invalid JSON response from Freemius API: ' . json_last_error_msg(),
                        $statusCode
                    );
                }
                
                $this->logger->info("Freemius API request successful", [
                    'request_id' => $requestId,
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'status_code' => $statusCode,
                    'response_time_ms' => $responseTime,
                    'attempt' => $attempt,
                    'rate_limit_info' => $rateLimitInfo
                ]);
                
                return $data;
                
            } catch (ClientException $e) {
                // 4xx errors - analyze and potentially retry for specific cases
                $statusCode = $e->getResponse()->getStatusCode();
                $responseBody = $e->getResponse()->getBody()->getContents();
                $rateLimitInfo = $this->extractRateLimitInfo($e->getResponse()->getHeaders());
                
                $this->logger->error("Freemius API client error", [
                    'request_id' => $requestId,
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'status_code' => $statusCode,
                    'response_body' => $responseBody,
                    'attempt' => $attempt,
                    'rate_limit_info' => $rateLimitInfo
                ]);
                
                // Handle rate limiting (429) with retry
                if ($statusCode === 429 && $attempt < $this->retryAttempts) {
                    $retryAfter = $rateLimitInfo['retry_after'] ?? ($this->retryDelay * $attempt);
                    $this->logger->warning("Rate limited, retrying after delay", [
                        'request_id' => $requestId,
                        'retry_after_seconds' => $retryAfter,
                        'attempt' => $attempt
                    ]);
                    sleep($retryAfter);
                    continue;
                }
                
                // Handle temporary authentication issues (401) with retry
                if ($statusCode === 401 && $attempt < $this->retryAttempts && $attempt === 1) {
                    $this->logger->warning("Authentication error, retrying once", [
                        'request_id' => $requestId,
                        'attempt' => $attempt
                    ]);
                    sleep($this->retryDelay);
                    continue;
                }
                
                throw new FreemiusApiException(
                    $this->parseErrorMessage($responseBody, "HTTP {$statusCode} error"),
                    $statusCode,
                    $e
                );
                
            } catch (ServerException $e) {
                // 5xx errors - retry with exponential backoff
                $statusCode = $e->getResponse()->getStatusCode();
                $responseBody = $e->getResponse()->getBody()->getContents();
                $lastException = $e;
                
                $this->logger->warning("Freemius API server error, retrying", [
                    'request_id' => $requestId,
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'status_code' => $statusCode,
                    'response_body' => $responseBody,
                    'attempt' => $attempt,
                    'max_attempts' => $this->retryAttempts
                ]);
                
                if ($attempt < $this->retryAttempts) {
                    $backoffDelay = min($this->retryDelay * pow(2, $attempt - 1), 30); // Max 30 seconds
                    sleep($backoffDelay);
                    continue;
                }
                
                throw new FreemiusApiException(
                    $this->parseErrorMessage($responseBody, "HTTP {$statusCode} server error after {$attempt} attempts"),
                    $statusCode,
                    $e
                );
                
            } catch (RequestException $e) {
                // Network errors - retry with exponential backoff
                $lastException = $e;
                
                $this->logger->warning("Freemius API network error, retrying", [
                    'request_id' => $requestId,
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'error' => $e->getMessage(),
                    'attempt' => $attempt,
                    'max_attempts' => $this->retryAttempts
                ]);
                
                if ($attempt < $this->retryAttempts) {
                    $backoffDelay = min($this->retryDelay * pow(2, $attempt - 1), 30); // Max 30 seconds
                    sleep($backoffDelay);
                    continue;
                }
                
                throw new FreemiusApiException(
                    "Network error after {$attempt} attempts: " . $e->getMessage(),
                    0,
                    $e
                );
                
            } catch (GuzzleException $e) {
                // Other Guzzle errors
                $lastException = $e;
                
                $this->logger->error("Freemius API Guzzle error", [
                    'request_id' => $requestId,
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'error' => $e->getMessage(),
                    'attempt' => $attempt
                ]);
                
                throw new FreemiusApiException(
                    "HTTP client error: " . $e->getMessage(),
                    0,
                    $e
                );
            }
        }
        
        // This should never be reached, but just in case
        throw new FreemiusApiException(
            "Maximum retry attempts ({$this->retryAttempts}) exceeded",
            0,
            $lastException
        );
    }
    
    /**
     * Extract rate limit information from response headers
     */
    private function extractRateLimitInfo(array $headers): array
    {
        $rateLimitInfo = [
            'limit' => null,
            'remaining' => null,
            'reset_time' => null,
            'retry_after' => null
        ];
        
        // Common rate limit header patterns
        $headerMappings = [
            'x-ratelimit-limit' => 'limit',
            'x-ratelimit-remaining' => 'remaining',
            'x-ratelimit-reset' => 'reset_time',
            'retry-after' => 'retry_after',
            'x-rate-limit-limit' => 'limit',
            'x-rate-limit-remaining' => 'remaining',
            'x-rate-limit-reset' => 'reset_time'
        ];
        
        foreach ($headers as $headerName => $headerValues) {
            $normalizedName = strtolower($headerName);
            if (isset($headerMappings[$normalizedName])) {
                $rateLimitInfo[$headerMappings[$normalizedName]] = is_array($headerValues) ? $headerValues[0] : $headerValues;
            }
        }
        
        return $rateLimitInfo;
    }
    
    /**
     * Parse error message from API response
     */
    private function parseErrorMessage(string $responseBody, string $fallback): string
    {
        $data = json_decode($responseBody, true);
        
        if (json_last_error() === JSON_ERROR_NONE && isset($data['error'])) {
            if (is_string($data['error'])) {
                return $data['error'];
            }
            
            if (is_array($data['error'])) {
                if (isset($data['error']['message'])) {
                    return $data['error']['message'];
                }
                
                if (isset($data['error']['code'])) {
                    return "Error code: " . $data['error']['code'];
                }
                
                return json_encode($data['error']);
            }
        }
        
        return $fallback;
    }
    
    /**
     * Check if API is reachable and perform comprehensive health check
     */
    public function healthCheck(): array
    {
        $healthStatus = [
            'api_reachable' => false,
            'authentication_valid' => false,
            'response_time_ms' => null,
            'last_error' => null,
            'timestamp' => date('c'),
            'details' => []
        ];
        
        try {
            $startTime = microtime(true);
            
            // Try to make a simple request to test connectivity
            // Since Freemius doesn't have a dedicated ping endpoint, we'll use a lightweight endpoint
            try {
                // Try to get a non-existent product to test API connectivity without affecting real data
                $this->makeRequest('GET', '/products/999999.json');
            } catch (FreemiusApiException $e) {
                // If we get a 404, that means the API is reachable and authentication worked
                if ($e->getCode() === 404) {
                    $healthStatus['api_reachable'] = true;
                    $healthStatus['authentication_valid'] = true;
                } elseif ($e->getCode() === 401 || $e->getCode() === 403) {
                    $healthStatus['api_reachable'] = true;
                    $healthStatus['authentication_valid'] = false;
                    $healthStatus['last_error'] = 'Authentication failed: ' . $e->getMessage();
                } else {
                    throw $e; // Re-throw other errors
                }
            }
            
            $healthStatus['response_time_ms'] = round((microtime(true) - $startTime) * 1000, 2);
            
            // Additional health checks
            $healthStatus['details'] = [
                'base_url' => $this->baseUrl,
                'timeout' => $this->timeout,
                'retry_attempts' => $this->retryAttempts,
                'bearer_token_configured' => !empty($this->bearerToken),
                'bearer_token_masked' => $this->getBearerTokenMasked()
            ];
            
            $this->logger->info("Freemius API health check completed", $healthStatus);
            
        } catch (FreemiusApiException $e) {
            $healthStatus['last_error'] = $e->getMessage();
            $healthStatus['response_time_ms'] = round((microtime(true) - $startTime) * 1000, 2);
            
            $this->logger->error("Freemius API health check failed", [
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
        }
        
        return $healthStatus;
    }
    
    /**
     * Get current rate limit status from response headers
     */
    public function getRateLimitStatus(): ?array
    {
        // This would be populated from the last response headers
        // For now, return basic structure that could be populated
        return [
            'limit' => null,
            'remaining' => null,
            'reset_time' => null,
            'retry_after' => null
        ];
    }
    
    /**
     * Get API connection statistics
     */
    public function getConnectionStats(): array
    {
        return [
            'base_url' => $this->baseUrl,
            'timeout_seconds' => $this->timeout,
            'retry_attempts' => $this->retryAttempts,
            'retry_delay_seconds' => $this->retryDelay,
            'bearer_token_configured' => !empty($this->bearerToken),
            'bearer_token_masked' => $this->getBearerTokenMasked()
        ];
    }
    
    /**
     * Test specific endpoint connectivity
     */
    public function testEndpoint(string $endpoint, string $method = 'GET', array $params = []): array
    {
        $testResult = [
            'endpoint' => $endpoint,
            'method' => $method,
            'success' => false,
            'response_time_ms' => null,
            'status_code' => null,
            'error' => null,
            'timestamp' => date('c')
        ];
        
        try {
            $startTime = microtime(true);
            $response = $this->makeRequest($method, $endpoint, $params);
            $testResult['response_time_ms'] = round((microtime(true) - $startTime) * 1000, 2);
            $testResult['success'] = true;
            $testResult['status_code'] = 200; // Successful response
            
        } catch (FreemiusApiException $e) {
            $testResult['response_time_ms'] = round((microtime(true) - $startTime) * 1000, 2);
            $testResult['status_code'] = $e->getCode();
            $testResult['error'] = $e->getMessage();
        }
        
        return $testResult;
    }
    
    /**
     * Perform comprehensive API monitoring check
     */
    public function performMonitoringCheck(): array
    {
        $monitoringResult = [
            'overall_status' => 'unknown',
            'timestamp' => date('c'),
            'checks' => [],
            'summary' => [
                'total_checks' => 0,
                'passed_checks' => 0,
                'failed_checks' => 0,
                'average_response_time_ms' => 0
            ]
        ];
        
        // Define monitoring checks
        $checks = [
            'health_check' => ['method' => 'healthCheck', 'critical' => true],
            'products_endpoint' => ['endpoint' => '/products/999999.json', 'method' => 'GET', 'critical' => true],
            'installs_endpoint' => ['endpoint' => '/products/999999/installs.json', 'method' => 'GET', 'critical' => false]
        ];
        
        $totalResponseTime = 0;
        $responseTimeCount = 0;
        
        foreach ($checks as $checkName => $checkConfig) {
            $monitoringResult['checks'][$checkName] = [
                'name' => $checkName,
                'critical' => $checkConfig['critical'],
                'status' => 'unknown',
                'details' => []
            ];
            
            try {
                if ($checkName === 'health_check') {
                    $result = $this->healthCheck();
                    $monitoringResult['checks'][$checkName]['status'] = 
                        ($result['api_reachable'] && $result['authentication_valid']) ? 'passed' : 'failed';
                    $monitoringResult['checks'][$checkName]['details'] = $result;
                    
                    if ($result['response_time_ms'] !== null) {
                        $totalResponseTime += $result['response_time_ms'];
                        $responseTimeCount++;
                    }
                } else {
                    $result = $this->testEndpoint($checkConfig['endpoint'], $checkConfig['method']);
                    $monitoringResult['checks'][$checkName]['status'] = $result['success'] ? 'passed' : 'failed';
                    $monitoringResult['checks'][$checkName]['details'] = $result;
                    
                    if ($result['response_time_ms'] !== null) {
                        $totalResponseTime += $result['response_time_ms'];
                        $responseTimeCount++;
                    }
                }
                
            } catch (\Exception $e) {
                $monitoringResult['checks'][$checkName]['status'] = 'failed';
                $monitoringResult['checks'][$checkName]['details'] = [
                    'error' => $e->getMessage(),
                    'exception_type' => get_class($e)
                ];
            }
        }
        
        // Calculate summary
        $monitoringResult['summary']['total_checks'] = count($checks);
        $monitoringResult['summary']['passed_checks'] = count(array_filter(
            $monitoringResult['checks'], 
            fn($check) => $check['status'] === 'passed'
        ));
        $monitoringResult['summary']['failed_checks'] = count(array_filter(
            $monitoringResult['checks'], 
            fn($check) => $check['status'] === 'failed'
        ));
        
        if ($responseTimeCount > 0) {
            $monitoringResult['summary']['average_response_time_ms'] = round($totalResponseTime / $responseTimeCount, 2);
        }
        
        // Determine overall status
        $criticalChecks = array_filter($checks, fn($check) => $check['critical']);
        $criticalChecksPassed = count(array_filter(
            $monitoringResult['checks'],
            fn($check) => $check['critical'] && $check['status'] === 'passed'
        ));
        
        if ($criticalChecksPassed === count($criticalChecks)) {
            $monitoringResult['overall_status'] = 'healthy';
        } elseif ($criticalChecksPassed > 0) {
            $monitoringResult['overall_status'] = 'degraded';
        } else {
            $monitoringResult['overall_status'] = 'unhealthy';
        }
        
        $this->logger->info("Freemius API monitoring check completed", [
            'overall_status' => $monitoringResult['overall_status'],
            'passed_checks' => $monitoringResult['summary']['passed_checks'],
            'failed_checks' => $monitoringResult['summary']['failed_checks'],
            'average_response_time' => $monitoringResult['summary']['average_response_time_ms']
        ]);
        
        return $monitoringResult;
    }
    
    /**
     * Set bearer token
     */
    public function setBearerToken(string $token): void
    {
        $this->bearerToken = $token;
    }
    
    /**
     * Get current bearer token (masked for security)
     */
    public function getBearerTokenMasked(): string
    {
        if (strlen($this->bearerToken) <= 8) {
            return str_repeat('*', strlen($this->bearerToken));
        }
        
        return substr($this->bearerToken, 0, 4) . str_repeat('*', strlen($this->bearerToken) - 8) . substr($this->bearerToken, -4);
    }
    
    /**
     * Get API client configuration
     */
    public function getClientConfiguration(): array
    {
        return [
            'base_url' => $this->baseUrl,
            'timeout_seconds' => $this->timeout,
            'retry_attempts' => $this->retryAttempts,
            'retry_delay_seconds' => $this->retryDelay,
            'bearer_token_configured' => !empty($this->bearerToken),
            'bearer_token_masked' => $this->getBearerTokenMasked(),
            'supported_methods' => ['GET', 'POST', 'PUT'],
            'supported_endpoints' => [
                'products', 'installations', 'users', 'subscriptions', 'licenses'
            ]
        ];
    }
    
    /**
     * Update client configuration
     */
    public function updateConfiguration(array $config): void
    {
        if (isset($config['timeout'])) {
            $this->timeout = (int) $config['timeout'];
        }
        
        if (isset($config['retry_attempts'])) {
            $this->retryAttempts = (int) $config['retry_attempts'];
        }
        
        if (isset($config['retry_delay'])) {
            $this->retryDelay = (int) $config['retry_delay'];
        }
        
        if (isset($config['bearer_token'])) {
            $this->setBearerToken($config['bearer_token']);
        }
        
        // Recreate HTTP client with new configuration
        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => $this->timeout,
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'User-Agent' => 'GuardGeo-API/1.0'
            ]
        ]);
        
        $this->logger->info("Freemius API client configuration updated", $config);
    }
    
    /**
     * Get supported webhook events
     */
    public function getSupportedWebhookEvents(): array
    {
        return [
            'install' => [
                'install.activated',
                'install.deactivated',
                'install.uninstalled',
                'install.upgraded'
            ],
            'subscription' => [
                'subscription.created',
                'subscription.updated',
                'subscription.cancelled',
                'subscription.expired',
                'subscription.renewed'
            ],
            'license' => [
                'license.activated',
                'license.deactivated',
                'license.created',
                'license.updated',
                'license.cancelled',
                'license.expired'
            ],
            'payment' => [
                'payment.completed',
                'payment.failed',
                'payment.refunded'
            ],
            'user' => [
                'user.created',
                'user.updated'
            ],
            'product' => [
                'product.updated'
            ]
        ];
    }
    
    /**
     * Validate webhook event type
     */
    public function isValidWebhookEvent(string $eventType): bool
    {
        $supportedEvents = $this->getSupportedWebhookEvents();
        
        foreach ($supportedEvents as $category => $events) {
            if (in_array($eventType, $events)) {
                return true;
            }
        }
        
        return false;
    }
}

/**
 * Custom exception for Freemius API errors
 */
class FreemiusApiException extends \Exception
{
    public function __construct(string $message, int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}