<?php

namespace Skpassegna\GuardgeoApi\Views\Templates;

/**
 * Base Template
 * 
 * Abstract base class for all templates providing common functionality
 * for rendering, data binding, and template composition.
 */
abstract class BaseTemplate
{
    protected array $data;
    protected array $sections;
    protected string $layout;

    public function __construct(array $data = [])
    {
        $this->data = $data;
        $this->sections = [];
        $this->layout = 'default';
    }

    /**
     * Render the template
     *
     * @return string
     */
    abstract public function render(): string;

    /**
     * Set template data
     *
     * @param array $data
     * @return self
     */
    public function with(array $data): self
    {
        $this->data = array_merge($this->data, $data);
        return $this;
    }

    /**
     * Get data value with default
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function get(string $key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }

    /**
     * Check if data key exists and is truthy
     *
     * @param string $key
     * @return bool
     */
    protected function has(string $key): bool
    {
        return isset($this->data[$key]) && $this->data[$key];
    }

    /**
     * Escape HTML content
     *
     * @param string $content
     * @return string
     */
    protected function escape(string $content): string
    {
        return htmlspecialchars($content, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Include a partial template
     *
     * @param string $partial
     * @param array $data
     * @return string
     */
    protected function partial(string $partial, array $data = []): string
    {
        $partialClass = 'Skpassegna\\GuardgeoApi\\Views\\Partials\\' . ucfirst($partial);
        
        if (class_exists($partialClass)) {
            $partialInstance = new $partialClass(array_merge($this->data, $data));
            return $partialInstance->render();
        }
        
        return '';
    }

    /**
     * Set a section content
     *
     * @param string $name
     * @param string $content
     * @return self
     */
    protected function section(string $name, string $content): self
    {
        $this->sections[$name] = $content;
        return $this;
    }

    /**
     * Get section content
     *
     * @param string $name
     * @param string $default
     * @return string
     */
    protected function getSection(string $name, string $default = ''): string
    {
        return $this->sections[$name] ?? $default;
    }

    /**
     * Render a component
     *
     * @param string $component
     * @param array $props
     * @return string
     */
    protected function component(string $component, array $props = []): string
    {
        $componentClass = 'Skpassegna\\GuardgeoApi\\Views\\Components\\' . ucfirst($component);
        
        if (class_exists($componentClass)) {
            $componentInstance = new $componentClass($props);
            return $componentInstance->render();
        }
        
        return '';
    }

    /**
     * Generate asset URL
     *
     * @param string $path
     * @return string
     */
    protected function asset(string $path): string
    {
        return '/assets/' . ltrim($path, '/');
    }

    /**
     * Generate route URL
     *
     * @param string $route
     * @param array $params
     * @return string
     */
    protected function route(string $route, array $params = []): string
    {
        $url = '/admin/' . ltrim($route, '/');
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }
}