<?php

/**
 * Scheduled Maintenance Script
 * 
 * Comprehensive automated maintenance tasks for the GuardGeo platform.
 * This script should be run via cron job for regular system maintenance.
 * 
 * Usage:
 *   php scripts/scheduled-maintenance.php [--task=all|cache|backup|health|logs]
 *   
 * Cron examples:
 *   # Run full maintenance daily at 2 AM
 *   0 2 * * * /usr/bin/php /path/to/guardgeo/scripts/scheduled-maintenance.php --task=all
 *   
 *   # Run cache cleanup every 6 hours
 *   0 */6 * * * /usr/bin/php /path/to/guardgeo/scripts/scheduled-maintenance.php --task=cache
 *   
 *   # Run health checks every hour
 *   0 * * * * /usr/bin/php /path/to/guardgeo/scripts/scheduled-maintenance.php --task=health
 */

require_once dirname(__DIR__) . '/vendor/autoload.php';

use Skpassegna\GuardgeoApi\Config\Environment;
use Skpassegna\GuardgeoApi\Services\SystemHealthService;
use Skpassegna\GuardgeoApi\Services\CacheManager;
use Skpassegna\GuardgeoApi\Services\BackupRecoveryService;
use Skpassegna\GuardgeoApi\Services\LogRotationService;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Services\IpRegistryApiClient;
use Skpassegna\GuardgeoApi\Models\AdminUserModel;

/**
 * Scheduled Maintenance Manager
 */
class ScheduledMaintenanceManager
{
    private LoggingService $logger;
    private SystemHealthService $healthService;
    private CacheManager $cacheManager;
    private BackupRecoveryService $backupService;
    private LogRotationService $logRotationService;
    private array $config;
    private string $lockFile;
    
    public function __construct()
    {
        // Load environment
        Environment::load(dirname(__DIR__) . '/.env');
        
        // Initialize services
        $this->logger = LoggingService::getInstance();
        $this->healthService = new SystemHealthService();
        $this->cacheManager = new CacheManager();
        $this->backupService = new BackupRecoveryService();
        $this->logRotationService = new LogRotationService();
        
        // Load configuration
        $this->config = $this->loadMaintenanceConfig();
        
        // Set lock file path
        $this->lockFile = sys_get_temp_dir() . '/guardgeo_maintenance.lock';
    }
    
    /**
     * Run scheduled maintenance tasks
     */
    public function run(string $task = 'all'): int
    {
        $startTime = microtime(true);
        
        try {
            // Check if maintenance is already running
            if ($this->isMaintenanceRunning()) {
                $this->logger->warning('Maintenance already running, skipping execution');
                return 1;
            }
            
            // Create lock file
            $this->createLockFile();
            
            $this->logger->info('Starting scheduled maintenance', [
                'task' => $task,
                'pid' => getmypid(),
                'memory_limit' => ini_get('memory_limit')
            ]);
            
            $results = [];
            
            // Execute requested tasks
            switch ($task) {
                case 'all':
                    $results = $this->runAllTasks();
                    break;
                case 'cache':
                    $results['cache'] = $this->runCacheMaintenance();
                    break;
                case 'backup':
                    $results['backup'] = $this->runBackupMaintenance();
                    break;
                case 'health':
                    $results['health'] = $this->runHealthChecks();
                    break;
                case 'logs':
                    $results['logs'] = $this->runLogMaintenance();
                    break;
                case 'performance':
                    $results['performance'] = $this->runPerformanceOptimization();
                    break;
                default:
                    throw new \InvalidArgumentException("Unknown task: {$task}");
            }
            
            $executionTime = microtime(true) - $startTime;
            
            $this->logger->info('Scheduled maintenance completed', [
                'task' => $task,
                'execution_time' => round($executionTime, 2),
                'results' => $results,
                'memory_peak' => memory_get_peak_usage(true)
            ]);
            
            // Generate maintenance report
            $this->generateMaintenanceReport($task, $results, $executionTime);
            
            return 0;
            
        } catch (\Exception $e) {
            $this->logger->error('Scheduled maintenance failed', [
                'task' => $task,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
            
        } finally {
            // Always remove lock file
            $this->removeLockFile();
        }
    }
    
    /**
     * Run all maintenance tasks
     */
    private function runAllTasks(): array
    {
        $results = [];
        
        // 1. Health checks (quick assessment)
        $results['health'] = $this->runHealthChecks();
        
        // 2. Cache maintenance
        $results['cache'] = $this->runCacheMaintenance();
        
        // 3. Log maintenance
        $results['logs'] = $this->runLogMaintenance();
        
        // 4. Performance optimization
        $results['performance'] = $this->runPerformanceOptimization();
        
        // 5. Backup maintenance (if scheduled)
        if ($this->shouldRunBackup()) {
            $results['backup'] = $this->runBackupMaintenance();
        }
        
        return $results;
    }
    
    /**
     * Run health checks
     */
    private function runHealthChecks(): array
    {
        try {
            $this->logger->info('Running system health checks');
            
            // Quick health check
            $quickHealth = $this->healthService->getQuickStatus();
            
            // If quick check shows issues, run comprehensive check
            $comprehensiveHealth = null;
            if ($quickHealth['overall_status'] !== 'healthy') {
                $this->logger->warning('Quick health check detected issues, running comprehensive check');
                $comprehensiveHealth = $this->healthService->performHealthCheck();
            }
            
            // Check for critical issues that need immediate attention
            $criticalIssues = $this->identifyCriticalIssues($quickHealth, $comprehensiveHealth);
            
            if (!empty($criticalIssues)) {
                $this->logger->critical('Critical system issues detected', [
                    'issues' => $criticalIssues
                ]);
                
                // Send alerts if configured
                $this->sendCriticalAlerts($criticalIssues);
            }
            
            return [
                'success' => true,
                'quick_health' => $quickHealth,
                'comprehensive_health' => $comprehensiveHealth,
                'critical_issues' => $criticalIssues
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Health check maintenance failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Run cache maintenance
     */
    private function runCacheMaintenance(): array
    {
        try {
            $this->logger->info('Running cache maintenance');
            
            // Perform scheduled cache maintenance
            $maintenanceResults = $this->cacheManager->performScheduledMaintenance();
            
            // Get cache health status
            $cacheHealth = $this->cacheManager->getCacheHealth();
            
            // Refresh expired IP data if needed
            $refreshResults = null;
            if ($cacheHealth['statistics']['expired_ips'] > 0) {
                $this->logger->info('Refreshing expired IP data', [
                    'expired_count' => $cacheHealth['statistics']['expired_ips']
                ]);
                
                $apiClient = new IpRegistryApiClient();
                $refreshResults = $this->cacheManager->performBatchRefresh($apiClient);
            }
            
            return [
                'success' => true,
                'maintenance_results' => $maintenanceResults,
                'cache_health' => $cacheHealth,
                'refresh_results' => $refreshResults
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Cache maintenance failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Run backup maintenance
     */
    private function runBackupMaintenance(): array
    {
        try {
            $this->logger->info('Running backup maintenance');
            
            $results = [];
            
            // Create backup if scheduled
            if ($this->shouldCreateBackup()) {
                $systemUser = new AdminUserModel([
                    'id' => 0,
                    'email' => 'system@maintenance',
                    'role' => 'super_admin'
                ]);
                
                $backupOptions = [
                    'include_database' => true,
                    'include_configuration' => true,
                    'include_logs' => false,
                    'create_archive' => true
                ];
                
                $results['backup_created'] = $this->backupService->createFullBackup($systemUser, $backupOptions);
            }
            
            // Cleanup old backups
            $keepDays = $this->config['backup']['retention_days'] ?? 30;
            $results['cleanup'] = $this->backupService->cleanupOldBackups($keepDays);
            
            return [
                'success' => true,
                'results' => $results
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Backup maintenance failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Run log maintenance
     */
    private function runLogMaintenance(): array
    {
        try {
            $this->logger->info('Running log maintenance');
            
            // Rotate logs
            $rotationResults = $this->logRotationService->rotateLogs();
            
            // Clean up old log files
            $cleanupResults = $this->logRotationService->cleanupOldLogs();
            
            // Compress old logs if configured
            $compressionResults = null;
            if ($this->config['logs']['compress_old'] ?? true) {
                $compressionResults = $this->logRotationService->compressOldLogs();
            }
            
            return [
                'success' => true,
                'rotation_results' => $rotationResults,
                'cleanup_results' => $cleanupResults,
                'compression_results' => $compressionResults
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Log maintenance failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Run performance optimization
     */
    private function runPerformanceOptimization(): array
    {
        try {
            $this->logger->info('Running performance optimization');
            
            $results = [];
            
            // Database optimization
            $results['database'] = $this->optimizeDatabase();
            
            // Clear OPcache if available
            if (function_exists('opcache_reset')) {
                opcache_reset();
                $results['opcache'] = 'cleared';
            }
            
            // Memory cleanup
            if (function_exists('gc_collect_cycles')) {
                $collected = gc_collect_cycles();
                $results['garbage_collection'] = $collected . ' cycles collected';
            }
            
            return [
                'success' => true,
                'results' => $results
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Performance optimization failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Optimize database
     */
    private function optimizeDatabase(): array
    {
        try {
            $db = \Skpassegna\GuardgeoApi\Database\DatabaseConnection::getInstance();
            
            $tables = [
                'admin_users',
                'freemius_products',
                'freemius_installations',
                'ip_intelligence',
                'system_logs',
                'api_requests',
                'system_config'
            ];
            
            $results = [];
            
            foreach ($tables as $table) {
                try {
                    $db->exec("VACUUM ANALYZE {$table}");
                    $results[$table] = 'optimized';
                } catch (\Exception $e) {
                    $results[$table] = 'error: ' . $e->getMessage();
                }
            }
            
            return $results;
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Check if maintenance is already running
     */
    private function isMaintenanceRunning(): bool
    {
        if (!file_exists($this->lockFile)) {
            return false;
        }
        
        $lockContent = file_get_contents($this->lockFile);
        $lockData = json_decode($lockContent, true);
        
        if (!$lockData || !isset($lockData['pid'])) {
            return false;
        }
        
        // Check if process is still running
        if (function_exists('posix_kill')) {
            return posix_kill($lockData['pid'], 0);
        }
        
        // Fallback: check lock file age (consider stale after 1 hour)
        $lockAge = time() - filemtime($this->lockFile);
        return $lockAge < 3600;
    }
    
    /**
     * Create lock file
     */
    private function createLockFile(): void
    {
        $lockData = [
            'pid' => getmypid(),
            'started_at' => date('c'),
            'hostname' => gethostname()
        ];
        
        file_put_contents($this->lockFile, json_encode($lockData));
    }
    
    /**
     * Remove lock file
     */
    private function removeLockFile(): void
    {
        if (file_exists($this->lockFile)) {
            unlink($this->lockFile);
        }
    }
    
    /**
     * Load maintenance configuration
     */
    private function loadMaintenanceConfig(): array
    {
        return [
            'backup' => [
                'enabled' => Environment::get('MAINTENANCE_BACKUP_ENABLED', 'true') === 'true',
                'schedule' => Environment::get('MAINTENANCE_BACKUP_SCHEDULE', 'daily'),
                'retention_days' => (int) Environment::get('MAINTENANCE_BACKUP_RETENTION_DAYS', 30)
            ],
            'cache' => [
                'cleanup_frequency' => Environment::get('MAINTENANCE_CACHE_CLEANUP_FREQUENCY', 'daily'),
                'refresh_expired' => Environment::get('MAINTENANCE_CACHE_REFRESH_EXPIRED', 'true') === 'true'
            ],
            'logs' => [
                'rotation_enabled' => Environment::get('MAINTENANCE_LOG_ROTATION_ENABLED', 'true') === 'true',
                'retention_days' => (int) Environment::get('MAINTENANCE_LOG_RETENTION_DAYS', 30),
                'compress_old' => Environment::get('MAINTENANCE_LOG_COMPRESS_OLD', 'true') === 'true'
            ],
            'alerts' => [
                'enabled' => Environment::get('MAINTENANCE_ALERTS_ENABLED', 'false') === 'true',
                'email' => Environment::get('MAINTENANCE_ALERTS_EMAIL', ''),
                'webhook_url' => Environment::get('MAINTENANCE_ALERTS_WEBHOOK_URL', '')
            ]
        ];
    }
    
    /**
     * Check if backup should be created
     */
    private function shouldCreateBackup(): bool
    {
        if (!$this->config['backup']['enabled']) {
            return false;
        }
        
        $schedule = $this->config['backup']['schedule'];
        $lastBackupFile = sys_get_temp_dir() . '/guardgeo_last_backup';
        
        if (!file_exists($lastBackupFile)) {
            return true;
        }
        
        $lastBackup = (int) file_get_contents($lastBackupFile);
        $now = time();
        
        switch ($schedule) {
            case 'hourly':
                return ($now - $lastBackup) >= 3600;
            case 'daily':
                return ($now - $lastBackup) >= 86400;
            case 'weekly':
                return ($now - $lastBackup) >= 604800;
            default:
                return false;
        }
    }
    
    /**
     * Check if backup should run (different from creation)
     */
    private function shouldRunBackup(): bool
    {
        // Run backup maintenance (cleanup) more frequently than creation
        return $this->config['backup']['enabled'];
    }
    
    /**
     * Identify critical issues from health checks
     */
    private function identifyCriticalIssues(array $quickHealth, ?array $comprehensiveHealth): array
    {
        $criticalIssues = [];
        
        // Check overall status
        if ($quickHealth['overall_status'] === 'error') {
            $criticalIssues[] = 'System overall status is in error state';
        }
        
        // Check specific components from comprehensive health check
        if ($comprehensiveHealth) {
            foreach ($comprehensiveHealth['checks'] ?? [] as $checkName => $checkData) {
                if ($checkData['status'] === 'error') {
                    $criticalIssues[] = "Critical issue in {$checkName}: " . ($checkData['message'] ?? 'Unknown error');
                }
            }
        }
        
        return $criticalIssues;
    }
    
    /**
     * Send critical alerts
     */
    private function sendCriticalAlerts(array $criticalIssues): void
    {
        if (!$this->config['alerts']['enabled']) {
            return;
        }
        
        $message = "GuardGeo Critical System Alert\n\n";
        $message .= "The following critical issues were detected:\n\n";
        
        foreach ($criticalIssues as $issue) {
            $message .= "- {$issue}\n";
        }
        
        $message .= "\nTimestamp: " . date('c') . "\n";
        $message .= "Server: " . gethostname() . "\n";
        
        // Send email alert if configured
        if (!empty($this->config['alerts']['email'])) {
            $this->sendEmailAlert($this->config['alerts']['email'], $message);
        }
        
        // Send webhook alert if configured
        if (!empty($this->config['alerts']['webhook_url'])) {
            $this->sendWebhookAlert($this->config['alerts']['webhook_url'], $criticalIssues);
        }
    }
    
    /**
     * Send email alert
     */
    private function sendEmailAlert(string $email, string $message): void
    {
        try {
            $subject = 'GuardGeo Critical System Alert - ' . date('Y-m-d H:i:s');
            $headers = [
                'From: <EMAIL>',
                'Content-Type: text/plain; charset=UTF-8'
            ];
            
            mail($email, $subject, $message, implode("\r\n", $headers));
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to send email alert', [
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Send webhook alert
     */
    private function sendWebhookAlert(string $webhookUrl, array $criticalIssues): void
    {
        try {
            $payload = [
                'alert_type' => 'critical_system_issues',
                'timestamp' => date('c'),
                'server' => gethostname(),
                'issues' => $criticalIssues
            ];
            
            $context = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => 'Content-Type: application/json',
                    'content' => json_encode($payload)
                ]
            ]);
            
            file_get_contents($webhookUrl, false, $context);
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to send webhook alert', [
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Generate maintenance report
     */
    private function generateMaintenanceReport(string $task, array $results, float $executionTime): void
    {
        $report = [
            'maintenance_run' => [
                'task' => $task,
                'timestamp' => date('c'),
                'execution_time' => round($executionTime, 2),
                'memory_peak' => memory_get_peak_usage(true),
                'server' => gethostname(),
                'php_version' => PHP_VERSION
            ],
            'results' => $results
        ];
        
        // Save report to file
        $reportFile = dirname(__DIR__) . '/logs/maintenance_' . date('Y-m-d') . '.json';
        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT) . "\n", FILE_APPEND | LOCK_EX);
        
        // Log summary
        $this->logger->info('Maintenance report generated', [
            'report_file' => $reportFile,
            'task' => $task,
            'execution_time' => round($executionTime, 2)
        ]);
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    $options = getopt('', ['task:']);
    $task = $options['task'] ?? 'all';
    
    $manager = new ScheduledMaintenanceManager();
    $exitCode = $manager->run($task);
    
    exit($exitCode);
}