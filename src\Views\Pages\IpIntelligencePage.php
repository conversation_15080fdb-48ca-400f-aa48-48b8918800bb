<?php

namespace Skpassegna\GuardgeoApi\Views\Pages;

use Skpassegna\GuardgeoApi\Views\Templates\BaseTemplate;
use Skpassegna\GuardgeoApi\Views\Components\Card;
use Skpassegna\GuardgeoApi\Views\Components\Button;
use Skpassegna\GuardgeoApi\Views\Components\Table;
use Skpassegna\GuardgeoApi\Views\Components\Modal;

/**
 * IP Intelligence Page Template
 * 
 * IP intelligence management page with search, filtering, and management capabilities
 * using the component-based design system.
 */
class IpIntelligencePage extends BaseTemplate
{
    public function render(): string
    {
        $canManage = $this->get('canManage', false);
        
        return $this->renderSearchFilters($canManage) . 
               $this->renderStatisticsCards() . 
               $this->renderIpRecordsTable($canManage) . 
               $this->renderIpDetailsModal($canManage) . 
               $this->renderPageScript($canManage);
    }

    private function renderSearchFilters(bool $canManage): string
    {
        $searchCard = new Card([
            'content' => $this->getSearchFiltersContent($canManage),
            'padding' => 'p-6'
        ]);

        return '<div class="mb-6">' . $searchCard->render() . '</div>';
    }

    private function getSearchFiltersContent(bool $canManage): string
    {
        $bulkRefreshBtn = $canManage ? 
            Button::primary('Bulk Refresh', [
                'icon' => 'fas fa-refresh',
                'onclick' => 'performBulkRefresh()',
                'id' => 'bulkRefreshBtn'
            ])->render() : '';

        return <<<HTML
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <input 
                        type="text" 
                        id="ipSearch" 
                        placeholder="Search by IP address or hostname..."
                        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div class="flex items-center space-x-4">
                <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="all">All Records</option>
                    <option value="fresh">Fresh Only</option>
                    <option value="stale">Stale Only</option>
                </select>
                
                <select id="sortBy" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="cached_at">Last Cached</option>
                    <option value="ip">IP Address</option>
                    <option value="security_expires_at">Security Expiry</option>
                </select>
                
                {$this->component('button', [
                    'text' => 'Refresh',
                    'icon' => 'fas fa-sync-alt',
                    'variant' => 'primary',
                    'onclick' => 'refreshSearch()',
                    'id' => 'refreshSearch'
                ])}
                
                {$bulkRefreshBtn}
            </div>
        </div>
HTML;
    }

    private function renderStatisticsCards(): string
    {
        $totalCard = Card::stat([
            'icon' => 'fas fa-database',
            'iconColor' => 'bg-blue-500',
            'title' => 'Total Records',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('total-records-card');

        $freshCard = Card::stat([
            'icon' => 'fas fa-check',
            'iconColor' => 'bg-green-500',
            'title' => 'Fresh Records',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('fresh-records-card');

        $staleCard = Card::stat([
            'icon' => 'fas fa-clock',
            'iconColor' => 'bg-yellow-500',
            'title' => 'Stale Records',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('stale-records-card');

        $threatCard = Card::stat([
            'icon' => 'fas fa-exclamation-triangle',
            'iconColor' => 'bg-red-500',
            'title' => 'Threat IPs',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('threat-ips-card');

        return <<<HTML
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {$totalCard->render()}
            {$freshCard->render()}
            {$staleCard->render()}
            {$threatCard->render()}
        </div>
HTML;
    }

    private function renderIpRecordsTable(bool $canManage): string
    {
        $tableCard = new Card([
            'title' => 'IP Intelligence Records',
            'content' => $this->getTableContent($canManage)
        ]);

        return $tableCard->render();
    }

    private function getTableContent(bool $canManage): string
    {
        return <<<HTML
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Security</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cached</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody id="ipRecordsTable" class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                            <p class="text-gray-500">Loading IP records...</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div id="paginationContainer" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing <span id="recordsFrom">0</span> to <span id="recordsTo">0</span> of <span id="recordsTotal">0</span> results
            </div>
            <div id="paginationButtons" class="flex space-x-2">
                <!-- Pagination buttons will be inserted here -->
            </div>
        </div>
HTML;
    }

    private function renderIpDetailsModal(bool $canManage): string
    {
        return Modal::info('IP Intelligence Details', '', [
            'id' => 'ipDetailsModal',
            'size' => '4xl',
            'content' => '<div id="ipDetailsContent"><!-- Details content will be loaded here --></div>',
            'visible' => false
        ])->render();
    }

    private function renderPageScript(bool $canManage): string
    {
        $canManageJs = $canManage ? 'true' : 'false';
        
        return <<<HTML
        <script>
            class IpIntelligenceManager {
                constructor() {
                    this.currentPage = 1;
                    this.currentFilters = {
                        search: '',
                        status: 'all',
                        sort: 'cached_at',
                        order: 'desc'
                    };
                    this.canManage = {$canManageJs};
                    this.init();
                }

                init() {
                    this.loadIpStatistics();
                    this.loadIpRecords();
                    this.bindEvents();
                }

                bindEvents() {
                    // Search functionality
                    document.getElementById('ipSearch').addEventListener('input', this.debounce(() => {
                        this.currentFilters.search = document.getElementById('ipSearch').value;
                        this.currentPage = 1;
                        this.loadIpRecords();
                    }, 500));

                    // Filter functionality
                    document.getElementById('statusFilter').addEventListener('change', (e) => {
                        this.currentFilters.status = e.target.value;
                        this.currentPage = 1;
                        this.loadIpRecords();
                    });

                    document.getElementById('sortBy').addEventListener('change', (e) => {
                        this.currentFilters.sort = e.target.value;
                        this.currentPage = 1;
                        this.loadIpRecords();
                    });

                    // Refresh button
                    document.getElementById('refreshSearch').addEventListener('click', () => {
                        this.loadIpStatistics();
                        this.loadIpRecords();
                    });

                    // Bulk refresh button
                    const bulkRefreshBtn = document.getElementById('bulkRefreshBtn');
                    if (bulkRefreshBtn) {
                        bulkRefreshBtn.addEventListener('click', () => this.performBulkRefresh());
                    }
                }

                async loadIpStatistics() {
                    try {
                        const response = await fetch('/admin/api/ip-intelligence/statistics');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateStatistics(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading IP statistics:', error);
                    }
                }

                async loadIpRecords() {
                    try {
                        const params = new URLSearchParams({
                            page: this.currentPage,
                            limit: 25,
                            ...this.currentFilters
                        });

                        const response = await fetch('/admin/api/ip-intelligence/records?' + params);
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateIpRecordsTable(data.data, data.pagination);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading IP records:', error);
                    }
                }

                updateStatistics(stats) {
                    this.updateStatCard('.total-records-card', stats.total_records || 0);
                    this.updateStatCard('.fresh-records-card', stats.fresh_records || 0);
                    this.updateStatCard('.stale-records-card', stats.stale_records || 0);
                    this.updateStatCard('.threat-ips-card', stats.threat_ips || 0);
                }

                updateStatCard(selector, value) {
                    const card = document.querySelector(selector);
                    if (card) {
                        const valueElement = card.querySelector('.text-2xl');
                        if (valueElement) {
                            valueElement.textContent = value;
                        }
                    }
                }

                updateIpRecordsTable(records, pagination) {
                    const tbody = document.getElementById('ipRecordsTable');

                    if (records.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-12 text-center text-gray-500">No IP records found</td></tr>';
                        return;
                    }

                    let html = '';
                    records.forEach(record => {
                        const isStale = record.is_stale;
                        const statusClass = isStale ? 'text-yellow-600' : 'text-green-600';
                        const statusText = isStale ? 'Stale' : 'Fresh';

                        html += `
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">\${record.ip}</div>
                                    \${record.hostname ? `<div class="text-xs text-gray-500">\${record.hostname}</div>` : ''}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">\${record.location || 'Unknown'}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">\${record.security_status || 'Unknown'}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \${statusClass === 'text-green-600' ? 'bg-green-100' : 'bg-yellow-100'} \${statusClass}">
                                        \${statusText}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    \${record.cached_at}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="ipManager.viewIpDetails('\${record.ip}')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                    \${this.canManage ? `<button onclick="ipManager.refreshIp('\${record.ip}')" class="text-green-600 hover:text-green-900 mr-3">Refresh</button>` : ''}
                                    \${this.canManage ? `<button onclick="ipManager.deleteIp('\${record.ip}')" class="text-red-600 hover:text-red-900">Delete</button>` : ''}
                                </td>
                            </tr>
                        `;
                    });

                    tbody.innerHTML = html;
                    this.updatePagination(pagination);
                }

                updatePagination(pagination) {
                    document.getElementById('recordsFrom').textContent = ((pagination.current_page - 1) * pagination.per_page) + 1;
                    document.getElementById('recordsTo').textContent = Math.min(pagination.current_page * pagination.per_page, pagination.total);
                    document.getElementById('recordsTotal').textContent = pagination.total;

                    // Update pagination buttons
                    const container = document.getElementById('paginationButtons');
                    let html = '';

                    if (pagination.current_page > 1) {
                        html += `<button onclick="ipManager.changePage(\${pagination.current_page - 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</button>`;
                    }

                    if (pagination.current_page < pagination.total_pages) {
                        html += `<button onclick="ipManager.changePage(\${pagination.current_page + 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>`;
                    }

                    container.innerHTML = html;
                }

                changePage(page) {
                    this.currentPage = page;
                    this.loadIpRecords();
                }

                async viewIpDetails(ip) {
                    try {
                        const response = await fetch('/admin/api/ip-intelligence/details?ip=' + encodeURIComponent(ip));
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.showIpDetailsModal(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading IP details:', error);
                    }
                }

                showIpDetailsModal(data) {
                    const modal = document.getElementById('ipDetailsModal');
                    const content = document.getElementById('ipDetailsContent');

                    content.innerHTML = `
                        <div class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h4>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">IP Address</dt>
                                            <dd class="text-sm text-gray-900">\${data.ip_data.ip}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Type</dt>
                                            <dd class="text-sm text-gray-900">\${data.ip_data.type || 'Unknown'}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Hostname</dt>
                                            <dd class="text-sm text-gray-900">\${data.ip_data.hostname || 'Not available'}</dd>
                                        </div>
                                    </dl>
                                </div>

                                <div>
                                    <h4 class="text-lg font-medium text-gray-900 mb-4">Cache Status</h4>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Cached At</dt>
                                            <dd class="text-sm text-gray-900">\${data.ip_data.cached_at}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                                            <dd class="text-sm \${data.is_stale ? 'text-yellow-600' : 'text-green-600'}">\${data.is_stale ? 'Stale' : 'Fresh'}</dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>

                            \${data.can_refresh ? `
                            <div class="flex justify-end">
                                <button onclick="ipManager.refreshIpFromModal('\${data.ip_data.ip}')" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                    <i class="fas fa-refresh mr-2"></i>Refresh Data
                                </button>
                            </div>
                            ` : ''}
                        </div>
                    `;

                    modal.classList.remove('hidden');
                }

                async refreshIp(ip) {
                    if (!confirm('Are you sure you want to refresh data for ' + ip + '?')) return;

                    try {
                        const response = await fetch('/admin/api/ip-intelligence/refresh', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ ip: ip })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                alert('IP data refreshed successfully');
                                this.loadIpRecords();
                            } else {
                                alert('Failed to refresh IP data: ' + data.error);
                            }
                        }
                    } catch (error) {
                        console.error('Error refreshing IP:', error);
                        alert('Error refreshing IP data');
                    }
                }

                async deleteIp(ip) {
                    if (!confirm('Are you sure you want to delete the record for ' + ip + '?')) return;

                    try {
                        const response = await fetch('/admin/api/ip-intelligence/delete', {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ ip: ip })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                alert('IP record deleted successfully');
                                this.loadIpRecords();
                            } else {
                                alert('Failed to delete IP record: ' + data.error);
                            }
                        }
                    } catch (error) {
                        console.error('Error deleting IP:', error);
                        alert('Error deleting IP record');
                    }
                }

                async performBulkRefresh() {
                    if (!confirm('This will refresh up to 10 stale IP records. Continue?')) return;

                    try {
                        const response = await fetch('/admin/api/ip-intelligence/bulk-refresh', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ limit: 10 })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                alert(data.message);
                                this.loadIpStatistics();
                                this.loadIpRecords();
                            } else {
                                alert('Bulk refresh failed: ' + data.error);
                            }
                        }
                    } catch (error) {
                        console.error('Error performing bulk refresh:', error);
                        alert('Error performing bulk refresh');
                    }
                }

                debounce(func, wait) {
                    let timeout;
                    return function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeout);
                            func(...args);
                        };
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                    };
                }
            }

            // Global functions for onclick handlers
            function closeModal(modalId = 'ipDetailsModal') {
                document.getElementById(modalId).classList.add('hidden');
            }

            // Initialize IP Intelligence manager when DOM is loaded
            let ipManager;
            document.addEventListener('DOMContentLoaded', function() {
                ipManager = new IpIntelligenceManager();
            });
        </script>
HTML;
    }
}