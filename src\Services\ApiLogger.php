<?php

namespace Skpassegna\GuardgeoApi\Services;

/**
 * API Logger
 * 
 * Specialized logger for API requests, responses, and related operations.
 * Provides structured logging for API interactions with enhanced context.
 */
class ApiLogger implements LoggerInterface
{
    private LoggingService $loggingService;
    
    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }
    
    /**
     * Log debug message
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_DEBUG, $message, $context);
    }
    
    /**
     * Log info message
     */
    public function info(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_INFO, $message, $context);
    }
    
    /**
     * Log warning message
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_WARNING, $message, $context);
    }
    
    /**
     * Log error message
     */
    public function error(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_ERROR, $message, $context);
    }
    
    /**
     * Log critical message
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_CRITICAL, $message, $context);
    }
    
    /**
     * Log API request received
     */
    public function logRequestReceived(array $requestData): void
    {
        $message = sprintf(
            'API request received from IP %s for plugin %s',
            $requestData['ip'] ?? 'unknown',
            $requestData['plugin_id'] ?? 'unknown'
        );
        
        $context = [
            'request_data' => $requestData,
            'endpoint' => '/api/analyze',
            'method' => 'POST'
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log API validation step
     */
    public function logValidation(string $step, bool $success, array $details = []): void
    {
        $message = sprintf(
            'API validation %s: %s',
            $step,
            $success ? 'SUCCESS' : 'FAILED'
        );
        
        $context = [
            'validation_step' => $step,
            'success' => $success,
            'details' => $details
        ];
        
        $level = $success ? LoggingService::LEVEL_INFO : LoggingService::LEVEL_WARNING;
        $this->log($level, $message, $context);
    }
    
    /**
     * Log Freemius validation
     */
    public function logFreemiusValidation(int $pluginId, int $installId, bool $valid, array $details = []): void
    {
        $message = sprintf(
            'Freemius validation for plugin %d, install %d: %s',
            $pluginId,
            $installId,
            $valid ? 'VALID' : 'INVALID'
        );
        
        $context = [
            'plugin_id' => $pluginId,
            'install_id' => $installId,
            'valid' => $valid,
            'freemius_details' => $details
        ];
        
        $level = $valid ? LoggingService::LEVEL_INFO : LoggingService::LEVEL_WARNING;
        $this->log($level, $message, $context);
    }
    
    /**
     * Log IP intelligence lookup
     */
    public function logIpLookup(string $ip, bool $cached, array $intelligence = []): void
    {
        $message = sprintf(
            'IP intelligence lookup for %s: %s',
            $ip,
            $cached ? 'CACHED' : 'FRESH'
        );
        
        $context = [
            'ip' => $ip,
            'cached' => $cached,
            'intelligence_summary' => [
                'country' => $intelligence['location']['country'] ?? 'unknown',
                'is_vpn' => $intelligence['security']['is_vpn'] ?? false,
                'is_proxy' => $intelligence['security']['is_proxy'] ?? false,
                'threat_level' => $this->calculateThreatLevel($intelligence)
            ]
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log API response sent
     */
    public function logResponseSent(int $statusCode, array $responseData, int $responseTime = null): void
    {
        $message = sprintf(
            'API response sent: %d in %dms',
            $statusCode,
            $responseTime ?? 0
        );
        
        $context = [
            'status_code' => $statusCode,
            'response_time_ms' => $responseTime,
            'response_size' => strlen(json_encode($responseData)),
            'success' => $statusCode >= 200 && $statusCode < 300
        ];
        
        $level = $statusCode >= 400 ? LoggingService::LEVEL_WARNING : LoggingService::LEVEL_INFO;
        $this->log($level, $message, $context);
    }
    
    /**
     * Log API error
     */
    public function logApiError(string $errorCode, string $errorMessage, array $context = []): void
    {
        $message = sprintf('API Error [%s]: %s', $errorCode, $errorMessage);
        
        $errorContext = array_merge($context, [
            'error_code' => $errorCode,
            'error_message' => $errorMessage
        ]);
        
        $this->error($message, $errorContext);
    }
    
    /**
     * Log rate limiting
     */
    public function logRateLimit(string $ip, int $requestCount, int $limit): void
    {
        $message = sprintf(
            'Rate limit check for IP %s: %d/%d requests',
            $ip,
            $requestCount,
            $limit
        );
        
        $context = [
            'ip' => $ip,
            'request_count' => $requestCount,
            'rate_limit' => $limit,
            'exceeded' => $requestCount > $limit
        ];
        
        $level = $requestCount > $limit ? LoggingService::LEVEL_WARNING : LoggingService::LEVEL_DEBUG;
        $this->log($level, $message, $context);
    }
    
    /**
     * Log external API call
     */
    public function logExternalApiCall(string $service, string $endpoint, int $responseCode, int $responseTime): void
    {
        $message = sprintf(
            'External API call to %s %s: %d in %dms',
            $service,
            $endpoint,
            $responseCode,
            $responseTime
        );
        
        $context = [
            'external_service' => $service,
            'endpoint' => $endpoint,
            'response_code' => $responseCode,
            'response_time_ms' => $responseTime,
            'success' => $responseCode >= 200 && $responseCode < 300
        ];
        
        $level = $responseCode >= 400 ? LoggingService::LEVEL_WARNING : LoggingService::LEVEL_INFO;
        $this->log($level, $message, $context);
    }
    
    /**
     * Calculate threat level from IP intelligence
     */
    private function calculateThreatLevel(array $intelligence): string
    {
        $security = $intelligence['security'] ?? [];
        
        if ($security['is_threat'] ?? false) {
            return 'HIGH';
        }
        
        if (($security['is_vpn'] ?? false) || ($security['is_proxy'] ?? false)) {
            return 'MEDIUM';
        }
        
        if ($security['is_anonymous'] ?? false) {
            return 'LOW';
        }
        
        return 'NONE';
    }
    
    /**
     * Log API request with basic parameters
     */
    public function logApiRequest(string $ip, string $visitorHash, int $pluginId, int $installId, string $url): void
    {
        $message = sprintf(
            'API request from %s for plugin %d, install %d',
            $ip,
            $pluginId,
            $installId
        );
        
        $context = [
            'ip' => $ip,
            'visitor_hash' => $visitorHash,
            'plugin_id' => $pluginId,
            'install_id' => $installId,
            'url' => $url,
            'endpoint' => '/api/analyze',
            'method' => 'POST'
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log API response with status and validation result
     */
    public function logApiResponse(string $ip, int $pluginId, int $installId, int $statusCode, bool $freemiusValid): void
    {
        $message = sprintf(
            'API response %d for %s (plugin %d, install %d) - Freemius: %s',
            $statusCode,
            $ip,
            $pluginId,
            $installId,
            $freemiusValid ? 'VALID' : 'INVALID'
        );
        
        $context = [
            'ip' => $ip,
            'plugin_id' => $pluginId,
            'install_id' => $installId,
            'status_code' => $statusCode,
            'freemius_valid' => $freemiusValid,
            'success' => $statusCode >= 200 && $statusCode < 300
        ];
        
        $level = $statusCode >= 400 ? LoggingService::LEVEL_WARNING : LoggingService::LEVEL_INFO;
        $this->log($level, $message, $context);
    }

    /**
     * Internal log method
     */
    private function log(string $level, string $message, array $context): void
    {
        $this->loggingService->log(LoggingService::TYPE_API, $level, $message, $context);
    }
}