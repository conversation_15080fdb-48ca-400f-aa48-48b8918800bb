<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Utils\AuthMiddleware;
use Skpassegna\GuardgeoApi\Utils\RoleManager;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Services\MonitoringService;
use Skpassegna\GuardgeoApi\Services\ViewRenderer;
use Skpassegna\GuardgeoApi\Views\Pages\DashboardPage;
use Skpassegna\GuardgeoApi\Views\Pages\IpIntelligencePage;
use Skpassegna\GuardgeoApi\Views\Pages\FreemiusPage;
use Skpassegna\GuardgeoApi\Views\Pages\LogsPage;
use Skpassegna\GuardgeoApi\Views\Pages\UserManagementPage;
use Skpassegna\GuardgeoApi\Views\Pages\SettingsPage;

/**
 * Admin Controller
 * 
 * Handles admin dashboard interface routing and page rendering
 * with role-based access control and modern component-based design system.
 */
class AdminController
{
    private AuthMiddleware $authMiddleware;
    private RoleManager $roleManager;
    private LoggingService $logger;
    private MonitoringService $monitoring;
    private ViewRenderer $viewRenderer;

    public function __construct(
        AuthMiddleware $authMiddleware,
        RoleManager $roleManager,
        LoggingService $logger,
        MonitoringService $monitoring,
        ViewRenderer $viewRenderer
    ) {
        $this->authMiddleware = $authMiddleware;
        $this->roleManager = $roleManager;
        $this->logger = $logger;
        $this->monitoring = $monitoring;
        $this->viewRenderer = $viewRenderer;
    }

    /**
     * Main dashboard page
     *
     * @return void
     */
    public function dashboard(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }
        
        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'admin_dashboard')) {
            echo $this->viewRenderer->renderError(403, 'Access denied', 'Access Denied');
            return;
        }

        $this->renderDashboard($user);
    }

    /**
     * IP Intelligence management page
     *
     * @return void
     */
    public function ipIntelligence(): void
    {
        $this->requireAuthentication();
        
        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'ip_intelligence_viewer')) {
            $this->renderAccessDenied();
            return;
        }

        $this->renderIpIntelligence($user);
    }

    /**
     * Freemius integration management page
     *
     * @return void
     */
    public function freemius(): void
    {
        $this->requireAuthentication();
        
        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'freemius_viewer')) {
            $this->renderAccessDenied();
            return;
        }

        $this->renderFreemius($user);
    }

    /**
     * Logs and monitoring page
     *
     * @return void
     */
    public function logs(): void
    {
        $this->requireAuthentication();
        
        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'log_viewer')) {
            $this->renderAccessDenied();
            return;
        }

        $this->renderLogs($user);
    }

    /**
     * User management page (Super Admin only)
     *
     * @return void
     */
    public function users(): void
    {
        $this->requireAuthentication();
        
        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'user_management')) {
            $this->renderAccessDenied();
            return;
        }

        $this->renderUsers($user);
    }

    /**
     * Settings page (Super Admin only)
     *
     * @return void
     */
    public function settings(): void
    {
        $this->requireAuthentication();
        
        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'system_settings')) {
            $this->renderAccessDenied();
            return;
        }

        $this->renderSettings($user);
    }

    /**
     * System maintenance page (Super Admin and Dev only)
     *
     * @return void
     */
    public function maintenance(): void
    {
        $this->requireAuthentication();
        
        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'system_maintenance')) {
            $this->renderAccessDenied();
            return;
        }

        $this->renderMaintenance($user);
    }

    /**
     * Require authentication and redirect if not authenticated
     *
     * @return bool
     */
    private function requireAuthentication(): bool
    {
        if (!$this->authMiddleware->isAuthenticated()) {
            $currentUrl = $_SERVER['REQUEST_URI'] ?? '/admin/dashboard';
            $redirectUrl = '/admin/login?redirect=' . urlencode($currentUrl);
            $this->viewRenderer->redirect($redirectUrl);
            return false;
        }
        return true;
    }

    /**
     * Render dashboard page
     *
     * @param array $user
     * @return void
     */
    private function renderDashboard(array $user): void
    {
        $widgets = $this->roleManager->getDashboardWidgets($user['role']);
        $navigation = $this->roleManager->getNavigationMenu($user['role']);
        
        echo $this->viewRenderer->renderAdminPage(DashboardPage::class, [
            'title' => 'Dashboard - GuardGeo Admin',
            'pageTitle' => 'Dashboard',
            'pageDescription' => 'System overview and key metrics',
            'currentPage' => 'dashboard',
            'user' => $user,
            'navigation' => $navigation,
            'widgets' => $widgets
        ]);
    }

    /**
     * Render IP Intelligence page
     *
     * @param array $user
     * @return void
     */
    private function renderIpIntelligence(array $user): void
    {
        $canManage = $this->roleManager->canAccessFeature($user['role'], 'ip_intelligence_manager');
        $navigation = $this->roleManager->getNavigationMenu($user['role']);
        
        echo $this->viewRenderer->renderAdminPage(IpIntelligencePage::class, [
            'title' => 'IP Intelligence - GuardGeo Admin',
            'pageTitle' => 'IP Intelligence',
            'pageDescription' => 'Manage IP data and intelligence cache',
            'currentPage' => 'ip-intelligence',
            'user' => $user,
            'navigation' => $navigation,
            'canManage' => $canManage
        ]);
    }

    /**
     * Render Freemius page
     *
     * @param array $user
     * @return void
     */
    private function renderFreemius(array $user): void
    {
        $canManage = $this->roleManager->canAccessFeature($user['role'], 'freemius_manager');
        $navigation = $this->roleManager->getNavigationMenu($user['role']);
        
        echo $this->viewRenderer->renderAdminPage(FreemiusPage::class, [
            'title' => 'Freemius Integration - GuardGeo Admin',
            'pageTitle' => 'Freemius Integration',
            'pageDescription' => 'Manage products and installations',
            'currentPage' => 'freemius',
            'user' => $user,
            'navigation' => $navigation,
            'canManage' => $canManage
        ]);
    }

    /**
     * Render Logs page
     *
     * @param array $user
     * @return void
     */
    private function renderLogs(array $user): void
    {
        $canExport = $this->roleManager->hasPermission($user['role'], RoleManager::CATEGORY_LOGS, RoleManager::ACTION_EXPORT);
        $navigation = $this->roleManager->getNavigationMenu($user['role']);
        
        echo $this->viewRenderer->renderAdminPage(LogsPage::class, [
            'title' => 'Logs & Monitoring - GuardGeo Admin',
            'pageTitle' => 'Logs & Monitoring',
            'pageDescription' => 'System activity and monitoring',
            'currentPage' => 'logs',
            'user' => $user,
            'navigation' => $navigation,
            'canExport' => $canExport
        ]);
    }

    /**
     * Render Users page
     *
     * @param array $user
     * @return void
     */
    private function renderUsers(array $user): void
    {
        $navigation = $this->roleManager->getNavigationMenu($user['role']);
        
        echo $this->viewRenderer->renderAdminPage(UserManagementPage::class, [
            'title' => 'User Management - GuardGeo Admin',
            'pageTitle' => 'User Management',
            'pageDescription' => 'Manage admin users and permissions',
            'currentPage' => 'users',
            'user' => $user,
            'navigation' => $navigation
        ]);
    }

    /**
     * Render Settings page
     *
     * @param array $user
     * @return void
     */
    private function renderSettings(array $user): void
    {
        $navigation = $this->roleManager->getNavigationMenu($user['role']);
        
        echo $this->viewRenderer->renderAdminPage(SettingsPage::class, [
            'title' => 'Settings - GuardGeo Admin',
            'pageTitle' => 'Settings',
            'pageDescription' => 'System configuration and settings',
            'currentPage' => 'settings',
            'user' => $user,
            'navigation' => $navigation
        ]);
    }

    /**
     * Render access denied page
     *
     * @return void
     */
    private function renderAccessDenied(): void
    {
        http_response_code(403);
        header('Content-Type: text/html; charset=utf-8');
        
        echo $this->getAccessDeniedHtml();
    }

    /**
     * Require authentication and redirect if not authenticated
     *
     * @return bool
     */
    private function requireAuthentication(): bool
    {
        if (!$this->authMiddleware->isAuthenticated()) {
            $currentUrl = $_SERVER['REQUEST_URI'] ?? '/admin/dashboard';
            $redirectUrl = '/admin/login?redirect=' . urlencode($currentUrl);
            header("Location: {$redirectUrl}");
            exit;
        }
        
        return true;
    }

    /**
     * Render access denied page
     *
     * @return void
     */
    private function renderAccessDenied(): void
    {
        echo $this->viewRenderer->renderError(403, 'You do not have permission to access this page.', 'Access Denied');
    }

    /**
     * Handle form submission with validation
     *
     * @param array $validationRules
     * @param callable $processCallback
     * @param array $options
     * @return void
     */
    protected function handleFormSubmission(
        array $validationRules,
        callable $processCallback,
        array $options = []
    ): void {
        $formValidator = new \Skpassegna\GuardgeoApi\Utils\FormValidator();
        $ajaxHandler = new \Skpassegna\GuardgeoApi\Utils\AjaxFormHandler(
            $formValidator,
            $this->authMiddleware,
            $this->logger
        );

        $ajaxHandler->handleSubmission($validationRules, $processCallback, $options);
    }

    /**
     * Handle file upload with form data
     *
     * @param array $validationRules
     * @param array $fileRules
     * @param callable $processCallback
     * @param array $options
     * @return void
     */
    protected function handleFileUpload(
        array $validationRules,
        array $fileRules,
        callable $processCallback,
        array $options = []
    ): void {
        $formValidator = new \Skpassegna\GuardgeoApi\Utils\FormValidator();
        $ajaxHandler = new \Skpassegna\GuardgeoApi\Utils\AjaxFormHandler(
            $formValidator,
            $this->authMiddleware,
            $this->logger
        );

        $ajaxHandler->handleFileUpload($validationRules, $fileRules, $processCallback, $options);
    }

    /**
     * Get CSRF token for forms
     *
     * @return string
     */
    protected function getCsrfToken(): string
    {
        $sessionManager = new \Skpassegna\GuardgeoApi\Utils\SessionManager();
        $sessionManager->startSession();
        return $sessionManager->getCsrfToken();
    }

    /**
     * Validate form data
     *
     * @param array $data
     * @param array $rules
     * @param array $customMessages
     * @return array
     */
    protected function validateFormData(array $data, array $rules, array $customMessages = []): array
    {
        $validator = new \Skpassegna\GuardgeoApi\Utils\FormValidator();
        return $validator->validate($data, $rules, $customMessages);
    }

    /**
     * Send JSON response
     *
     * @param array $data
     * @param int $statusCode
     * @return void
     */
    protected function sendJsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Get request data (POST or JSON)
     *
     * @return array
     */
    protected function getRequestData(): array
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        if (strpos($contentType, 'application/json') !== false) {
            $json = file_get_contents('php://input');
            return json_decode($json, true) ?: [];
        }
        
        return $_POST;
    }

    /**
     * Check if request is AJAX
     *
     * @return bool
     */
    protected function isAjaxRequest(): bool
    {
        return (
            !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest'
        ) || (
            !empty($_SERVER['HTTP_ACCEPT']) &&
            strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false
        );
    }

    /**
     * Get admin layout HTML
     *
     * @param string $title
     * @param string $pageId
     * @param array $user
     * @param array $navigation
     * @param array $data
     * @return string
     */
    private function getAdminLayoutHtml(string $title, string $pageId, array $user, array $navigation, array $data): string
    {
        $pageTitle = $data['pageTitle'] ?? $title;
        $pageDescription = $data['pageDescription'] ?? '';
        
        return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title} - GuardGeo Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .content-transition { transition: margin-left 0.3s ease-in-out; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Mobile menu overlay -->
    <div id="mobileMenuOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 hidden lg:hidden"></div>
    
    <!-- Sidebar -->
    <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 sidebar-transition">
        <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <h1 class="text-xl font-bold text-gray-900">GuardGeo Admin</h1>
            <button id="closeSidebar" class="lg:hidden text-gray-500 hover:text-gray-700">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <nav class="mt-6">
            {$this->generateNavigationHtml($navigation, $pageId)}
        </nav>
        
        <!-- User info -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">{$this->getUserInitials($user['email'])}</span>
                    </div>
                </div>
                <div class="ml-3 flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">{$user['email']}</p>
                    <p class="text-xs text-gray-500">{$this->getRoleDisplayName($user['role'])}</p>
                </div>
                <div class="ml-2">
                    <button id="logoutBtn" class="text-gray-400 hover:text-gray-600" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main content -->
    <div id="mainContent" class="lg:ml-64 content-transition">
        <!-- Top bar -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between h-16 px-6">
                <div class="flex items-center">
                    <button id="openSidebar" class="lg:hidden text-gray-500 hover:text-gray-700 mr-4">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">{$pageTitle}</h2>
                        {$pageDescription ? "<p class=\"text-sm text-gray-600\">{$pageDescription}</p>" : ''}
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <button class="text-gray-400 hover:text-gray-600" title="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>
                    
                    <!-- User menu -->
                    <div class="relative">
                        <button id="userMenuBtn" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-medium">{$this->getUserInitials($user['email'])}</span>
                            </div>
                        </button>
                        
                        <div id="userMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                            <div class="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
                                <div class="font-medium">{$user['email']}</div>
                                <div class="text-gray-500">{$this->getRoleDisplayName($user['role'])}</div>
                            </div>
                            <button id="logoutBtnTop" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>Sign out
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Page content -->
        <main class="p-6">
            <div id="pageContent">
                {$this->getPageContent($pageId, $data)}
            </div>
        </main>
    </div>
    
    <!-- Loading overlay -->
    <div id="loadingOverlay" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 flex items-center">
            <svg class="animate-spin h-5 w-5 text-blue-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-gray-700">Loading...</span>
        </div>
    </div>
    
    <script>
        {$this->getAdminLayoutScript()}
    </script>
</body>
</html>
HTML;
    }

    /**
     * Generate navigation HTML
     *
     * @param array $navigation
     * @param string $currentPageId
     * @return string
     */
    private function generateNavigationHtml(array $navigation, string $currentPageId): string
    {
        $html = '';
        
        foreach ($navigation as $item) {
            $isActive = $item['id'] === $currentPageId;
            $activeClass = $isActive ? 'bg-blue-50 border-r-2 border-blue-500 text-blue-700' : 'text-gray-700 hover:bg-gray-50';
            
            $icon = $this->getIconClass($item['icon']);
            
            $html .= <<<HTML
            <a href="{$item['url']}" class="flex items-center px-6 py-3 {$activeClass} transition-colors duration-200">
                <i class="{$icon} mr-3"></i>
                <span class="font-medium">{$item['title']}</span>
            </a>
HTML;
        }
        
        return $html;
    }

    /**
     * Get icon class for navigation items
     *
     * @param string $icon
     * @return string
     */
    private function getIconClass(string $icon): string
    {
        return match ($icon) {
            'dashboard' => 'fas fa-tachometer-alt',
            'globe' => 'fas fa-globe',
            'plugin' => 'fas fa-plug',
            'file-text' => 'fas fa-file-alt',
            'users' => 'fas fa-users',
            'settings' => 'fas fa-cog',
            default => 'fas fa-circle'
        };
    }

    /**
     * Get user initials from email
     *
     * @param string $email
     * @return string
     */
    private function getUserInitials(string $email): string
    {
        $parts = explode('@', $email);
        $username = $parts[0] ?? '';
        
        if (strlen($username) >= 2) {
            return strtoupper(substr($username, 0, 2));
        }
        
        return strtoupper(substr($email, 0, 1));
    }

    /**
     * Get role display name
     *
     * @param string $role
     * @return string
     */
    private function getRoleDisplayName(string $role): string
    {
        return match ($role) {
            'super_admin' => 'Super Admin',
            'dev' => 'Developer',
            'marketing' => 'Marketing',
            'sales' => 'Sales',
            default => ucfirst($role)
        };
    }

    /**
     * Get page content based on page ID
     *
     * @param string $pageId
     * @param array $data
     * @return string
     */
    private function getPageContent(string $pageId, array $data): string
    {
        return match ($pageId) {
            'dashboard' => $this->getDashboardContent($data),
            'ip-intelligence' => $this->getIpIntelligenceContent($data),
            'freemius' => $this->getFreemiusContent($data),
            'logs' => $this->getLogsContent($data),
            'users' => $this->getUsersContent($data),
            'settings' => $this->getSettingsContent($data),
            default => '<div class="text-center py-12"><h3 class="text-lg font-medium text-gray-900">Page content coming soon</h3></div>'
        };
    }

    /**
     * Get dashboard content
     *
     * @param array $data
     * @return string
     */
    private function getDashboardContent(array $data): string
    {
        return <<<HTML
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- System Status Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div id="systemStatusIcon" class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <i class="fas fa-spinner fa-spin text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">System Status</h3>
                        <p id="systemStatusText" class="text-sm text-gray-600">Loading...</p>
                    </div>
                </div>
            </div>
            
            <!-- API Requests Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-chart-line text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">API Requests</h3>
                        <p id="apiRequestsText" class="text-sm text-gray-600">Loading...</p>
                        <p id="apiRequestsDetail" class="text-xs text-gray-500"></p>
                    </div>
                </div>
            </div>
            
            <!-- IP Intelligence Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-globe text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">IP Intelligence</h3>
                        <p id="ipIntelligenceText" class="text-sm text-gray-600">Loading...</p>
                        <p id="ipIntelligenceDetail" class="text-xs text-gray-500"></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- API Usage Chart -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">API Usage (This Week)</h3>
                <div id="apiUsageChart" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                    <p class="text-gray-500">Loading API usage data...</p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">IP Intelligence Cache</h3>
                <div id="ipCacheChart" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                    <p class="text-gray-500">Loading cache data...</p>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
            </div>
            <div id="recentActivityContainer" class="p-6">
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                    <p class="text-gray-500">Loading recent activity...</p>
                </div>
            </div>
        </div>
        
        <script>
            // Load dashboard data when page loads
            document.addEventListener('DOMContentLoaded', function() {
                loadDashboardData();
                
                // Refresh data every 30 seconds
                setInterval(loadDashboardData, 30000);
            });
            
            async function loadDashboardData() {
                try {
                    // Load system overview
                    const overviewResponse = await fetch('/admin/api/dashboard/overview');
                    if (overviewResponse.ok) {
                        const overviewData = await overviewResponse.json();
                        if (overviewData.success) {
                            updateSystemOverview(overviewData.data);
                        }
                    }
                    
                    // Load API usage stats
                    const apiUsageResponse = await fetch('/admin/api/dashboard/api-usage?period=week');
                    if (apiUsageResponse.ok) {
                        const apiUsageData = await apiUsageResponse.json();
                        if (apiUsageData.success) {
                            updateApiUsageChart(apiUsageData.data);
                        }
                    }
                    
                    // Load IP intelligence stats
                    const ipStatsResponse = await fetch('/admin/api/dashboard/ip-stats');
                    if (ipStatsResponse.ok) {
                        const ipStatsData = await ipStatsResponse.json();
                        if (ipStatsData.success) {
                            updateIpIntelligenceChart(ipStatsData.data);
                        }
                    }
                    
                    // Load recent activity
                    const activityResponse = await fetch('/admin/api/dashboard/activity?limit=10');
                    if (activityResponse.ok) {
                        const activityData = await activityResponse.json();
                        if (activityData.success) {
                            updateRecentActivity(activityData.data);
                        }
                    }
                    
                } catch (error) {
                    console.error('Error loading dashboard data:', error);
                }
            }
            
            function updateSystemOverview(data) {
                // Update system status
                const statusIcon = document.getElementById('systemStatusIcon');
                const statusText = document.getElementById('systemStatusText');
                
                if (data.system_status === 'operational') {
                    statusIcon.className = 'w-8 h-8 bg-green-500 rounded-full flex items-center justify-center';
                    statusIcon.innerHTML = '<i class="fas fa-check text-white text-sm"></i>';
                    statusText.textContent = 'All systems operational';
                    statusText.className = 'text-sm text-green-600';
                } else {
                    statusIcon.className = 'w-8 h-8 bg-red-500 rounded-full flex items-center justify-center';
                    statusIcon.innerHTML = '<i class="fas fa-exclamation text-white text-sm"></i>';
                    statusText.textContent = 'System error detected';
                    statusText.className = 'text-sm text-red-600';
                }
                
                // Update API requests
                const apiText = document.getElementById('apiRequestsText');
                const apiDetail = document.getElementById('apiRequestsDetail');
                if (data.api_requests) {
                    apiText.textContent = data.api_requests.today + ' requests today';
                    apiDetail.textContent = data.api_requests.week + ' this week';
                }
                
                // Update IP intelligence
                const ipText = document.getElementById('ipIntelligenceText');
                const ipDetail = document.getElementById('ipIntelligenceDetail');
                if (data.ip_intelligence) {
                    ipText.textContent = data.ip_intelligence.total_records + ' IP records';
                    ipDetail.textContent = 'Cache: ' + data.ip_intelligence.cache_status;
                }
            }
            
            function updateApiUsageChart(data) {
                const container = document.getElementById('apiUsageChart');
                
                if (data.total_requests === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center py-8">No API requests this week</p>';
                    return;
                }
                
                const successRate = data.total_requests > 0 ? Math.round((data.success_requests / data.total_requests) * 100) : 0;
                
                container.innerHTML = `
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">Total Requests</span>
                            <span class="text-lg font-bold text-gray-900">${data.total_requests}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">Success Rate</span>
                            <span class="text-lg font-bold text-green-600">${successRate}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">Avg Response Time</span>
                            <span class="text-lg font-bold text-blue-600">${data.avg_response_time}ms</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: ${successRate}%"></div>
                        </div>
                    </div>
                `;
            }
            
            function updateIpIntelligenceChart(data) {
                const container = document.getElementById('ipCacheChart');
                
                if (data.total_records === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center py-8">No IP records cached</p>';
                    return;
                }
                
                container.innerHTML = `
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">Total Records</span>
                            <span class="text-lg font-bold text-gray-900">${data.total_records}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">Fresh Records</span>
                            <span class="text-lg font-bold text-green-600">${data.fresh_records}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">Cache Efficiency</span>
                            <span class="text-lg font-bold text-purple-600">${data.cache_efficiency}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: ${data.cache_efficiency}%"></div>
                        </div>
                    </div>
                `;
            }
            
            function updateRecentActivity(activities) {
                const container = document.getElementById('recentActivityContainer');
                
                if (activities.length === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center py-8">No recent activity</p>';
                    return;
                }
                
                let html = '<div class="space-y-4">';
                
                activities.forEach(activity => {
                    const levelColor = activity.level === 'error' ? 'text-red-600' : 
                                     activity.level === 'warning' ? 'text-yellow-600' : 'text-gray-600';
                    const typeIcon = activity.type === 'api' ? 'fa-plug' : 'fa-user-shield';
                    
                    html += `
                        <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <i class="fas ${typeIcon} ${levelColor}"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">${activity.message}</p>
                                <p class="text-xs text-gray-500">${activity.formatted_time}</p>
                                ${activity.ip ? `<p class="text-xs text-gray-400">IP: ${activity.ip}</p>` : ''}
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
                container.innerHTML = html;
            }
        </script>
HTML;
    }

    /**
     * Get IP Intelligence content
     *
     * @param array $data
     * @return string
     */
    private function getIpIntelligenceContent(array $data): string
    {
        $canManage = $data['canManage'] ?? false;
        
        return <<<HTML
        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div class="flex-1 max-w-lg">
                        <div class="relative">
                            <input 
                                type="text" 
                                id="ipSearch" 
                                placeholder="Search by IP address or hostname..."
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">All Records</option>
                            <option value="fresh">Fresh Only</option>
                            <option value="stale">Stale Only</option>
                        </select>
                        
                        <select id="sortBy" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="cached_at">Last Cached</option>
                            <option value="ip">IP Address</option>
                            <option value="security_expires_at">Security Expiry</option>
                        </select>
                        
                        <button id="refreshSearch" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                            <i class="fas fa-sync-alt mr-2"></i>Refresh
                        </button>
                        
                        {$canManage ? '
                        <button id="bulkRefreshBtn" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                            <i class="fas fa-refresh mr-2"></i>Bulk Refresh
                        </button>
                        ' : ''}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-database text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Total Records</h3>
                        <p id="totalRecords" class="text-sm text-gray-600">Loading...</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Fresh Records</h3>
                        <p id="freshRecords" class="text-sm text-gray-600">Loading...</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-clock text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Stale Records</h3>
                        <p id="staleRecords" class="text-sm text-gray-600">Loading...</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Threat IPs</h3>
                        <p id="threatIps" class="text-sm text-gray-600">Loading...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- IP Records Table -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">IP Intelligence Records</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Security</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cached</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="ipRecordsTable" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                                <p class="text-gray-500">Loading IP records...</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div id="paginationContainer" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing <span id="recordsFrom">0</span> to <span id="recordsTo">0</span> of <span id="recordsTotal">0</span> results
                </div>
                <div id="paginationButtons" class="flex space-x-2">
                    <!-- Pagination buttons will be inserted here -->
                </div>
            </div>
        </div>
        
        <!-- IP Details Modal -->
        <div id="ipDetailsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">IP Intelligence Details</h3>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="ipDetailsContent" class="p-6">
                    <!-- Details content will be loaded here -->
                </div>
            </div>
        </div>
        
        <script>
            let currentPage = 1;
            let currentFilters = {
                search: '',
                status: 'all',
                sort: 'cached_at',
                order: 'desc'
            };
            
            document.addEventListener('DOMContentLoaded', function() {
                loadIpStatistics();
                loadIpRecords();
                
                // Search functionality
                document.getElementById('ipSearch').addEventListener('input', debounce(function() {
                    currentFilters.search = this.value;
                    currentPage = 1;
                    loadIpRecords();
                }, 500));
                
                // Filter functionality
                document.getElementById('statusFilter').addEventListener('change', function() {
                    currentFilters.status = this.value;
                    currentPage = 1;
                    loadIpRecords();
                });
                
                document.getElementById('sortBy').addEventListener('change', function() {
                    currentFilters.sort = this.value;
                    currentPage = 1;
                    loadIpRecords();
                });
                
                // Refresh button
                document.getElementById('refreshSearch').addEventListener('click', function() {
                    loadIpStatistics();
                    loadIpRecords();
                });
                
                // Bulk refresh button
                const bulkRefreshBtn = document.getElementById('bulkRefreshBtn');
                if (bulkRefreshBtn) {
                    bulkRefreshBtn.addEventListener('click', performBulkRefresh);
                }
                
                // Modal close
                document.getElementById('closeModal').addEventListener('click', closeModal);
                document.getElementById('ipDetailsModal').addEventListener('click', function(e) {
                    if (e.target === this) closeModal();
                });
            });
            
            async function loadIpStatistics() {
                try {
                    const response = await fetch('/admin/api/ip-intelligence/statistics');
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateStatistics(data.data);
                        }
                    }
                } catch (error) {
                    console.error('Error loading IP statistics:', error);
                }
            }
            
            async function loadIpRecords() {
                try {
                    const params = new URLSearchParams({
                        page: currentPage,
                        limit: 25,
                        ...currentFilters
                    });
                    
                    const response = await fetch('/admin/api/ip-intelligence/records?' + params);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateIpRecordsTable(data.data, data.pagination);
                        }
                    }
                } catch (error) {
                    console.error('Error loading IP records:', error);
                }
            }
            
            function updateStatistics(stats) {
                document.getElementById('totalRecords').textContent = stats.total_records || 0;
                document.getElementById('freshRecords').textContent = stats.fresh_records || 0;
                document.getElementById('staleRecords').textContent = stats.stale_records || 0;
                document.getElementById('threatIps').textContent = stats.threat_ips || 0;
            }
            
            function updateIpRecordsTable(records, pagination) {
                const tbody = document.getElementById('ipRecordsTable');
                
                if (records.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-12 text-center text-gray-500">No IP records found</td></tr>';
                    return;
                }
                
                let html = '';
                records.forEach(record => {
                    const isStale = record.is_stale;
                    const statusClass = isStale ? 'text-yellow-600' : 'text-green-600';
                    const statusText = isStale ? 'Stale' : 'Fresh';
                    
                    html += `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${record.ip}</div>
                                ${record.hostname ? `<div class="text-xs text-gray-500">${record.hostname}</div>` : ''}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">${record.location || 'Unknown'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">${record.security_status || 'Unknown'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass === 'text-green-600' ? 'bg-green-100' : 'bg-yellow-100'} ${statusClass}">
                                    ${statusText}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${record.cached_at}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewIpDetails('${record.ip}')" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                ${canManage ? `<button onclick="refreshIp('${record.ip}')" class="text-green-600 hover:text-green-900 mr-3">Refresh</button>` : ''}
                                ${canManage ? `<button onclick="deleteIp('${record.ip}')" class="text-red-600 hover:text-red-900">Delete</button>` : ''}
                            </td>
                        </tr>
                    `;
                });
                
                tbody.innerHTML = html;
                updatePagination(pagination);
            }
            
            function updatePagination(pagination) {
                document.getElementById('recordsFrom').textContent = ((pagination.current_page - 1) * pagination.per_page) + 1;
                document.getElementById('recordsTo').textContent = Math.min(pagination.current_page * pagination.per_page, pagination.total);
                document.getElementById('recordsTotal').textContent = pagination.total;
                
                // Update pagination buttons
                const container = document.getElementById('paginationButtons');
                let html = '';
                
                if (pagination.current_page > 1) {
                    html += `<button onclick="changePage(${pagination.current_page - 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</button>`;
                }
                
                if (pagination.current_page < pagination.total_pages) {
                    html += `<button onclick="changePage(${pagination.current_page + 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>`;
                }
                
                container.innerHTML = html;
            }
            
            function changePage(page) {
                currentPage = page;
                loadIpRecords();
            }
            
            async function viewIpDetails(ip) {
                try {
                    const response = await fetch('/admin/api/ip-intelligence/details?ip=' + encodeURIComponent(ip));
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            showIpDetailsModal(data.data);
                        }
                    }
                } catch (error) {
                    console.error('Error loading IP details:', error);
                }
            }
            
            function showIpDetailsModal(data) {
                const modal = document.getElementById('ipDetailsModal');
                const content = document.getElementById('ipDetailsContent');
                
                content.innerHTML = `
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h4>
                                <dl class="space-y-2">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">IP Address</dt>
                                        <dd class="text-sm text-gray-900">${data.ip_data.ip}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Type</dt>
                                        <dd class="text-sm text-gray-900">${data.ip_data.type || 'Unknown'}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Hostname</dt>
                                        <dd class="text-sm text-gray-900">${data.ip_data.hostname || 'Not available'}</dd>
                                    </div>
                                </dl>
                            </div>
                            
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Cache Status</h4>
                                <dl class="space-y-2">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Cached At</dt>
                                        <dd class="text-sm text-gray-900">${data.ip_data.cached_at}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                                        <dd class="text-sm ${data.is_stale ? 'text-yellow-600' : 'text-green-600'}">${data.is_stale ? 'Stale' : 'Fresh'}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                        
                        ${data.can_refresh ? `
                        <div class="flex justify-end">
                            <button onclick="refreshIpFromModal('${data.ip_data.ip}')" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                <i class="fas fa-refresh mr-2"></i>Refresh Data
                            </button>
                        </div>
                        ` : ''}
                    </div>
                `;
                
                modal.classList.remove('hidden');
            }
            
            function closeModal() {
                document.getElementById('ipDetailsModal').classList.add('hidden');
            }
            
            async function refreshIp(ip) {
                if (!confirm('Are you sure you want to refresh data for ' + ip + '?')) return;
                
                try {
                    const response = await fetch('/admin/api/ip-intelligence/refresh', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ ip: ip })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            alert('IP data refreshed successfully');
                            loadIpRecords();
                        } else {
                            alert('Failed to refresh IP data: ' + data.error);
                        }
                    }
                } catch (error) {
                    console.error('Error refreshing IP:', error);
                    alert('Error refreshing IP data');
                }
            }
            
            async function deleteIp(ip) {
                if (!confirm('Are you sure you want to delete the record for ' + ip + '?')) return;
                
                try {
                    const response = await fetch('/admin/api/ip-intelligence/delete', {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ ip: ip })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            alert('IP record deleted successfully');
                            loadIpRecords();
                        } else {
                            alert('Failed to delete IP record: ' + data.error);
                        }
                    }
                } catch (error) {
                    console.error('Error deleting IP:', error);
                    alert('Error deleting IP record');
                }
            }
            
            async function performBulkRefresh() {
                if (!confirm('This will refresh up to 10 stale IP records. Continue?')) return;
                
                try {
                    const response = await fetch('/admin/api/ip-intelligence/bulk-refresh', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ limit: 10 })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            alert(data.message);
                            loadIpStatistics();
                            loadIpRecords();
                        } else {
                            alert('Bulk refresh failed: ' + data.error);
                        }
                    }
                } catch (error) {
                    console.error('Error performing bulk refresh:', error);
                    alert('Error performing bulk refresh');
                }
            }
            
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        </script>
HTML;
                    console.error('Error loading IP statistics:', error);
                }
            }
            
            async function loadIpRecords() {
                try {
                    showLoading();
                    
                    const params = new URLSearchParams({
                        page: currentPage,
                        limit: 25,
                        search: currentFilters.search,
                        status: currentFilters.status,
                        sort: currentFilters.sort,
                        order: currentFilters.order
                    });
                    
                    const response = await fetch('/admin/api/ip-intelligence/records?' + params);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateIpRecordsTable(data.data, data.pagination);
                        }
                    }
                } catch (error) {
                    console.error('Error loading IP records:', error);
                } finally {
                    hideLoading();
                }
            }
            
            function updateStatistics(stats) {
                document.getElementById('totalRecords').textContent = stats.total_records.toLocaleString();
                document.getElementById('freshRecords').textContent = stats.fresh_records.toLocaleString();
                document.getElementById('staleRecords').textContent = stats.stale_records.toLocaleString();
                document.getElementById('threatIps').textContent = stats.threat_ips.toLocaleString();
            }
            
            function updateIpRecordsTable(records, pagination) {
                const tbody = document.getElementById('ipRecordsTable');
                
                if (records.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-12 text-center text-gray-500">No IP records found</td></tr>';
                    return;
                }
                
                let html = '';
                records.forEach(record => {
                    const statusBadge = record.is_stale ? 
                        '<span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Stale</span>' :
                        '<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Fresh</span>';
                    
                    const threatBadges = [];
                    if (record.security.is_threat) threatBadges.push('<span class="px-1 py-0.5 text-xs bg-red-100 text-red-800 rounded">Threat</span>');
                    if (record.security.is_vpn) threatBadges.push('<span class="px-1 py-0.5 text-xs bg-orange-100 text-orange-800 rounded">VPN</span>');
                    if (record.security.is_proxy) threatBadges.push('<span class="px-1 py-0.5 text-xs bg-purple-100 text-purple-800 rounded">Proxy</span>');
                    
                    html += `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${record.ip}</div>
                                ${record.hostname ? `<div class="text-sm text-gray-500">${record.hostname}</div>` : ''}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">${record.location.city || 'Unknown'}</div>
                                <div class="text-sm text-gray-500">${record.location.country || 'Unknown'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-wrap gap-1">
                                    ${threatBadges.join('')}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">${statusBadge}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${formatRelativeTime(record.cached_at)}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewIpDetails('${record.ip}')" class="text-blue-600 hover:text-blue-900 mr-3">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                ${${canManage} ? `
                                <button onclick="refreshIpData('${record.ip}')" class="text-green-600 hover:text-green-900 mr-3">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                                <button onclick="deleteIpRecord('${record.ip}')" class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                                ` : ''}
                            </td>
                        </tr>
                    `;
                });
                
                tbody.innerHTML = html;
                updatePagination(pagination);
            }
            
            function updatePagination(pagination) {
                const from = ((pagination.current_page - 1) * pagination.per_page) + 1;
                const to = Math.min(pagination.current_page * pagination.per_page, pagination.total);
                
                document.getElementById('recordsFrom').textContent = from;
                document.getElementById('recordsTo').textContent = to;
                document.getElementById('recordsTotal').textContent = pagination.total;
                
                // Generate pagination buttons
                let buttonsHtml = '';
                
                // Previous button
                if (pagination.current_page > 1) {
                    buttonsHtml += `<button onclick="changePage(${pagination.current_page - 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</button>`;
                }
                
                // Page numbers
                const startPage = Math.max(1, pagination.current_page - 2);
                const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
                
                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === pagination.current_page;
                    const activeClass = isActive ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50';
                    buttonsHtml += `<button onclick="changePage(${i})" class="px-3 py-2 text-sm border border-gray-300 rounded-md ${activeClass}">${i}</button>`;
                }
                
                // Next button
                if (pagination.current_page < pagination.total_pages) {
                    buttonsHtml += `<button onclick="changePage(${pagination.current_page + 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>`;
                }
                
                document.getElementById('paginationButtons').innerHTML = buttonsHtml;
            }
            
            function changePage(page) {
                currentPage = page;
                loadIpRecords();
            }
            
            async function viewIpDetails(ip) {
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/ip-intelligence/details?ip=' + encodeURIComponent(ip));
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            showIpDetailsModal(data.data);
                        } else {
                            alert('Error: ' + data.error);
                        }
                    }
                } catch (error) {
                    console.error('Error loading IP details:', error);
                    alert('Error loading IP details');
                } finally {
                    hideLoading();
                }
            }
            
            function showIpDetailsModal(data) {
                const modal = document.getElementById('ipDetailsModal');
                const content = document.getElementById('ipDetailsContent');
                
                // Generate detailed view HTML
                content.innerHTML = generateIpDetailsHtml(data);
                
                modal.classList.remove('hidden');
            }
            
            function closeModal() {
                document.getElementById('ipDetailsModal').classList.add('hidden');
            }
            
            function generateIpDetailsHtml(data) {
                const ipData = data.ip_data;
                
                return `
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Basic Information</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">IP Address:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${ipData.ip}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Type:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${ipData.type}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Hostname:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${ipData.hostname || 'N/A'}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Status:</dt>
                                    <dd class="text-sm font-medium ${ipData.is_stale ? 'text-yellow-600' : 'text-green-600'}">${ipData.status}</dd>
                                </div>
                            </dl>
                        </div>
                        
                        <!-- Location Information -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Location</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Country:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${ipData.location.country}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Region:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${ipData.location.region || 'N/A'}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">City:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${ipData.location.city || 'N/A'}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Coordinates:</dt>
                                    <dd class="text-sm font-medium text-gray-900">
                                        ${ipData.location.coordinates.latitude && ipData.location.coordinates.longitude ? 
                                          `${ipData.location.coordinates.latitude}, ${ipData.location.coordinates.longitude}` : 'N/A'}
                                    </dd>
                                </div>
                            </dl>
                        </div>
                        
                        <!-- Security Information -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Security Assessment</h4>
                            <div class="space-y-2">
                                ${Object.entries(ipData.security).map(([key, value]) => `
                                    <div class="flex justify-between">
                                        <dt class="text-sm text-gray-600">${key.replace('is_', '').replace('_', ' ').toUpperCase()}:</dt>
                                        <dd class="text-sm font-medium ${value ? 'text-red-600' : 'text-green-600'}">
                                            ${value ? 'Yes' : 'No'}
                                        </dd>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <!-- Connection Information -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Connection</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">ASN:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${ipData.connection.asn || 'N/A'}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Organization:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${ipData.connection.organization || 'N/A'}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Type:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${ipData.connection.type || 'N/A'}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                    
                    <!-- Cache Information -->
                    <div class="mt-6 bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-3">Cache Information</h4>
                        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                            <div>
                                <dt class="text-sm text-gray-600">Cached At:</dt>
                                <dd class="text-sm font-medium text-gray-900">${formatDateTime(ipData.cached_at)}</dd>
                            </div>
                            <div>
                                <dt class="text-sm text-gray-600">Security Expires:</dt>
                                <dd class="text-sm font-medium text-gray-900">${formatDateTime(ipData.expires_at.security)}</dd>
                            </div>
                            <div>
                                <dt class="text-sm text-gray-600">Location Expires:</dt>
                                <dd class="text-sm font-medium text-gray-900">${formatDateTime(ipData.expires_at.location)}</dd>
                            </div>
                            <div>
                                <dt class="text-sm text-gray-600">Connection Expires:</dt>
                                <dd class="text-sm font-medium text-gray-900">${formatDateTime(ipData.expires_at.connection)}</dd>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            async function refreshIpData(ip) {
                if (!confirm('Are you sure you want to refresh the data for IP ' + ip + '?')) {
                    return;
                }
                
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/ip-intelligence/refresh', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ ip: ip })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert('IP data refreshed successfully');
                        loadIpRecords();
                        loadIpStatistics();
                    } else {
                        alert('Error: ' + data.error);
                    }
                } catch (error) {
                    console.error('Error refreshing IP data:', error);
                    alert('Error refreshing IP data');
                } finally {
                    hideLoading();
                }
            }
            
            async function deleteIpRecord(ip) {
                if (!confirm('Are you sure you want to delete the record for IP ' + ip + '? This action cannot be undone.')) {
                    return;
                }
                
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/ip-intelligence/delete', {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ ip: ip })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert('IP record deleted successfully');
                        loadIpRecords();
                        loadIpStatistics();
                    } else {
                        alert('Error: ' + data.error);
                    }
                } catch (error) {
                    console.error('Error deleting IP record:', error);
                    alert('Error deleting IP record');
                } finally {
                    hideLoading();
                }
            }
            
            async function performBulkRefresh() {
                const limit = prompt('How many stale records would you like to refresh? (1-50)', '10');
                if (!limit || isNaN(limit) || limit < 1 || limit > 50) {
                    return;
                }
                
                if (!confirm(`Are you sure you want to refresh ${limit} stale IP records? This may take some time.`)) {
                    return;
                }
                
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/ip-intelligence/bulk-refresh', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ limit: parseInt(limit) })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert(data.message);
                        loadIpRecords();
                        loadIpStatistics();
                    } else {
                        alert('Error: ' + data.error);
                    }
                } catch (error) {
                    console.error('Error performing bulk refresh:', error);
                    alert('Error performing bulk refresh');
                } finally {
                    hideLoading();
                }
            }
            
            function formatRelativeTime(timestamp) {
                const date = new Date(timestamp);
                const now = new Date();
                const diff = now - date;
                
                const minutes = Math.floor(diff / 60000);
                const hours = Math.floor(diff / 3600000);
                const days = Math.floor(diff / 86400000);
                
                if (days > 0) return days + ' day' + (days > 1 ? 's' : '') + ' ago';
                if (hours > 0) return hours + ' hour' + (hours > 1 ? 's' : '') + ' ago';
                if (minutes > 0) return minutes + ' minute' + (minutes > 1 ? 's' : '') + ' ago';
                return 'Just now';
            }
            
            function formatDateTime(timestamp) {
                return new Date(timestamp).toLocaleString();
            }
            
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        </script>
HTML;
    }

    /**
     * Get Freemius content
     *
     * @param array $data
     * @return string
     */
    private function getFreemiusContent(array $data): string
    {
        $canManage = $data['canManage'] ?? false;
        
        return <<<HTML
        <!-- Tab Navigation -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6">
                    <button id="productsTab" class="py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                        Products
                    </button>
                    <button id="installationsTab" class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        Installations
                    </button>
                    <button id="statisticsTab" class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        Statistics
                    </button>
                </nav>
            </div>
        </div>

        <!-- Products Tab Content -->
        <div id="productsContent" class="tab-content">
            <!-- Search and Filters -->
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="p-6">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                        <div class="flex-1 max-w-lg">
                            <div class="relative">
                                <input 
                                    type="text" 
                                    id="productSearch" 
                                    placeholder="Search products by title or slug..."
                                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <select id="productSort" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="created">Created Date</option>
                                <option value="title">Title</option>
                                <option value="installs_count">Install Count</option>
                                <option value="active_installs_count">Active Installs</option>
                            </select>
                            
                            <button id="refreshProducts" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-sync-alt mr-2"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Freemius Products</h3>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Installs</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="productsTable" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                                    <p class="text-gray-500">Loading products...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div id="productsPagination" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Showing <span id="productsFrom">0</span> to <span id="productsTo">0</span> of <span id="productsTotal">0</span> results
                    </div>
                    <div id="productsPaginationButtons" class="flex space-x-2">
                        <!-- Pagination buttons will be inserted here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Installations Tab Content -->
        <div id="installationsContent" class="tab-content hidden">
            <!-- Search and Filters -->
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="p-6">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                        <div class="flex-1 max-w-lg">
                            <div class="relative">
                                <input 
                                    type="text" 
                                    id="installationSearch" 
                                    placeholder="Search installations by URL or ID..."
                                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <select id="installationStatus" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all">All Installations</option>
                                <option value="active">Active Only</option>
                                <option value="inactive">Inactive Only</option>
                                <option value="premium">Premium Only</option>
                                <option value="trial">Trial Only</option>
                            </select>
                            
                            <select id="installationSort" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="created">Created Date</option>
                                <option value="last_seen_at">Last Seen</option>
                                <option value="url">URL</option>
                            </select>
                            
                            <button id="refreshInstallations" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-sync-alt mr-2"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Installations Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Freemius Installations</h3>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Site</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Version</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Seen</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="installationsTable" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                                    <p class="text-gray-500">Loading installations...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div id="installationsPagination" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Showing <span id="installationsFrom">0</span> to <span id="installationsTo">0</span> of <span id="installationsTotal">0</span> results
                    </div>
                    <div id="installationsPaginationButtons" class="flex space-x-2">
                        <!-- Pagination buttons will be inserted here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Tab Content -->
        <div id="statisticsContent" class="tab-content hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- Product Statistics -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-box text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Total Products</h3>
                            <p id="totalProducts" class="text-sm text-gray-600">Loading...</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-download text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Total Installations</h3>
                            <p id="totalInstallations" class="text-sm text-gray-600">Loading...</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-crown text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Premium Installations</h3>
                            <p id="premiumInstallations" class="text-sm text-gray-600">Loading...</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-clock text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Active Trials</h3>
                            <p id="activeTrials" class="text-sm text-gray-600">Loading...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Detailed Statistics</h3>
                <div id="detailedStats" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                    <p class="text-gray-500">Loading detailed statistics...</p>
                </div>
            </div>
        </div>

        <!-- Details Modal -->
        <div id="detailsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 id="modalTitle" class="text-lg font-medium text-gray-900">Details</h3>
                    <button id="closeDetailsModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="detailsContent" class="p-6">
                    <!-- Details content will be loaded here -->
                </div>
            </div>
        </div>

        <script>
            let currentTab = 'products';
            let currentPage = { products: 1, installations: 1 };
            let currentFilters = {
                products: { search: '', sort: 'created', order: 'desc' },
                installations: { search: '', status: 'all', sort: 'created', order: 'desc' }
            };
            
            document.addEventListener('DOMContentLoaded', function() {
                initializeTabs();
                loadTabContent('products');
                
                // Tab switching
                document.getElementById('productsTab').addEventListener('click', () => switchTab('products'));
                document.getElementById('installationsTab').addEventListener('click', () => switchTab('installations'));
                document.getElementById('statisticsTab').addEventListener('click', () => switchTab('statistics'));
                
                // Products search and filters
                document.getElementById('productSearch').addEventListener('input', debounce(function() {
                    currentFilters.products.search = this.value;
                    currentPage.products = 1;
                    loadProducts();
                }, 500));
                
                document.getElementById('productSort').addEventListener('change', function() {
                    currentFilters.products.sort = this.value;
                    currentPage.products = 1;
                    loadProducts();
                });
                
                document.getElementById('refreshProducts').addEventListener('click', loadProducts);
                
                // Installations search and filters
                document.getElementById('installationSearch').addEventListener('input', debounce(function() {
                    currentFilters.installations.search = this.value;
                    currentPage.installations = 1;
                    loadInstallations();
                }, 500));
                
                document.getElementById('installationStatus').addEventListener('change', function() {
                    currentFilters.installations.status = this.value;
                    currentPage.installations = 1;
                    loadInstallations();
                });
                
                document.getElementById('installationSort').addEventListener('change', function() {
                    currentFilters.installations.sort = this.value;
                    currentPage.installations = 1;
                    loadInstallations();
                });
                
                document.getElementById('refreshInstallations').addEventListener('click', loadInstallations);
                
                // Modal close
                document.getElementById('closeDetailsModal').addEventListener('click', closeDetailsModal);
                document.getElementById('detailsModal').addEventListener('click', function(e) {
                    if (e.target === this) closeDetailsModal();
                });
            });
            
            function initializeTabs() {
                const tabs = ['products', 'installations', 'statistics'];
                tabs.forEach(tab => {
                    const tabButton = document.getElementById(tab + 'Tab');
                    const tabContent = document.getElementById(tab + 'Content');
                    
                    if (tab === 'products') {
                        tabButton.classList.add('border-blue-500', 'text-blue-600');
                        tabButton.classList.remove('border-transparent', 'text-gray-500');
                        tabContent.classList.remove('hidden');
                    } else {
                        tabButton.classList.add('border-transparent', 'text-gray-500');
                        tabButton.classList.remove('border-blue-500', 'text-blue-600');
                        tabContent.classList.add('hidden');
                    }
                });
            }
            
            function switchTab(tab) {
                currentTab = tab;
                
                // Update tab buttons
                const tabs = ['products', 'installations', 'statistics'];
                tabs.forEach(t => {
                    const tabButton = document.getElementById(t + 'Tab');
                    const tabContent = document.getElementById(t + 'Content');
                    
                    if (t === tab) {
                        tabButton.classList.add('border-blue-500', 'text-blue-600');
                        tabButton.classList.remove('border-transparent', 'text-gray-500');
                        tabContent.classList.remove('hidden');
                    } else {
                        tabButton.classList.add('border-transparent', 'text-gray-500');
                        tabButton.classList.remove('border-blue-500', 'text-blue-600');
                        tabContent.classList.add('hidden');
                    }
                });
                
                loadTabContent(tab);
            }
            
            function loadTabContent(tab) {
                switch (tab) {
                    case 'products':
                        loadProducts();
                        break;
                    case 'installations':
                        loadInstallations();
                        break;
                    case 'statistics':
                        loadStatistics();
                        break;
                }
            }
            
            async function loadProducts() {
                try {
                    showLoading();
                    
                    const params = new URLSearchParams({
                        page: currentPage.products,
                        limit: 25,
                        search: currentFilters.products.search,
                        sort: currentFilters.products.sort,
                        order: currentFilters.products.order
                    });
                    
                    const response = await fetch('/admin/api/freemius/products?' + params);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateProductsTable(data.data, data.pagination);
                        }
                    }
                } catch (error) {
                    console.error('Error loading products:', error);
                } finally {
                    hideLoading();
                }
            }
            
            async function loadInstallations() {
                try {
                    showLoading();
                    
                    const params = new URLSearchParams({
                        page: currentPage.installations,
                        limit: 25,
                        search: currentFilters.installations.search,
                        status: currentFilters.installations.status,
                        sort: currentFilters.installations.sort,
                        order: currentFilters.installations.order
                    });
                    
                    const response = await fetch('/admin/api/freemius/installations?' + params);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateInstallationsTable(data.data, data.pagination);
                        }
                    }
                } catch (error) {
                    console.error('Error loading installations:', error);
                } finally {
                    hideLoading();
                }
            }
            
            async function loadStatistics() {
                try {
                    const response = await fetch('/admin/api/freemius/statistics');
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateStatistics(data.data);
                        }
                    }
                } catch (error) {
                    console.error('Error loading statistics:', error);
                }
            }
            
            function updateProductsTable(products, pagination) {
                const tbody = document.getElementById('productsTable');
                
                if (products.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-12 text-center text-gray-500">No products found</td></tr>';
                    return;
                }
                
                let html = '';
                products.forEach(product => {
                    const statusBadge = product.is_released ? 
                        '<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Released</span>' :
                        '<span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Draft</span>';
                    
                    html += `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${product.title}</div>
                                <div class="text-sm text-gray-500">${product.slug}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.type}</td>
                            <td class="px-6 py-4 whitespace-nowrap">${statusBadge}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">${product.active_installs_count.toLocaleString()} active</div>
                                <div class="text-sm text-gray-500">${product.installs_count.toLocaleString()} total</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$${product.earnings}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewProductDetails('${product.id}')" class="text-blue-600 hover:text-blue-900 mr-3">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                ${${canManage} ? `
                                <button onclick="syncProduct('${product.id}')" class="text-green-600 hover:text-green-900">
                                    <i class="fas fa-sync-alt"></i> Sync
                                </button>
                                ` : ''}
                            </td>
                        </tr>
                    `;
                });
                
                tbody.innerHTML = html;
                updatePagination('products', pagination);
            }
            
            function updateInstallationsTable(installations, pagination) {
                const tbody = document.getElementById('installationsTable');
                
                if (installations.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-12 text-center text-gray-500">No installations found</td></tr>';
                    return;
                }
                
                let html = '';
                installations.forEach(installation => {
                    const statusBadge = getStatusBadge(installation.status);
                    
                    html += `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${installation.title || 'Untitled'}</div>
                                <div class="text-sm text-gray-500">${installation.url || 'No URL'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${installation.plugin_id}</td>
                            <td class="px-6 py-4 whitespace-nowrap">${statusBadge}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${installation.version}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${installation.last_seen_at ? formatRelativeTime(installation.last_seen_at) : 'Never'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewInstallationDetails('${installation.id}')" class="text-blue-600 hover:text-blue-900 mr-3">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                ${${canManage} ? `
                                <button onclick="syncInstallation('${installation.id}')" class="text-green-600 hover:text-green-900">
                                    <i class="fas fa-sync-alt"></i> Sync
                                </button>
                                ` : ''}
                            </td>
                        </tr>
                    `;
                });
                
                tbody.innerHTML = html;
                updatePagination('installations', pagination);
            }
            
            function updateStatistics(stats) {
                // Update summary cards
                document.getElementById('totalProducts').textContent = stats.products.total_products || 0;
                document.getElementById('totalInstallations').textContent = stats.installations.total_installations || 0;
                document.getElementById('premiumInstallations').textContent = stats.installations.premium_installations || 0;
                document.getElementById('activeTrials').textContent = stats.installations.active_trials || 0;
                
                // Update detailed stats
                const detailedStats = document.getElementById('detailedStats');
                detailedStats.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-3">Product Statistics</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Released Products:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${stats.products.released_products || 0}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Total Installs:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${(stats.products.total_installs || 0).toLocaleString()}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Active Installs:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${(stats.products.total_active_installs || 0).toLocaleString()}</dd>
                                </div>
                            </dl>
                        </div>
                        
                        <div>
                            <h4 class="font-medium text-gray-900 mb-3">Installation Statistics</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Active Installations:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${stats.installations.active_installations || 0}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Inactive Installations:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${(stats.installations.total_installations - stats.installations.active_installations) || 0}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Average Revenue:</dt>
                                    <dd class="text-sm font-medium text-gray-900">$${(stats.installations.avg_gross || 0).toFixed(2)}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                `;
            }
            
            function updatePagination(type, pagination) {
                const from = ((pagination.current_page - 1) * pagination.per_page) + 1;
                const to = Math.min(pagination.current_page * pagination.per_page, pagination.total);
                
                document.getElementById(type + 'From').textContent = from;
                document.getElementById(type + 'To').textContent = to;
                document.getElementById(type + 'Total').textContent = pagination.total;
                
                // Generate pagination buttons
                let buttonsHtml = '';
                
                // Previous button
                if (pagination.current_page > 1) {
                    buttonsHtml += `<button onclick="changePage('${type}', ${pagination.current_page - 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</button>`;
                }
                
                // Page numbers
                const startPage = Math.max(1, pagination.current_page - 2);
                const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
                
                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === pagination.current_page;
                    const activeClass = isActive ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50';
                    buttonsHtml += `<button onclick="changePage('${type}', ${i})" class="px-3 py-2 text-sm border border-gray-300 rounded-md ${activeClass}">${i}</button>`;
                }
                
                // Next button
                if (pagination.current_page < pagination.total_pages) {
                    buttonsHtml += `<button onclick="changePage('${type}', ${pagination.current_page + 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>`;
                }
                
                document.getElementById(type + 'PaginationButtons').innerHTML = buttonsHtml;
            }
            
            function changePage(type, page) {
                currentPage[type] = page;
                if (type === 'products') {
                    loadProducts();
                } else if (type === 'installations') {
                    loadInstallations();
                }
            }
            
            function getStatusBadge(status) {
                const badges = {
                    'active': '<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Active</span>',
                    'inactive': '<span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">Inactive</span>',
                    'premium': '<span class="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">Premium</span>',
                    'trial': '<span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Trial</span>',
                    'free': '<span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">Free</span>',
                    'uninstalled': '<span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Uninstalled</span>'
                };
                return badges[status] || badges['inactive'];
            }
            
            async function viewProductDetails(productId) {
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/freemius/product-details?id=' + encodeURIComponent(productId));
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            showDetailsModal('Product Details', generateProductDetailsHtml(data.data));
                        } else {
                            alert('Error: ' + data.error);
                        }
                    }
                } catch (error) {
                    console.error('Error loading product details:', error);
                    alert('Error loading product details');
                } finally {
                    hideLoading();
                }
            }
            
            async function viewInstallationDetails(installationId) {
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/freemius/installation-details?id=' + encodeURIComponent(installationId));
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            showDetailsModal('Installation Details', generateInstallationDetailsHtml(data.data));
                        } else {
                            alert('Error: ' + data.error);
                        }
                    }
                } catch (error) {
                    console.error('Error loading installation details:', error);
                    alert('Error loading installation details');
                } finally {
                    hideLoading();
                }
            }
            
            function showDetailsModal(title, content) {
                document.getElementById('modalTitle').textContent = title;
                document.getElementById('detailsContent').innerHTML = content;
                document.getElementById('detailsModal').classList.remove('hidden');
            }
            
            function closeDetailsModal() {
                document.getElementById('detailsModal').classList.add('hidden');
            }
            
            function generateProductDetailsHtml(data) {
                const product = data.product;
                
                return `
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Basic Information</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">ID:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${product.id}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Title:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${product.title}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Slug:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${product.slug}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Type:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${product.type}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Environment:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${product.environment}</dd>
                                </div>
                            </dl>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Statistics</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Total Installs:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${product.installs_count.toLocaleString()}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Active Installs:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${product.active_installs_count.toLocaleString()}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Total Purchases:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${product.total_purchases}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Total Subscriptions:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${product.total_subscriptions}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Earnings:</dt>
                                    <dd class="text-sm font-medium text-gray-900">$${product.earnings}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                    
                    <div class="mt-6 bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-3">Settings</h4>
                        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                            <div>
                                <dt class="text-sm text-gray-600">Released:</dt>
                                <dd class="text-sm font-medium ${product.is_released ? 'text-green-600' : 'text-red-600'}">${product.is_released ? 'Yes' : 'No'}</dd>
                            </div>
                            <div>
                                <dt class="text-sm text-gray-600">SDK Required:</dt>
                                <dd class="text-sm font-medium ${product.is_sdk_required ? 'text-green-600' : 'text-red-600'}">${product.is_sdk_required ? 'Yes' : 'No'}</dd>
                            </div>
                            <div>
                                <dt class="text-sm text-gray-600">Pricing Visible:</dt>
                                <dd class="text-sm font-medium ${product.is_pricing_visible ? 'text-green-600' : 'text-red-600'}">${product.is_pricing_visible ? 'Yes' : 'No'}</dd>
                            </div>
                            <div>
                                <dt class="text-sm text-gray-600">WP.org Compliant:</dt>
                                <dd class="text-sm font-medium ${product.is_wp_org_compliant ? 'text-green-600' : 'text-red-600'}">${product.is_wp_org_compliant ? 'Yes' : 'No'}</dd>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            function generateInstallationDetailsHtml(data) {
                const installation = data.installation;
                
                return `
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Site Information</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Installation ID:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${installation.id}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Site URL:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${installation.url || 'N/A'}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Title:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${installation.title || 'N/A'}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Version:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${installation.version}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Status:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${installation.status}</dd>
                                </div>
                            </dl>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Subscription Details</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Plan ID:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${installation.plan_id || 'N/A'}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">License ID:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${installation.license_id || 'N/A'}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Gross Revenue:</dt>
                                    <dd class="text-sm font-medium text-gray-900">$${installation.gross.toFixed(2)}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Country:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${installation.country_code || 'N/A'}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Language:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${installation.language || 'N/A'}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                    
                    <div class="mt-6 bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-3">Status Flags</h4>
                        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                            <div>
                                <dt class="text-sm text-gray-600">Active:</dt>
                                <dd class="text-sm font-medium ${installation.is_active ? 'text-green-600' : 'text-red-600'}">${installation.is_active ? 'Yes' : 'No'}</dd>
                            </div>
                            <div>
                                <dt class="text-sm text-gray-600">Premium:</dt>
                                <dd class="text-sm font-medium ${installation.is_premium ? 'text-green-600' : 'text-red-600'}">${installation.is_premium ? 'Yes' : 'No'}</dd>
                            </div>
                            <div>
                                <dt class="text-sm text-gray-600">Uninstalled:</dt>
                                <dd class="text-sm font-medium ${installation.is_uninstalled ? 'text-red-600' : 'text-green-600'}">${installation.is_uninstalled ? 'Yes' : 'No'}</dd>
                            </div>
                            <div>
                                <dt class="text-sm text-gray-600">Disconnected:</dt>
                                <dd class="text-sm font-medium ${installation.is_disconnected ? 'text-red-600' : 'text-green-600'}">${installation.is_disconnected ? 'Yes' : 'No'}</dd>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            async function syncProduct(productId) {
                if (!confirm('Are you sure you want to sync product data from Freemius?')) {
                    return;
                }
                
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/freemius/sync-product', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ product_id: productId })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert('Product data synchronized successfully');
                        loadProducts();
                    } else {
                        alert('Error: ' + data.error);
                    }
                } catch (error) {
                    console.error('Error syncing product:', error);
                    alert('Error syncing product data');
                } finally {
                    hideLoading();
                }
            }
            
            async function syncInstallation(installationId) {
                if (!confirm('Are you sure you want to sync installation data from Freemius?')) {
                    return;
                }
                
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/freemius/sync-installation', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ installation_id: installationId })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert('Installation data synchronized successfully');
                        loadInstallations();
                    } else {
                        alert('Error: ' + data.error);
                    }
                } catch (error) {
                    console.error('Error syncing installation:', error);
                    alert('Error syncing installation data');
                } finally {
                    hideLoading();
                }
            }
            
            function formatRelativeTime(timestamp) {
                const date = new Date(timestamp);
                const now = new Date();
                const diff = now - date;
                
                const minutes = Math.floor(diff / 60000);
                const hours = Math.floor(diff / 3600000);
                const days = Math.floor(diff / 86400000);
                
                if (days > 0) return days + ' day' + (days > 1 ? 's' : '') + ' ago';
                if (hours > 0) return hours + ' hour' + (hours > 1 ? 's' : '') + ' ago';
                if (minutes > 0) return minutes + ' minute' + (minutes > 1 ? 's' : '') + ' ago';
                return 'Just now';
            }
            
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        </script>
HTML;
    }

    /**
     * Get Logs content
     *
     * @param array $data
     * @return string
     */
    private function getLogsContent(array $data): string
    {
        $canExport = $data['canExport'] ?? false;
        
        return <<<HTML
        <!-- Tab Navigation -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6">
                    <button id="systemLogsTab" class="py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                        System Logs
                    </button>
                    <button id="apiLogsTab" class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        API Logs
                    </button>
                    <button id="logStatsTab" class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        Statistics
                    </button>
                </nav>
            </div>
        </div>

        <!-- System Logs Tab Content -->
        <div id="systemLogsContent" class="tab-content">
            <!-- Search and Filters -->
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                            <input 
                                type="text" 
                                id="systemLogSearch" 
                                placeholder="Search logs..."
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                            <select id="systemLogType" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all">All Types</option>
                                <option value="api">API</option>
                                <option value="admin">Admin</option>
                                <option value="error">Error</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Level</label>
                            <select id="systemLogLevel" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all">All Levels</option>
                                <option value="info">Info</option>
                                <option value="warning">Warning</option>
                                <option value="error">Error</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                            <div class="flex space-x-2">
                                <input type="date" id="systemLogDateFrom" class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <input type="date" id="systemLogDateTo" class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between mt-4">
                        <div class="flex space-x-2">
                            <button id="refreshSystemLogs" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-sync-alt mr-2"></i>Refresh
                            </button>
                            <button id="clearSystemFilters" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                                <i class="fas fa-times mr-2"></i>Clear Filters
                            </button>
                        </div>
                        
                        ${canExport ? `
                        <div class="flex space-x-2">
                            <button id="exportSystemCsv" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                                <i class="fas fa-download mr-2"></i>Export CSV
                            </button>
                            <button id="exportSystemJson" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                                <i class="fas fa-download mr-2"></i>Export JSON
                            </button>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>

            <!-- System Logs Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">System Logs</h3>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP</th>
                            </tr>
                        </thead>
                        <tbody id="systemLogsTable" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                                    <p class="text-gray-500">Loading system logs...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div id="systemLogsPagination" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Showing <span id="systemLogsFrom">0</span> to <span id="systemLogsTo">0</span> of <span id="systemLogsTotal">0</span> results
                    </div>
                    <div id="systemLogsPaginationButtons" class="flex space-x-2">
                        <!-- Pagination buttons will be inserted here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- API Logs Tab Content -->
        <div id="apiLogsContent" class="tab-content hidden">
            <!-- Search and Filters -->
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                            <input 
                                type="text" 
                                id="apiLogSearch" 
                                placeholder="Search by IP, URL, or hash..."
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select id="apiLogStatus" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all">All Status</option>
                                <option value="success">Success (2xx)</option>
                                <option value="error">Error (4xx/5xx)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                            <div class="flex space-x-2">
                                <input type="date" id="apiLogDateFrom" class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <input type="date" id="apiLogDateTo" class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                            <select id="apiLogSort" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="created_at">Time</option>
                                <option value="response_time_ms">Response Time</option>
                                <option value="response_status">Status Code</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between mt-4">
                        <div class="flex space-x-2">
                            <button id="refreshApiLogs" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-sync-alt mr-2"></i>Refresh
                            </button>
                            <button id="clearApiFilters" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                                <i class="fas fa-times mr-2"></i>Clear Filters
                            </button>
                        </div>
                        
                        ${canExport ? `
                        <div class="flex space-x-2">
                            <button id="exportApiCsv" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                                <i class="fas fa-download mr-2"></i>Export CSV
                            </button>
                            <button id="exportApiJson" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                                <i class="fas fa-download mr-2"></i>Export JSON
                            </button>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>

            <!-- API Logs Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">API Request Logs</h3>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plugin/Install</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Response Time</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Freemius</th>
                            </tr>
                        </thead>
                        <tbody id="apiLogsTable" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                                    <p class="text-gray-500">Loading API logs...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div id="apiLogsPagination" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Showing <span id="apiLogsFrom">0</span> to <span id="apiLogsTo">0</span> of <span id="apiLogsTotal">0</span> results
                    </div>
                    <div id="apiLogsPaginationButtons" class="flex space-x-2">
                        <!-- Pagination buttons will be inserted here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Tab Content -->
        <div id="logStatsContent" class="tab-content hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- System Log Statistics -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-file-alt text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">System Logs</h3>
                            <p id="totalSystemLogs" class="text-sm text-gray-600">Loading...</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">System Errors</h3>
                            <p id="systemErrors" class="text-sm text-gray-600">Loading...</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-plug text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">API Requests</h3>
                            <p id="totalApiRequests" class="text-sm text-gray-600">Loading...</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-tachometer-alt text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Avg Response</h3>
                            <p id="avgResponseTime" class="text-sm text-gray-600">Loading...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Detailed Statistics</h3>
                <div id="detailedLogStats" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                    <p class="text-gray-500">Loading detailed statistics...</p>
                </div>
            </div>
        </div>

        <script>
            let currentLogTab = 'systemLogs';
            let currentLogPage = { systemLogs: 1, apiLogs: 1 };
            let currentLogFilters = {
                systemLogs: { search: '', type: 'all', level: 'all', date_from: '', date_to: '', sort: 'created_at', order: 'desc' },
                apiLogs: { search: '', status: 'all', date_from: '', date_to: '', sort: 'created_at', order: 'desc' }
            };
            
            document.addEventListener('DOMContentLoaded', function() {
                initializeLogTabs();
                loadLogTabContent('systemLogs');
                
                // Tab switching
                document.getElementById('systemLogsTab').addEventListener('click', () => switchLogTab('systemLogs'));
                document.getElementById('apiLogsTab').addEventListener('click', () => switchLogTab('apiLogs'));
                document.getElementById('logStatsTab').addEventListener('click', () => switchLogTab('logStats'));
                
                // System logs filters
                document.getElementById('systemLogSearch').addEventListener('input', debounce(function() {
                    currentLogFilters.systemLogs.search = this.value;
                    currentLogPage.systemLogs = 1;
                    loadSystemLogs();
                }, 500));
                
                document.getElementById('systemLogType').addEventListener('change', function() {
                    currentLogFilters.systemLogs.type = this.value;
                    currentLogPage.systemLogs = 1;
                    loadSystemLogs();
                });
                
                document.getElementById('systemLogLevel').addEventListener('change', function() {
                    currentLogFilters.systemLogs.level = this.value;
                    currentLogPage.systemLogs = 1;
                    loadSystemLogs();
                });
                
                document.getElementById('systemLogDateFrom').addEventListener('change', function() {
                    currentLogFilters.systemLogs.date_from = this.value;
                    currentLogPage.systemLogs = 1;
                    loadSystemLogs();
                });
                
                document.getElementById('systemLogDateTo').addEventListener('change', function() {
                    currentLogFilters.systemLogs.date_to = this.value;
                    currentLogPage.systemLogs = 1;
                    loadSystemLogs();
                });
                
                document.getElementById('refreshSystemLogs').addEventListener('click', loadSystemLogs);
                document.getElementById('clearSystemFilters').addEventListener('click', clearSystemFilters);
                
                // API logs filters
                document.getElementById('apiLogSearch').addEventListener('input', debounce(function() {
                    currentLogFilters.apiLogs.search = this.value;
                    currentLogPage.apiLogs = 1;
                    loadApiLogs();
                }, 500));
                
                document.getElementById('apiLogStatus').addEventListener('change', function() {
                    currentLogFilters.apiLogs.status = this.value;
                    currentLogPage.apiLogs = 1;
                    loadApiLogs();
                });
                
                document.getElementById('apiLogDateFrom').addEventListener('change', function() {
                    currentLogFilters.apiLogs.date_from = this.value;
                    currentLogPage.apiLogs = 1;
                    loadApiLogs();
                });
                
                document.getElementById('apiLogDateTo').addEventListener('change', function() {
                    currentLogFilters.apiLogs.date_to = this.value;
                    currentLogPage.apiLogs = 1;
                    loadApiLogs();
                });
                
                document.getElementById('apiLogSort').addEventListener('change', function() {
                    currentLogFilters.apiLogs.sort = this.value;
                    currentLogPage.apiLogs = 1;
                    loadApiLogs();
                });
                
                document.getElementById('refreshApiLogs').addEventListener('click', loadApiLogs);
                document.getElementById('clearApiFilters').addEventListener('click', clearApiFilters);
                
                // Export buttons
                ${canExport ? `
                document.getElementById('exportSystemCsv')?.addEventListener('click', () => exportLogs('system', 'csv'));
                document.getElementById('exportSystemJson')?.addEventListener('click', () => exportLogs('system', 'json'));
                document.getElementById('exportApiCsv')?.addEventListener('click', () => exportLogs('api', 'csv'));
                document.getElementById('exportApiJson')?.addEventListener('click', () => exportLogs('api', 'json'));
                ` : ''}
            });
            
            function initializeLogTabs() {
                const tabs = ['systemLogs', 'apiLogs', 'logStats'];
                tabs.forEach(tab => {
                    const tabButton = document.getElementById(tab + 'Tab');
                    const tabContent = document.getElementById(tab + 'Content');
                    
                    if (tab === 'systemLogs') {
                        tabButton.classList.add('border-blue-500', 'text-blue-600');
                        tabButton.classList.remove('border-transparent', 'text-gray-500');
                        tabContent.classList.remove('hidden');
                    } else {
                        tabButton.classList.add('border-transparent', 'text-gray-500');
                        tabButton.classList.remove('border-blue-500', 'text-blue-600');
                        tabContent.classList.add('hidden');
                    }
                });
            }
            
            function switchLogTab(tab) {
                currentLogTab = tab;
                
                // Update tab buttons
                const tabs = ['systemLogs', 'apiLogs', 'logStats'];
                tabs.forEach(t => {
                    const tabButton = document.getElementById(t + 'Tab');
                    const tabContent = document.getElementById(t + 'Content');
                    
                    if (t === tab) {
                        tabButton.classList.add('border-blue-500', 'text-blue-600');
                        tabButton.classList.remove('border-transparent', 'text-gray-500');
                        tabContent.classList.remove('hidden');
                    } else {
                        tabButton.classList.add('border-transparent', 'text-gray-500');
                        tabButton.classList.remove('border-blue-500', 'text-blue-600');
                        tabContent.classList.add('hidden');
                    }
                });
                
                loadLogTabContent(tab);
            }
            
            function loadLogTabContent(tab) {
                switch (tab) {
                    case 'systemLogs':
                        loadSystemLogs();
                        break;
                    case 'apiLogs':
                        loadApiLogs();
                        break;
                    case 'logStats':
                        loadLogStatistics();
                        break;
                }
            }
            
            async function loadSystemLogs() {
                try {
                    showLoading();
                    
                    const params = new URLSearchParams({
                        page: currentLogPage.systemLogs,
                        limit: 50,
                        ...currentLogFilters.systemLogs
                    });
                    
                    const response = await fetch('/admin/api/logs/system?' + params);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateSystemLogsTable(data.data, data.pagination);
                        }
                    }
                } catch (error) {
                    console.error('Error loading system logs:', error);
                } finally {
                    hideLoading();
                }
            }
            
            async function loadApiLogs() {
                try {
                    showLoading();
                    
                    const params = new URLSearchParams({
                        page: currentLogPage.apiLogs,
                        limit: 50,
                        ...currentLogFilters.apiLogs
                    });
                    
                    const response = await fetch('/admin/api/logs/api?' + params);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateApiLogsTable(data.data, data.pagination);
                        }
                    }
                } catch (error) {
                    console.error('Error loading API logs:', error);
                } finally {
                    hideLoading();
                }
            }
            
            async function loadLogStatistics() {
                try {
                    const response = await fetch('/admin/api/logs/statistics');
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateLogStatistics(data.data);
                        }
                    }
                } catch (error) {
                    console.error('Error loading log statistics:', error);
                }
            }
            
            function updateSystemLogsTable(logs, pagination) {
                const tbody = document.getElementById('systemLogsTable');
                
                if (logs.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-12 text-center text-gray-500">No system logs found</td></tr>';
                    return;
                }
                
                let html = '';
                logs.forEach(log => {
                    const levelClass = getLevelClass(log.level);
                    const typeClass = getTypeClass(log.type);
                    
                    html += `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${log.formatted_time}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium ${typeClass} rounded-full">${log.type}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium ${levelClass} rounded-full">${log.level}</span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 max-w-md truncate" title="${log.message}">
                                ${log.message}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${log.user_email || 'System'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${log.ip || 'N/A'}
                            </td>
                        </tr>
                    `;
                });
                
                tbody.innerHTML = html;
                updateLogPagination('systemLogs', pagination);
            }
            
            function updateApiLogsTable(logs, pagination) {
                const tbody = document.getElementById('apiLogsTable');
                
                if (logs.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-12 text-center text-gray-500">No API logs found</td></tr>';
                    return;
                }
                
                let html = '';
                logs.forEach(log => {
                    html += `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${log.formatted_time}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${log.ip}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${log.plugin_id}/${log.install_id}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium ${log.status_class} rounded-full">${log.response_status}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${log.response_time_ms}ms
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium ${log.freemius_valid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} rounded-full">
                                    ${log.freemius_valid ? 'Valid' : 'Invalid'}
                                </span>
                            </td>
                        </tr>
                    `;
                });
                
                tbody.innerHTML = html;
                updateLogPagination('apiLogs', pagination);
            }
            
            function updateLogStatistics(stats) {
                // Update summary cards
                document.getElementById('totalSystemLogs').textContent = stats.system_logs.total.toLocaleString();
                document.getElementById('systemErrors').textContent = stats.system_logs.errors.toLocaleString();
                document.getElementById('totalApiRequests').textContent = stats.api_logs.total.toLocaleString();
                document.getElementById('avgResponseTime').textContent = stats.api_logs.avg_response_time + 'ms';
                
                // Update detailed stats
                const detailedStats = document.getElementById('detailedLogStats');
                detailedStats.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-3">System Logs (Last 24h)</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Total Logs:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${stats.system_logs.last_24h.toLocaleString()}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Errors:</dt>
                                    <dd class="text-sm font-medium text-red-600">${stats.system_logs.errors.toLocaleString()}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Warnings:</dt>
                                    <dd class="text-sm font-medium text-yellow-600">${stats.system_logs.warnings.toLocaleString()}</dd>
                                </div>
                            </dl>
                        </div>
                        
                        <div>
                            <h4 class="font-medium text-gray-900 mb-3">API Requests (Last 24h)</h4>
                            <dl class="space-y-2">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Total Requests:</dt>
                                    <dd class="text-sm font-medium text-gray-900">${stats.api_logs.last_24h.toLocaleString()}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Successful:</dt>
                                    <dd class="text-sm font-medium text-green-600">${stats.api_logs.success.toLocaleString()}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Errors:</dt>
                                    <dd class="text-sm font-medium text-red-600">${stats.api_logs.errors.toLocaleString()}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                `;
            }
            
            function updateLogPagination(type, pagination) {
                const from = ((pagination.current_page - 1) * pagination.per_page) + 1;
                const to = Math.min(pagination.current_page * pagination.per_page, pagination.total);
                
                document.getElementById(type + 'From').textContent = from;
                document.getElementById(type + 'To').textContent = to;
                document.getElementById(type + 'Total').textContent = pagination.total;
                
                // Generate pagination buttons
                let buttonsHtml = '';
                
                // Previous button
                if (pagination.current_page > 1) {
                    buttonsHtml += `<button onclick="changeLogPage('${type}', ${pagination.current_page - 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</button>`;
                }
                
                // Page numbers
                const startPage = Math.max(1, pagination.current_page - 2);
                const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
                
                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === pagination.current_page;
                    const activeClass = isActive ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50';
                    buttonsHtml += `<button onclick="changeLogPage('${type}', ${i})" class="px-3 py-2 text-sm border border-gray-300 rounded-md ${activeClass}">${i}</button>`;
                }
                
                // Next button
                if (pagination.current_page < pagination.total_pages) {
                    buttonsHtml += `<button onclick="changeLogPage('${type}', ${pagination.current_page + 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>`;
                }
                
                document.getElementById(type + 'PaginationButtons').innerHTML = buttonsHtml;
            }
            
            function changeLogPage(type, page) {
                currentLogPage[type] = page;
                if (type === 'systemLogs') {
                    loadSystemLogs();
                } else if (type === 'apiLogs') {
                    loadApiLogs();
                }
            }
            
            function clearSystemFilters() {
                document.getElementById('systemLogSearch').value = '';
                document.getElementById('systemLogType').value = 'all';
                document.getElementById('systemLogLevel').value = 'all';
                document.getElementById('systemLogDateFrom').value = '';
                document.getElementById('systemLogDateTo').value = '';
                
                currentLogFilters.systemLogs = { search: '', type: 'all', level: 'all', date_from: '', date_to: '', sort: 'created_at', order: 'desc' };
                currentLogPage.systemLogs = 1;
                loadSystemLogs();
            }
            
            function clearApiFilters() {
                document.getElementById('apiLogSearch').value = '';
                document.getElementById('apiLogStatus').value = 'all';
                document.getElementById('apiLogDateFrom').value = '';
                document.getElementById('apiLogDateTo').value = '';
                document.getElementById('apiLogSort').value = 'created_at';
                
                currentLogFilters.apiLogs = { search: '', status: 'all', date_from: '', date_to: '', sort: 'created_at', order: 'desc' };
                currentLogPage.apiLogs = 1;
                loadApiLogs();
            }
            
            function getLevelClass(level) {
                const classes = {
                    'info': 'bg-blue-100 text-blue-800',
                    'warning': 'bg-yellow-100 text-yellow-800',
                    'error': 'bg-red-100 text-red-800'
                };
                return classes[level] || 'bg-gray-100 text-gray-800';
            }
            
            function getTypeClass(type) {
                const classes = {
                    'api': 'bg-green-100 text-green-800',
                    'admin': 'bg-purple-100 text-purple-800',
                    'error': 'bg-red-100 text-red-800'
                };
                return classes[type] || 'bg-gray-100 text-gray-800';
            }
            
            async function exportLogs(type, format) {
                const dateFrom = type === 'system' ? 
                    document.getElementById('systemLogDateFrom').value : 
                    document.getElementById('apiLogDateFrom').value;
                const dateTo = type === 'system' ? 
                    document.getElementById('systemLogDateTo').value : 
                    document.getElementById('apiLogDateTo').value;
                
                const params = new URLSearchParams({
                    type: type,
                    format: format,
                    limit: 5000
                });
                
                if (dateFrom) params.append('date_from', dateFrom);
                if (dateTo) params.append('date_to', dateTo);
                
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/logs/export?' + params);
                    if (response.ok) {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `${type}_logs_${new Date().toISOString().split('T')[0]}.${format}`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                    } else {
                        alert('Export failed. Please try again.');
                    }
                } catch (error) {
                    console.error('Export error:', error);
                    alert('Export failed. Please try again.');
                } finally {
                    hideLoading();
                }
            }
            
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        </script>
HTML;
    }

    /**
     * Get Users content placeholder
     *
     * @param array $data
     * @return string
     */
    private function getUsersContent(array $data): string
    {
        return <<<HTML
        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div class="flex-1 max-w-lg">
                        <div class="relative">
                            <input 
                                type="text" 
                                id="userSearch" 
                                placeholder="Search users by email..."
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <select id="roleFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">All Roles</option>
                            <option value="super_admin">Super Admin</option>
                            <option value="dev">Developer</option>
                            <option value="marketing">Marketing</option>
                            <option value="sales">Sales</option>
                        </select>
                        
                        <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                        
                        <button id="refreshUsers" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                            <i class="fas fa-sync-alt mr-2"></i>Refresh
                        </button>
                        
                        <button id="createUserBtn" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>Create User
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-users text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Total Users</h3>
                        <p id="totalUsers" class="text-sm text-gray-600">Loading...</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Active Users</h3>
                        <p id="activeUsers" class="text-sm text-gray-600">Loading...</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-crown text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Super Admins</h3>
                        <p id="superAdmins" class="text-sm text-gray-600">Loading...</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-clock text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Active (30d)</h3>
                        <p id="activeLast30Days" class="text-sm text-gray-600">Loading...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Admin Users</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="usersTable" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                                <p class="text-gray-500">Loading users...</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div id="usersPagination" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing <span id="usersFrom">0</span> to <span id="usersTo">0</span> of <span id="usersTotal">0</span> results
                </div>
                <div id="usersPaginationButtons" class="flex space-x-2">
                    <!-- Pagination buttons will be inserted here -->
                </div>
            </div>
        </div>

        <!-- Create/Edit User Modal -->
        <div id="userModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 id="userModalTitle" class="text-lg font-medium text-gray-900">Create User</h3>
                    <button id="closeUserModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="userForm" class="p-6">
                    <input type="hidden" id="userId" name="id">
                    
                    <div class="space-y-4">
                        <div>
                            <label for="userEmail" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                            <input 
                                type="email" 
                                id="userEmail" 
                                name="email" 
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="<EMAIL>"
                            >
                            <div id="emailError" class="hidden text-sm text-red-600 mt-1"></div>
                        </div>
                        
                        <div>
                            <label for="userRole" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                            <select 
                                id="userRole" 
                                name="role" 
                                required
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">Select Role</option>
                                <option value="super_admin">Super Admin</option>
                                <option value="dev">Developer</option>
                                <option value="marketing">Marketing</option>
                                <option value="sales">Sales</option>
                            </select>
                            <div id="roleError" class="hidden text-sm text-red-600 mt-1"></div>
                        </div>
                        
                        <div>
                            <label for="userPassword" class="block text-sm font-medium text-gray-700 mb-1">
                                Password <span id="passwordOptional" class="hidden text-gray-500">(leave blank to keep current)</span>
                            </label>
                            <input 
                                type="password" 
                                id="userPassword" 
                                name="password"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Minimum 12 characters"
                            >
                            <div id="passwordError" class="hidden text-sm text-red-600 mt-1"></div>
                            <p class="text-xs text-gray-500 mt-1">Password must be at least 12 characters long</p>
                        </div>
                        
                        <div id="statusField" class="hidden">
                            <label class="flex items-center">
                                <input type="checkbox" id="userActive" name="is_active" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Active</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" id="cancelUserBtn" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                            Cancel
                        </button>
                        <button type="submit" id="saveUserBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                            <span id="saveUserText">Create User</span>
                            <span id="saveUserSpinner" class="hidden ml-2">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <script>
            let currentUserPage = 1;
            let currentUserFilters = {
                search: '',
                role: 'all',
                status: 'all',
                sort: 'created_at',
                order: 'desc'
            };
            let isEditMode = false;
            
            document.addEventListener('DOMContentLoaded', function() {
                loadUserStatistics();
                loadUsers();
                
                // Search and filters
                document.getElementById('userSearch').addEventListener('input', debounce(function() {
                    currentUserFilters.search = this.value;
                    currentUserPage = 1;
                    loadUsers();
                }, 500));
                
                document.getElementById('roleFilter').addEventListener('change', function() {
                    currentUserFilters.role = this.value;
                    currentUserPage = 1;
                    loadUsers();
                });
                
                document.getElementById('statusFilter').addEventListener('change', function() {
                    currentUserFilters.status = this.value;
                    currentUserPage = 1;
                    loadUsers();
                });
                
                document.getElementById('refreshUsers').addEventListener('click', function() {
                    loadUserStatistics();
                    loadUsers();
                });
                
                // Modal functionality
                document.getElementById('createUserBtn').addEventListener('click', openCreateUserModal);
                document.getElementById('closeUserModal').addEventListener('click', closeUserModal);
                document.getElementById('cancelUserBtn').addEventListener('click', closeUserModal);
                document.getElementById('userModal').addEventListener('click', function(e) {
                    if (e.target === this) closeUserModal();
                });
                
                // Form submission
                document.getElementById('userForm').addEventListener('submit', handleUserFormSubmit);
            });
            
            async function loadUserStatistics() {
                try {
                    const response = await fetch('/admin/api/users/statistics');
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateUserStatistics(data.data);
                        }
                    }
                } catch (error) {
                    console.error('Error loading user statistics:', error);
                }
            }
            
            async function loadUsers() {
                try {
                    showLoading();
                    
                    const params = new URLSearchParams({
                        page: currentUserPage,
                        limit: 25,
                        ...currentUserFilters
                    });
                    
                    const response = await fetch('/admin/api/users?' + params);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            updateUsersTable(data.data, data.pagination);
                        }
                    }
                } catch (error) {
                    console.error('Error loading users:', error);
                } finally {
                    hideLoading();
                }
            }
            
            function updateUserStatistics(stats) {
                document.getElementById('totalUsers').textContent = stats.total_users.toLocaleString();
                document.getElementById('activeUsers').textContent = stats.active_users.toLocaleString();
                document.getElementById('superAdmins').textContent = stats.roles.super_admin.toLocaleString();
                document.getElementById('activeLast30Days').textContent = stats.active_last_30_days.toLocaleString();
            }
            
            function updateUsersTable(users, pagination) {
                const tbody = document.getElementById('usersTable');
                
                if (users.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-12 text-center text-gray-500">No users found</td></tr>';
                    return;
                }
                
                let html = '';
                users.forEach(user => {
                    const statusBadge = user.is_active ? 
                        '<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Active</span>' :
                        '<span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Inactive</span>';
                    
                    const roleBadge = getRoleBadge(user.role);
                    
                    html += `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-700">${user.email.charAt(0).toUpperCase()}</span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">${user.email}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">${roleBadge}</td>
                            <td class="px-6 py-4 whitespace-nowrap">${statusBadge}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.formatted_created}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.formatted_last_login}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="editUser('${user.id}')" class="text-blue-600 hover:text-blue-900 mr-3">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button onclick="deleteUser('${user.id}', '${user.email}')" class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </td>
                        </tr>
                    `;
                });
                
                tbody.innerHTML = html;
                updateUserPagination(pagination);
            }
            
            function updateUserPagination(pagination) {
                const from = ((pagination.current_page - 1) * pagination.per_page) + 1;
                const to = Math.min(pagination.current_page * pagination.per_page, pagination.total);
                
                document.getElementById('usersFrom').textContent = from;
                document.getElementById('usersTo').textContent = to;
                document.getElementById('usersTotal').textContent = pagination.total;
                
                // Generate pagination buttons
                let buttonsHtml = '';
                
                // Previous button
                if (pagination.current_page > 1) {
                    buttonsHtml += `<button onclick="changeUserPage(${pagination.current_page - 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</button>`;
                }
                
                // Page numbers
                const startPage = Math.max(1, pagination.current_page - 2);
                const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
                
                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === pagination.current_page;
                    const activeClass = isActive ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50';
                    buttonsHtml += `<button onclick="changeUserPage(${i})" class="px-3 py-2 text-sm border border-gray-300 rounded-md ${activeClass}">${i}</button>`;
                }
                
                // Next button
                if (pagination.current_page < pagination.total_pages) {
                    buttonsHtml += `<button onclick="changeUserPage(${pagination.current_page + 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>`;
                }
                
                document.getElementById('usersPaginationButtons').innerHTML = buttonsHtml;
            }
            
            function changeUserPage(page) {
                currentUserPage = page;
                loadUsers();
            }
            
            function getRoleBadge(role) {
                const badges = {
                    'super_admin': '<span class="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">Super Admin</span>',
                    'dev': '<span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">Developer</span>',
                    'marketing': '<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Marketing</span>',
                    'sales': '<span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Sales</span>'
                };
                return badges[role] || '<span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">' + role + '</span>';
            }
            
            function openCreateUserModal() {
                isEditMode = false;
                document.getElementById('userModalTitle').textContent = 'Create User';
                document.getElementById('saveUserText').textContent = 'Create User';
                document.getElementById('passwordOptional').classList.add('hidden');
                document.getElementById('statusField').classList.add('hidden');
                document.getElementById('userPassword').required = true;
                
                // Reset form
                document.getElementById('userForm').reset();
                clearFormErrors();
                
                document.getElementById('userModal').classList.remove('hidden');
            }
            
            async function editUser(userId) {
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/users/details?id=' + encodeURIComponent(userId));
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            openEditUserModal(data.data.user);
                        } else {
                            alert('Error: ' + data.error);
                        }
                    }
                } catch (error) {
                    console.error('Error loading user details:', error);
                    alert('Error loading user details');
                } finally {
                    hideLoading();
                }
            }
            
            function openEditUserModal(user) {
                isEditMode = true;
                document.getElementById('userModalTitle').textContent = 'Edit User';
                document.getElementById('saveUserText').textContent = 'Update User';
                document.getElementById('passwordOptional').classList.remove('hidden');
                document.getElementById('statusField').classList.remove('hidden');
                document.getElementById('userPassword').required = false;
                
                // Populate form
                document.getElementById('userId').value = user.id;
                document.getElementById('userEmail').value = user.email;
                document.getElementById('userRole').value = user.role;
                document.getElementById('userActive').checked = user.is_active;
                document.getElementById('userPassword').value = '';
                
                clearFormErrors();
                
                document.getElementById('userModal').classList.remove('hidden');
            }
            
            function closeUserModal() {
                document.getElementById('userModal').classList.add('hidden');
                document.getElementById('userForm').reset();
                clearFormErrors();
            }
            
            async function handleUserFormSubmit(e) {
                e.preventDefault();
                
                const formData = new FormData(e.target);
                const userData = {
                    email: formData.get('email'),
                    role: formData.get('role'),
                    password: formData.get('password')
                };
                
                if (isEditMode) {
                    userData.id = formData.get('id');
                    userData.is_active = formData.get('is_active') === 'on';
                }
                
                try {
                    const saveBtn = document.getElementById('saveUserBtn');
                    const saveText = document.getElementById('saveUserText');
                    const saveSpinner = document.getElementById('saveUserSpinner');
                    
                    saveBtn.disabled = true;
                    saveSpinner.classList.remove('hidden');
                    
                    const url = isEditMode ? '/admin/api/users/update' : '/admin/api/users/create';
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(userData)
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert(data.message);
                        closeUserModal();
                        loadUsers();
                        loadUserStatistics();
                    } else {
                        if (data.errors) {
                            showFormErrors(data.errors);
                        } else {
                            alert('Error: ' + data.error);
                        }
                    }
                } catch (error) {
                    console.error('Error saving user:', error);
                    alert('Error saving user');
                } finally {
                    const saveBtn = document.getElementById('saveUserBtn');
                    const saveSpinner = document.getElementById('saveUserSpinner');
                    
                    saveBtn.disabled = false;
                    saveSpinner.classList.add('hidden');
                }
            }
            
            async function deleteUser(userId, userEmail) {
                if (!confirm(`Are you sure you want to delete user "${userEmail}"? This action cannot be undone.`)) {
                    return;
                }
                
                try {
                    showLoading();
                    
                    const response = await fetch('/admin/api/users/delete', {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ id: userId })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert(data.message);
                        loadUsers();
                        loadUserStatistics();
                    } else {
                        alert('Error: ' + data.error);
                    }
                } catch (error) {
                    console.error('Error deleting user:', error);
                    alert('Error deleting user');
                } finally {
                    hideLoading();
                }
            }
            
            function showFormErrors(errors) {
                clearFormErrors();
                
                Object.keys(errors).forEach(field => {
                    const errorElement = document.getElementById(field + 'Error');
                    if (errorElement) {
                        errorElement.textContent = errors[field];
                        errorElement.classList.remove('hidden');
                    }
                });
            }
            
            function clearFormErrors() {
                const errorElements = document.querySelectorAll('[id$="Error"]');
                errorElements.forEach(element => {
                    element.textContent = '';
                    element.classList.add('hidden');
                });
            }
            
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        </script>
HTML;
    }

    /**
     * Get Settings content placeholder
     *
     * @param array $data
     * @return string
     */
    private function getSettingsContent(array $data): string
    {
        return '<div class="text-center py-12"><h3 class="text-lg font-medium text-gray-900">Settings interface will be implemented in task 9.1</h3></div>';
    }

    /**
     * Get access denied HTML
     *
     * @return string
     */
    private function getAccessDeniedHtml(): string
    {
        return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Denied - GuardGeo Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full text-center">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-lock text-red-600 text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
            <p class="text-gray-600 mb-6">You don't have permission to access this page.</p>
            <a href="/admin/dashboard" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>
</body>
</html>
HTML;
    }

    /**
     * Get admin layout JavaScript
     *
     * @return string
     */
    private function getAdminLayoutScript(): string
    {
        return <<<JS
        // Mobile menu functionality
        const sidebar = document.getElementById('sidebar');
        const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
        const openSidebarBtn = document.getElementById('openSidebar');
        const closeSidebarBtn = document.getElementById('closeSidebar');
        
        function openMobileMenu() {
            sidebar.classList.remove('-translate-x-full');
            mobileMenuOverlay.classList.remove('hidden');
        }
        
        function closeMobileMenu() {
            sidebar.classList.add('-translate-x-full');
            mobileMenuOverlay.classList.add('hidden');
        }
        
        openSidebarBtn?.addEventListener('click', openMobileMenu);
        closeSidebarBtn?.addEventListener('click', closeMobileMenu);
        mobileMenuOverlay?.addEventListener('click', closeMobileMenu);
        
        // User menu functionality
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userMenu = document.getElementById('userMenu');
        
        userMenuBtn?.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });
        
        // Close user menu when clicking outside
        document.addEventListener('click', function() {
            userMenu?.classList.add('hidden');
        });
        
        // Logout functionality
        const logoutBtns = [document.getElementById('logoutBtn'), document.getElementById('logoutBtnTop')];
        
        logoutBtns.forEach(btn => {
            btn?.addEventListener('click', async function(e) {
                e.preventDefault();
                
                if (confirm('Are you sure you want to logout?')) {
                    try {
                        const response = await fetch('/admin/logout', {
                            method: 'POST',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            window.location.href = result.redirect || '/admin/login';
                        } else {
                            alert('Logout failed. Please try again.');
                        }
                    } catch (error) {
                        console.error('Logout error:', error);
                        // Fallback to direct navigation
                        window.location.href = '/admin/logout';
                    }
                }
            });
        });
        
        // Loading overlay functions
        window.showLoading = function() {
            document.getElementById('loadingOverlay')?.classList.remove('hidden');
        };
        
        window.hideLoading = function() {
            document.getElementById('loadingOverlay')?.classList.add('hidden');
        };
        
        // Auto-hide loading on page load
        window.addEventListener('load', function() {
            hideLoading();
        });
JS;
    }
}