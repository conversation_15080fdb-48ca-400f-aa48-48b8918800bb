<?php

namespace Skpassegna\GuardgeoApi\Config;

/**
 * Environment Configuration Manager
 * 
 * Handles loading and accessing environment variables and configuration
 * for different deployment stages (development, staging, production).
 */
class Environment
{
    private static array $config = [];
    private static bool $loaded = false;
    
    /**
     * Load environment configuration
     */
    public static function load(): void
    {
        if (self::$loaded) {
            return;
        }
        
        // Load from environment variables first
        self::$config = $_ENV;
        
        // Load from .env file if it exists
        $envFile = __DIR__ . '/../../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos(trim($line), '#') === 0) {
                    continue; // Skip comments
                }
                
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $key = trim($key);
                    $value = trim($value);
                    
                    // Remove quotes if present
                    if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
                        (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
                        $value = substr($value, 1, -1);
                    }
                    
                    self::$config[$key] = $value;
                }
            }
        }
        
        self::$loaded = true;
    }
    
    /**
     * Get environment variable value
     */
    public static function get(string $key, $default = null)
    {
        self::load();
        
        return self::$config[$key] ?? $default;
    }
    
    /**
     * Set environment variable value
     */
    public static function set(string $key, $value): void
    {
        self::load();
        self::$config[$key] = $value;
    }
    
    /**
     * Check if environment variable exists
     */
    public static function has(string $key): bool
    {
        self::load();
        
        return isset(self::$config[$key]);
    }
    
    /**
     * Get current environment (development, staging, production)
     */
    public static function getEnvironment(): string
    {
        return self::get('APP_ENV', 'development');
    }
    
    /**
     * Check if running in development environment
     */
    public static function isDevelopment(): bool
    {
        return self::getEnvironment() === 'development';
    }
    
    /**
     * Check if running in staging environment
     */
    public static function isStaging(): bool
    {
        return self::getEnvironment() === 'staging';
    }
    
    /**
     * Check if running in production environment
     */
    public static function isProduction(): bool
    {
        return self::getEnvironment() === 'production';
    }
    
    /**
     * Get database configuration
     */
    public static function getDatabaseConfig(): array
    {
        return [
            'host' => self::get('DB_HOST', 'localhost'),
            'port' => (int) self::get('DB_PORT', 5432),
            'database' => self::get('DB_NAME', 'guardgeo'),
            'username' => self::get('DB_USER', 'postgres'),
            'password' => self::get('DB_PASSWORD', ''),
            'charset' => self::get('DB_CHARSET', 'utf8'),
        ];
    }
    
    /**
     * Get Freemius API configuration
     */
    public static function getFreemiusConfig(): array
    {
        return [
            'api_token' => self::get('FREEMIUS_API_TOKEN'),
            'base_url' => self::get('FREEMIUS_API_BASE_URL', 'https://api.freemius.com/v1'),
            'timeout' => (int) self::get('FREEMIUS_API_TIMEOUT', 30),
            'retry_attempts' => (int) self::get('FREEMIUS_API_RETRY_ATTEMPTS', 3),
            'retry_delay' => (int) self::get('FREEMIUS_API_RETRY_DELAY', 1),
        ];
    }
    
    /**
     * Get ipRegistry API configuration
     */
    public static function getIpRegistryConfig(): array
    {
        return [
            'api_key' => self::get('IPREGISTRY_API_KEY'),
            'base_url' => self::get('IPREGISTRY_API_BASE_URL', 'https://api.ipregistry.co'),
            'timeout' => (int) self::get('IPREGISTRY_API_TIMEOUT', 30),
            'retry_attempts' => (int) self::get('IPREGISTRY_API_RETRY_ATTEMPTS', 3),
            'retry_delay' => (int) self::get('IPREGISTRY_API_RETRY_DELAY', 1),
        ];
    }
    
    /**
     * Get logging configuration
     */
    public static function getLoggingConfig(): array
    {
        return [
            'level' => self::get('LOG_LEVEL', 'info'),
            'file_path' => self::get('LOG_FILE_PATH', __DIR__ . '/../../logs'),
            'max_file_size' => (int) self::get('LOG_MAX_FILE_SIZE', 10485760), // 10MB
            'max_files' => (int) self::get('LOG_MAX_FILES', 5),
        ];
    }
    
    /**
     * Get authentication configuration
     */
    public static function getAuthConfig(): array
    {
        return [
            'session_lifetime' => (int) self::get('AUTH_SESSION_LIFETIME', 86400), // 24 hours
            'session_name' => self::get('AUTH_SESSION_NAME', 'GUARDGEO_ADMIN_SESSION'),
            'password_min_length' => (int) self::get('AUTH_PASSWORD_MIN_LENGTH', 12),
            'allowed_email_domains' => self::get('ADMIN_ALLOWED_EMAIL_DOMAINS', 'skpassegna.me,guardgeo.com'),
            'blocked_email_domains' => self::get('ADMIN_BLOCKED_EMAIL_DOMAINS', ''),
            'email_domain_strict_mode' => (bool) self::get('EMAIL_DOMAIN_STRICT_MODE', true),
            'max_login_attempts' => (int) self::get('AUTH_MAX_LOGIN_ATTEMPTS', 5),
            'lockout_duration' => (int) self::get('AUTH_LOCKOUT_DURATION', 900), // 15 minutes
        ];
    }
    
    /**
     * Get all configuration as array
     */
    public static function all(): array
    {
        self::load();
        
        return self::$config;
    }
    
    /**
     * Get required environment variables and validate they exist
     */
    public static function validateRequired(array $required): array
    {
        $missing = [];
        
        foreach ($required as $key) {
            if (!self::has($key) || empty(self::get($key))) {
                $missing[] = $key;
            }
        }
        
        return $missing;
    }
}