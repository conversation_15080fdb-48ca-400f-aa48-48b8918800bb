<?php

namespace Skpassegna\GuardgeoApi\Database;

use DateTime;

/**
 * API Request Repository
 * 
 * Handles database operations for API request tracking and analytics.
 */
class ApiRequestRepository extends BaseRepository
{
    protected string $table = 'api_requests';
    
    /**
     * Get popular IPs from recent requests
     */
    public function getPopularIps(DateTime $cutoffDate, int $threshold, int $limit): array
    {
        $cutoffStr = $cutoffDate->format('Y-m-d H:i:s');
        
        $query = $this->queryBuilder
            ->select('ip, COUNT(*) as request_count')
            ->from($this->table)
            ->where('created_at >= ? AND ip IS NOT NULL')
            ->groupBy('ip')
            ->having('COUNT(*) >= ?')
            ->orderBy('COUNT(*) DESC')
            ->limit($limit)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, [$cutoffStr, $threshold]);
            $rows = $result->fetchAll();
            
            return array_column($rows, 'ip');
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get popular IPs", [
                'cutoff_date' => $cutoffStr,
                'threshold' => $threshold,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get active geographic regions from recent requests
     */
    public function getActiveRegions(DateTime $cutoffDate, int $limit): array
    {
        $cutoffStr = $cutoffDate->format('Y-m-d H:i:s');
        
        // This would require joining with IP intelligence data
        // For now, return a simplified implementation
        try {
            $query = "
                SELECT 
                    ii.location_data->>'country'->>'code' as country_code,
                    COUNT(*) as request_count
                FROM {$this->table} ar
                JOIN ip_intelligence ii ON ar.ip = ii.ip
                WHERE ar.created_at >= ?
                AND ii.location_data->>'country'->>'code' IS NOT NULL
                GROUP BY ii.location_data->>'country'->>'code'
                ORDER BY COUNT(*) DESC
                LIMIT ?
            ";
            
            $result = $this->connection->executeQuery($query, [$cutoffStr, $limit]);
            $rows = $result->fetchAll();
            
            return array_column($rows, 'country_code');
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get active regions", [
                'cutoff_date' => $cutoffStr,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get total request count
     */
    public function getTotalRequestCount(): int
    {
        try {
            $query = $this->queryBuilder
                ->select('COUNT(*) as count')
                ->from($this->table)
                ->build();
            
            $result = $this->connection->executeQuery($query);
            $row = $result->fetch();
            
            return (int) ($row['count'] ?? 0);
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get total request count", [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * Get average response time
     */
    public function getAverageResponseTime(): float
    {
        try {
            $query = $this->queryBuilder
                ->select('AVG(response_time_ms) as avg_time')
                ->from($this->table)
                ->where('response_time_ms IS NOT NULL')
                ->build();
            
            $result = $this->connection->executeQuery($query);
            $row = $result->fetch();
            
            return (float) ($row['avg_time'] ?? 0);
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get average response time", [
                'error' => $e->getMessage()
            ]);
            return 0.0;
        }
    }
    
    /**
     * Get request statistics for a date range
     */
    public function getRequestStatistics(DateTime $startDate, DateTime $endDate): array
    {
        $startStr = $startDate->format('Y-m-d H:i:s');
        $endStr = $endDate->format('Y-m-d H:i:s');
        
        try {
            $query = $this->queryBuilder
                ->select('
                    COUNT(*) as total_requests,
                    COUNT(CASE WHEN status_code = 200 THEN 1 END) as successful_requests,
                    COUNT(CASE WHEN status_code >= 400 THEN 1 END) as failed_requests,
                    AVG(response_time_ms) as avg_response_time,
                    COUNT(DISTINCT ip) as unique_ips
                ')
                ->from($this->table)
                ->where('created_at BETWEEN ? AND ?')
                ->build();
            
            $result = $this->connection->executeQuery($query, [$startStr, $endStr]);
            $row = $result->fetch();
            
            return [
                'total_requests' => (int) ($row['total_requests'] ?? 0),
                'successful_requests' => (int) ($row['successful_requests'] ?? 0),
                'failed_requests' => (int) ($row['failed_requests'] ?? 0),
                'avg_response_time' => (float) ($row['avg_response_time'] ?? 0),
                'unique_ips' => (int) ($row['unique_ips'] ?? 0),
                'success_rate' => $row['total_requests'] > 0 
                    ? round(($row['successful_requests'] / $row['total_requests']) * 100, 2) 
                    : 0
            ];
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get request statistics", [
                'start_date' => $startStr,
                'end_date' => $endStr,
                'error' => $e->getMessage()
            ]);
            return [
                'total_requests' => 0,
                'successful_requests' => 0,
                'failed_requests' => 0,
                'avg_response_time' => 0,
                'unique_ips' => 0,
                'success_rate' => 0
            ];
        }
    }
    
    /**
     * Log API request
     */
    public function logRequest(array $requestData): bool
    {
        try {
            $data = [
                'ip' => $requestData['ip'] ?? null,
                'method' => $requestData['method'] ?? 'POST',
                'endpoint' => $requestData['endpoint'] ?? '/api/analyze',
                'user_agent' => $requestData['user_agent'] ?? null,
                'request_data' => json_encode($requestData['request_data'] ?? []),
                'response_data' => json_encode($requestData['response_data'] ?? []),
                'status_code' => $requestData['status_code'] ?? 200,
                'response_time_ms' => $requestData['response_time_ms'] ?? null,
                'cache_hit' => $requestData['cache_hit'] ?? false,
                'freemius_plugin_id' => $requestData['freemius_plugin_id'] ?? null,
                'freemius_install_id' => $requestData['freemius_install_id'] ?? null,
                'created_at' => (new DateTime())->format('Y-m-d H:i:s')
            ];
            
            $this->insert($data);
            return true;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to log API request", [
                'request_data' => $requestData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Get hourly request distribution
     */
    public function getHourlyDistribution(DateTime $date): array
    {
        $dateStr = $date->format('Y-m-d');
        
        try {
            $query = "
                SELECT 
                    EXTRACT(HOUR FROM created_at) as hour,
                    COUNT(*) as request_count
                FROM {$this->table}
                WHERE DATE(created_at) = ?
                GROUP BY EXTRACT(HOUR FROM created_at)
                ORDER BY hour
            ";
            
            $result = $this->connection->executeQuery($query, [$dateStr]);
            $rows = $result->fetchAll();
            
            // Fill in missing hours with 0
            $distribution = array_fill(0, 24, 0);
            foreach ($rows as $row) {
                $distribution[(int) $row['hour']] = (int) $row['request_count'];
            }
            
            return $distribution;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get hourly distribution", [
                'date' => $dateStr,
                'error' => $e->getMessage()
            ]);
            return array_fill(0, 24, 0);
        }
    }
    
    /**
     * Get top IPs by request count
     */
    public function getTopIps(int $limit = 10, DateTime $startDate = null): array
    {
        $queryBuilder = $this->queryBuilder
            ->select('ip, COUNT(*) as request_count')
            ->from($this->table)
            ->where('ip IS NOT NULL');
        
        $params = [];
        
        if ($startDate !== null) {
            $queryBuilder->where('created_at >= ?');
            $params[] = $startDate->format('Y-m-d H:i:s');
        }
        
        $query = $queryBuilder
            ->groupBy('ip')
            ->orderBy('COUNT(*) DESC')
            ->limit($limit)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, $params);
            return $result->fetchAll();
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get top IPs", [
                'limit' => $limit,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get error rate statistics
     */
    public function getErrorRates(DateTime $startDate, DateTime $endDate): array
    {
        $startStr = $startDate->format('Y-m-d H:i:s');
        $endStr = $endDate->format('Y-m-d H:i:s');
        
        try {
            $query = "
                SELECT 
                    status_code,
                    COUNT(*) as count,
                    ROUND((COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()), 2) as percentage
                FROM {$this->table}
                WHERE created_at BETWEEN ? AND ?
                GROUP BY status_code
                ORDER BY count DESC
            ";
            
            $result = $this->connection->executeQuery($query, [$startStr, $endStr]);
            return $result->fetchAll();
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get error rates", [
                'start_date' => $startStr,
                'end_date' => $endStr,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Clean up old request logs
     */
    public function cleanupOldRequests(int $retentionDays): int
    {
        $cutoffDate = (new DateTime())->modify("-{$retentionDays} days")->format('Y-m-d H:i:s');
        
        try {
            $query = $this->queryBuilder
                ->delete($this->table)
                ->where('created_at < ?')
                ->build();
            
            $result = $this->connection->executeQuery($query, [$cutoffDate]);
            return $result->rowCount();
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to cleanup old requests", [
                'retention_days' => $retentionDays,
                'cutoff_date' => $cutoffDate,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
}