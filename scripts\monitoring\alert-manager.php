<?php
/**
 * GuardGeo Admin Platform Alert Manager
 * 
 * This script handles alerting and notifications for the monitoring system.
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Skpassegna\GuardgeoApi\Config\ConfigManager;
use Skpassegna\GuardgeoApi\Services\LoggingService;

class AlertManager
{
    private $config;
    private $logger;
    private $alertHistory = [];

    public function __construct()
    {
        $this->config = ConfigManager::getInstance();
        $this->logger = new LoggingService();
    }

    public function processHealthCheckResults(array $healthResults): void
    {
        $this->loadAlertHistory();
        
        foreach ($healthResults['checks'] as $checkName => $checkResult) {
            $this->processCheck($checkName, $checkResult);
        }
        
        $this->saveAlertHistory();
    }

    private function processCheck(string $checkName, array $checkResult): void
    {
        $status = $checkResult['status'];
        $lastStatus = $this->alertHistory[$checkName]['last_status'] ?? 'healthy';
        $lastAlertTime = $this->alertHistory[$checkName]['last_alert_time'] ?? 0;
        $alertCount = $this->alertHistory[$checkName]['alert_count'] ?? 0;
        
        // Check if status changed or if it's been critical/warning for too long
        $shouldAlert = false;
        $alertReason = '';
        
        if ($status !== $lastStatus) {
            // Status changed
            if ($status === 'critical') {
                $shouldAlert = true;
                $alertReason = 'Status changed to CRITICAL';
            } elseif ($status === 'warning' && $lastStatus === 'healthy') {
                $shouldAlert = true;
                $alertReason = 'Status changed to WARNING';
            } elseif ($status === 'healthy' && ($lastStatus === 'critical' || $lastStatus === 'warning')) {
                $shouldAlert = true;
                $alertReason = 'Status RECOVERED to healthy';
            }
        } elseif ($status === 'critical' && (time() - $lastAlertTime) > 3600) {
            // Critical status persisting for more than 1 hour
            $shouldAlert = true;
            $alertReason = 'CRITICAL status persisting';
        } elseif ($status === 'warning' && (time() - $lastAlertTime) > 7200) {
            // Warning status persisting for more than 2 hours
            $shouldAlert = true;
            $alertReason = 'WARNING status persisting';
        }
        
        if ($shouldAlert) {
            $this->sendAlert($checkName, $checkResult, $alertReason);
            $this->alertHistory[$checkName] = [
                'last_status' => $status,
                'last_alert_time' => time(),
                'alert_count' => $alertCount + 1
            ];
        } else {
            $this->alertHistory[$checkName]['last_status'] = $status;
        }
    }

    private function sendAlert(string $checkName, array $checkResult, string $reason): void
    {
        $alertData = [
            'timestamp' => date('c'),
            'check_name' => $checkName,
            'status' => $checkResult['status'],
            'reason' => $reason,
            'details' => $checkResult['details'],
            'server' => gethostname(),
            'environment' => $this->config->get('app.env', 'unknown')
        ];

        // Log the alert
        $this->logger->logAlert($alertData);

        // Send email alert
        $this->sendEmailAlert($alertData);

        // Send webhook alert if configured
        $webhookUrl = $this->config->get('monitoring.alerts.webhook');
        if ($webhookUrl) {
            $this->sendWebhookAlert($webhookUrl, $alertData);
        }

        // Send Slack alert if configured
        $slackWebhook = $this->config->get('monitoring.alerts.slack_webhook');
        if ($slackWebhook) {
            $this->sendSlackAlert($slackWebhook, $alertData);
        }
    }

    private function sendEmailAlert(array $alertData): void
    {
        $alertEmail = $this->config->get('monitoring.alerts.email');
        if (!$alertEmail) {
            return;
        }

        $subject = sprintf(
            '[GuardGeo Alert] %s - %s (%s)',
            strtoupper($alertData['status']),
            $alertData['check_name'],
            $alertData['environment']
        );

        $body = $this->generateEmailBody($alertData);

        $headers = [
            'From: ' . $this->config->get('mail.from.address', '<EMAIL>'),
            'Reply-To: ' . $this->config->get('mail.from.address', '<EMAIL>'),
            'Content-Type: text/html; charset=UTF-8',
            'X-Priority: ' . ($alertData['status'] === 'critical' ? '1' : '3')
        ];

        $success = mail($alertEmail, $subject, $body, implode("\r\n", $headers));
        
        if (!$success) {
            $this->logger->logError('Failed to send email alert', [
                'alert_data' => $alertData,
                'email' => $alertEmail
            ]);
        }
    }

    private function sendWebhookAlert(string $webhookUrl, array $alertData): void
    {
        $payload = json_encode([
            'alert_type' => 'guardgeo_health_check',
            'timestamp' => $alertData['timestamp'],
            'status' => $alertData['status'],
            'check_name' => $alertData['check_name'],
            'reason' => $alertData['reason'],
            'server' => $alertData['server'],
            'environment' => $alertData['environment'],
            'details' => $alertData['details']
        ]);

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $webhookUrl,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $payload,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'User-Agent: GuardGeo-AlertManager/1.0'
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error || $httpCode >= 400) {
            $this->logger->logError('Failed to send webhook alert', [
                'webhook_url' => $webhookUrl,
                'http_code' => $httpCode,
                'error' => $error,
                'response' => $response
            ]);
        }
    }

    private function sendSlackAlert(string $slackWebhook, array $alertData): void
    {
        $color = match($alertData['status']) {
            'critical' => 'danger',
            'warning' => 'warning',
            'healthy' => 'good',
            default => '#808080'
        };

        $emoji = match($alertData['status']) {
            'critical' => ':red_circle:',
            'warning' => ':warning:',
            'healthy' => ':white_check_mark:',
            default => ':grey_question:'
        };

        $payload = [
            'username' => 'GuardGeo Monitor',
            'icon_emoji' => ':shield:',
            'attachments' => [
                [
                    'color' => $color,
                    'title' => sprintf('%s GuardGeo Alert - %s', $emoji, $alertData['check_name']),
                    'text' => $alertData['reason'],
                    'fields' => [
                        [
                            'title' => 'Status',
                            'value' => strtoupper($alertData['status']),
                            'short' => true
                        ],
                        [
                            'title' => 'Environment',
                            'value' => $alertData['environment'],
                            'short' => true
                        ],
                        [
                            'title' => 'Server',
                            'value' => $alertData['server'],
                            'short' => true
                        ],
                        [
                            'title' => 'Time',
                            'value' => $alertData['timestamp'],
                            'short' => true
                        ]
                    ],
                    'footer' => 'GuardGeo Monitoring',
                    'ts' => time()
                ]
            ]
        ];

        // Add details if available
        if (!empty($alertData['details'])) {
            $detailsText = '';
            foreach ($alertData['details'] as $key => $value) {
                if (is_array($value)) {
                    $value = json_encode($value, JSON_PRETTY_PRINT);
                }
                $detailsText .= "*{$key}:* {$value}\n";
            }
            
            if (strlen($detailsText) > 0) {
                $payload['attachments'][0]['fields'][] = [
                    'title' => 'Details',
                    'value' => "```\n" . trim($detailsText) . "\n```",
                    'short' => false
                ];
            }
        }

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $slackWebhook,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json'
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error || $httpCode >= 400) {
            $this->logger->logError('Failed to send Slack alert', [
                'slack_webhook' => $slackWebhook,
                'http_code' => $httpCode,
                'error' => $error,
                'response' => $response
            ]);
        }
    }

    private function generateEmailBody(array $alertData): string
    {
        $statusColor = match($alertData['status']) {
            'critical' => '#dc3545',
            'warning' => '#ffc107',
            'healthy' => '#28a745',
            default => '#6c757d'
        };

        $detailsHtml = '';
        if (!empty($alertData['details'])) {
            $detailsHtml = '<h3>Details:</h3><pre style="background: #f8f9fa; padding: 10px; border-radius: 4px;">';
            $detailsHtml .= htmlspecialchars(json_encode($alertData['details'], JSON_PRETTY_PRINT));
            $detailsHtml .= '</pre>';
        }

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>GuardGeo Alert</title>
        </head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <div style='background: {$statusColor}; color: white; padding: 15px; border-radius: 4px; margin-bottom: 20px;'>
                    <h1 style='margin: 0; font-size: 24px;'>GuardGeo Alert</h1>
                    <p style='margin: 5px 0 0 0; font-size: 16px;'>{$alertData['reason']}</p>
                </div>
                
                <div style='background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px;'>
                    <table style='width: 100%; border-collapse: collapse;'>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold; width: 120px;'>Check:</td>
                            <td style='padding: 8px 0;'>{$alertData['check_name']}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>Status:</td>
                            <td style='padding: 8px 0; color: {$statusColor}; font-weight: bold; text-transform: uppercase;'>{$alertData['status']}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>Environment:</td>
                            <td style='padding: 8px 0;'>{$alertData['environment']}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>Server:</td>
                            <td style='padding: 8px 0;'>{$alertData['server']}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>Time:</td>
                            <td style='padding: 8px 0;'>{$alertData['timestamp']}</td>
                        </tr>
                    </table>
                </div>
                
                {$detailsHtml}
                
                <div style='margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d;'>
                    <p>This alert was generated automatically by the GuardGeo monitoring system.</p>
                    <p>Please investigate and resolve the issue as soon as possible.</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }

    private function loadAlertHistory(): void
    {
        $historyFile = __DIR__ . '/../../storage/alert_history.json';
        
        if (file_exists($historyFile)) {
            $content = file_get_contents($historyFile);
            $this->alertHistory = json_decode($content, true) ?: [];
        }
    }

    private function saveAlertHistory(): void
    {
        $historyFile = __DIR__ . '/../../storage/alert_history.json';
        $storageDir = dirname($historyFile);
        
        if (!is_dir($storageDir)) {
            mkdir($storageDir, 0755, true);
        }
        
        file_put_contents($historyFile, json_encode($this->alertHistory, JSON_PRETTY_PRINT));
    }

    public function testAlert(string $checkName = 'test', string $status = 'warning'): void
    {
        $testAlert = [
            'status' => $status,
            'timestamp' => date('c'),
            'details' => [
                'message' => 'This is a test alert to verify the alerting system is working correctly.',
                'test_parameter' => 'test_value'
            ]
        ];

        $this->sendAlert($checkName, $testAlert, 'Test alert triggered manually');
        echo "Test alert sent successfully.\n";
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    $alertManager = new AlertManager();
    
    // Check command line arguments
    if (isset($argv[1]) && $argv[1] === 'test') {
        $status = $argv[2] ?? 'warning';
        $alertManager->testAlert('manual_test', $status);
    } else {
        // Process health check results from stdin or file
        $input = '';
        if (!empty($argv[1]) && file_exists($argv[1])) {
            $input = file_get_contents($argv[1]);
        } else {
            $input = stream_get_contents(STDIN);
        }
        
        if (!empty($input)) {
            $healthResults = json_decode($input, true);
            if ($healthResults) {
                $alertManager->processHealthCheckResults($healthResults);
                echo "Alerts processed successfully.\n";
            } else {
                echo "Invalid JSON input.\n";
                exit(1);
            }
        } else {
            echo "Usage: php alert-manager.php [health-check-results.json]\n";
            echo "       php alert-manager.php test [critical|warning|healthy]\n";
            exit(1);
        }
    }
}