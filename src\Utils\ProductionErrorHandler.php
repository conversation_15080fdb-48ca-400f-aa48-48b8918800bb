<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Utils\ErrorClassifier;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;

/**
 * Production Error Handler
 * 
 * Production-ready error handling system that prevents information disclosure
 * while providing appropriate error responses and comprehensive logging.
 */
class ProductionErrorHandler
{
    private ErrorClassifier $classifier;
    private ResponseFormatter $responseFormatter;
    private LoggingService $logger;
    private bool $isProduction;
    private array $sensitivePatterns;
    private array $allowedErrorDetails;

    public function __construct(
        ErrorClassifier $classifier,
        ResponseFormatter $responseFormatter,
        LoggingService $logger,
        bool $isProduction = true
    ) {
        $this->classifier = $classifier;
        $this->responseFormatter = $responseFormatter;
        $this->logger = $logger;
        $this->isProduction = $isProduction;
        
        $this->initializeSensitivePatterns();
        $this->initializeAllowedErrorDetails();
    }

    /**
     * Handle error with production-safe response
     */
    public function handleProductionError(string $errorCode, string $message, array $context = [], ?\Throwable $exception = null): void
    {
        $errorId = uniqid('err_');
        $classification = $this->classifier->classifyError($errorCode);
        
        // Log full error details internally
        $this->logFullErrorDetails($errorCode, $message, $context, $exception, $errorId, $classification);
        
        // Prepare sanitized response for client
        $clientResponse = $this->prepareClientResponse($errorCode, $message, $context, $errorId, $classification);
        
        // Set appropriate HTTP status code
        http_response_code($classification['http_code']);
        
        // Set security headers
        $this->setSecurityHeaders();
        
        // Send response
        echo $clientResponse;
        
        // Log response sent
        $this->logResponseSent($errorCode, $classification['http_code'], $errorId);
    }

    /**
     * Handle exception with production safety
     */
    public function handleProductionException(\Throwable $exception, array $additionalContext = []): void
    {
        $classification = $this->classifier->classifyException($exception);
        $errorCode = $this->getErrorCodeFromException($exception);
        
        $context = array_merge($additionalContext, [
            'exception_class' => get_class($exception),
            'file' => $this->sanitizeFilePath($exception->getFile()),
            'line' => $exception->getLine()
        ]);
        
        // Never expose exception details in production
        $safeMessage = $this->isProduction ? 
            $classification['user_message'] : 
            $exception->getMessage();
        
        $this->handleProductionError($errorCode, $safeMessage, $context, $exception);
    }

    /**
     * Sanitize error message for client consumption
     */
    public function sanitizeErrorMessage(string $message, string $errorCode): string
    {
        if (!$this->isProduction) {
            return $message;
        }
        
        // Check if message contains sensitive information
        if ($this->containsSensitiveInformation($message)) {
            $classification = $this->classifier->classifyError($errorCode);
            return $classification['user_message'];
        }
        
        // Remove file paths, SQL queries, and other sensitive data
        $sanitized = $this->removeSensitivePatterns($message);
        
        // Ensure message is appropriate for client
        return $this->makeClientAppropriate($sanitized, $errorCode);
    }

    /**
     * Sanitize error details for client
     */
    public function sanitizeErrorDetails(array $details, string $errorCode): array
    {
        if (!$this->isProduction) {
            return $this->removeInternalDetails($details);
        }
        
        $sanitized = [];
        $allowedFields = $this->allowedErrorDetails[$errorCode] ?? $this->allowedErrorDetails['default'];
        
        foreach ($allowedFields as $field) {
            if (isset($details[$field])) {
                $sanitized[$field] = $this->sanitizeFieldValue($details[$field], $field);
            }
        }
        
        // Always include error ID for support purposes
        $sanitized['error_id'] = $details['error_id'] ?? uniqid('err_');
        $sanitized['timestamp'] = date('c');
        
        return $sanitized;
    }

    /**
     * Check if system should expose detailed errors
     */
    public function shouldExposeDetailedErrors(): bool
    {
        return !$this->isProduction && 
               (isset($_SERVER['HTTP_X_DEBUG_MODE']) || 
                isset($_GET['debug']) && $_GET['debug'] === 'true');
    }

    /**
     * Get safe error response for critical failures
     */
    public function getCriticalFailureResponse(): string
    {
        $errorId = uniqid('crit_');
        
        $this->logger->critical('Critical system failure - generic response sent', [
            'error_id' => $errorId,
            'timestamp' => date('c'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        http_response_code(500);
        $this->setSecurityHeaders();
        
        return $this->responseFormatter->formatError(
            'INTERNAL_ERROR',
            'An internal server error occurred',
            ['error_id' => $errorId],
            500
        );
    }

    /**
     * Handle database errors with special care
     */
    public function handleDatabaseError(\Throwable $exception, array $context = []): void
    {
        $errorId = uniqid('db_');
        
        // Log full database error details
        $this->logger->error('Database error occurred', [
            'error_id' => $errorId,
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'context' => $context,
            'sql_state' => $this->extractSqlState($exception),
            'timestamp' => date('c')
        ]);
        
        // Never expose database details to client
        $this->handleProductionError(
            'DATABASE_ERROR',
            'Database operation failed',
            ['error_id' => $errorId],
            $exception
        );
    }

    /**
     * Handle external API errors
     */
    public function handleExternalApiError(string $service, \Throwable $exception, array $context = []): void
    {
        $errorId = uniqid('api_');
        
        // Log external API error
        $this->logger->error('External API error', [
            'error_id' => $errorId,
            'service' => $service,
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'context' => $context,
            'timestamp' => date('c')
        ]);
        
        // Provide generic external service error
        $this->handleProductionError(
            'EXTERNAL_API_ERROR',
            "External service temporarily unavailable",
            [
                'error_id' => $errorId,
                'service' => $service
            ],
            $exception
        );
    }

    /**
     * Initialize sensitive patterns to remove from messages
     */
    private function initializeSensitivePatterns(): void
    {
        $this->sensitivePatterns = [
            // File paths
            '/\/[a-zA-Z0-9_\-\/\.]+\.php/',
            '/[A-Z]:\\\\[a-zA-Z0-9_\-\\\\\.]+/',
            
            // SQL queries and database info
            '/SELECT\s+.*\s+FROM\s+/i',
            '/INSERT\s+INTO\s+/i',
            '/UPDATE\s+.*\s+SET\s+/i',
            '/DELETE\s+FROM\s+/i',
            '/SQLSTATE\[\d+\]/',
            
            // Connection strings
            '/host=[^;]+/i',
            '/password=[^;]+/i',
            '/user=[^;]+/i',
            '/dbname=[^;]+/i',
            
            // Stack traces
            '/Stack trace:.*$/s',
            '/#\d+\s+\/.*\.php\(\d+\)/',
            
            // Internal paths and configuration
            '/\/var\/www\//',
            '/\/home\/<USER>\/]+\//',
            '/\/usr\/local\//',
            
            // API keys and tokens
            '/[a-zA-Z0-9]{32,}/',
            '/Bearer\s+[a-zA-Z0-9\-_\.]+/i',
            
            // IP addresses (internal)
            '/192\.168\.\d+\.\d+/',
            '/10\.\d+\.\d+\.\d+/',
            '/172\.(1[6-9]|2[0-9]|3[01])\.\d+\.\d+/'
        ];
    }

    /**
     * Initialize allowed error details per error type
     */
    private function initializeAllowedErrorDetails(): void
    {
        $this->allowedErrorDetails = [
            'VALIDATION_ERROR' => [
                'field_errors', 'validation_errors', 'field_count'
            ],
            'AUTHENTICATION_ERROR' => [
                'reason'
            ],
            'AUTHORIZATION_ERROR' => [
                'required_permission', 'current_role'
            ],
            'RATE_LIMIT_EXCEEDED' => [
                'retry_after', 'limit_type'
            ],
            'METHOD_NOT_ALLOWED' => [
                'allowed_methods', 'received_method'
            ],
            'NOT_FOUND' => [
                'resource_type'
            ],
            'INVALID_INSTALLATION' => [
                'plugin_id', 'validation_details'
            ],
            'default' => [
                'error_id', 'timestamp', 'recovery_action'
            ]
        ];
    }

    /**
     * Log full error details for internal use
     */
    private function logFullErrorDetails(string $errorCode, string $message, array $context, ?\Throwable $exception, string $errorId, array $classification): void
    {
        $logData = [
            'error_id' => $errorId,
            'error_code' => $errorCode,
            'message' => $message,
            'classification' => $classification,
            'context' => $context,
            'request_data' => $this->getRequestData(),
            'server_data' => $this->getServerData(),
            'timestamp' => date('c')
        ];
        
        if ($exception) {
            $logData['exception'] = [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'code' => $exception->getCode(),
                'trace' => $exception->getTraceAsString()
            ];
        }
        
        $logLevel = $classification['log_level'];
        $this->logger->$logLevel('Production error handled', $logData);
    }

    /**
     * Prepare sanitized response for client
     */
    private function prepareClientResponse(string $errorCode, string $message, array $context, string $errorId, array $classification): string
    {
        $sanitizedMessage = $this->sanitizeErrorMessage($message, $errorCode);
        $sanitizedDetails = $this->sanitizeErrorDetails(
            array_merge($context, ['error_id' => $errorId]), 
            $errorCode
        );
        
        return $this->responseFormatter->formatError(
            $errorCode,
            $sanitizedMessage,
            $sanitizedDetails,
            $classification['http_code']
        );
    }

    /**
     * Check if message contains sensitive information
     */
    private function containsSensitiveInformation(string $message): bool
    {
        foreach ($this->sensitivePatterns as $pattern) {
            if (preg_match($pattern, $message)) {
                return true;
            }
        }
        
        // Check for common sensitive keywords
        $sensitiveKeywords = [
            'password', 'token', 'key', 'secret', 'connection',
            'database', 'sql', 'query', 'stack trace', 'exception'
        ];
        
        $lowerMessage = strtolower($message);
        foreach ($sensitiveKeywords as $keyword) {
            if (strpos($lowerMessage, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Remove sensitive patterns from message
     */
    private function removeSensitivePatterns(string $message): string
    {
        $sanitized = $message;
        
        foreach ($this->sensitivePatterns as $pattern) {
            $sanitized = preg_replace($pattern, '[REDACTED]', $sanitized);
        }
        
        return $sanitized;
    }

    /**
     * Make message appropriate for client consumption
     */
    private function makeClientAppropriate(string $message, string $errorCode): string
    {
        // If message is too technical or long, use generic message
        if (strlen($message) > 200 || $this->isTooTechnical($message)) {
            $classification = $this->classifier->classifyError($errorCode);
            return $classification['user_message'];
        }
        
        return $message;
    }

    /**
     * Check if message is too technical for client
     */
    private function isTooTechnical(string $message): bool
    {
        $technicalTerms = [
            'exception', 'stack', 'trace', 'class', 'method',
            'function', 'variable', 'array', 'object', 'null',
            'undefined', 'fatal', 'parse', 'syntax'
        ];
        
        $lowerMessage = strtolower($message);
        $technicalCount = 0;
        
        foreach ($technicalTerms as $term) {
            if (strpos($lowerMessage, $term) !== false) {
                $technicalCount++;
            }
        }
        
        return $technicalCount >= 2;
    }

    /**
     * Sanitize field value
     */
    private function sanitizeFieldValue($value, string $field): mixed
    {
        if (is_string($value)) {
            // Limit string length
            if (strlen($value) > 500) {
                $value = substr($value, 0, 497) . '...';
            }
            
            // Remove sensitive patterns
            $value = $this->removeSensitivePatterns($value);
        }
        
        if (is_array($value)) {
            // Recursively sanitize arrays
            return array_map(function($item) use ($field) {
                return $this->sanitizeFieldValue($item, $field);
            }, $value);
        }
        
        return $value;
    }

    /**
     * Remove internal details from error details
     */
    private function removeInternalDetails(array $details): array
    {
        $internalFields = [
            'trace', 'stack_trace', 'file', 'line', 'sql',
            'query', 'connection_string', 'password', 'token',
            'key', 'secret', 'internal_error', 'debug_info'
        ];
        
        foreach ($internalFields as $field) {
            unset($details[$field]);
        }
        
        return $details;
    }

    /**
     * Set security headers for error responses
     */
    private function setSecurityHeaders(): void
    {
        header('Content-Type: application/json; charset=utf-8');
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
    }

    /**
     * Get sanitized request data for logging
     */
    private function getRequestData(): array
    {
        $data = [
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 200)
        ];
        
        // Don't log sensitive headers
        $safeHeaders = ['content-type', 'accept', 'accept-language'];
        foreach ($safeHeaders as $header) {
            $headerKey = 'HTTP_' . strtoupper(str_replace('-', '_', $header));
            if (isset($_SERVER[$headerKey])) {
                $data['headers'][$header] = $_SERVER[$headerKey];
            }
        }
        
        return $data;
    }

    /**
     * Get sanitized server data for logging
     */
    private function getServerData(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'timestamp' => date('c')
        ];
    }

    /**
     * Sanitize file path for logging
     */
    private function sanitizeFilePath(string $path): string
    {
        // Only show relative path from project root
        $projectRoot = dirname(__DIR__, 2);
        if (strpos($path, $projectRoot) === 0) {
            return substr($path, strlen($projectRoot) + 1);
        }
        
        return basename($path);
    }

    /**
     * Extract SQL state from database exception
     */
    private function extractSqlState(\Throwable $exception): ?string
    {
        $message = $exception->getMessage();
        if (preg_match('/SQLSTATE\[(\w+)\]/', $message, $matches)) {
            return $matches[1];
        }
        
        return null;
    }

    /**
     * Get error code from exception type
     */
    private function getErrorCodeFromException(\Throwable $exception): string
    {
        $class = get_class($exception);
        
        if (strpos($class, 'Database') !== false || strpos($class, 'PDO') !== false) {
            return 'DATABASE_ERROR';
        }
        
        if (strpos($class, 'FreemiusApi') !== false) {
            return 'FREEMIUS_API_ERROR';
        }
        
        if (strpos($class, 'IpRegistryApi') !== false) {
            return 'IP_INTELLIGENCE_ERROR';
        }
        
        if (strpos($class, 'InvalidArgument') !== false) {
            return 'VALIDATION_ERROR';
        }
        
        return 'INTERNAL_ERROR';
    }

    /**
     * Log response sent
     */
    private function logResponseSent(string $errorCode, int $httpCode, string $errorId): void
    {
        $this->logger->info('Error response sent', [
            'error_code' => $errorCode,
            'http_code' => $httpCode,
            'error_id' => $errorId,
            'production_mode' => $this->isProduction,
            'timestamp' => date('c')
        ]);
    }
}