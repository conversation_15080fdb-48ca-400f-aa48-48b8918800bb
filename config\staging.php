<?php

/**
 * Staging Environment Configuration Overrides
 * 
 * These settings override the base configuration when running in staging mode.
 * Staging should closely mirror production but with some debugging enabled.
 */

return [
    'app' => [
        'debug' => true, // Enable debugging in staging
        'url' => 'https://staging.guardgeo.com',
    ],
    
    'logging' => [
        'level' => 'info',
        'max_files' => 7,
        'max_file_size' => 20971520, // 20MB
    ],
    
    'api' => [
        'rate_limit_enabled' => true,
        'rate_limit_requests' => 2000, // Higher limit for testing
        'rate_limit_window' => 3600,
        'request_timeout' => 45,
    ],
    
    'security' => [
        'session_secure' => true,
        'session_httponly' => true,
        'csrf_protection' => true,
    ],
    
    'auth' => [
        'session_lifetime' => 28800, // 8 hours
        'max_login_attempts' => 5,
        'lockout_duration' => 600, // 10 minutes
    ],
    
    'monitoring' => [
        'enabled' => true,
        'performance_tracking' => true,
        'error_reporting' => true,
        'health_check_interval' => 300,
    ],
    
    'cache' => [
        'enabled' => true,
        'default_ttl' => 1800, // 30 minutes
    ],
    
    // Staging-specific features
    'staging' => [
        'enable_test_data' => true,
        'enable_performance_monitoring' => true,
        'enable_detailed_logging' => true,
        'simulate_load_testing' => false,
    ]
];