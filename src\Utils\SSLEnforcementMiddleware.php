<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Config\EnvironmentManager;
use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * SSL/HTTPS Enforcement Middleware
 * 
 * Enforces HTTPS connections in production and staging environments,
 * sets security headers, and handles SSL-related security measures.
 */
class SSLEnforcementMiddleware
{
    private EnvironmentManager $envManager;
    private LoggingService $logger;
    private array $securityHeaders = [];
    
    public function __construct()
    {
        $this->envManager = EnvironmentManager::getInstance();
        $this->logger = LoggingService::getInstance();
        $this->initializeSecurityHeaders();
    }
    
    /**
     * Process request and enforce SSL if required
     */
    public function process(): void
    {
        try {
            // Check if SSL enforcement is enabled
            if ($this->envManager->isSSLEnforced()) {
                $this->enforceHTTPS();
            }
            
            // Set security headers
            $this->setSecurityHeaders();
            
            // Validate SSL certificate if in production
            if ($this->envManager->isProduction()) {
                $this->validateSSLCertificate();
            }
            
            $this->logger->debug('SSL enforcement middleware processed', [
                'ssl_enforced' => $this->envManager->isSSLEnforced(),
                'is_https' => $this->isHTTPS(),
                'environment' => $this->envManager->getEnvironment()
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error('SSL enforcement middleware error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // In production, fail securely
            if ($this->envManager->isProduction()) {
                $this->sendSecurityError();
            }
        }
    }
    
    /**
     * Initialize security headers based on environment
     */
    private function initializeSecurityHeaders(): void
    {
        $this->securityHeaders = [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'X-Permitted-Cross-Domain-Policies' => 'none',
            'X-Download-Options' => 'noopen',
        ];
        
        // Add HTTPS-specific headers if SSL is enforced
        if ($this->envManager->isSSLEnforced()) {
            $this->securityHeaders['Strict-Transport-Security'] = 
                'max-age=31536000; includeSubDomains; preload';
            
            $this->securityHeaders['Content-Security-Policy'] = 
                $this->envManager->get('security.headers.Content-Security-Policy', 
                    $this->getDefaultCSP());
        }
        
        // Environment-specific headers
        if ($this->envManager->isDevelopment()) {
            // Less restrictive CSP for development
            $this->securityHeaders['Content-Security-Policy'] = 
                "default-src 'self' 'unsafe-inline' 'unsafe-eval'; img-src 'self' data: https:;";
        }
    }
    
    /**
     * Enforce HTTPS connection
     */
    private function enforceHTTPS(): void
    {
        if (!$this->isHTTPS()) {
            $httpsUrl = $this->getHTTPSUrl();
            
            $this->logger->warning('HTTP request redirected to HTTPS', [
                'original_url' => $this->getCurrentUrl(),
                'redirect_url' => $httpsUrl,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'ip_address' => $this->getClientIP()
            ]);
            
            // Send permanent redirect to HTTPS
            header('HTTP/1.1 301 Moved Permanently');
            header("Location: {$httpsUrl}");
            header('Connection: close');
            exit;
        }
    }
    
    /**
     * Set security headers
     */
    private function setSecurityHeaders(): void
    {
        foreach ($this->securityHeaders as $header => $value) {
            header("{$header}: {$value}");
        }
        
        // Remove server information
        header_remove('X-Powered-By');
        header_remove('Server');
        
        $this->logger->debug('Security headers set', [
            'headers_count' => count($this->securityHeaders),
            'environment' => $this->envManager->getEnvironment()
        ]);
    }
    
    /**
     * Validate SSL certificate in production
     */
    private function validateSSLCertificate(): void
    {
        $certPath = $this->envManager->get('ssl.certificate_path');
        $keyPath = $this->envManager->get('ssl.private_key_path');
        
        if (!$certPath || !$keyPath) {
            $this->logger->warning('SSL certificate paths not configured in production');
            return;
        }
        
        // Check certificate file exists and is readable
        if (!file_exists($certPath) || !is_readable($certPath)) {
            $this->logger->error('SSL certificate file not accessible', [
                'certificate_path' => $certPath
            ]);
            return;
        }
        
        // Check private key file exists and is readable
        if (!file_exists($keyPath) || !is_readable($keyPath)) {
            $this->logger->error('SSL private key file not accessible', [
                'private_key_path' => $keyPath
            ]);
            return;
        }
        
        // Validate certificate expiration
        $this->validateCertificateExpiration($certPath);
        
        $this->logger->info('SSL certificate validation completed', [
            'certificate_path' => $certPath,
            'private_key_path' => $keyPath
        ]);
    }
    
    /**
     * Validate certificate expiration
     */
    private function validateCertificateExpiration(string $certPath): void
    {
        try {
            $certContent = file_get_contents($certPath);
            $cert = openssl_x509_parse($certContent);
            
            if ($cert === false) {
                $this->logger->error('Failed to parse SSL certificate', [
                    'certificate_path' => $certPath
                ]);
                return;
            }
            
            $expiryDate = $cert['validTo_time_t'];
            $currentTime = time();
            $daysUntilExpiry = ($expiryDate - $currentTime) / (24 * 60 * 60);
            
            if ($daysUntilExpiry < 0) {
                $this->logger->error('SSL certificate has expired', [
                    'certificate_path' => $certPath,
                    'expired_days_ago' => abs($daysUntilExpiry)
                ]);
            } elseif ($daysUntilExpiry < 30) {
                $this->logger->warning('SSL certificate expires soon', [
                    'certificate_path' => $certPath,
                    'days_until_expiry' => $daysUntilExpiry
                ]);
            } else {
                $this->logger->debug('SSL certificate is valid', [
                    'certificate_path' => $certPath,
                    'days_until_expiry' => $daysUntilExpiry
                ]);
            }
            
        } catch (\Exception $e) {
            $this->logger->error('Error validating SSL certificate expiration', [
                'certificate_path' => $certPath,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Check if current request is HTTPS
     */
    private function isHTTPS(): bool
    {
        // Check standard HTTPS indicators
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') {
            return true;
        }
        
        if (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443) {
            return true;
        }
        
        // Check for proxy headers (load balancer, CDN)
        if (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && 
            $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
            return true;
        }
        
        if (isset($_SERVER['HTTP_X_FORWARDED_SSL']) && 
            $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on') {
            return true;
        }
        
        if (isset($_SERVER['HTTP_CF_VISITOR'])) {
            $cfVisitor = json_decode($_SERVER['HTTP_CF_VISITOR'], true);
            if (isset($cfVisitor['scheme']) && $cfVisitor['scheme'] === 'https') {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get current URL
     */
    private function getCurrentUrl(): string
    {
        $protocol = $this->isHTTPS() ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        
        return "{$protocol}://{$host}{$uri}";
    }
    
    /**
     * Get HTTPS version of current URL
     */
    private function getHTTPSUrl(): string
    {
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        
        return "https://{$host}{$uri}";
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP(): string
    {
        // Check for IP from various headers (proxy, load balancer, CDN)
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'HTTP_CLIENT_IP',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];
        
        foreach ($headers as $header) {
            if (isset($_SERVER[$header]) && !empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]);
                
                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Get default Content Security Policy
     */
    private function getDefaultCSP(): string
    {
        return "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; " .
               "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; " .
               "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; " .
               "img-src 'self' data: https:; " .
               "connect-src 'self'; " .
               "object-src 'none'; " .
               "base-uri 'self'; " .
               "form-action 'self'; " .
               "frame-ancestors 'none'; " .
               "upgrade-insecure-requests";
    }
    
    /**
     * Send security error response
     */
    private function sendSecurityError(): void
    {
        http_response_code(503);
        header('Content-Type: application/json');
        
        $response = [
            'success' => false,
            'error' => [
                'code' => 'SSL_CONFIGURATION_ERROR',
                'message' => 'SSL configuration error. Please contact administrator.',
            ],
            'timestamp' => date('c')
        ];
        
        echo json_encode($response);
        exit;
    }
    
    /**
     * Get SSL certificate information
     */
    public function getSSLCertificateInfo(): array
    {
        $certPath = $this->envManager->get('ssl.certificate_path');
        
        if (!$certPath || !file_exists($certPath)) {
            return [
                'configured' => false,
                'error' => 'Certificate path not configured or file not found'
            ];
        }
        
        try {
            $certContent = file_get_contents($certPath);
            $cert = openssl_x509_parse($certContent);
            
            if ($cert === false) {
                return [
                    'configured' => true,
                    'valid' => false,
                    'error' => 'Failed to parse certificate'
                ];
            }
            
            $expiryDate = $cert['validTo_time_t'];
            $currentTime = time();
            $daysUntilExpiry = ($expiryDate - $currentTime) / (24 * 60 * 60);
            
            return [
                'configured' => true,
                'valid' => true,
                'subject' => $cert['subject'],
                'issuer' => $cert['issuer'],
                'valid_from' => date('Y-m-d H:i:s', $cert['validFrom_time_t']),
                'valid_to' => date('Y-m-d H:i:s', $cert['validTo_time_t']),
                'days_until_expiry' => round($daysUntilExpiry, 1),
                'is_expired' => $daysUntilExpiry < 0,
                'expires_soon' => $daysUntilExpiry < 30 && $daysUntilExpiry > 0
            ];
            
        } catch (\Exception $e) {
            return [
                'configured' => true,
                'valid' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Check if SSL is properly configured
     */
    public function isSSLProperlyConfigured(): bool
    {
        if (!$this->envManager->isSSLEnforced()) {
            return true; // SSL not required
        }
        
        $certInfo = $this->getSSLCertificateInfo();
        
        return $certInfo['configured'] && 
               $certInfo['valid'] && 
               !($certInfo['is_expired'] ?? false);
    }
    
    /**
     * Get SSL configuration status
     */
    public function getSSLStatus(): array
    {
        return [
            'ssl_enforced' => $this->envManager->isSSLEnforced(),
            'is_https_request' => $this->isHTTPS(),
            'certificate_info' => $this->getSSLCertificateInfo(),
            'security_headers_enabled' => !empty($this->securityHeaders),
            'environment' => $this->envManager->getEnvironment()
        ];
    }
}