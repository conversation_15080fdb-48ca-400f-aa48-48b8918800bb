<?php

namespace Skpassegna\GuardgeoApi\Database;

/**
 * Database Configuration
 * 
 * Manages database configuration settings for different environments
 */
class DatabaseConfig
{
    private static array $config = [];
    private static bool $initialized = false;
    
    /**
     * Initialize database configuration
     */
    public static function initialize(): void
    {
        if (self::$initialized) {
            return;
        }
        
        // Load configuration from environment variables or defaults
        self::$config = [
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => (int) ($_ENV['DB_PORT'] ?? 5432),
            'database' => $_ENV['DB_NAME'] ?? 'guardgeo_admin',
            'username' => $_ENV['DB_USER'] ?? 'postgres',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
            'charset' => $_ENV['DB_CHARSET'] ?? 'utf8',
            'options' => [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false,
                \PDO::ATTR_PERSISTENT => filter_var($_ENV['DB_PERSISTENT'] ?? 'true', FILTER_VALIDATE_BOOLEAN),
                \PDO::ATTR_TIMEOUT => (int) ($_ENV['DB_TIMEOUT'] ?? 30),
            ]
        ];
        
        self::$initialized = true;
    }
    
    /**
     * Get database configuration
     */
    public static function getConfig(): array
    {
        if (!self::$initialized) {
            self::initialize();
        }
        
        return self::$config;
    }
    
    /**
     * Get specific configuration value
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        if (!self::$initialized) {
            self::initialize();
        }
        
        return self::$config[$key] ?? $default;
    }
    
    /**
     * Set configuration value
     */
    public static function set(string $key, mixed $value): void
    {
        if (!self::$initialized) {
            self::initialize();
        }
        
        self::$config[$key] = $value;
    }
    
    /**
     * Override configuration with custom values
     */
    public static function override(array $config): void
    {
        if (!self::$initialized) {
            self::initialize();
        }
        
        self::$config = array_merge(self::$config, $config);
    }
    
    /**
     * Get DSN string for PDO connection
     */
    public static function getDsn(): string
    {
        if (!self::$initialized) {
            self::initialize();
        }
        
        return sprintf(
            'pgsql:host=%s;port=%d;dbname=%s;charset=%s',
            self::$config['host'],
            self::$config['port'],
            self::$config['database'],
            self::$config['charset']
        );
    }
    
    /**
     * Validate configuration
     */
    public static function validate(): array
    {
        if (!self::$initialized) {
            self::initialize();
        }
        
        $errors = [];
        
        if (empty(self::$config['host'])) {
            $errors[] = 'Database host is required';
        }
        
        if (empty(self::$config['database'])) {
            $errors[] = 'Database name is required';
        }
        
        if (empty(self::$config['username'])) {
            $errors[] = 'Database username is required';
        }
        
        if (self::$config['port'] < 1 || self::$config['port'] > 65535) {
            $errors[] = 'Database port must be between 1 and 65535';
        }
        
        return $errors;
    }
    
    /**
     * Test database connection
     */
    public static function testConnection(): array
    {
        try {
            $dsn = self::getDsn();
            $pdo = new \PDO(
                $dsn,
                self::$config['username'],
                self::$config['password'],
                self::$config['options']
            );
            
            // Test with a simple query
            $statement = $pdo->query('SELECT version() as version');
            $result = $statement->fetch();
            
            return [
                'success' => true,
                'message' => 'Database connection successful',
                'version' => $result['version'] ?? 'Unknown',
                'dsn' => $dsn
            ];
            
        } catch (\PDOException $e) {
            return [
                'success' => false,
                'message' => 'Database connection failed: ' . $e->getMessage(),
                'error_code' => $e->getCode(),
                'dsn' => self::getDsn()
            ];
        }
    }
    
    /**
     * Get configuration for display (without sensitive data)
     */
    public static function getDisplayConfig(): array
    {
        if (!self::$initialized) {
            self::initialize();
        }
        
        $displayConfig = self::$config;
        $displayConfig['password'] = str_repeat('*', strlen($displayConfig['password']));
        
        return $displayConfig;
    }
}