<?php
/**
 * Freemius Webhook Endpoint
 * 
 * Direct endpoint for receiving Freemius webhooks.
 * This file can be accessed directly at: https://yourdomain.com/webhook.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Skpassegna\GuardgeoApi\Controllers\WebhookController;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;

// Set error reporting for production
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);

// Set headers early
header('Content-Type: application/json');

try {
    // Create webhook controller
    $webhookController = new WebhookController();
    
    // Handle the webhook
    $webhookController->handleFreemiusWebhook();
    
} catch (\Exception $e) {
    // Log the error
    error_log("Webhook endpoint error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'code' => 'WEBHOOK_ERROR'
    ]);
}