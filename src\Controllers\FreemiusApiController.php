<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Utils\AuthMiddleware;
use Skpassegna\GuardgeoApi\Utils\RoleManager;
use Skpassegna\GuardgeoApi\Services\FreemiusService;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Database\ProductRepository;
use Skpassegna\GuardgeoApi\Database\InstallationRepository;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;

/**
 * Freemius API Controller
 * 
 * Provides JSON API endpoints for Freemius integration management
 * with product and installation data viewing and synchronization.
 */
class FreemiusApiController
{
    private AuthMiddleware $authMiddleware;
    private RoleManager $roleManager;
    private FreemiusService $freemiusService;
    private ProductRepository $productRepository;
    private InstallationRepository $installationRepository;
    private LoggingService $logger;

    public function __construct(
        AuthMiddleware $authMiddleware,
        RoleManager $roleManager,
        FreemiusService $freemiusService,
        ProductRepository $productRepository,
        InstallationRepository $installationRepository,
        LoggingService $logger
    ) {
        $this->authMiddleware = $authMiddleware;
        $this->roleManager = $roleManager;
        $this->freemiusService = $freemiusService;
        $this->productRepository = $productRepository;
        $this->installationRepository = $installationRepository;
        $this->logger = $logger;
    }

    /**
     * Get Freemius products with pagination and filtering
     *
     * @return void
     */
    public function getProducts(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'freemius_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            // Get query parameters
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = min(max(1, (int)($_GET['limit'] ?? 25)), 100);
            $search = $_GET['search'] ?? '';
            $sortBy = $_GET['sort'] ?? 'created';
            $sortOrder = $_GET['order'] ?? 'desc';

            // Build filters
            $filters = [];
            
            if (!empty($search)) {
                $filters['search'] = $search;
            }

            // Get products
            $result = $this->productRepository->getProductsPaginated(
                $page,
                $limit,
                $filters,
                $sortBy,
                $sortOrder
            );

            $this->sendJsonResponse([
                'success' => true,
                'data' => $result['products'],
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $result['total'],
                    'total_pages' => ceil($result['total'] / $limit)
                ],
                'filters' => [
                    'search' => $search,
                    'sort' => $sortBy,
                    'order' => $sortOrder
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Freemius products API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load products'
            ], 500);
        }
    }

    /**
     * Get detailed product information
     *
     * @return void
     */
    public function getProductDetails(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'freemius_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            $productId = $_GET['id'] ?? '';
            
            if (empty($productId)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Product ID is required'
                ], 400);
                return;
            }

            $product = $this->productRepository->getById($productId);
            
            if (!$product) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Product not found'
                ], 404);
                return;
            }

            // Get installation count for this product
            $installationCount = $this->installationRepository->getCountByProductId($productId);
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => [
                    'product' => $product,
                    'installation_count' => $installationCount,
                    'can_sync' => $this->roleManager->canAccessFeature($user['role'], 'freemius_manager')
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Product details API error', [
                'error' => $e->getMessage(),
                'user' => $user['email'],
                'product_id' => $_GET['id'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load product details'
            ], 500);
        }
    }

    /**
     * Get Freemius installations with pagination and filtering
     *
     * @return void
     */
    public function getInstallations(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'freemius_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            // Get query parameters
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = min(max(1, (int)($_GET['limit'] ?? 25)), 100);
            $search = $_GET['search'] ?? '';
            $productId = $_GET['product_id'] ?? '';
            $status = $_GET['status'] ?? 'all'; // all, active, inactive, premium, trial
            $sortBy = $_GET['sort'] ?? 'created';
            $sortOrder = $_GET['order'] ?? 'desc';

            // Build filters
            $filters = [];
            
            if (!empty($search)) {
                $filters['search'] = $search;
            }
            
            if (!empty($productId)) {
                $filters['product_id'] = $productId;
            }
            
            if ($status !== 'all') {
                $filters['status'] = $status;
            }

            // Get installations
            $result = $this->installationRepository->getInstallationsPaginated(
                $page,
                $limit,
                $filters,
                $sortBy,
                $sortOrder
            );

            $this->sendJsonResponse([
                'success' => true,
                'data' => $result['installations'],
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $result['total'],
                    'total_pages' => ceil($result['total'] / $limit)
                ],
                'filters' => [
                    'search' => $search,
                    'product_id' => $productId,
                    'status' => $status,
                    'sort' => $sortBy,
                    'order' => $sortOrder
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Freemius installations API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load installations'
            ], 500);
        }
    }

    /**
     * Get detailed installation information
     *
     * @return void
     */
    public function getInstallationDetails(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'freemius_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            $installationId = $_GET['id'] ?? '';
            
            if (empty($installationId)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Installation ID is required'
                ], 400);
                return;
            }

            $installation = $this->installationRepository->getById($installationId);
            
            if (!$installation) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Installation not found'
                ], 404);
                return;
            }

            // Get related product information
            $product = $this->productRepository->getById($installation['plugin_id']);
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => [
                    'installation' => $installation,
                    'product' => $product,
                    'can_sync' => $this->roleManager->canAccessFeature($user['role'], 'freemius_manager')
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Installation details API error', [
                'error' => $e->getMessage(),
                'user' => $user['email'],
                'installation_id' => $_GET['id'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load installation details'
            ], 500);
        }
    }

    /**
     * Synchronize product data from Freemius API (manager role required)
     *
     * @return void
     */
    public function syncProductData(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'freemius_manager')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Management access required'
            ], 403);
            return;
        }

        try {
            $requestData = json_decode(file_get_contents('php://input'), true);
            $productId = $requestData['product_id'] ?? '';
            
            if (empty($productId)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Product ID is required'
                ], 400);
                return;
            }

            // Sync product data from Freemius
            $success = $this->freemiusService->syncProductData($productId);
            
            if (!$success) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Failed to sync product data from Freemius'
                ], 500);
                return;
            }

            // Log the sync action
            $this->logger->logAdminAction('Product data synchronized', [
                'product_id' => $productId,
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => true,
                'message' => 'Product data synchronized successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Product sync API error', [
                'error' => $e->getMessage(),
                'user' => $user['email'],
                'product_id' => $requestData['product_id'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to sync product data'
            ], 500);
        }
    }

    /**
     * Synchronize installation data from Freemius API (manager role required)
     *
     * @return void
     */
    public function syncInstallationData(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'freemius_manager')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Management access required'
            ], 403);
            return;
        }

        try {
            $requestData = json_decode(file_get_contents('php://input'), true);
            $installationId = $requestData['installation_id'] ?? '';
            
            if (empty($installationId)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Installation ID is required'
                ], 400);
                return;
            }

            // Sync installation data from Freemius
            $success = $this->freemiusService->syncInstallationData($installationId);
            
            if (!$success) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Failed to sync installation data from Freemius'
                ], 500);
                return;
            }

            // Log the sync action
            $this->logger->logAdminAction('Installation data synchronized', [
                'installation_id' => $installationId,
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => true,
                'message' => 'Installation data synchronized successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Installation sync API error', [
                'error' => $e->getMessage(),
                'user' => $user['email'],
                'installation_id' => $requestData['installation_id'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to sync installation data'
            ], 500);
        }
    }

    /**
     * Get Freemius integration statistics
     *
     * @return void
     */
    public function getStatistics(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'freemius_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            $stats = [
                'products' => $this->productRepository->getStatistics(),
                'installations' => $this->installationRepository->getStatistics()
            ];
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Freemius statistics API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load statistics'
            ], 500);
        }
    }

    /**
     * Validate installation against Freemius API
     *
     * @return void
     */
    public function validateInstallation(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'freemius_manager')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Management access required'
            ], 403);
            return;
        }

        try {
            $requestData = json_decode(file_get_contents('php://input'), true);
            $pluginId = $requestData['plugin_id'] ?? '';
            $installId = $requestData['install_id'] ?? '';
            
            if (empty($pluginId) || empty($installId)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Plugin ID and Install ID are required'
                ], 400);
                return;
            }

            // Validate installation
            $result = $this->freemiusService->validateInstallation($pluginId, $installId);
            
            // Log the validation action
            $this->logger->logAdminAction('Installation validation performed', [
                'plugin_id' => $pluginId,
                'install_id' => $installId,
                'result' => $result['valid'] ? 'valid' : 'invalid',
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Installation validation API error', [
                'error' => $e->getMessage(),
                'user' => $user['email'],
                'plugin_id' => $requestData['plugin_id'] ?? 'unknown',
                'install_id' => $requestData['install_id'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to validate installation'
            ], 500);
        }
    }

    /**
     * Require authentication and return status
     *
     * @return bool
     */
    private function requireAuthentication(): bool
    {
        if (!$this->authMiddleware->isAuthenticated()) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Authentication required'
            ], 401);
            return false;
        }

        return true;
    }

    /**
     * Send JSON response
     *
     * @param array $data
     * @param int $statusCode
     * @return void
     */
    private function sendJsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Create instance with dependencies
     *
     * @return self
     */
    public static function create(): self
    {
        $logger = new LoggingService();
        $db = new DatabaseConnection();
        $sessionManager = new \Skpassegna\GuardgeoApi\Utils\SessionManager();
        $passwordValidator = new \Skpassegna\GuardgeoApi\Utils\PasswordValidator();
        $emailValidator = new \Skpassegna\GuardgeoApi\Utils\EmailDomainValidator();
        
        $authService = new \Skpassegna\GuardgeoApi\Services\AuthService(
            $db,
            $sessionManager,
            $passwordValidator,
            $emailValidator,
            $logger
        );
        
        $authMiddleware = new AuthMiddleware($authService);
        $roleManager = new RoleManager();
        $freemiusService = new FreemiusService($logger);
        $productRepository = new ProductRepository($db);
        $installationRepository = new InstallationRepository($db);

        return new self(
            $authMiddleware,
            $roleManager,
            $freemiusService,
            $productRepository,
            $installationRepository,
            $logger
        );
    }
}