<?php

namespace Skpassegna\GuardgeoApi\Models;

use DateTime;

/**
 * Admin User Model
 * 
 * Represents an administrative user with role-based access control.
 */
class AdminUserModel extends BaseModel
{
    // Properties
    public ?int $id = null;
    public ?string $email = null;
    public ?string $password_hash = null;
    public ?string $role = null;
    public ?DateTime $created_at = null;
    public ?DateTime $updated_at = null;
    public ?DateTime $last_login = null;
    public bool $is_active = true;

    // Role constants
    public const ROLE_SUPER_ADMIN = 'super_admin';
    public const ROLE_DEV = 'dev';
    public const ROLE_MARKETING = 'marketing';
    public const ROLE_SALES = 'sales';

    public const VALID_ROLES = [
        self::ROLE_SUPER_ADMIN,
        self::ROLE_DEV,
        self::ROLE_MARKETING,
        self::ROLE_SALES
    ];

    /**
     * Constructor
     */
    public function __construct(array $data = [])
    {
        $this->id = $this->toNullableInt($data['id'] ?? null);
        $this->email = $this->toNullableString($data['email'] ?? null);
        $this->password_hash = $this->toNullableString($data['password_hash'] ?? null);
        $this->role = $this->toNullableString($data['role'] ?? null);
        $this->is_active = $this->toBool($data['is_active'] ?? true);
        
        $this->created_at = isset($data['created_at']) 
            ? $this->dbToDateTime($data['created_at']) 
            : null;
        $this->updated_at = isset($data['updated_at']) 
            ? $this->dbToDateTime($data['updated_at']) 
            : null;
        $this->last_login = isset($data['last_login']) 
            ? $this->dbToDateTime($data['last_login']) 
            : null;
    }

    /**
     * Create from database row
     */
    public static function fromDatabaseRow(array $row): self
    {
        return new self($row);
    }

    /**
     * Create from API data
     */
    public static function fromApiData(array $data): self
    {
        return new self($data);
    }

    /**
     * Get user ID
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * Set user ID
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Get user email
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * Set user email
     */
    public function setEmail(?string $email): self
    {
        $this->email = ModelValidator::sanitizeEmail($email);
        return $this;
    }

    /**
     * Get password hash
     */
    public function getPasswordHash(): ?string
    {
        return $this->password_hash;
    }

    /**
     * Set password hash
     */
    public function setPasswordHash(?string $hash): self
    {
        $this->password_hash = $hash;
        return $this;
    }

    /**
     * Get user role
     */
    public function getRole(): ?string
    {
        return $this->role;
    }

    /**
     * Set user role
     */
    public function setRole(?string $role): self
    {
        $this->role = $role;
        return $this;
    }

    /**
     * Check if user is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Set user active status
     */
    public function setActive(bool $active): self
    {
        $this->is_active = $active;
        return $this;
    }

    /**
     * Get created timestamp
     */
    public function getCreatedAt(): ?DateTime
    {
        return $this->created_at;
    }

    /**
     * Set created timestamp
     */
    public function setCreatedAt(?DateTime $createdAt): self
    {
        $this->created_at = $createdAt;
        return $this;
    }

    /**
     * Get updated timestamp
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->updated_at;
    }

    /**
     * Set updated timestamp
     */
    public function setUpdatedAt(?DateTime $updatedAt): self
    {
        $this->updated_at = $updatedAt;
        return $this;
    }

    /**
     * Get last login timestamp
     */
    public function getLastLogin(): ?DateTime
    {
        return $this->last_login;
    }

    /**
     * Set last login timestamp
     */
    public function setLastLogin(?DateTime $lastLogin): self
    {
        $this->last_login = $lastLogin;
        return $this;
    }

    /**
     * Check if user is Super Admin
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === self::ROLE_SUPER_ADMIN;
    }

    /**
     * Check if user is Dev
     */
    public function isDev(): bool
    {
        return $this->role === self::ROLE_DEV;
    }

    /**
     * Check if user is Marketing
     */
    public function isMarketing(): bool
    {
        return $this->role === self::ROLE_MARKETING;
    }

    /**
     * Check if user is Sales
     */
    public function isSales(): bool
    {
        return $this->role === self::ROLE_SALES;
    }

    /**
     * Get role display name
     */
    public function getRoleDisplayName(): string
    {
        return match ($this->role) {
            self::ROLE_SUPER_ADMIN => 'Super Administrator',
            self::ROLE_DEV => 'Developer',
            self::ROLE_MARKETING => 'Marketing',
            self::ROLE_SALES => 'Sales',
            default => 'Unknown'
        };
    }

    /**
     * Get user permissions based on role
     */
    public function getPermissions(): array
    {
        return match ($this->role) {
            self::ROLE_SUPER_ADMIN => [
                'admin.dashboard.view',
                'admin.users.view',
                'admin.users.create',
                'admin.users.edit',
                'admin.users.delete',
                'admin.ip_intelligence.view',
                'admin.ip_intelligence.manage',
                'admin.freemius.view',
                'admin.freemius.manage',
                'admin.logs.view',
                'admin.logs.export',
                'admin.settings.view',
                'admin.settings.edit',
                'admin.system.maintenance'
            ],
            self::ROLE_DEV => [
                'admin.dashboard.view',
                'admin.ip_intelligence.view',
                'admin.ip_intelligence.manage',
                'admin.freemius.view',
                'admin.freemius.manage',
                'admin.logs.view',
                'admin.system.maintenance'
            ],
            self::ROLE_MARKETING => [
                'admin.dashboard.view',
                'admin.ip_intelligence.view',
                'admin.freemius.view',
                'admin.logs.view'
            ],
            self::ROLE_SALES => [
                'admin.dashboard.view',
                'admin.ip_intelligence.view',
                'admin.freemius.view',
                'admin.logs.view'
            ],
            default => []
        };
    }

    /**
     * Check if user has specific permission
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->getPermissions());
    }

    /**
     * Check if user can access admin area
     */
    public function canAccessAdmin(): bool
    {
        return $this->is_active && ModelValidator::validateAdminRole($this->role ?? '');
    }

    /**
     * Get user data for session storage (without sensitive data)
     */
    public function getSessionData(): array
    {
        return [
            'id' => $this->id,
            'email' => $this->email,
            'role' => $this->role,
            'role_display' => $this->getRoleDisplayName(),
            'permissions' => $this->getPermissions(),
            'is_super_admin' => $this->isSuperAdmin()
        ];
    }

    /**
     * Comprehensive validation
     */
    public function validate(): array
    {
        $errors = [];

        // Email validation
        if ($error = $this->validateRequired($this->email, 'email')) {
            $errors[] = $error;
        } elseif ($error = $this->validateEmail($this->email, 'email')) {
            $errors[] = $error;
        }

        // Role validation
        if ($error = $this->validateRequired($this->role, 'role')) {
            $errors[] = $error;
        } elseif ($error = $this->validateEnum($this->role, self::VALID_ROLES, 'role')) {
            $errors[] = $error;
        }

        // Password hash validation (for new users)
        if ($this->id === null && empty($this->password_hash)) {
            $errors[] = 'Password hash is required for new users';
        }

        // Email length validation
        if ($this->email && ($error = $this->validateStringLength($this->email, 255, 'email'))) {
            $errors[] = $error;
        }

        return $errors;
    }

    /**
     * Convert to database array
     */
    public function toDatabaseArray(): array
    {
        return [
            'id' => $this->id,
            'email' => $this->email,
            'password_hash' => $this->password_hash,
            'role' => $this->role,
            'is_active' => $this->is_active,
            'created_at' => $this->dateTimeToDb($this->created_at),
            'updated_at' => $this->dateTimeToDb($this->updated_at),
            'last_login' => $this->dateTimeToDb($this->last_login)
        ];
    }

    /**
     * JSON serialization
     */
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'email' => $this->email,
            'role' => $this->role,
            'role_display' => $this->getRoleDisplayName(),
            'is_active' => $this->is_active,
            'permissions' => $this->getPermissions(),
            'created_at' => $this->dateTimeToJson($this->created_at),
            'updated_at' => $this->dateTimeToJson($this->updated_at),
            'last_login' => $this->dateTimeToJson($this->last_login),
            'is_super_admin' => $this->isSuperAdmin(),
            'can_access_admin' => $this->canAccessAdmin()
        ];
    }

    /**
     * Transform for external API integration
     */
    public function toExternalApiFormat(): array
    {
        return [
            'user_id' => $this->id,
            'email' => $this->email,
            'role' => $this->role,
            'active' => $this->is_active,
            'permissions' => $this->getPermissions()
        ];
    }

    /**
     * Get safe array for API responses (excludes sensitive data)
     */
    public function toSafeArray(): array
    {
        return [
            'id' => $this->id,
            'email' => $this->email,
            'role' => $this->role,
            'role_display' => $this->getRoleDisplayName(),
            'is_active' => $this->is_active,
            'created_at' => $this->dateTimeToJson($this->created_at),
            'updated_at' => $this->dateTimeToJson($this->updated_at),
            'last_login' => $this->dateTimeToJson($this->last_login)
        ];
    }

    /**
     * Handle foreign key relationships
     */
    public function getRelatedSystemLogs(): array
    {
        // This would be implemented by the repository layer
        return [];
    }

    /**
     * Validate foreign key constraints
     */
    public function validateForeignKeys(): array
    {
        $errors = [];
        
        // Admin users don't have foreign key dependencies
        // but we validate that the role exists in our enum
        if ($this->role && !ModelValidator::validateAdminRole($this->role)) {
            $errors[] = 'Invalid role specified';
        }
        
        return $errors;
    }
}