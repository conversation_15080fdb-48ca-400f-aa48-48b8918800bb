<?php

namespace Skpassegna\GuardgeoApi\Views\Pages;

use Skpassegna\GuardgeoApi\Views\Templates\BaseTemplate;
use Skpassegna\GuardgeoApi\Views\Components\Card;
use Skpassegna\GuardgeoApi\Views\Components\Button;

/**
 * Logs Page Template
 * 
 * System logs and monitoring page with advanced filtering and export capabilities
 * using the component-based design system.
 */
class LogsPage extends BaseTemplate
{
    public function render(): string
    {
        $canExport = $this->get('canExport', false);
        
        return $this->renderStatisticsCards() . 
               $this->renderLogsTabs($canExport) . 
               $this->renderPageScript($canExport);
    }

    private function renderStatisticsCards(): string
    {
        $systemLogsCard = Card::stat([
            'icon' => 'fas fa-server',
            'iconColor' => 'bg-blue-500',
            'title' => 'System Logs',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('system-logs-card');

        $apiLogsCard = Card::stat([
            'icon' => 'fas fa-plug',
            'iconColor' => 'bg-green-500',
            'title' => 'API Logs',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('api-logs-card');

        $errorsCard = Card::stat([
            'icon' => 'fas fa-exclamation-triangle',
            'iconColor' => 'bg-red-500',
            'title' => 'Errors (24h)',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('errors-card');

        $avgResponseCard = Card::stat([
            'icon' => 'fas fa-clock',
            'iconColor' => 'bg-purple-500',
            'title' => 'Avg Response',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('avg-response-card');

        return <<<HTML
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {$systemLogsCard->render()}
            {$apiLogsCard->render()}
            {$errorsCard->render()}
            {$avgResponseCard->render()}
        </div>
HTML;
    }

    private function renderLogsTabs(bool $canExport): string
    {
        $logsCard = new Card([
            'content' => $this->getLogsTabsContent($canExport)
        ]);

        return $logsCard->render();
    }

    private function getLogsTabsContent(bool $canExport): string
    {
        $exportButton = $canExport ? 
            Button::secondary('Export Logs', [
                'icon' => 'fas fa-download',
                'onclick' => 'showExportModal()',
                'id' => 'exportLogsBtn'
            ])->render() : '';

        return <<<HTML
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex">
                <button id="systemLogsTab" class="py-2 px-4 border-b-2 border-blue-500 text-blue-600 font-medium text-sm">
                    System Logs
                </button>
                <button id="apiLogsTab" class="py-2 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm ml-8">
                    API Logs
                </button>
            </nav>
        </div>

        <!-- System Logs Tab Content -->
        <div id="systemLogsContent" class="p-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 mb-6">
                <div class="flex-1 max-w-lg">
                    <div class="relative">
                        <input 
                            type="text" 
                            id="systemLogsSearch" 
                            placeholder="Search logs..."
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <select id="systemLogsType" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="all">All Types</option>
                        <option value="api">API</option>
                        <option value="admin">Admin</option>
                        <option value="error">Error</option>
                    </select>
                    
                    <select id="systemLogsLevel" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="all">All Levels</option>
                        <option value="info">Info</option>
                        <option value="warning">Warning</option>
                        <option value="error">Error</option>
                    </select>
                    
                    <input type="date" id="systemLogsDateFrom" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <input type="date" id="systemLogsDateTo" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    
                    {$this->component('button', [
                        'text' => 'Filter',
                        'icon' => 'fas fa-filter',
                        'variant' => 'primary',
                        'onclick' => 'filterSystemLogs()',
                        'id' => 'filterSystemLogsBtn'
                    ])}
                    
                    {$exportButton}
                </div>
            </div>
            
            <div id="systemLogsTable" class="overflow-x-auto">
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                    <p class="text-gray-500">Loading system logs...</p>
                </div>
            </div>
            
            <!-- System Logs Pagination -->
            <div id="systemLogsPagination" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing <span id="systemLogsFrom">0</span> to <span id="systemLogsTo">0</span> of <span id="systemLogsTotal">0</span> results
                </div>
                <div id="systemLogsPaginationButtons" class="flex space-x-2">
                    <!-- Pagination buttons will be inserted here -->
                </div>
            </div>
        </div>

        <!-- API Logs Tab Content -->
        <div id="apiLogsContent" class="p-6 hidden">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 mb-6">
                <div class="flex-1 max-w-lg">
                    <div class="relative">
                        <input 
                            type="text" 
                            id="apiLogsSearch" 
                            placeholder="Search API logs..."
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <select id="apiLogsStatus" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="all">All Status</option>
                        <option value="success">Success</option>
                        <option value="error">Error</option>
                    </select>
                    
                    <input type="date" id="apiLogsDateFrom" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <input type="date" id="apiLogsDateTo" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    
                    {$this->component('button', [
                        'text' => 'Filter',
                        'icon' => 'fas fa-filter',
                        'variant' => 'primary',
                        'onclick' => 'filterApiLogs()',
                        'id' => 'filterApiLogsBtn'
                    ])}
                </div>
            </div>
            
            <div id="apiLogsTable" class="overflow-x-auto">
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                    <p class="text-gray-500">Loading API logs...</p>
                </div>
            </div>
            
            <!-- API Logs Pagination -->
            <div id="apiLogsPagination" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing <span id="apiLogsFrom">0</span> to <span id="apiLogsTo">0</span> of <span id="apiLogsTotal">0</span> results
                </div>
                <div id="apiLogsPaginationButtons" class="flex space-x-2">
                    <!-- Pagination buttons will be inserted here -->
                </div>
            </div>
        </div>

        <!-- Export Modal -->
        <div id="exportModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Export Logs</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeExportModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Log Type</label>
                            <select id="exportLogType" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="system">System Logs</option>
                                <option value="api">API Logs</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Format</label>
                            <select id="exportFormat" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="csv">CSV</option>
                                <option value="json">JSON</option>
                            </select>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                                <input type="date" id="exportDateFrom" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                                <input type="date" id="exportDateTo" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Limit</label>
                            <select id="exportLimit" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="1000">1,000 records</option>
                                <option value="5000">5,000 records</option>
                                <option value="10000">10,000 records</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    {$this->component('button', [
                        'text' => 'Cancel',
                        'variant' => 'outline',
                        'onclick' => 'closeExportModal()'
                    ])}
                    {$this->component('button', [
                        'text' => 'Export',
                        'variant' => 'primary',
                        'onclick' => 'performExport()'
                    ])}
                </div>
            </div>
        </div>
HTML;
    }

    private function renderPageScript(bool $canExport): string
    {
        $canExportJs = $canExport ? 'true' : 'false';
        
        return <<<HTML
        <script>
            class LogsManager {
                constructor() {
                    this.currentTab = 'system';
                    this.systemLogsPage = 1;
                    this.apiLogsPage = 1;
                    this.canExport = {$canExportJs};
                    this.init();
                }

                init() {
                    this.loadLogStatistics();
                    this.loadSystemLogs();
                    this.bindEvents();
                }

                bindEvents() {
                    // Tab switching
                    document.getElementById('systemLogsTab').addEventListener('click', () => {
                        this.showTab('system');
                    });

                    document.getElementById('apiLogsTab').addEventListener('click', () => {
                        this.showTab('api');
                        if (this.currentTab !== 'api') {
                            this.loadApiLogs();
                        }
                    });

                    // System logs search and filters
                    document.getElementById('systemLogsSearch').addEventListener('input', this.debounce(() => {
                        this.systemLogsPage = 1;
                        this.loadSystemLogs();
                    }, 500));

                    document.getElementById('filterSystemLogsBtn').addEventListener('click', () => {
                        this.systemLogsPage = 1;
                        this.loadSystemLogs();
                    });

                    // API logs search and filters
                    document.getElementById('apiLogsSearch').addEventListener('input', this.debounce(() => {
                        this.apiLogsPage = 1;
                        this.loadApiLogs();
                    }, 500));

                    document.getElementById('filterApiLogsBtn').addEventListener('click', () => {
                        this.apiLogsPage = 1;
                        this.loadApiLogs();
                    });
                }

                showTab(tab) {
                    this.currentTab = tab;
                    
                    // Update tab buttons
                    document.getElementById('systemLogsTab').className = tab === 'system' ? 
                        'py-2 px-4 border-b-2 border-blue-500 text-blue-600 font-medium text-sm' :
                        'py-2 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm ml-8';
                        
                    document.getElementById('apiLogsTab').className = tab === 'api' ? 
                        'py-2 px-4 border-b-2 border-blue-500 text-blue-600 font-medium text-sm ml-8' :
                        'py-2 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm ml-8';
                    
                    // Show/hide content
                    document.getElementById('systemLogsContent').classList.toggle('hidden', tab !== 'system');
                    document.getElementById('apiLogsContent').classList.toggle('hidden', tab !== 'api');
                }

                async loadLogStatistics() {
                    try {
                        const response = await fetch('/admin/api/logs/statistics');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateStatistics(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading log statistics:', error);
                    }
                }

                async loadSystemLogs() {
                    try {
                        const params = new URLSearchParams({
                            page: this.systemLogsPage,
                            limit: 50,
                            search: document.getElementById('systemLogsSearch').value,
                            type: document.getElementById('systemLogsType').value,
                            level: document.getElementById('systemLogsLevel').value,
                            date_from: document.getElementById('systemLogsDateFrom').value,
                            date_to: document.getElementById('systemLogsDateTo').value
                        });

                        const response = await fetch('/admin/api/logs/system?' + params);
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateSystemLogsTable(data.data, data.pagination);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading system logs:', error);
                    }
                }

                async loadApiLogs() {
                    try {
                        const params = new URLSearchParams({
                            page: this.apiLogsPage,
                            limit: 50,
                            search: document.getElementById('apiLogsSearch').value,
                            status: document.getElementById('apiLogsStatus').value,
                            date_from: document.getElementById('apiLogsDateFrom').value,
                            date_to: document.getElementById('apiLogsDateTo').value
                        });

                        const response = await fetch('/admin/api/logs/api?' + params);
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateApiLogsTable(data.data, data.pagination);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading API logs:', error);
                    }
                }

                updateStatistics(stats) {
                    this.updateStatCard('.system-logs-card', stats.system_logs.total || 0, stats.system_logs.last_24h + ' in 24h');
                    this.updateStatCard('.api-logs-card', stats.api_logs.total || 0, stats.api_logs.last_24h + ' in 24h');
                    this.updateStatCard('.errors-card', (stats.system_logs.errors + stats.api_logs.errors) || 0);
                    this.updateStatCard('.avg-response-card', (stats.api_logs.avg_response_time || 0) + 'ms');
                }

                updateStatCard(selector, value, subtitle = '') {
                    const card = document.querySelector(selector);
                    if (card) {
                        const valueElement = card.querySelector('.text-2xl');
                        const subtitleElement = card.querySelector('.text-sm.text-gray-600');
                        if (valueElement) {
                            valueElement.textContent = value;
                        }
                        if (subtitleElement && subtitle) {
                            subtitleElement.textContent = subtitle;
                        }
                    }
                }

                updateSystemLogsTable(logs, pagination) {
                    const container = document.getElementById('systemLogsTable');

                    if (logs.length === 0) {
                        container.innerHTML = '<p class="text-gray-500 text-center py-8">No system logs found</p>';
                        return;
                    }

                    let html = `
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Level</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Message</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">User</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                    `;

                    logs.forEach(log => {
                        const levelColor = log.level === 'error' ? 'text-red-600 bg-red-100' : 
                                         log.level === 'warning' ? 'text-yellow-600 bg-yellow-100' : 'text-blue-600 bg-blue-100';

                        html += `
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">\${log.formatted_time}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \${levelColor}">
                                        \${log.level}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">\${log.type}</td>
                                <td class="px-6 py-4 text-sm text-gray-900">\${log.message}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">\${log.user_email || 'System'}</td>
                            </tr>
                        `;
                    });

                    html += '</tbody></table>';
                    container.innerHTML = html;

                    this.updatePagination('systemLogs', pagination);
                }

                updateApiLogsTable(logs, pagination) {
                    const container = document.getElementById('apiLogsTable');

                    if (logs.length === 0) {
                        container.innerHTML = '<p class="text-gray-500 text-center py-8">No API logs found</p>';
                        return;
                    }

                    let html = `
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">IP</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Response Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Plugin</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                    `;

                    logs.forEach(log => {
                        html += `
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">\${log.formatted_time}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">\${log.ip}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \${log.status_class}">
                                        \${log.response_status}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">\${log.response_time_ms}ms</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">\${log.plugin_id || 'Unknown'}</td>
                            </tr>
                        `;
                    });

                    html += '</tbody></table>';
                    container.innerHTML = html;

                    this.updatePagination('apiLogs', pagination);
                }

                updatePagination(type, pagination) {
                    const fromElement = document.getElementById(type + 'From');
                    const toElement = document.getElementById(type + 'To');
                    const totalElement = document.getElementById(type + 'Total');
                    const buttonsContainer = document.getElementById(type + 'PaginationButtons');

                    fromElement.textContent = ((pagination.current_page - 1) * pagination.per_page) + 1;
                    toElement.textContent = Math.min(pagination.current_page * pagination.per_page, pagination.total);
                    totalElement.textContent = pagination.total;

                    let html = '';

                    if (pagination.current_page > 1) {
                        html += `<button onclick="logsManager.changePage('${type}', \${pagination.current_page - 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</button>`;
                    }

                    if (pagination.current_page < pagination.total_pages) {
                        html += `<button onclick="logsManager.changePage('${type}', \${pagination.current_page + 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>`;
                    }

                    buttonsContainer.innerHTML = html;
                }

                changePage(type, page) {
                    if (type === 'systemLogs') {
                        this.systemLogsPage = page;
                        this.loadSystemLogs();
                    } else if (type === 'apiLogs') {
                        this.apiLogsPage = page;
                        this.loadApiLogs();
                    }
                }

                debounce(func, wait) {
                    let timeout;
                    return function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeout);
                            func(...args);
                        };
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                    };
                }
            }

            // Global functions
            function showExportModal() {
                document.getElementById('exportModal').classList.remove('hidden');
            }

            function closeExportModal() {
                document.getElementById('exportModal').classList.add('hidden');
            }

            function performExport() {
                const logType = document.getElementById('exportLogType').value;
                const format = document.getElementById('exportFormat').value;
                const dateFrom = document.getElementById('exportDateFrom').value;
                const dateTo = document.getElementById('exportDateTo').value;
                const limit = document.getElementById('exportLimit').value;

                const params = new URLSearchParams({
                    type: logType,
                    format: format,
                    date_from: dateFrom,
                    date_to: dateTo,
                    limit: limit
                });

                window.open('/admin/api/logs/export?' + params, '_blank');
                closeExportModal();
            }

            // Initialize logs manager when DOM is loaded
            let logsManager;
            document.addEventListener('DOMContentLoaded', function() {
                logsManager = new LogsManager();
            });
        </script>
HTML;
    }
}