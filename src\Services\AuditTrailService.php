<?php

namespace Skpassegna\GuardgeoApi\Services;

use DateTime;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Database\DatabaseException;

/**
 * Audit Trail Service
 * 
 * Provides comprehensive audit trail functionality for tracking all system activities,
 * user actions, and data changes with detailed attribution and context.
 */
class AuditTrailService
{
    private LoggingService $loggingService;
    
    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }
    
    /**
     * Record user action in audit trail
     */
    public function recordUserAction(int $userId, string $action, string $resource, array $details = []): void
    {
        $auditData = [
            'user_id' => $userId,
            'action' => $action,
            'resource' => $resource,
            'details' => $details,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'session_id' => session_id() ?: null,
            'timestamp' => (new DateTime())->format('c')
        ];
        
        $this->loggingService->logAdminAction($userId, $action, $auditData);
    }
    
    /**
     * Record data modification
     */
    public function recordDataModification(int $userId, string $table, string $operation, array $oldData = [], array $newData = [], string $recordId = null): void
    {
        $changes = $this->calculateChanges($oldData, $newData);
        
        $auditData = [
            'table' => $table,
            'operation' => $operation,
            'record_id' => $recordId,
            'changes' => $changes,
            'old_data' => $this->sanitizeData($oldData),
            'new_data' => $this->sanitizeData($newData),
            'change_count' => count($changes)
        ];
        
        $this->recordUserAction($userId, "data_{$operation}", $table, $auditData);
    }
    
    /**
     * Record login attempt
     */
    public function recordLoginAttempt(string $email, bool $success, string $reason = '', array $additionalData = []): void
    {
        $auditData = array_merge($additionalData, [
            'email' => $email,
            'success' => $success,
            'reason' => $reason,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'timestamp' => (new DateTime())->format('c')
        ]);
        
        $this->loggingService->getLogger(LoggingService::TYPE_ADMIN)
            ->logLoginAttempt($email, $success, $reason);
        
        // Also store in audit trail
        $this->storeAuditRecord('authentication', 'login_attempt', $auditData);
    }
    
    /**
     * Record permission check
     */
    public function recordPermissionCheck(int $userId, string $permission, string $resource, bool $granted, array $context = []): void
    {
        $auditData = [
            'user_id' => $userId,
            'permission' => $permission,
            'resource' => $resource,
            'granted' => $granted,
            'context' => $context,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->recordUserAction($userId, 'permission_check', $resource, $auditData);
    }
    
    /**
     * Record security event
     */
    public function recordSecurityEvent(string $event, string $severity, array $details = [], int $userId = null): void
    {
        $auditData = [
            'event' => $event,
            'severity' => $severity,
            'details' => $details,
            'user_id' => $userId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'timestamp' => (new DateTime())->format('c')
        ];
        
        $this->loggingService->getLogger(LoggingService::TYPE_ERROR)
            ->logSecurityIncident($event, $severity, $auditData);
        
        // Store in audit trail
        $this->storeAuditRecord('security', $event, $auditData);
    }
    
    /**
     * Record API access
     */
    public function recordApiAccess(array $requestData, array $responseData, int $responseTime = null): void
    {
        $auditData = [
            'endpoint' => '/api/analyze',
            'method' => 'POST',
            'ip' => $requestData['ip'] ?? null,
            'plugin_id' => $requestData['plugin_id'] ?? null,
            'install_id' => $requestData['install_id'] ?? null,
            'response_status' => $responseData['status'] ?? null,
            'response_time_ms' => $responseTime,
            'freemius_valid' => $responseData['freemius_valid'] ?? null,
            'timestamp' => (new DateTime())->format('c')
        ];
        
        $this->storeAuditRecord('api', 'request', $auditData);
    }
    
    /**
     * Record configuration change
     */
    public function recordConfigurationChange(int $userId, string $setting, $oldValue, $newValue, array $context = []): void
    {
        $auditData = [
            'setting' => $setting,
            'old_value' => $this->sanitizeConfigValue($setting, $oldValue),
            'new_value' => $this->sanitizeConfigValue($setting, $newValue),
            'context' => $context
        ];
        
        $this->recordUserAction($userId, 'configuration_change', 'system_config', $auditData);
    }
    
    /**
     * Record bulk operation
     */
    public function recordBulkOperation(int $userId, string $operation, string $resource, array $targetIds, array $results = []): void
    {
        $auditData = [
            'operation' => $operation,
            'resource' => $resource,
            'target_ids' => $targetIds,
            'target_count' => count($targetIds),
            'results' => $results,
            'success_count' => count(array_filter($results, fn($r) => $r['success'] ?? false)),
            'failure_count' => count(array_filter($results, fn($r) => !($r['success'] ?? true)))
        ];
        
        $this->recordUserAction($userId, "bulk_{$operation}", $resource, $auditData);
    }
    
    /**
     * Record export operation
     */
    public function recordExport(int $userId, string $dataType, array $filters, int $recordCount, string $format = 'csv'): void
    {
        $auditData = [
            'data_type' => $dataType,
            'filters' => $filters,
            'record_count' => $recordCount,
            'format' => $format,
            'file_size' => null // Could be populated if file is generated
        ];
        
        $this->recordUserAction($userId, 'data_export', $dataType, $auditData);
    }
    
    /**
     * Get audit trail for specific user
     */
    public function getUserAuditTrail(int $userId, DateTime $fromDate = null, DateTime $toDate = null, int $limit = 100, int $offset = 0): array
    {
        try {
            $sql = "SELECT sl.*, au.email as user_email, au.role as user_role
                    FROM system_logs sl
                    LEFT JOIN admin_users au ON sl.user_id = au.id
                    WHERE sl.user_id = ? AND sl.type = 'admin'";
            
            $params = [$userId];
            
            if ($fromDate) {
                $sql .= " AND sl.created_at >= ?";
                $params[] = $fromDate->format('Y-m-d H:i:s');
            }
            
            if ($toDate) {
                $sql .= " AND sl.created_at <= ?";
                $params[] = $toDate->format('Y-m-d H:i:s');
            }
            
            $sql .= " ORDER BY sl.created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $statement = DatabaseConnection::execute($sql, $params);
            $records = $statement->fetchAll();
            
            // Decode context JSON for each record
            foreach ($records as &$record) {
                $record['context'] = json_decode($record['context'], true);
            }
            
            return $records;
            
        } catch (DatabaseException $e) {
            $this->loggingService->getLogger(LoggingService::TYPE_ERROR)
                ->error('Failed to retrieve user audit trail', [
                    'user_id' => $userId,
                    'error' => $e->getMessage()
                ]);
            
            return [];
        }
    }
    
    /**
     * Get audit trail for specific resource
     */
    public function getResourceAuditTrail(string $resource, string $resourceId = null, DateTime $fromDate = null, DateTime $toDate = null, int $limit = 100): array
    {
        try {
            $sql = "SELECT sl.*, au.email as user_email, au.role as user_role
                    FROM system_logs sl
                    LEFT JOIN admin_users au ON sl.user_id = au.id
                    WHERE sl.context::text LIKE ?";
            
            $searchPattern = '%"resource":"' . $resource . '"%';
            $params = [$searchPattern];
            
            if ($resourceId) {
                $sql .= " AND sl.context::text LIKE ?";
                $params[] = '%"record_id":"' . $resourceId . '"%';
            }
            
            if ($fromDate) {
                $sql .= " AND sl.created_at >= ?";
                $params[] = $fromDate->format('Y-m-d H:i:s');
            }
            
            if ($toDate) {
                $sql .= " AND sl.created_at <= ?";
                $params[] = $toDate->format('Y-m-d H:i:s');
            }
            
            $sql .= " ORDER BY sl.created_at DESC LIMIT ?";
            $params[] = $limit;
            
            $statement = DatabaseConnection::execute($sql, $params);
            $records = $statement->fetchAll();
            
            // Decode context JSON for each record
            foreach ($records as &$record) {
                $record['context'] = json_decode($record['context'], true);
            }
            
            return $records;
            
        } catch (DatabaseException $e) {
            $this->loggingService->getLogger(LoggingService::TYPE_ERROR)
                ->error('Failed to retrieve resource audit trail', [
                    'resource' => $resource,
                    'resource_id' => $resourceId,
                    'error' => $e->getMessage()
                ]);
            
            return [];
        }
    }
    
    /**
     * Get security audit trail
     */
    public function getSecurityAuditTrail(DateTime $fromDate = null, DateTime $toDate = null, string $severity = null, int $limit = 100): array
    {
        try {
            $sql = "SELECT sl.*, au.email as user_email
                    FROM system_logs sl
                    LEFT JOIN admin_users au ON sl.user_id = au.id
                    WHERE sl.type = 'error' AND sl.context::text LIKE '%security%'";
            
            $params = [];
            
            if ($severity) {
                $sql .= " AND sl.context::text LIKE ?";
                $params[] = '%"severity":"' . $severity . '"%';
            }
            
            if ($fromDate) {
                $sql .= " AND sl.created_at >= ?";
                $params[] = $fromDate->format('Y-m-d H:i:s');
            }
            
            if ($toDate) {
                $sql .= " AND sl.created_at <= ?";
                $params[] = $toDate->format('Y-m-d H:i:s');
            }
            
            $sql .= " ORDER BY sl.created_at DESC LIMIT ?";
            $params[] = $limit;
            
            $statement = DatabaseConnection::execute($sql, $params);
            $records = $statement->fetchAll();
            
            // Decode context JSON for each record
            foreach ($records as &$record) {
                $record['context'] = json_decode($record['context'], true);
            }
            
            return $records;
            
        } catch (DatabaseException $e) {
            $this->loggingService->getLogger(LoggingService::TYPE_ERROR)
                ->error('Failed to retrieve security audit trail', [
                    'error' => $e->getMessage()
                ]);
            
            return [];
        }
    }
    
    /**
     * Get audit statistics
     */
    public function getAuditStatistics(DateTime $fromDate = null, DateTime $toDate = null): array
    {
        try {
            $sql = "SELECT 
                        type,
                        level,
                        COUNT(*) as count,
                        COUNT(DISTINCT user_id) as unique_users
                    FROM system_logs 
                    WHERE 1=1";
            
            $params = [];
            
            if ($fromDate) {
                $sql .= " AND created_at >= ?";
                $params[] = $fromDate->format('Y-m-d H:i:s');
            }
            
            if ($toDate) {
                $sql .= " AND created_at <= ?";
                $params[] = $toDate->format('Y-m-d H:i:s');
            }
            
            $sql .= " GROUP BY type, level ORDER BY count DESC";
            
            $statement = DatabaseConnection::execute($sql, $params);
            $stats = $statement->fetchAll();
            
            // Get user activity stats
            $userStatsSql = "SELECT 
                                user_id,
                                COUNT(*) as action_count,
                                MAX(created_at) as last_activity
                            FROM system_logs 
                            WHERE user_id IS NOT NULL";
            
            if ($fromDate) {
                $userStatsSql .= " AND created_at >= ?";
            }
            
            if ($toDate) {
                $userStatsSql .= " AND created_at <= ?";
            }
            
            $userStatsSql .= " GROUP BY user_id ORDER BY action_count DESC LIMIT 10";
            
            $userStatsStmt = DatabaseConnection::execute($userStatsSql, $params);
            $userStats = $userStatsStmt->fetchAll();
            
            return [
                'activity_by_type_level' => $stats,
                'top_active_users' => $userStats,
                'period' => [
                    'from' => $fromDate ? $fromDate->format('c') : null,
                    'to' => $toDate ? $toDate->format('c') : null
                ],
                'generated_at' => (new DateTime())->format('c')
            ];
            
        } catch (DatabaseException $e) {
            $this->loggingService->getLogger(LoggingService::TYPE_ERROR)
                ->error('Failed to retrieve audit statistics', [
                    'error' => $e->getMessage()
                ]);
            
            return ['error' => 'Failed to retrieve statistics'];
        }
    }
    
    /**
     * Store audit record in database
     */
    private function storeAuditRecord(string $category, string $action, array $data): void
    {
        try {
            $sql = "INSERT INTO system_logs (type, level, message, context, ip, user_id, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
            
            $message = sprintf('%s: %s', $category, $action);
            $params = [
                'audit',
                'info',
                $message,
                json_encode($data, JSON_UNESCAPED_SLASHES),
                $data['ip_address'] ?? null,
                $data['user_id'] ?? null
            ];
            
            DatabaseConnection::execute($sql, $params);
            
        } catch (DatabaseException $e) {
            // Log to file if database logging fails
            error_log("AuditTrailService: Failed to store audit record - " . $e->getMessage());
        }
    }
    
    /**
     * Calculate changes between old and new data
     */
    private function calculateChanges(array $oldData, array $newData): array
    {
        $changes = [];
        
        // Find modified and new fields
        foreach ($newData as $key => $newValue) {
            $oldValue = $oldData[$key] ?? null;
            
            if ($oldValue !== $newValue) {
                $changes[$key] = [
                    'old' => $this->sanitizeValue($oldValue),
                    'new' => $this->sanitizeValue($newValue),
                    'type' => $oldValue === null ? 'added' : 'modified'
                ];
            }
        }
        
        // Find removed fields
        foreach ($oldData as $key => $oldValue) {
            if (!array_key_exists($key, $newData)) {
                $changes[$key] = [
                    'old' => $this->sanitizeValue($oldValue),
                    'new' => null,
                    'type' => 'removed'
                ];
            }
        }
        
        return $changes;
    }
    
    /**
     * Sanitize data for audit logging
     */
    private function sanitizeData(array $data): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            $sanitized[$key] = $this->sanitizeValue($value);
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize individual value
     */
    private function sanitizeValue($value): string
    {
        if ($value === null) {
            return 'null';
        }
        
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }
        
        if (is_array($value) || is_object($value)) {
            return json_encode($value);
        }
        
        $stringValue = (string)$value;
        
        // Truncate long values
        if (strlen($stringValue) > 200) {
            return substr($stringValue, 0, 197) . '...';
        }
        
        return $stringValue;
    }
    
    /**
     * Sanitize configuration values
     */
    private function sanitizeConfigValue(string $setting, $value): string
    {
        // Hide sensitive configuration values
        $sensitiveSettings = ['password', 'key', 'secret', 'token', 'credential'];
        
        foreach ($sensitiveSettings as $sensitive) {
            if (stripos($setting, $sensitive) !== false) {
                return '[REDACTED]';
            }
        }
        
        return $this->sanitizeValue($value);
    }
}