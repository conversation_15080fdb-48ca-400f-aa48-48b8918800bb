<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Services\AuthService;
use Skpassegna\GuardgeoApi\Utils\AuthMiddleware;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;
use Skpassegna\GuardgeoApi\Utils\RequestValidator;
use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Authentication Controller
 * 
 * Handles admin authentication operations including login, logout,
 * and session management.
 */
class AuthController
{
    private AuthService $authService;
    private AuthMiddleware $authMiddleware;
    private ResponseFormatter $responseFormatter;
    private RequestValidator $requestValidator;
    private LoggingService $logger;

    public function __construct(
        AuthService $authService,
        AuthMiddleware $authMiddleware,
        ResponseFormatter $responseFormatter,
        RequestValidator $requestValidator,
        LoggingService $logger
    ) {
        $this->authService = $authService;
        $this->authMiddleware = $authMiddleware;
        $this->responseFormatter = $responseFormatter;
        $this->requestValidator = $requestValidator;
        $this->logger = $logger;
    }

    /**
     * Display login form
     *
     * @return void
     */
    public function showLoginForm(): void
    {
        // Check if already authenticated
        if ($this->authMiddleware->isAuthenticated()) {
            $this->redirect('/admin/dashboard');
            return;
        }

        $this->renderLoginPage();
    }

    /**
     * Handle login request
     *
     * @return void
     */
    public function login(): void
    {
        try {
            // Check if already authenticated
            if ($this->authMiddleware->isAuthenticated()) {
                $this->sendJsonResponse([
                    'success' => true,
                    'redirect' => '/admin/dashboard'
                ]);
                return;
            }

            // Validate request method
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Method not allowed'
                ], 405);
                return;
            }

            // Get request data
            $requestData = $this->getRequestData();

            // Validate required fields
            $validation = $this->requestValidator->validate($requestData, [
                'email' => 'required|email',
                'password' => 'required|min:1'
            ]);

            if (!$validation['valid']) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Validation failed',
                    'errors' => $validation['errors']
                ], 400);
                return;
            }

            // Validate CSRF token
            if (!$this->authMiddleware->validateCsrfToken($requestData['csrf_token'] ?? null)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Invalid CSRF token'
                ], 403);
                return;
            }

            // Attempt authentication
            $result = $this->authService->authenticate(
                $requestData['email'],
                $requestData['password']
            );

            if ($result['success']) {
                // Determine redirect URL
                $redirectUrl = $requestData['redirect'] ?? '/admin/dashboard';
                
                // Validate redirect URL to prevent open redirects
                if (!$this->isValidRedirectUrl($redirectUrl)) {
                    $redirectUrl = '/admin/dashboard';
                }

                $this->sendJsonResponse([
                    'success' => true,
                    'message' => 'Login successful',
                    'redirect' => $redirectUrl,
                    'user' => [
                        'email' => $result['user']['email'],
                        'role' => $result['user']['role']
                    ]
                ]);
            } else {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => $result['error']
                ], 401);
            }

        } catch (\Exception $e) {
            $this->logger->logError('Login error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'An error occurred during login'
            ], 500);
        }
    }

    /**
     * Handle logout request
     *
     * @return void
     */
    public function logout(): void
    {
        try {
            // Logout user
            $success = $this->authService->logout();

            if ($this->isAjaxRequest()) {
                $this->sendJsonResponse([
                    'success' => $success,
                    'message' => 'Logged out successfully',
                    'redirect' => '/admin/login'
                ]);
            } else {
                $this->redirect('/admin/login');
            }

        } catch (\Exception $e) {
            $this->logger->logError('Logout error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($this->isAjaxRequest()) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'An error occurred during logout'
                ], 500);
            } else {
                $this->redirect('/admin/login?error=logout_failed');
            }
        }
    }

    /**
     * Check authentication status (AJAX endpoint)
     *
     * @return void
     */
    public function checkAuth(): void
    {
        $user = $this->authMiddleware->getCurrentUser();

        if ($user) {
            $this->sendJsonResponse([
                'success' => true,
                'authenticated' => true,
                'user' => [
                    'id' => $user['id'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ]
            ]);
        } else {
            $this->sendJsonResponse([
                'success' => true,
                'authenticated' => false
            ]);
        }
    }

    /**
     * Get CSRF token (AJAX endpoint)
     *
     * @return void
     */
    public function getCsrfToken(): void
    {
        $sessionManager = new \Skpassegna\GuardgeoApi\Utils\SessionManager();
        $sessionManager->startSession();
        
        $token = $sessionManager->getCsrfToken();

        $this->sendJsonResponse([
            'success' => true,
            'csrf_token' => $token
        ]);
    }

    /**
     * Render login page
     *
     * @return void
     */
    private function renderLoginPage(): void
    {
        $sessionManager = new \Skpassegna\GuardgeoApi\Utils\SessionManager();
        $sessionManager->startSession();
        $csrfToken = $sessionManager->getCsrfToken();
        
        $redirectUrl = $_GET['redirect'] ?? '';
        $error = $_GET['error'] ?? '';

        header('Content-Type: text/html; charset=utf-8');
        
        echo $this->getLoginPageHtml($csrfToken, $redirectUrl, $error);
    }

    /**
     * Get login page HTML
     *
     * @param string $csrfToken
     * @param string $redirectUrl
     * @param string $error
     * @return string
     */
    private function getLoginPageHtml(string $csrfToken, string $redirectUrl, string $error): string
    {
        $errorMessage = '';
        if ($error) {
            $errorMessages = [
                'invalid_credentials' => 'Invalid email or password',
                'account_inactive' => 'Your account is inactive',
                'domain_not_authorized' => 'Your email domain is not authorized',
                'logout_failed' => 'An error occurred during logout'
            ];
            $errorMessage = $errorMessages[$error] ?? 'An error occurred';
        }

        return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GuardGeo Admin - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full space-y-8 p-8">
        <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-900">GuardGeo Admin</h1>
            <p class="mt-2 text-gray-600">Sign in to your account</p>
        </div>
        
        <form id="loginForm" class="mt-8 space-y-6" method="POST" action="/admin/login">
            <input type="hidden" name="csrf_token" value="{$csrfToken}">
            <input type="hidden" name="redirect" value="{$redirectUrl}">
            
            <div class="space-y-4">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                    <input 
                        id="email" 
                        name="email" 
                        type="email" 
                        required 
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter your email"
                    >
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                    <input 
                        id="password" 
                        name="password" 
                        type="password" 
                        required 
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter your password"
                    >
                </div>
            </div>
            
            <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {$errorMessage}
            </div>
            
            <div>
                <button 
                    type="submit" 
                    id="loginButton"
                    class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <span id="loginButtonText">Sign in</span>
                    <span id="loginSpinner" class="hidden ml-2">
                        <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </span>
                </button>
            </div>
        </form>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const button = document.getElementById('loginButton');
            const buttonText = document.getElementById('loginButtonText');
            const spinner = document.getElementById('loginSpinner');
            const errorDiv = document.getElementById('errorMessage');
            
            // Show loading state
            button.disabled = true;
            buttonText.textContent = 'Signing in...';
            spinner.classList.remove('hidden');
            errorDiv.classList.add('hidden');
            
            try {
                const formData = new FormData(this);
                const response = await fetch('/admin/login', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    window.location.href = result.redirect || '/admin/dashboard';
                } else {
                    errorDiv.textContent = result.error || 'Login failed';
                    errorDiv.classList.remove('hidden');
                }
            } catch (error) {
                errorDiv.textContent = 'An error occurred. Please try again.';
                errorDiv.classList.remove('hidden');
            } finally {
                // Reset loading state
                button.disabled = false;
                buttonText.textContent = 'Sign in';
                spinner.classList.add('hidden');
            }
        });
        
        // Show error message if present
        const errorMessage = '{$errorMessage}';
        if (errorMessage) {
            document.getElementById('errorMessage').classList.remove('hidden');
        }
    </script>
</body>
</html>
HTML;
    }

    /**
     * Get request data from POST or JSON
     *
     * @return array
     */
    private function getRequestData(): array
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        if (strpos($contentType, 'application/json') !== false) {
            $json = file_get_contents('php://input');
            return json_decode($json, true) ?: [];
        }
        
        return $_POST;
    }

    /**
     * Check if request is AJAX
     *
     * @return bool
     */
    private function isAjaxRequest(): bool
    {
        return (
            !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest'
        ) || (
            !empty($_SERVER['HTTP_ACCEPT']) &&
            strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false
        );
    }

    /**
     * Send JSON response
     *
     * @param array $data
     * @param int $statusCode
     * @return void
     */
    private function sendJsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Redirect to URL
     *
     * @param string $url
     * @return void
     */
    private function redirect(string $url): void
    {
        header("Location: {$url}");
        exit;
    }

    /**
     * Validate redirect URL to prevent open redirects
     *
     * @param string $url
     * @return bool
     */
    private function isValidRedirectUrl(string $url): bool
    {
        // Only allow relative URLs starting with /admin/
        return preg_match('/^\/admin\/[a-zA-Z0-9\-_\/]*$/', $url);
    }
}