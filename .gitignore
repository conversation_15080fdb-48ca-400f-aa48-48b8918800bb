### Universal ###
# $ git config --global mergetool.keepBackup false
*.orig

# Created by git when using merge tools for conflicts
*.BACKUP.*
*.BASE.*
*.LOCAL.*
*.REMOTE.*
*_BACKUP_*.txt
*_BASE_*.txt
*_LOCAL_*.txt
*_REMOTE_*.txt

# Covers PHPUnit
# Reference: https://phpunit.de/

# Generated files
.phpunit.result.cache
.phpunit.cache

# PHPUnit
/app/phpunit.xml
/phpunit.xml

# Build data
/build/

# Node rules:
## Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

## Dependency directory
## Commenting this out is preferred by some people, see
## https://docs.npmjs.com/misc/faq#should-i-check-my-node_modules-folder-into-git
node_modules

# Book build output
_book

# eBook build output
*.epub
*.mobi
*.pdf


# Common files that should be ignored in all projects

# Logs
*.log
# Temporary files
*.tmp
*~

*.bak
# Environment files (containing secrets, API keys, credentials)
.env
*.env
.env.*

# Local configuration that shouldn't be shared
*.local

### Php ###
# php specific files

vendor/
composer.lock

### Composer ###
# PHP Composer package manager files

# Composer (PHP)
vendor/
composer.lock
composer.phar
.composer/

composer.json.bak
composer/
.php_cs.cache
.phpunit.result.cache
composer-setup.php

.composer-update-check
composer-test-cleanup
composer-temp*/
.composer-cache/

# Other

.swagger-mcp
test_*.php