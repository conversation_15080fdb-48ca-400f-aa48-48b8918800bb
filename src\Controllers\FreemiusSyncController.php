<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Services\FreemiusService;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;
use Skpassegna\GuardgeoApi\Utils\Logger;

/**
 * Freemius Synchronization Controller
 * 
 * Handles manual and batch synchronization operations for Freemius data.
 * Used by admin interface for data management.
 */
class FreemiusSyncController
{
    private FreemiusService $freemiusService;
    private ResponseFormatter $responseFormatter;
    private Logger $logger;
    
    /**
     * Constructor
     */
    public function __construct(
        ?FreemiusService $freemiusService = null,
        ?ResponseFormatter $responseFormatter = null
    ) {
        $this->freemiusService = $freemiusService ?? new FreemiusService();
        $this->responseFormatter = $responseFormatter ?? new ResponseFormatter();
        $this->logger = new Logger();
    }
    
    /**
     * Synchronize expired products
     */
    public function syncExpiredProducts(): void
    {
        try {
            $this->logger->info("Manual product synchronization requested");
            
            // Get batch size from request
            $batchSize = (int) ($_GET['batch_size'] ?? 20);
            $batchSize = max(1, min(100, $batchSize)); // Limit between 1 and 100
            
            $result = $this->freemiusService->synchronizeExpiredProducts($batchSize);
            
            $this->responseFormatter->sendSuccess([
                'message' => 'Product synchronization completed',
                'synchronization_result' => $result
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Product synchronization failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->responseFormatter->sendError(
                'Product synchronization failed: ' . $e->getMessage(),
                500,
                'SYNC_ERROR'
            );
        }
    }
    
    /**
     * Synchronize expired installations
     */
    public function syncExpiredInstallations(): void
    {
        try {
            $this->logger->info("Manual installation synchronization requested");
            
            // Get batch size from request
            $batchSize = (int) ($_GET['batch_size'] ?? 50);
            $batchSize = max(1, min(200, $batchSize)); // Limit between 1 and 200
            
            $result = $this->freemiusService->synchronizeExpiredInstallations($batchSize);
            
            $this->responseFormatter->sendSuccess([
                'message' => 'Installation synchronization completed',
                'synchronization_result' => $result
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Installation synchronization failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->responseFormatter->sendError(
                'Installation synchronization failed: ' . $e->getMessage(),
                500,
                'SYNC_ERROR'
            );
        }
    }
    
    /**
     * Comprehensive batch synchronization
     */
    public function batchSyncAll(): void
    {
        try {
            $this->logger->info("Comprehensive batch synchronization requested");
            
            // Parse options from request
            $options = [
                'product_batch_size' => max(1, min(50, (int) ($_GET['product_batch_size'] ?? 20))),
                'installation_batch_size' => max(1, min(100, (int) ($_GET['installation_batch_size'] ?? 50))),
                'include_products' => ($_GET['include_products'] ?? 'true') === 'true',
                'include_installations' => ($_GET['include_installations'] ?? 'true') === 'true',
                'max_execution_time' => max(60, min(600, (int) ($_GET['max_execution_time'] ?? 300))),
                'stop_on_high_failure_rate' => ($_GET['stop_on_high_failure_rate'] ?? 'true') === 'true',
                'failure_rate_threshold' => max(10, min(90, (int) ($_GET['failure_rate_threshold'] ?? 50)))
            ];
            
            $result = $this->freemiusService->batchSynchronizeExpiredData($options);
            
            $this->responseFormatter->sendSuccess([
                'message' => 'Batch synchronization completed',
                'synchronization_result' => $result,
                'options_used' => $options
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Batch synchronization failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->responseFormatter->sendError(
                'Batch synchronization failed: ' . $e->getMessage(),
                500,
                'BATCH_SYNC_ERROR'
            );
        }
    }
    
    /**
     * Synchronize specific product
     */
    public function syncProduct(): void
    {
        try {
            $productId = (int) ($_GET['product_id'] ?? 0);
            
            if ($productId <= 0) {
                $this->responseFormatter->sendError(
                    'Valid product ID is required',
                    400,
                    'INVALID_PRODUCT_ID'
                );
                return;
            }
            
            $this->logger->info("Manual product synchronization requested", [
                'product_id' => $productId
            ]);
            
            $product = $this->freemiusService->synchronizeProduct($productId);
            
            if ($product) {
                $this->responseFormatter->sendSuccess([
                    'message' => 'Product synchronized successfully',
                    'product' => $product->jsonSerialize()
                ]);
            } else {
                $this->responseFormatter->sendError(
                    'Product synchronization failed - product not found or API error',
                    404,
                    'PRODUCT_NOT_FOUND'
                );
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Product synchronization failed", [
                'product_id' => $_GET['product_id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            
            $this->responseFormatter->sendError(
                'Product synchronization failed: ' . $e->getMessage(),
                500,
                'PRODUCT_SYNC_ERROR'
            );
        }
    }
    
    /**
     * Synchronize specific installation
     */
    public function syncInstallation(): void
    {
        try {
            $pluginId = (int) ($_GET['plugin_id'] ?? 0);
            $installId = (int) ($_GET['install_id'] ?? 0);
            
            if ($pluginId <= 0 || $installId <= 0) {
                $this->responseFormatter->sendError(
                    'Valid plugin_id and install_id are required',
                    400,
                    'INVALID_INSTALLATION_IDS'
                );
                return;
            }
            
            $this->logger->info("Manual installation synchronization requested", [
                'plugin_id' => $pluginId,
                'install_id' => $installId
            ]);
            
            $installation = $this->freemiusService->synchronizeInstallation($pluginId, $installId);
            
            if ($installation) {
                $this->responseFormatter->sendSuccess([
                    'message' => 'Installation synchronized successfully',
                    'installation' => $installation->jsonSerialize()
                ]);
            } else {
                $this->responseFormatter->sendError(
                    'Installation synchronization failed - installation not found or API error',
                    404,
                    'INSTALLATION_NOT_FOUND'
                );
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Installation synchronization failed", [
                'plugin_id' => $_GET['plugin_id'] ?? 'unknown',
                'install_id' => $_GET['install_id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            
            $this->responseFormatter->sendError(
                'Installation synchronization failed: ' . $e->getMessage(),
                500,
                'INSTALLATION_SYNC_ERROR'
            );
        }
    }
    
    /**
     * Get synchronization statistics
     */
    public function getSyncStatistics(): void
    {
        try {
            $statistics = $this->freemiusService->getValidationStatistics();
            $healthCheck = $this->freemiusService->healthCheck();
            
            $this->responseFormatter->sendSuccess([
                'statistics' => $statistics,
                'health_check' => $healthCheck,
                'timestamp' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get sync statistics", [
                'error' => $e->getMessage()
            ]);
            
            $this->responseFormatter->sendError(
                'Failed to retrieve synchronization statistics: ' . $e->getMessage(),
                500,
                'STATS_ERROR'
            );
        }
    }
    
    /**
     * Clear expired cache entries
     */
    public function clearExpiredCache(): void
    {
        try {
            $this->logger->info("Manual cache cleanup requested");
            
            $result = $this->freemiusService->clearExpiredCache();
            
            $this->responseFormatter->sendSuccess([
                'message' => 'Cache cleanup completed',
                'cleanup_result' => $result
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Cache cleanup failed", [
                'error' => $e->getMessage()
            ]);
            
            $this->responseFormatter->sendError(
                'Cache cleanup failed: ' . $e->getMessage(),
                500,
                'CACHE_CLEANUP_ERROR'
            );
        }
    }
    
    /**
     * Validate installation manually
     */
    public function validateInstallation(): void
    {
        try {
            $pluginId = (int) ($_GET['plugin_id'] ?? 0);
            $installId = (int) ($_GET['install_id'] ?? 0);
            
            if ($pluginId <= 0 || $installId <= 0) {
                $this->responseFormatter->sendError(
                    'Valid plugin_id and install_id are required',
                    400,
                    'INVALID_INSTALLATION_IDS'
                );
                return;
            }
            
            $this->logger->info("Manual installation validation requested", [
                'plugin_id' => $pluginId,
                'install_id' => $installId
            ]);
            
            $validationResult = $this->freemiusService->validateInstallation($pluginId, $installId);
            
            $this->responseFormatter->sendSuccess([
                'message' => 'Installation validation completed',
                'validation_result' => $validationResult
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Installation validation failed", [
                'plugin_id' => $_GET['plugin_id'] ?? 'unknown',
                'install_id' => $_GET['install_id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            
            $this->responseFormatter->sendError(
                'Installation validation failed: ' . $e->getMessage(),
                500,
                'VALIDATION_ERROR'
            );
        }
    }
}