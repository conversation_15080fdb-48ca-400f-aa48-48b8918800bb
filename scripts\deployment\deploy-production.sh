#!/bin/bash

# GuardGeo Admin Platform - Production Deployment Script
# 
# This script automates the deployment process for production environments.
# Run with: ./scripts/deployment/deploy-production.sh

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
BACKUP_DIR="/var/backups/guardgeo"
LOG_FILE="/var/log/guardgeo/deployment.log"
DEPLOYMENT_ID="$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check required commands
    local required_commands=("php" "composer" "psql" "pg_dump" "git" "curl" "tar" "openssl")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "Required command '$cmd' not found"
        fi
    done
    
    # Check PHP version
    local php_version=$(php -r "echo PHP_VERSION;")
    if [[ $(echo "$php_version" | cut -d. -f1) -lt 8 ]] || [[ $(echo "$php_version" | cut -d. -f2) -lt 1 ]]; then
        error "PHP 8.1 or higher is required. Current version: $php_version"
    fi
    
    # Check required PHP extensions
    local required_extensions=("pdo" "pdo_pgsql" "curl" "json" "mbstring" "openssl" "zip")
    for ext in "${required_extensions[@]}"; do
        if ! php -m | grep -q "^$ext$"; then
            error "Required PHP extension '$ext' not found"
        fi
    done
    
    # Check if .env file exists
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        error ".env file not found. Please create it from the template in config/deployment/production.env.template"
    fi
    
    # Check database connectivity
    if ! php -r "
        require '$PROJECT_ROOT/vendor/autoload.php';
        use Skpassegna\GuardgeoApi\Database\DatabaseManager;
        try {
            \$db = DatabaseManager::getInstance();
            \$connection = \$db->getConnection();
            \$stmt = \$connection->query('SELECT 1');
            echo 'Database connection successful';
        } catch (Exception \$e) {
            echo 'Database connection failed: ' . \$e->getMessage();
            exit(1);
        }
    "; then
        error "Database connection test failed"
    fi
    
    success "Prerequisites check passed"
}

# Create backup before deployment
create_backup() {
    log "Creating pre-deployment backup..."
    
    # Ensure backup directory exists
    mkdir -p "$BACKUP_DIR"
    
    # Create backup using our backup manager
    if php "$PROJECT_ROOT/scripts/backup/backup-manager.php" backup > /tmp/backup_result.json; then
        local backup_id=$(cat /tmp/backup_result.json | php -r "echo json_decode(file_get_contents('php://stdin'), true)['backup_id'] ?? 'unknown';")
        log "Backup created successfully: $backup_id"
        echo "$backup_id" > /tmp/deployment_backup_id
    else
        error "Failed to create backup"
    fi
    
    rm -f /tmp/backup_result.json
}

# Put application in maintenance mode
enable_maintenance_mode() {
    log "Enabling maintenance mode..."
    
    # Create maintenance flag file
    touch "$PROJECT_ROOT/maintenance.flag"
    
    # Wait for current requests to complete
    sleep 5
    
    success "Maintenance mode enabled"
}

# Disable maintenance mode
disable_maintenance_mode() {
    log "Disabling maintenance mode..."
    
    # Remove maintenance flag file
    rm -f "$PROJECT_ROOT/maintenance.flag"
    
    success "Maintenance mode disabled"
}

# Update application code
update_code() {
    log "Updating application code..."
    
    cd "$PROJECT_ROOT"
    
    # Stash any local changes
    if git status --porcelain | grep -q .; then
        warning "Local changes detected, stashing them"
        git stash push -m "Pre-deployment stash $DEPLOYMENT_ID"
    fi
    
    # Fetch latest changes
    git fetch origin
    
    # Get current branch
    local current_branch=$(git rev-parse --abbrev-ref HEAD)
    log "Current branch: $current_branch"
    
    # Pull latest changes
    git pull origin "$current_branch"
    
    # Get commit hash for logging
    local commit_hash=$(git rev-parse HEAD)
    log "Deployed commit: $commit_hash"
    
    success "Code updated successfully"
}

# Update dependencies
update_dependencies() {
    log "Updating dependencies..."
    
    cd "$PROJECT_ROOT"
    
    # Update Composer dependencies
    composer install --no-dev --optimize-autoloader --no-scripts
    
    # Clear OPcache if available
    if php -r "exit(function_exists('opcache_reset') ? 0 : 1);"; then
        php -r "opcache_reset(); echo 'OPcache cleared';"
    fi
    
    success "Dependencies updated successfully"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Run migrations using our migration manager
    local migration_result=$(php -r "
        require '$PROJECT_ROOT/vendor/autoload.php';
        use Skpassegna\GuardgeoApi\Database\MigrationManager;
        try {
            \$manager = new MigrationManager();
            \$result = \$manager->migrate();
            echo json_encode(\$result);
        } catch (Exception \$e) {
            echo json_encode(['success' => false, 'error' => \$e->getMessage()]);
        }
    ")
    
    local success=$(echo "$migration_result" | php -r "echo json_decode(file_get_contents('php://stdin'), true)['success'] ? 'true' : 'false';")
    
    if [[ "$success" == "true" ]]; then
        success "Database migrations completed successfully"
    else
        local error_msg=$(echo "$migration_result" | php -r "echo json_decode(file_get_contents('php://stdin'), true)['error'] ?? 'Unknown error';")
        error "Database migration failed: $error_msg"
    fi
}

# Update file permissions
update_permissions() {
    log "Updating file permissions..."
    
    cd "$PROJECT_ROOT"
    
    # Set proper ownership (assuming www-data user)
    if id "www-data" &>/dev/null; then
        sudo chown -R www-data:www-data .
    fi
    
    # Set directory permissions
    find . -type d -exec chmod 755 {} \;
    
    # Set file permissions
    find . -type f -exec chmod 644 {} \;
    
    # Make scripts executable
    chmod +x scripts/**/*.sh
    chmod +x scripts/**/*.php
    
    # Secure sensitive files
    chmod 600 .env
    
    # Make logs directory writable
    chmod 755 logs/
    chmod 666 logs/*.log
    
    # Make storage directories writable
    mkdir -p storage/{cache,sessions,reports}
    chmod -R 755 storage/
    
    success "File permissions updated successfully"
}

# Restart services
restart_services() {
    log "Restarting services..."
    
    # Restart PHP-FPM
    if systemctl is-active --quiet php8.1-fpm; then
        sudo systemctl reload php8.1-fpm
        log "PHP-FPM reloaded"
    fi
    
    # Restart Nginx
    if systemctl is-active --quiet nginx; then
        sudo systemctl reload nginx
        log "Nginx reloaded"
    fi
    
    # Restart Redis (if used for sessions/cache)
    if systemctl is-active --quiet redis-server; then
        sudo systemctl restart redis-server
        log "Redis restarted"
    fi
    
    success "Services restarted successfully"
}

# Run health checks
run_health_checks() {
    log "Running post-deployment health checks..."
    
    # Wait for services to start
    sleep 10
    
    # Run health check script
    local health_result=$(php "$PROJECT_ROOT/scripts/monitoring/health-check.php")
    local health_status=$(echo "$health_result" | php -r "echo json_decode(file_get_contents('php://stdin'), true)['status'] ?? 'unknown';")
    
    if [[ "$health_status" == "healthy" ]]; then
        success "Health checks passed"
    elif [[ "$health_status" == "warning" ]]; then
        warning "Health checks passed with warnings"
        echo "$health_result" | php -r "
            \$data = json_decode(file_get_contents('php://stdin'), true);
            foreach (\$data['checks'] as \$check => \$result) {
                if (\$result['status'] === 'warning') {
                    echo \"Warning in \$check: \" . json_encode(\$result['details']) . \"\n\";
                }
            }
        "
    else
        error "Health checks failed: $health_status"
    fi
}

# Test critical functionality
test_functionality() {
    log "Testing critical functionality..."
    
    # Test API endpoint
    local api_url=$(php -r "
        require '$PROJECT_ROOT/vendor/autoload.php';
        use Skpassegna\GuardgeoApi\Config\ConfigManager;
        echo ConfigManager::getInstance()->get('app.url') . '/api/analyze';
    ")
    
    # Test with a sample request
    local api_response=$(curl -s -w "%{http_code}" -X POST "$api_url" \
        -H "Content-Type: application/json" \
        -d '{"ip":"*******","visitor_hash":"test","plugin_id":1,"install_id":1,"url":"https://test.com"}' \
        -o /tmp/api_test_response.json)
    
    if [[ "$api_response" == "200" ]] || [[ "$api_response" == "400" ]]; then
        success "API endpoint is responding"
    else
        warning "API endpoint returned status: $api_response"
    fi
    
    # Test admin interface
    local admin_url=$(php -r "
        require '$PROJECT_ROOT/vendor/autoload.php';
        use Skpassegna\GuardgeoApi\Config\ConfigManager;
        echo ConfigManager::getInstance()->get('app.url') . '/admin/login';
    ")
    
    local admin_response=$(curl -s -w "%{http_code}" "$admin_url" -o /dev/null)
    
    if [[ "$admin_response" == "200" ]]; then
        success "Admin interface is accessible"
    else
        warning "Admin interface returned status: $admin_response"
    fi
    
    # Clean up test files
    rm -f /tmp/api_test_response.json
}

# Send deployment notification
send_notification() {
    local status=$1
    local message=$2
    
    log "Sending deployment notification..."
    
    # Get alert email from config
    local alert_email=$(php -r "
        require '$PROJECT_ROOT/vendor/autoload.php';
        use Skpassegna\GuardgeoApi\Config\ConfigManager;
        echo ConfigManager::getInstance()->get('monitoring.alerts.email', '');
    ")
    
    if [[ -n "$alert_email" ]]; then
        local subject="GuardGeo Deployment $status - $DEPLOYMENT_ID"
        local body="Deployment $DEPLOYMENT_ID completed with status: $status

$message

Server: $(hostname)
Time: $(date)
Commit: $(git -C "$PROJECT_ROOT" rev-parse HEAD)
Branch: $(git -C "$PROJECT_ROOT" rev-parse --abbrev-ref HEAD)

Deployment log: $LOG_FILE"
        
        echo "$body" | mail -s "$subject" "$alert_email"
    fi
    
    # Send Slack notification if configured
    local slack_webhook=$(php -r "
        require '$PROJECT_ROOT/vendor/autoload.php';
        use Skpassegna\GuardgeoApi\Config\ConfigManager;
        echo ConfigManager::getInstance()->get('monitoring.alerts.slack_webhook', '');
    ")
    
    if [[ -n "$slack_webhook" ]]; then
        local color="good"
        local emoji=":white_check_mark:"
        
        if [[ "$status" == "FAILED" ]]; then
            color="danger"
            emoji=":x:"
        elif [[ "$status" == "WARNING" ]]; then
            color="warning"
            emoji=":warning:"
        fi
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"title\": \"$emoji GuardGeo Deployment $status\",
                    \"text\": \"$message\",
                    \"fields\": [
                        {\"title\": \"Deployment ID\", \"value\": \"$DEPLOYMENT_ID\", \"short\": true},
                        {\"title\": \"Server\", \"value\": \"$(hostname)\", \"short\": true},
                        {\"title\": \"Time\", \"value\": \"$(date)\", \"short\": true},
                        {\"title\": \"Commit\", \"value\": \"$(git -C "$PROJECT_ROOT" rev-parse --short HEAD)\", \"short\": true}
                    ]
                }]
            }" \
            "$slack_webhook" > /dev/null 2>&1
    fi
}

# Rollback function
rollback() {
    log "Rolling back deployment..."
    
    # Check if we have a backup ID
    if [[ -f /tmp/deployment_backup_id ]]; then
        local backup_id=$(cat /tmp/deployment_backup_id)
        log "Rolling back to backup: $backup_id"
        
        # Restore from backup
        php "$PROJECT_ROOT/scripts/backup/backup-manager.php" restore "$backup_id"
        
        # Restart services
        restart_services
        
        success "Rollback completed"
    else
        error "No backup ID found for rollback"
    fi
}

# Cleanup function
cleanup() {
    log "Cleaning up deployment artifacts..."
    
    # Remove temporary files
    rm -f /tmp/deployment_backup_id
    rm -f /tmp/backup_result.json
    rm -f /tmp/api_test_response.json
    
    # Disable maintenance mode if it's still enabled
    if [[ -f "$PROJECT_ROOT/maintenance.flag" ]]; then
        disable_maintenance_mode
    fi
}

# Main deployment function
main() {
    log "Starting GuardGeo production deployment - ID: $DEPLOYMENT_ID"
    
    # Set up error handling
    trap 'error "Deployment failed at line $LINENO"' ERR
    trap cleanup EXIT
    
    # Ensure log directory exists
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Run deployment steps
    check_root
    check_prerequisites
    create_backup
    enable_maintenance_mode
    
    # Main deployment steps
    update_code
    update_dependencies
    run_migrations
    update_permissions
    
    # Restart services and test
    restart_services
    disable_maintenance_mode
    run_health_checks
    test_functionality
    
    # Success
    success "Deployment completed successfully - ID: $DEPLOYMENT_ID"
    send_notification "SUCCESS" "Deployment completed successfully"
    
    # Clean up
    cleanup
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        if [[ -z "$2" ]]; then
            error "Usage: $0 rollback <backup_id>"
        fi
        echo "$2" > /tmp/deployment_backup_id
        rollback
        ;;
    "health-check")
        run_health_checks
        ;;
    "test")
        test_functionality
        ;;
    *)
        echo "Usage: $0 [deploy|rollback <backup_id>|health-check|test]"
        exit 1
        ;;
esac