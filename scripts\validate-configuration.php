#!/usr/bin/env php
<?php

/**
 * Configuration Validation Script
 * 
 * Validates the complete configuration setup for different environments
 * and provides detailed feedback on configuration issues.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Skpassegna\GuardgeoApi\Config\EnvironmentManager;
use Skpassegna\GuardgeoApi\Config\DeploymentManager;
use Skpassegna\GuardgeoApi\Utils\CredentialManager;
use Skpassegna\GuardgeoApi\Utils\SSLEnforcementMiddleware;

// ANSI color codes for output
const COLOR_GREEN = "\033[32m";
const COLOR_RED = "\033[31m";
const COLOR_YELLOW = "\033[33m";
const COLOR_BLUE = "\033[34m";
const COLOR_RESET = "\033[0m";

function printHeader(string $title): void
{
    echo COLOR_BLUE . "\n" . str_repeat("=", 60) . "\n";
    echo " " . strtoupper($title) . "\n";
    echo str_repeat("=", 60) . COLOR_RESET . "\n\n";
}

function printSuccess(string $message): void
{
    echo COLOR_GREEN . "✓ " . $message . COLOR_RESET . "\n";
}

function printError(string $message): void
{
    echo COLOR_RED . "✗ " . $message . COLOR_RESET . "\n";
}

function printWarning(string $message): void
{
    echo COLOR_YELLOW . "⚠ " . $message . COLOR_RESET . "\n";
}

function printInfo(string $message): void
{
    echo COLOR_BLUE . "ℹ " . $message . COLOR_RESET . "\n";
}

try {
    printHeader("GuardGeo Configuration Validation");
    
    // Get environment from command line or detect
    $environment = $argv[1] ?? null;
    
    if (!$environment) {
        printInfo("No environment specified, detecting automatically...");
    }
    
    // Initialize managers
    $envManager = EnvironmentManager::getInstance();
    $deploymentManager = DeploymentManager::getInstance();
    $credentialManager = CredentialManager::getInstance();
    
    // Load configuration
    printInfo("Loading configuration...");
    $config = $envManager->loadConfiguration();
    $deploymentConfig = $deploymentManager->loadDeploymentConfiguration();
    $credentialManager->loadCredentials();
    
    $detectedEnv = $envManager->getEnvironment();
    printInfo("Environment detected: " . strtoupper($detectedEnv));
    
    if ($environment && $environment !== $detectedEnv) {
        printWarning("Specified environment ($environment) differs from detected environment ($detectedEnv)");
    }
    
    // Validate Environment Configuration
    printHeader("Environment Configuration");
    
    $envSummary = $envManager->getDeploymentSummary();
    
    if ($envSummary['ssl_enforced']) {
        printSuccess("SSL/HTTPS enforcement is enabled");
    } else {
        if ($detectedEnv === 'production') {
            printError("SSL/HTTPS enforcement should be enabled in production");
        } else {
            printWarning("SSL/HTTPS enforcement is disabled");
        }
    }
    
    if ($envSummary['debug_mode']) {
        if ($detectedEnv === 'production') {
            printError("Debug mode must be disabled in production");
        } else {
            printInfo("Debug mode is enabled");
        }
    } else {
        printSuccess("Debug mode is properly configured");
    }
    
    if ($envSummary['monitoring_enabled']) {
        printSuccess("Monitoring is enabled");
    } else {
        printWarning("Monitoring is disabled");
    }
    
    if ($envSummary['cache_enabled']) {
        printSuccess("Caching is enabled");
    } else {
        printWarning("Caching is disabled");
    }
    
    // Validate Credentials
    printHeader("Credential Validation");
    
    $credentialSummary = $credentialManager->getSecuritySummary();
    
    if ($credentialSummary['encryption_enabled']) {
        printSuccess("Encryption is properly configured");
    } else {
        printError("Encryption key is missing or invalid");
    }
    
    if ($credentialSummary['database_configured']) {
        printSuccess("Database credentials are configured");
    } else {
        printError("Database credentials are missing");
    }
    
    foreach ($credentialSummary['apis_configured'] as $api => $configured) {
        if ($configured) {
            printSuccess(ucfirst($api) . " API credentials are configured");
        } else {
            printError(ucfirst($api) . " API credentials are missing");
        }
    }
    
    if ($credentialSummary['ssl_configured']) {
        printSuccess("SSL certificate paths are configured");
    } else {
        if ($detectedEnv === 'production') {
            printError("SSL certificate paths are required in production");
        } else {
            printInfo("SSL certificate paths are not configured");
        }
    }
    
    // Test credential connectivity
    printHeader("Connectivity Tests");
    
    $connectivityResults = $credentialManager->testCredentialConnectivity();
    
    foreach ($connectivityResults as $service => $result) {
        if ($result['status'] === 'success') {
            printSuccess($service . ": " . $result['message']);
        } else {
            printError($service . ": " . $result['message']);
        }
    }
    
    // Validate SSL Configuration
    if ($envSummary['ssl_enforced']) {
        printHeader("SSL Configuration");
        
        $sslMiddleware = new SSLEnforcementMiddleware();
        $sslStatus = $sslMiddleware->getSSLStatus();
        
        if ($sslStatus['certificate_info']['configured']) {
            $certInfo = $sslStatus['certificate_info'];
            
            if ($certInfo['valid']) {
                printSuccess("SSL certificate is valid");
                
                if (isset($certInfo['days_until_expiry'])) {
                    $daysUntilExpiry = $certInfo['days_until_expiry'];
                    
                    if ($daysUntilExpiry < 0) {
                        printError("SSL certificate has expired " . abs($daysUntilExpiry) . " days ago");
                    } elseif ($daysUntilExpiry < 30) {
                        printWarning("SSL certificate expires in " . round($daysUntilExpiry) . " days");
                    } else {
                        printSuccess("SSL certificate expires in " . round($daysUntilExpiry) . " days");
                    }
                }
                
                if (isset($certInfo['subject']['CN'])) {
                    printInfo("Certificate subject: " . $certInfo['subject']['CN']);
                }
                
                if (isset($certInfo['issuer']['CN'])) {
                    printInfo("Certificate issuer: " . $certInfo['issuer']['CN']);
                }
                
            } else {
                printError("SSL certificate is invalid: " . ($certInfo['error'] ?? 'Unknown error'));
            }
        } else {
            printError("SSL certificate is not configured");
        }
    }
    
    // Validate Deployment Readiness
    printHeader("Deployment Readiness");
    
    $readinessResults = $deploymentManager->validateDeploymentReadiness($detectedEnv);
    
    if ($readinessResults['ready']) {
        printSuccess("System is ready for deployment");
    } else {
        printError("System is NOT ready for deployment");
    }
    
    // Show detailed check results
    foreach ($readinessResults['checks'] as $category => $checks) {
        printInfo("\n" . ucfirst(str_replace('_', ' ', $category)) . ":");
        
        foreach ($checks as $check => $status) {
            $checkName = ucfirst(str_replace('_', ' ', $check));
            
            switch ($status) {
                case 'OK':
                    printSuccess("  " . $checkName);
                    break;
                case 'FAIL':
                    printError("  " . $checkName);
                    break;
                case 'WARNING':
                    printWarning("  " . $checkName);
                    break;
                default:
                    printInfo("  " . $checkName . ": " . $status);
            }
        }
    }
    
    // Show errors and warnings
    if (!empty($readinessResults['errors'])) {
        printInfo("\nErrors:");
        foreach ($readinessResults['errors'] as $error) {
            printError("  " . $error);
        }
    }
    
    if (!empty($readinessResults['warnings'])) {
        printInfo("\nWarnings:");
        foreach ($readinessResults['warnings'] as $warning) {
            printWarning("  " . $warning);
        }
    }
    
    // Configuration Summary
    printHeader("Configuration Summary");
    
    $deploymentSummary = $deploymentManager->getDeploymentSummary();
    
    printInfo("Environment: " . strtoupper($deploymentSummary['environment']));
    printInfo("Application: " . $deploymentSummary['application']['name'] . " v" . $deploymentSummary['application']['version']);
    printInfo("PHP Version Required: " . $deploymentSummary['application']['php_version_required'] . " (Current: " . PHP_VERSION . ")");
    printInfo("SSL Required: " . ($deploymentSummary['ssl_required'] ? 'Yes' : 'No'));
    printInfo("Backup Enabled: " . ($deploymentSummary['backup_enabled'] ? 'Yes' : 'No'));
    printInfo("Monitoring Enabled: " . ($deploymentSummary['monitoring_enabled'] ? 'Yes' : 'No'));
    printInfo("Deployment Ready: " . ($deploymentSummary['deployment_ready'] ? 'Yes' : 'No'));
    
    if (!empty($deploymentSummary['available_templates'])) {
        printInfo("Available Templates: " . implode(', ', $deploymentSummary['available_templates']));
    }
    
    // Final status
    printHeader("Validation Complete");
    
    $overallStatus = $readinessResults['ready'] && 
                    $credentialSummary['encryption_enabled'] && 
                    $credentialSummary['database_configured'] &&
                    $credentialSummary['apis_configured']['freemius'] &&
                    $credentialSummary['apis_configured']['ipregistry'];
    
    if ($overallStatus) {
        printSuccess("✓ Configuration validation PASSED");
        echo "\n" . COLOR_GREEN . "The system is properly configured and ready for deployment." . COLOR_RESET . "\n\n";
        exit(0);
    } else {
        printError("✗ Configuration validation FAILED");
        echo "\n" . COLOR_RED . "Please fix the configuration issues before deployment." . COLOR_RESET . "\n\n";
        exit(1);
    }
    
} catch (\Exception $e) {
    printError("Configuration validation failed with error: " . $e->getMessage());
    
    if (isset($argv[2]) && $argv[2] === '--debug') {
        echo "\n" . COLOR_RED . "Stack trace:\n" . $e->getTraceAsString() . COLOR_RESET . "\n\n";
    }
    
    exit(1);
}