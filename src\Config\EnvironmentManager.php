<?php

namespace Skpassegna\GuardgeoApi\Config;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Environment Configuration Manager
 * 
 * Handles complete environment-specific configuration loading,
 * SSL/HTTPS enforcement, and secure credential management.
 */
class EnvironmentManager
{
    private static ?EnvironmentManager $instance = null;
    private LoggingService $logger;
    private array $config = [];
    private string $environment;
    private bool $loaded = false;
    private array $requiredCredentials = [];
    
    /**
     * Private constructor for singleton pattern
     */
    private function __construct()
    {
        $this->logger = LoggingService::getInstance();
        $this->environment = $this->detectEnvironment();
        $this->initializeRequiredCredentials();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance(): EnvironmentManager
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Load complete environment configuration
     */
    public function loadConfiguration(): array
    {
        if ($this->loaded) {
            return $this->config;
        }
        
        try {
            // Load base configuration
            $this->config = $this->loadBaseConfiguration();
            
            // Load environment-specific configuration
            $this->loadEnvironmentSpecificConfiguration();
            
            // Load and validate credentials
            $this->loadSecureCredentials();
            
            // Apply environment-specific security settings
            $this->applySecurityConfiguration();
            
            // Enforce SSL/HTTPS for production
            $this->enforceSSLConfiguration();
            
            // Validate complete configuration
            $this->validateConfiguration();
            
            $this->loaded = true;
            
            $this->logger->info('Environment configuration loaded successfully', [
                'environment' => $this->environment,
                'config_sections' => array_keys($this->config),
                'ssl_enforced' => $this->isSSLEnforced()
            ]);
            
            return $this->config;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to load environment configuration', [
                'environment' => $this->environment,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
    
    /**
     * Detect current environment
     */
    private function detectEnvironment(): string
    {
        // Check environment variable first
        $env = $_ENV['APP_ENV'] ?? getenv('APP_ENV');
        if ($env) {
            return $env;
        }
        
        // Check server name patterns
        $serverName = $_SERVER['SERVER_NAME'] ?? $_SERVER['HTTP_HOST'] ?? '';
        
        if (strpos($serverName, 'staging') !== false) {
            return 'staging';
        }
        
        if (strpos($serverName, 'localhost') !== false || 
            strpos($serverName, '127.0.0.1') !== false ||
            strpos($serverName, 'dev') !== false) {
            return 'development';
        }
        
        // Default to production for security
        return 'production';
    }
    
    /**
     * Initialize required credentials for each environment
     */
    private function initializeRequiredCredentials(): void
    {
        $this->requiredCredentials = [
            'all' => [
                'DB_HOST',
                'DB_NAME',
                'DB_USERNAME',
                'DB_PASSWORD',
                'FREEMIUS_API_TOKEN',
                'IPREGISTRY_API_KEY'
            ],
            'production' => [
                'APP_ENCRYPTION_KEY',
                'SSL_CERTIFICATE_PATH',
                'SSL_PRIVATE_KEY_PATH',
                'BACKUP_ENCRYPTION_KEY'
            ],
            'staging' => [
                'APP_ENCRYPTION_KEY'
            ],
            'development' => []
        ];
    }
    
    /**
     * Load base configuration common to all environments
     */
    private function loadBaseConfiguration(): array
    {
        return [
            'app' => [
                'name' => 'GuardGeo Admin Platform',
                'version' => '1.0.0',
                'environment' => $this->environment,
                'timezone' => $this->getEnvVar('APP_TIMEZONE', 'UTC'),
                'url' => $this->getEnvVar('APP_URL', 'http://localhost'),
                'debug' => $this->getEnvVar('APP_DEBUG', 'false') === 'true',
            ],
            
            'database' => [
                'driver' => 'pgsql',
                'host' => $this->getEnvVar('DB_HOST', 'localhost'),
                'port' => (int) $this->getEnvVar('DB_PORT', '5432'),
                'database' => $this->getEnvVar('DB_NAME', 'guardgeo'),
                'username' => $this->getEnvVar('DB_USERNAME', 'postgres'),
                'password' => $this->getEnvVar('DB_PASSWORD', ''),
                'charset' => $this->getEnvVar('DB_CHARSET', 'utf8'),
                'ssl_ca' => $this->getEnvVar('DB_SSL_CA'),
                'ssl_verify' => $this->getEnvVar('DB_SSL_VERIFY', 'false') === 'true',
                'options' => [
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                    \PDO::ATTR_EMULATE_PREPARES => false,
                ]
            ],
            
            'security' => [
                'encryption_key' => $this->getEnvVar('APP_ENCRYPTION_KEY'),
                'hash_algorithm' => $this->getEnvVar('HASH_ALGORITHM', 'sha256'),
                'session_secure' => $this->getEnvVar('SESSION_SECURE', 'false') === 'true',
                'session_httponly' => $this->getEnvVar('SESSION_HTTPONLY', 'true') === 'true',
                'session_domain' => $this->getEnvVar('SESSION_DOMAIN'),
                'csrf_protection' => $this->getEnvVar('CSRF_PROTECTION', 'true') === 'true',
                'force_https' => false, // Will be set by enforceSSLConfiguration
            ],
            
            'ssl' => [
                'certificate_path' => $this->getEnvVar('SSL_CERTIFICATE_PATH'),
                'private_key_path' => $this->getEnvVar('SSL_PRIVATE_KEY_PATH'),
                'ca_bundle_path' => $this->getEnvVar('SSL_CA_BUNDLE_PATH'),
                'enforce_https' => false, // Will be set by environment
            ],
            
            'apis' => [
                'freemius' => [
                    'api_token' => $this->getEnvVar('FREEMIUS_API_TOKEN'),
                    'base_url' => $this->getEnvVar('FREEMIUS_API_BASE_URL', 'https://api.freemius.com/v1'),
                    'timeout' => (int) $this->getEnvVar('FREEMIUS_API_TIMEOUT', '30'),
                    'retry_attempts' => (int) $this->getEnvVar('FREEMIUS_API_RETRY_ATTEMPTS', '3'),
                    'retry_delay' => (int) $this->getEnvVar('FREEMIUS_API_RETRY_DELAY', '1'),
                ],
                'ipregistry' => [
                    'api_key' => $this->getEnvVar('IPREGISTRY_API_KEY'),
                    'base_url' => $this->getEnvVar('IPREGISTRY_API_BASE_URL', 'https://api.ipregistry.co'),
                    'timeout' => (int) $this->getEnvVar('IPREGISTRY_API_TIMEOUT', '30'),
                    'retry_attempts' => (int) $this->getEnvVar('IPREGISTRY_API_RETRY_ATTEMPTS', '3'),
                    'retry_delay' => (int) $this->getEnvVar('IPREGISTRY_API_RETRY_DELAY', '1'),
                ]
            ],
            
            'logging' => [
                'level' => $this->getEnvVar('LOG_LEVEL', 'info'),
                'file_path' => $this->getEnvVar('LOG_FILE_PATH', __DIR__ . '/../../logs'),
                'max_file_size' => (int) $this->getEnvVar('LOG_MAX_FILE_SIZE', '10485760'),
                'max_files' => (int) $this->getEnvVar('LOG_MAX_FILES', '5'),
            ],
            
            'cache' => [
                'enabled' => $this->getEnvVar('CACHE_ENABLED', 'true') === 'true',
                'driver' => $this->getEnvVar('CACHE_DRIVER', 'database'),
                'default_ttl' => (int) $this->getEnvVar('CACHE_DEFAULT_TTL', '3600'),
                'ip_cache' => [
                    'location_days' => (int) $this->getEnvVar('IP_CACHE_LOCATION_DAYS', '10'),
                    'security_days' => (int) $this->getEnvVar('IP_CACHE_SECURITY_DAYS', '3'),
                    'connection_days' => (int) $this->getEnvVar('IP_CACHE_CONNECTION_DAYS', '7'),
                    'company_days' => (int) $this->getEnvVar('IP_CACHE_COMPANY_DAYS', '30'),
                ]
            ],
            
            'monitoring' => [
                'enabled' => $this->getEnvVar('MONITORING_ENABLED', 'true') === 'true',
                'performance_tracking' => $this->getEnvVar('PERFORMANCE_TRACKING', 'true') === 'true',
                'error_reporting' => $this->getEnvVar('ERROR_REPORTING', 'true') === 'true',
                'health_check_interval' => (int) $this->getEnvVar('HEALTH_CHECK_INTERVAL', '300'),
            ]
        ];
    }
    
    /**
     * Load environment-specific configuration overrides
     */
    private function loadEnvironmentSpecificConfiguration(): void
    {
        $configFile = __DIR__ . "/../../config/{$this->environment}.php";
        
        if (file_exists($configFile)) {
            $overrides = require $configFile;
            if (is_array($overrides)) {
                $this->config = array_merge_recursive($this->config, $overrides);
                
                $this->logger->info('Environment-specific configuration loaded', [
                    'environment' => $this->environment,
                    'config_file' => $configFile
                ]);
            }
        }
        
        // Load local overrides if they exist
        $localConfigFile = __DIR__ . "/../../config/{$this->environment}.local.php";
        if (file_exists($localConfigFile)) {
            $localOverrides = require $localConfigFile;
            if (is_array($localOverrides)) {
                $this->config = array_merge_recursive($this->config, $localOverrides);
                
                $this->logger->info('Local configuration overrides loaded', [
                    'environment' => $this->environment,
                    'config_file' => $localConfigFile
                ]);
            }
        }
    }
    
    /**
     * Load and validate secure credentials
     */
    private function loadSecureCredentials(): void
    {
        $missing = [];
        
        // Check required credentials for all environments
        foreach ($this->requiredCredentials['all'] as $credential) {
            if (empty($this->getEnvVar($credential))) {
                $missing[] = $credential;
            }
        }
        
        // Check environment-specific required credentials
        if (isset($this->requiredCredentials[$this->environment])) {
            foreach ($this->requiredCredentials[$this->environment] as $credential) {
                if (empty($this->getEnvVar($credential))) {
                    $missing[] = $credential;
                }
            }
        }
        
        if (!empty($missing)) {
            throw new \RuntimeException(
                "Missing required credentials for {$this->environment} environment: " . 
                implode(', ', $missing)
            );
        }
        
        // Validate credential formats
        $this->validateCredentialFormats();
        
        $this->logger->info('Secure credentials loaded and validated', [
            'environment' => $this->environment,
            'credentials_count' => count($this->requiredCredentials['all']) + 
                                 count($this->requiredCredentials[$this->environment] ?? [])
        ]);
    }
    
    /**
     * Validate credential formats
     */
    private function validateCredentialFormats(): void
    {
        // Validate encryption key length
        $encryptionKey = $this->getEnvVar('APP_ENCRYPTION_KEY');
        if ($encryptionKey && strlen($encryptionKey) < 32) {
            throw new \RuntimeException('APP_ENCRYPTION_KEY must be at least 32 characters long');
        }
        
        // Validate API token formats
        $freemiusToken = $this->getEnvVar('FREEMIUS_API_TOKEN');
        if ($freemiusToken && strlen($freemiusToken) < 20) {
            $this->logger->warning('Freemius API token seems too short', [
                'token_length' => strlen($freemiusToken)
            ]);
        }
        
        $ipRegistryKey = $this->getEnvVar('IPREGISTRY_API_KEY');
        if ($ipRegistryKey && strlen($ipRegistryKey) < 16) {
            $this->logger->warning('ipRegistry API key seems too short', [
                'key_length' => strlen($ipRegistryKey)
            ]);
        }
        
        // Validate database password strength for production
        if ($this->environment === 'production') {
            $dbPassword = $this->getEnvVar('DB_PASSWORD');
            if ($dbPassword && strlen($dbPassword) < 12) {
                throw new \RuntimeException('Database password must be at least 12 characters long in production');
            }
        }
    }
    
    /**
     * Apply environment-specific security configuration
     */
    private function applySecurityConfiguration(): void
    {
        switch ($this->environment) {
            case 'production':
                $this->config['app']['debug'] = false;
                $this->config['security']['session_secure'] = true;
                $this->config['security']['force_https'] = true;
                $this->config['logging']['level'] = $this->getEnvVar('LOG_LEVEL', 'error');
                break;
                
            case 'staging':
                $this->config['app']['debug'] = $this->getEnvVar('APP_DEBUG', 'true') === 'true';
                $this->config['security']['session_secure'] = true;
                $this->config['security']['force_https'] = true;
                $this->config['logging']['level'] = $this->getEnvVar('LOG_LEVEL', 'info');
                break;
                
            case 'development':
                $this->config['app']['debug'] = $this->getEnvVar('APP_DEBUG', 'true') === 'true';
                $this->config['security']['session_secure'] = false;
                $this->config['security']['force_https'] = false;
                $this->config['logging']['level'] = $this->getEnvVar('LOG_LEVEL', 'debug');
                break;
        }
        
        $this->logger->info('Security configuration applied', [
            'environment' => $this->environment,
            'debug_mode' => $this->config['app']['debug'],
            'secure_sessions' => $this->config['security']['session_secure'],
            'force_https' => $this->config['security']['force_https']
        ]);
    }
    
    /**
     * Enforce SSL/HTTPS configuration for production environments
     */
    private function enforceSSLConfiguration(): void
    {
        if ($this->environment === 'production' || $this->environment === 'staging') {
            $this->config['ssl']['enforce_https'] = true;
            
            // Validate SSL certificate paths
            $certPath = $this->config['ssl']['certificate_path'];
            $keyPath = $this->config['ssl']['private_key_path'];
            
            if ($certPath && !file_exists($certPath)) {
                $this->logger->warning('SSL certificate file not found', [
                    'certificate_path' => $certPath
                ]);
            }
            
            if ($keyPath && !file_exists($keyPath)) {
                $this->logger->warning('SSL private key file not found', [
                    'private_key_path' => $keyPath
                ]);
            }
            
            // Set security headers for HTTPS
            $this->config['security']['headers'] = [
                'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains; preload',
                'X-Content-Type-Options' => 'nosniff',
                'X-Frame-Options' => 'DENY',
                'X-XSS-Protection' => '1; mode=block',
                'Referrer-Policy' => 'strict-origin-when-cross-origin',
                'Content-Security-Policy' => $this->getContentSecurityPolicy()
            ];
            
            $this->logger->info('SSL/HTTPS enforcement configured', [
                'environment' => $this->environment,
                'certificate_configured' => !empty($certPath),
                'private_key_configured' => !empty($keyPath)
            ]);
        }
    }
    
    /**
     * Get Content Security Policy header value
     */
    private function getContentSecurityPolicy(): string
    {
        return "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; " .
               "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; " .
               "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; " .
               "img-src 'self' data: https:; " .
               "connect-src 'self'; " .
               "object-src 'none'; " .
               "base-uri 'self'; " .
               "form-action 'self'; " .
               "frame-ancestors 'none'; " .
               "upgrade-insecure-requests";
    }
    
    /**
     * Validate complete configuration
     */
    private function validateConfiguration(): void
    {
        $validator = new ConfigValidator();
        $result = $validator->validate($this->config, $this->environment);
        
        if (!$result['valid']) {
            $errorMessages = array_map(fn($error) => $error['message'], $result['errors']);
            throw new \RuntimeException(
                "Configuration validation failed: " . implode('; ', $errorMessages)
            );
        }
        
        if (!empty($result['warnings'])) {
            foreach ($result['warnings'] as $warning) {
                $this->logger->warning('Configuration warning', [
                    'section' => $warning['section'],
                    'message' => $warning['message']
                ]);
            }
        }
    }
    
    /**
     * Get environment variable with fallback
     */
    private function getEnvVar(string $key, ?string $default = null): ?string
    {
        return $_ENV[$key] ?? getenv($key) ?: $default;
    }
    
    /**
     * Get configuration value using dot notation
     */
    public function get(string $key, $default = null)
    {
        if (!$this->loaded) {
            $this->loadConfiguration();
        }
        
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (!is_array($value) || !isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * Get current environment
     */
    public function getEnvironment(): string
    {
        return $this->environment;
    }
    
    /**
     * Check if SSL is enforced
     */
    public function isSSLEnforced(): bool
    {
        return $this->get('ssl.enforce_https', false);
    }
    
    /**
     * Check if running in production
     */
    public function isProduction(): bool
    {
        return $this->environment === 'production';
    }
    
    /**
     * Check if running in staging
     */
    public function isStaging(): bool
    {
        return $this->environment === 'staging';
    }
    
    /**
     * Check if running in development
     */
    public function isDevelopment(): bool
    {
        return $this->environment === 'development';
    }
    
    /**
     * Get all configuration
     */
    public function getAllConfiguration(): array
    {
        if (!$this->loaded) {
            $this->loadConfiguration();
        }
        
        return $this->config;
    }
    
    /**
     * Get configuration for specific section
     */
    public function getSection(string $section): array
    {
        return $this->get($section, []);
    }
    
    /**
     * Get masked credentials for display
     */
    public function getMaskedCredentials(): array
    {
        return [
            'database_password' => $this->maskCredential($this->get('database.password')),
            'freemius_api_token' => $this->maskCredential($this->get('apis.freemius.api_token')),
            'ipregistry_api_key' => $this->maskCredential($this->get('apis.ipregistry.api_key')),
            'encryption_key' => $this->maskCredential($this->get('security.encryption_key')),
        ];
    }
    
    /**
     * Mask credential for display
     */
    private function maskCredential(?string $credential): string
    {
        if (empty($credential)) {
            return 'Not configured';
        }
        
        if (strlen($credential) <= 8) {
            return str_repeat('*', strlen($credential));
        }
        
        return substr($credential, 0, 4) . str_repeat('*', strlen($credential) - 8) . substr($credential, -4);
    }
    
    /**
     * Export configuration for backup (without sensitive data)
     */
    public function exportSafeConfiguration(): array
    {
        $config = $this->getAllConfiguration();
        
        // Remove sensitive data
        unset($config['database']['password']);
        unset($config['apis']['freemius']['api_token']);
        unset($config['apis']['ipregistry']['api_key']);
        unset($config['security']['encryption_key']);
        
        return [
            'environment' => $this->environment,
            'timestamp' => date('Y-m-d H:i:s'),
            'configuration' => $config
        ];
    }
    
    /**
     * Get deployment configuration summary
     */
    public function getDeploymentSummary(): array
    {
        return [
            'environment' => $this->environment,
            'ssl_enforced' => $this->isSSLEnforced(),
            'debug_mode' => $this->get('app.debug'),
            'database_ssl' => $this->get('database.ssl_verify'),
            'session_secure' => $this->get('security.session_secure'),
            'csrf_protection' => $this->get('security.csrf_protection'),
            'monitoring_enabled' => $this->get('monitoring.enabled'),
            'cache_enabled' => $this->get('cache.enabled'),
            'credentials_configured' => [
                'database' => !empty($this->get('database.password')),
                'freemius' => !empty($this->get('apis.freemius.api_token')),
                'ipregistry' => !empty($this->get('apis.ipregistry.api_key')),
                'encryption' => !empty($this->get('security.encryption_key')),
            ]
        ];
    }
}