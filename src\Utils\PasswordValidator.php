<?php

namespace Skpassegna\GuardgeoApi\Utils;

/**
 * Password Validator
 * 
 * Validates password complexity requirements with 12+ character minimum
 * and robust security requirements.
 */
class PasswordValidator
{
    private int $minLength;
    private bool $requireUppercase;
    private bool $requireLowercase;
    private bool $requireNumbers;
    private bool $requireSpecialChars;
    private array $commonPasswords;

    public function __construct(
        int $minLength = 12,
        bool $requireUppercase = true,
        bool $requireLowercase = true,
        bool $requireNumbers = true,
        bool $requireSpecialChars = true
    ) {
        $this->minLength = $minLength;
        $this->requireUppercase = $requireUppercase;
        $this->requireLowercase = $requireLowercase;
        $this->requireNumbers = $requireNumbers;
        $this->requireSpecialChars = $requireSpecialChars;
        
        // Common passwords to reject
        $this->commonPasswords = [
            'password123',
            'admin123456',
            'administrator',
            'password1234',
            'welcome123456',
            'qwerty123456',
            '123456789012',
            'password12345',
            'admin1234567',
            'letmein12345'
        ];
    }

    /**
     * Validate password against all requirements
     *
     * @param string $password
     * @return array Validation result with errors
     */
    public function validate(string $password): array
    {
        $errors = [];
        $valid = true;

        // Check minimum length
        if (strlen($password) < $this->minLength) {
            $errors[] = "Password must be at least {$this->minLength} characters long";
            $valid = false;
        }

        // Check maximum length (prevent DoS)
        if (strlen($password) > 128) {
            $errors[] = "Password must not exceed 128 characters";
            $valid = false;
        }

        // Check for uppercase letters
        if ($this->requireUppercase && !preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password must contain at least one uppercase letter";
            $valid = false;
        }

        // Check for lowercase letters
        if ($this->requireLowercase && !preg_match('/[a-z]/', $password)) {
            $errors[] = "Password must contain at least one lowercase letter";
            $valid = false;
        }

        // Check for numbers
        if ($this->requireNumbers && !preg_match('/[0-9]/', $password)) {
            $errors[] = "Password must contain at least one number";
            $valid = false;
        }

        // Check for special characters
        if ($this->requireSpecialChars && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "Password must contain at least one special character";
            $valid = false;
        }

        // Check for common passwords
        if ($this->isCommonPassword($password)) {
            $errors[] = "Password is too common and easily guessable";
            $valid = false;
        }

        // Check for sequential characters
        if ($this->hasSequentialCharacters($password)) {
            $errors[] = "Password should not contain sequential characters (e.g., 123, abc)";
            $valid = false;
        }

        // Check for repeated characters
        if ($this->hasRepeatedCharacters($password)) {
            $errors[] = "Password should not contain excessive repeated characters";
            $valid = false;
        }

        // Check character diversity
        if (!$this->hasGoodCharacterDiversity($password)) {
            $errors[] = "Password should use a diverse mix of character types";
            $valid = false;
        }

        return [
            'valid' => $valid,
            'errors' => $errors,
            'strength' => $this->calculateStrength($password)
        ];
    }

    /**
     * Calculate password strength score (0-100)
     *
     * @param string $password
     * @return int Strength score
     */
    public function calculateStrength(string $password): int
    {
        $score = 0;
        $length = strlen($password);

        // Length scoring
        if ($length >= 12) $score += 25;
        if ($length >= 16) $score += 10;
        if ($length >= 20) $score += 10;

        // Character type scoring
        if (preg_match('/[a-z]/', $password)) $score += 10;
        if (preg_match('/[A-Z]/', $password)) $score += 10;
        if (preg_match('/[0-9]/', $password)) $score += 10;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 15;

        // Complexity bonuses
        if ($this->hasGoodCharacterDiversity($password)) $score += 10;
        if (!$this->hasSequentialCharacters($password)) $score += 5;
        if (!$this->hasRepeatedCharacters($password)) $score += 5;

        return min(100, $score);
    }

    /**
     * Check if password is in common passwords list
     *
     * @param string $password
     * @return bool True if password is common
     */
    private function isCommonPassword(string $password): bool
    {
        $lowerPassword = strtolower($password);
        
        foreach ($this->commonPasswords as $common) {
            if ($lowerPassword === strtolower($common)) {
                return true;
            }
        }

        // Check for simple patterns
        if (preg_match('/^password\d+$/i', $password)) {
            return true;
        }

        if (preg_match('/^admin\d+$/i', $password)) {
            return true;
        }

        return false;
    }

    /**
     * Check for sequential characters
     *
     * @param string $password
     * @return bool True if has sequential characters
     */
    private function hasSequentialCharacters(string $password): bool
    {
        $sequences = [
            '0123456789',
            'abcdefghijklmnopqrstuvwxyz',
            'qwertyuiopasdfghjklzxcvbnm'
        ];

        foreach ($sequences as $sequence) {
            for ($i = 0; $i <= strlen($sequence) - 4; $i++) {
                $subseq = substr($sequence, $i, 4);
                if (stripos($password, $subseq) !== false) {
                    return true;
                }
                // Check reverse sequence
                if (stripos($password, strrev($subseq)) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check for excessive repeated characters
     *
     * @param string $password
     * @return bool True if has excessive repeated characters
     */
    private function hasRepeatedCharacters(string $password): bool
    {
        // Check for 3 or more consecutive identical characters
        if (preg_match('/(.)\1{2,}/', $password)) {
            return true;
        }

        // Check if more than 50% of characters are the same
        $charCounts = array_count_values(str_split(strtolower($password)));
        $maxCount = max($charCounts);
        $totalLength = strlen($password);

        return ($maxCount / $totalLength) > 0.5;
    }

    /**
     * Check for good character diversity
     *
     * @param string $password
     * @return bool True if has good diversity
     */
    private function hasGoodCharacterDiversity(string $password): bool
    {
        $uniqueChars = count(array_unique(str_split(strtolower($password))));
        $totalLength = strlen($password);

        // At least 50% unique characters for passwords under 16 chars
        // At least 40% unique characters for longer passwords
        $requiredRatio = $totalLength <= 16 ? 0.5 : 0.4;

        return ($uniqueChars / $totalLength) >= $requiredRatio;
    }

    /**
     * Get password requirements as human-readable text
     *
     * @return array Requirements list
     */
    public function getRequirements(): array
    {
        $requirements = [];

        $requirements[] = "At least {$this->minLength} characters long";
        $requirements[] = "Maximum 128 characters";

        if ($this->requireUppercase) {
            $requirements[] = "At least one uppercase letter (A-Z)";
        }

        if ($this->requireLowercase) {
            $requirements[] = "At least one lowercase letter (a-z)";
        }

        if ($this->requireNumbers) {
            $requirements[] = "At least one number (0-9)";
        }

        if ($this->requireSpecialChars) {
            $requirements[] = "At least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)";
        }

        $requirements[] = "Not a common or easily guessable password";
        $requirements[] = "No sequential characters (123, abc, etc.)";
        $requirements[] = "No excessive repeated characters";
        $requirements[] = "Good mix of different character types";

        return $requirements;
    }
}