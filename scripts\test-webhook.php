<?php
/**
 * Freemius Webhook Test Script
 * 
 * This script tests the webhook implementation by simulating Freemius webhook calls.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Skpassegna\GuardgeoApi\Services\FreemiusWebhookHandler;
use Skpassegna\GuardgeoApi\Config\Environment;

// Load environment
Environment::load(__DIR__ . '/../.env');

echo "=== Freemius Webhook Test Script ===\n\n";

// Test webhook handler initialization
echo "1. Testing webhook handler initialization...\n";
try {
    $webhookHandler = new FreemiusWebhookHandler();
    echo "✓ Webhook handler initialized successfully\n\n";
} catch (Exception $e) {
    echo "✗ Failed to initialize webhook handler: " . $e->getMessage() . "\n";
    exit(1);
}

// Test configuration validation
echo "2. Testing webhook configuration...\n";
try {
    $config = $webhookHandler->validateConfiguration();
    if ($config['valid']) {
        echo "✓ Webhook configuration is valid\n";
    } else {
        echo "⚠ Webhook configuration has issues:\n";
        foreach ($config['issues'] as $issue) {
            echo "  - $issue\n";
        }
    }
    echo "\n";
} catch (Exception $e) {
    echo "✗ Failed to validate configuration: " . $e->getMessage() . "\n\n";
}

// Test webhook statistics
echo "3. Testing webhook statistics...\n";
try {
    $stats = $webhookHandler->getWebhookStatistics();
    echo "✓ Webhook statistics retrieved:\n";
    echo "  - Supported events: " . count($stats['supported_events']) . "\n";
    echo "  - Webhook secret configured: " . ($stats['webhook_secret_configured'] ? 'Yes' : 'No') . "\n";
    echo "\n";
} catch (Exception $e) {
    echo "✗ Failed to get webhook statistics: " . $e->getMessage() . "\n\n";
}

// Test sample webhook payloads
echo "4. Testing sample webhook payloads...\n";

$samplePayloads = [
    'install.activated' => [
        'type' => 'install.activated',
        'objects' => [
            'install' => [
                'id' => 12345,
                'secret_key' => 'sk_test_123',
                'public_key' => 'pk_test_123',
                'created' => '2024-01-01T00:00:00Z',
                'updated' => '2024-01-01T00:00:00Z',
                'site_id' => 67890,
                'plugin_id' => 54321,
                'user_id' => 98765,
                'url' => 'https://example.com',
                'title' => 'Test Site',
                'version' => '1.0.0',
                'is_active' => true,
                'is_premium' => true,
                'is_uninstalled' => false,
                'is_disconnected' => false,
                'is_locked' => false,
                'is_beta' => false,
                'gross' => 29.99,
                'country_code' => 'US',
                'language' => 'en'
            ]
        ]
    ],
    'subscription.created' => [
        'type' => 'subscription.created',
        'objects' => [
            'subscription' => [
                'id' => 11111,
                'install_id' => 12345,
                'plan_id' => 22222,
                'status' => 'active',
                'created' => '2024-01-01T00:00:00Z'
            ]
        ]
    ]
];

foreach ($samplePayloads as $eventType => $payload) {
    echo "  Testing $eventType...\n";
    
    try {
        // Create test headers (without signature for now)
        $headers = [
            'content-type' => 'application/json',
            'user-agent' => 'Freemius-Webhook/1.0'
        ];
        
        // Convert payload to JSON
        $jsonPayload = json_encode($payload);
        
        // Process the webhook (this will fail signature verification if secret is set)
        $result = $webhookHandler->processWebhook($headers, $jsonPayload);
        
        if ($result['success']) {
            echo "    ✓ Processed successfully\n";
        } else {
            echo "    ⚠ Processing failed: " . $result['error'] . "\n";
            if ($result['code'] === 'INVALID_SIGNATURE') {
                echo "    (This is expected if webhook secret is configured)\n";
            }
        }
        
    } catch (Exception $e) {
        echo "    ✗ Exception occurred: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Test signature verification (if secret is configured)
$webhookSecret = Environment::get('FREEMIUS_WEBHOOK_SECRET');
if (!empty($webhookSecret)) {
    echo "5. Testing signature verification...\n";
    
    $testPayload = json_encode($samplePayloads['install.activated']);
    $signature = hash_hmac('sha256', $testPayload, $webhookSecret);
    
    $headers = [
        'content-type' => 'application/json',
        'x-freemius-signature' => 'sha256=' . $signature,
        'user-agent' => 'Freemius-Webhook/1.0'
    ];
    
    try {
        $result = $webhookHandler->processWebhook($headers, $testPayload);
        
        if ($result['success']) {
            echo "  ✓ Signature verification passed\n";
        } else {
            echo "  ✗ Signature verification failed: " . $result['error'] . "\n";
        }
        
    } catch (Exception $e) {
        echo "  ✗ Exception during signature test: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
} else {
    echo "5. Skipping signature verification test (no webhook secret configured)\n\n";
}

echo "=== Test Complete ===\n";

// Display configuration recommendations
echo "\nConfiguration Recommendations:\n";
echo "- Set FREEMIUS_WEBHOOK_SECRET in your .env file for signature verification\n";
echo "- Configure webhook URL in Freemius Dashboard: https://yourdomain.com/api/webhook/freemius\n";
echo "- Test with real webhooks using Freemius Events Log replay feature\n";
echo "- Monitor logs/combined.log for webhook processing details\n";