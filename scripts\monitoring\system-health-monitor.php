<?php

/**
 * System Health Monitor Script
 * 
 * Comprehensive system health monitoring script that can be run independently
 * or integrated with external monitoring systems. Provides detailed health
 * status and performance metrics.
 * 
 * Usage:
 *   php scripts/monitoring/system-health-monitor.php [--format=json|text] [--type=quick|full]
 *   
 * Examples:
 *   php scripts/monitoring/system-health-monitor.php --format=json --type=full
 *   php scripts/monitoring/system-health-monitor.php --format=text --type=quick
 */

require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

use Skpassegna\GuardgeoApi\Config\Environment;
use Skpassegna\GuardgeoApi\Services\SystemHealthService;
use Skpassegna\GuardgeoApi\Services\MonitoringService;
use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * System Health Monitor
 */
class SystemHealthMonitor
{
    private SystemHealthService $healthService;
    private MonitoringService $monitoringService;
    private LoggingService $logger;
    private array $config;
    
    public function __construct()
    {
        // Load environment
        Environment::load(dirname(__DIR__, 2) . '/.env');
        
        // Initialize services
        $this->logger = LoggingService::getInstance();
        $this->healthService = new SystemHealthService();
        $this->monitoringService = new MonitoringService($this->logger);
        
        // Load configuration
        $this->config = $this->loadMonitoringConfig();
    }
    
    /**
     * Run health monitoring
     */
    public function run(string $type = 'quick', string $format = 'json'): int
    {
        try {
            $startTime = microtime(true);
            
            // Perform health check based on type
            switch ($type) {
                case 'full':
                case 'comprehensive':
                    $healthData = $this->performComprehensiveHealthCheck();
                    break;
                    
                case 'quick':
                default:
                    $healthData = $this->performQuickHealthCheck();
                    break;
            }
            
            $executionTime = microtime(true) - $startTime;
            $healthData['monitoring'] = [
                'execution_time' => round($executionTime, 3),
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true)
            ];
            
            // Output results
            $this->outputResults($healthData, $format);
            
            // Determine exit code based on health status
            $exitCode = $this->determineExitCode($healthData);
            
            // Log monitoring results
            $this->logMonitoringResults($type, $healthData, $executionTime);
            
            return $exitCode;
            
        } catch (\Exception $e) {
            $this->logger->error('Health monitoring failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $errorData = [
                'status' => 'error',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
            
            $this->outputResults($errorData, $format);
            
            return 2; // Critical error
        }
    }
    
    /**
     * Perform quick health check
     */
    private function performQuickHealthCheck(): array
    {
        $healthData = $this->healthService->getQuickStatus();
        
        // Add basic performance metrics
        $healthData['performance'] = [
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'memory_limit' => ini_get('memory_limit'),
            'php_version' => PHP_VERSION,
            'server_load' => function_exists('sys_getloadavg') ? sys_getloadavg() : null
        ];
        
        return $healthData;
    }
    
    /**
     * Perform comprehensive health check
     */
    private function performComprehensiveHealthCheck(): array
    {
        // Get comprehensive health check
        $healthData = $this->healthService->performHealthCheck();
        
        // Add performance metrics
        $performanceMetrics = $this->monitoringService->getPerformanceMetrics(
            (new DateTime())->modify('-1 hour'),
            new DateTime()
        );
        
        $healthData['performance_metrics'] = $performanceMetrics;
        
        // Add system resource analysis
        $healthData['resource_analysis'] = $this->analyzeSystemResources();
        
        // Add security status
        $healthData['security_status'] = $this->checkSecurityStatus();
        
        // Add maintenance recommendations
        $healthData['maintenance_recommendations'] = $this->generateMaintenanceRecommendations($healthData);
        
        return $healthData;
    }
    
    /**
     * Analyze system resources
     */
    private function analyzeSystemResources(): array
    {
        $analysis = [
            'memory' => $this->analyzeMemoryUsage(),
            'disk' => $this->analyzeDiskUsage(),
            'cpu' => $this->analyzeCpuUsage(),
            'network' => $this->analyzeNetworkStatus()
        ];
        
        return $analysis;
    }
    
    /**
     * Analyze memory usage
     */
    private function analyzeMemoryUsage(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        $usagePercent = $memoryLimit > 0 ? ($memoryUsage / $memoryLimit) * 100 : 0;
        $peakPercent = $memoryLimit > 0 ? ($memoryPeak / $memoryLimit) * 100 : 0;
        
        $status = 'healthy';
        $recommendations = [];
        
        if ($usagePercent > 80) {
            $status = 'warning';
            $recommendations[] = 'Memory usage is high, consider optimizing or increasing memory limit';
        }
        
        if ($peakPercent > 90) {
            $status = 'critical';
            $recommendations[] = 'Peak memory usage is very high, immediate attention required';
        }
        
        return [
            'status' => $status,
            'current_usage_bytes' => $memoryUsage,
            'current_usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'peak_usage_bytes' => $memoryPeak,
            'peak_usage_mb' => round($memoryPeak / 1024 / 1024, 2),
            'limit_bytes' => $memoryLimit,
            'limit_formatted' => ini_get('memory_limit'),
            'usage_percent' => round($usagePercent, 2),
            'peak_percent' => round($peakPercent, 2),
            'recommendations' => $recommendations
        ];
    }
    
    /**
     * Analyze disk usage
     */
    private function analyzeDiskUsage(): array
    {
        $paths = [
            'root' => '/',
            'logs' => dirname(__DIR__, 2) . '/logs',
            'temp' => sys_get_temp_dir()
        ];
        
        $diskAnalysis = [];
        
        foreach ($paths as $name => $path) {
            if (!is_dir($path)) {
                continue;
            }
            
            $freeBytes = disk_free_space($path);
            $totalBytes = disk_total_space($path);
            $usedBytes = $totalBytes - $freeBytes;
            $usagePercent = $totalBytes > 0 ? ($usedBytes / $totalBytes) * 100 : 0;
            
            $status = 'healthy';
            $recommendations = [];
            
            if ($usagePercent > 80) {
                $status = 'warning';
                $recommendations[] = "Disk usage for {$name} is high";
            }
            
            if ($usagePercent > 90) {
                $status = 'critical';
                $recommendations[] = "Disk usage for {$name} is critical, cleanup required";
            }
            
            $diskAnalysis[$name] = [
                'status' => $status,
                'path' => $path,
                'free_bytes' => $freeBytes,
                'free_gb' => round($freeBytes / 1024 / 1024 / 1024, 2),
                'total_bytes' => $totalBytes,
                'total_gb' => round($totalBytes / 1024 / 1024 / 1024, 2),
                'used_bytes' => $usedBytes,
                'used_gb' => round($usedBytes / 1024 / 1024 / 1024, 2),
                'usage_percent' => round($usagePercent, 2),
                'recommendations' => $recommendations
            ];
        }
        
        return $diskAnalysis;
    }
    
    /**
     * Analyze CPU usage
     */
    private function analyzeCpuUsage(): array
    {
        $loadAverage = function_exists('sys_getloadavg') ? sys_getloadavg() : null;
        $cpuCount = $this->getCpuCount();
        
        $analysis = [
            'load_average' => $loadAverage,
            'cpu_count' => $cpuCount,
            'status' => 'healthy',
            'recommendations' => []
        ];
        
        if ($loadAverage && $cpuCount > 0) {
            $load1min = $loadAverage[0];
            $loadPercent = ($load1min / $cpuCount) * 100;
            
            $analysis['load_percent'] = round($loadPercent, 2);
            
            if ($loadPercent > 80) {
                $analysis['status'] = 'warning';
                $analysis['recommendations'][] = 'CPU load is high';
            }
            
            if ($loadPercent > 100) {
                $analysis['status'] = 'critical';
                $analysis['recommendations'][] = 'CPU load is critical, system may be overloaded';
            }
        }
        
        return $analysis;
    }
    
    /**
     * Analyze network status
     */
    private function analyzeNetworkStatus(): array
    {
        $analysis = [
            'external_connectivity' => $this->testExternalConnectivity(),
            'dns_resolution' => $this->testDnsResolution(),
            'api_endpoints' => $this->testApiEndpoints()
        ];
        
        return $analysis;
    }
    
    /**
     * Test external connectivity
     */
    private function testExternalConnectivity(): array
    {
        $testUrls = [
            'google' => 'https://www.google.com',
            'cloudflare_dns' => '1.1.1.1'
        ];
        
        $results = [];
        
        foreach ($testUrls as $name => $url) {
            $startTime = microtime(true);
            
            if (strpos($url, 'http') === 0) {
                // HTTP test
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 5,
                        'method' => 'HEAD'
                    ]
                ]);
                
                $result = @file_get_contents($url, false, $context);
                $success = $result !== false;
            } else {
                // Ping test
                $success = $this->pingHost($url);
            }
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            $results[$name] = [
                'url' => $url,
                'success' => $success,
                'response_time_ms' => round($responseTime, 2)
            ];
        }
        
        return $results;
    }
    
    /**
     * Test DNS resolution
     */
    private function testDnsResolution(): array
    {
        $testDomains = [
            'google.com',
            'github.com',
            'api.ipregistry.co'
        ];
        
        $results = [];
        
        foreach ($testDomains as $domain) {
            $startTime = microtime(true);
            $ip = gethostbyname($domain);
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            $results[$domain] = [
                'success' => $ip !== $domain,
                'resolved_ip' => $ip !== $domain ? $ip : null,
                'response_time_ms' => round($responseTime, 2)
            ];
        }
        
        return $results;
    }
    
    /**
     * Test API endpoints
     */
    private function testApiEndpoints(): array
    {
        $baseUrl = Environment::get('APP_URL', 'http://localhost');
        
        $endpoints = [
            'health' => $baseUrl . '/api/health',
            'status' => $baseUrl . '/api/status'
        ];
        
        $results = [];
        
        foreach ($endpoints as $name => $url) {
            $startTime = microtime(true);
            
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'method' => 'GET'
                ]
            ]);
            
            $result = @file_get_contents($url, false, $context);
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            $results[$name] = [
                'url' => $url,
                'success' => $result !== false,
                'response_time_ms' => round($responseTime, 2),
                'response_size' => $result ? strlen($result) : 0
            ];
        }
        
        return $results;
    }
    
    /**
     * Check security status
     */
    private function checkSecurityStatus(): array
    {
        $securityChecks = [
            'https_enabled' => $this->checkHttpsEnabled(),
            'secure_headers' => $this->checkSecureHeaders(),
            'file_permissions' => $this->checkFilePermissions(),
            'environment_security' => $this->checkEnvironmentSecurity()
        ];
        
        $overallStatus = 'secure';
        $issues = [];
        
        foreach ($securityChecks as $check => $result) {
            if (!$result['secure']) {
                $overallStatus = 'warning';
                $issues[] = $result['message'];
            }
        }
        
        return [
            'overall_status' => $overallStatus,
            'checks' => $securityChecks,
            'issues' => $issues
        ];
    }
    
    /**
     * Check if HTTPS is enabled
     */
    private function checkHttpsEnabled(): array
    {
        $appUrl = Environment::get('APP_URL', '');
        $httpsEnabled = strpos($appUrl, 'https://') === 0;
        
        return [
            'secure' => $httpsEnabled,
            'message' => $httpsEnabled ? 'HTTPS is enabled' : 'HTTPS is not enabled',
            'app_url' => $appUrl
        ];
    }
    
    /**
     * Check secure headers
     */
    private function checkSecureHeaders(): array
    {
        // This would need to be tested in a web context
        // For CLI, we'll check configuration
        $secureHeadersConfigured = true; // Placeholder
        
        return [
            'secure' => $secureHeadersConfigured,
            'message' => $secureHeadersConfigured ? 'Security headers configured' : 'Security headers not configured'
        ];
    }
    
    /**
     * Check file permissions
     */
    private function checkFilePermissions(): array
    {
        $criticalFiles = [
            '.env' => 0600,
            'config/' => 0755,
            'logs/' => 0755
        ];
        
        $issues = [];
        $basePath = dirname(__DIR__, 2);
        
        foreach ($criticalFiles as $file => $expectedPerms) {
            $filePath = $basePath . '/' . $file;
            
            if (file_exists($filePath)) {
                $actualPerms = fileperms($filePath) & 0777;
                
                if ($actualPerms !== $expectedPerms) {
                    $issues[] = sprintf(
                        '%s has permissions %o, expected %o',
                        $file,
                        $actualPerms,
                        $expectedPerms
                    );
                }
            }
        }
        
        return [
            'secure' => empty($issues),
            'message' => empty($issues) ? 'File permissions are correct' : 'File permission issues found',
            'issues' => $issues
        ];
    }
    
    /**
     * Check environment security
     */
    private function checkEnvironmentSecurity(): array
    {
        $issues = [];
        
        // Check if debug mode is disabled in production
        $appEnv = Environment::get('APP_ENV', 'production');
        $debugMode = Environment::get('APP_DEBUG', 'false') === 'true';
        
        if ($appEnv === 'production' && $debugMode) {
            $issues[] = 'Debug mode is enabled in production';
        }
        
        // Check if default passwords are changed
        $defaultSecrets = [
            'APP_KEY' => 'change-me',
            'DB_PASSWORD' => 'password'
        ];
        
        foreach ($defaultSecrets as $key => $defaultValue) {
            $value = Environment::get($key, '');
            if ($value === $defaultValue) {
                $issues[] = "Default value detected for {$key}";
            }
        }
        
        return [
            'secure' => empty($issues),
            'message' => empty($issues) ? 'Environment security is good' : 'Environment security issues found',
            'issues' => $issues
        ];
    }
    
    /**
     * Generate maintenance recommendations
     */
    private function generateMaintenanceRecommendations(array $healthData): array
    {
        $recommendations = [];
        
        // Check overall health status
        if (($healthData['overall_status'] ?? 'healthy') !== 'healthy') {
            $recommendations[] = [
                'priority' => 'high',
                'category' => 'system_health',
                'message' => 'System health issues detected, review detailed health checks',
                'action' => 'Review and address health check failures'
            ];
        }
        
        // Check performance metrics
        if (isset($healthData['performance_metrics']['api']['error_rate'])) {
            $errorRate = $healthData['performance_metrics']['api']['error_rate'];
            if ($errorRate > 5) {
                $recommendations[] = [
                    'priority' => 'medium',
                    'category' => 'performance',
                    'message' => "API error rate is {$errorRate}%, consider investigation",
                    'action' => 'Review API error logs and optimize error handling'
                ];
            }
        }
        
        // Check resource usage
        if (isset($healthData['resource_analysis']['memory']['usage_percent'])) {
            $memoryUsage = $healthData['resource_analysis']['memory']['usage_percent'];
            if ($memoryUsage > 80) {
                $recommendations[] = [
                    'priority' => 'medium',
                    'category' => 'resources',
                    'message' => "Memory usage is {$memoryUsage}%, consider optimization",
                    'action' => 'Optimize memory usage or increase memory limit'
                ];
            }
        }
        
        // Check disk usage
        if (isset($healthData['resource_analysis']['disk'])) {
            foreach ($healthData['resource_analysis']['disk'] as $disk => $data) {
                if ($data['usage_percent'] > 80) {
                    $recommendations[] = [
                        'priority' => 'medium',
                        'category' => 'storage',
                        'message' => "Disk usage for {$disk} is {$data['usage_percent']}%",
                        'action' => 'Clean up old files or increase disk space'
                    ];
                }
            }
        }
        
        // Check security issues
        if (isset($healthData['security_status']['issues']) && !empty($healthData['security_status']['issues'])) {
            $recommendations[] = [
                'priority' => 'high',
                'category' => 'security',
                'message' => 'Security issues detected',
                'action' => 'Address security configuration issues'
            ];
        }
        
        return $recommendations;
    }
    
    /**
     * Output results in specified format
     */
    private function outputResults(array $data, string $format): void
    {
        switch ($format) {
            case 'json':
                echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n";
                break;
                
            case 'text':
                $this->outputTextFormat($data);
                break;
                
            default:
                throw new \InvalidArgumentException("Unknown format: {$format}");
        }
    }
    
    /**
     * Output results in text format
     */
    private function outputTextFormat(array $data): void
    {
        echo "=== GuardGeo System Health Monitor ===\n\n";
        
        // Overall status
        $status = $data['overall_status'] ?? 'unknown';
        $statusIcon = match($status) {
            'healthy' => '✓',
            'degraded' => '⚠',
            'error' => '✗',
            default => '?'
        };
        
        echo "Overall Status: {$statusIcon} " . strtoupper($status) . "\n";
        echo "Timestamp: " . ($data['timestamp'] ?? date('c')) . "\n\n";
        
        // Quick checks
        if (isset($data['quick_checks'])) {
            echo "=== Quick Checks ===\n";
            foreach ($data['quick_checks'] as $check => $value) {
                echo sprintf("%-20s: %s\n", ucfirst(str_replace('_', ' ', $check)), $value);
            }
            echo "\n";
        }
        
        // Detailed checks
        if (isset($data['checks'])) {
            echo "=== Detailed Health Checks ===\n";
            foreach ($data['checks'] as $checkName => $checkData) {
                $checkStatus = $checkData['status'] ?? 'unknown';
                $checkIcon = match($checkStatus) {
                    'healthy' => '✓',
                    'degraded', 'warning' => '⚠',
                    'error' => '✗',
                    default => '?'
                };
                
                echo sprintf("%s %-20s: %s\n", 
                    $checkIcon, 
                    ucfirst(str_replace('_', ' ', $checkName)), 
                    $checkData['message'] ?? 'No message'
                );
            }
            echo "\n";
        }
        
        // Performance metrics
        if (isset($data['performance'])) {
            echo "=== Performance Metrics ===\n";
            foreach ($data['performance'] as $metric => $value) {
                echo sprintf("%-20s: %s\n", ucfirst(str_replace('_', ' ', $metric)), $value);
            }
            echo "\n";
        }
        
        // Recommendations
        if (isset($data['maintenance_recommendations']) && !empty($data['maintenance_recommendations'])) {
            echo "=== Maintenance Recommendations ===\n";
            foreach ($data['maintenance_recommendations'] as $rec) {
                $priority = strtoupper($rec['priority'] ?? 'medium');
                echo "[{$priority}] {$rec['message']}\n";
                echo "  Action: {$rec['action']}\n\n";
            }
        }
        
        // Monitoring info
        if (isset($data['monitoring'])) {
            echo "=== Monitoring Info ===\n";
            echo sprintf("Execution Time: %.3f seconds\n", $data['monitoring']['execution_time']);
            echo sprintf("Memory Usage: %.2f MB\n", $data['monitoring']['memory_usage'] / 1024 / 1024);
            echo sprintf("Memory Peak: %.2f MB\n", $data['monitoring']['memory_peak'] / 1024 / 1024);
        }
    }
    
    /**
     * Determine exit code based on health status
     */
    private function determineExitCode(array $healthData): int
    {
        $status = $healthData['overall_status'] ?? 'unknown';
        
        return match($status) {
            'healthy' => 0,      // Success
            'degraded' => 1,     // Warning
            'error' => 2,        // Critical
            default => 3         // Unknown
        };
    }
    
    /**
     * Log monitoring results
     */
    private function logMonitoringResults(string $type, array $healthData, float $executionTime): void
    {
        $this->logger->info('System health monitoring completed', [
            'type' => $type,
            'overall_status' => $healthData['overall_status'] ?? 'unknown',
            'execution_time' => $executionTime,
            'memory_peak' => memory_get_peak_usage(true),
            'checks_performed' => count($healthData['checks'] ?? [])
        ]);
    }
    
    /**
     * Load monitoring configuration
     */
    private function loadMonitoringConfig(): array
    {
        return [
            'thresholds' => [
                'memory_warning' => 80,
                'memory_critical' => 90,
                'disk_warning' => 80,
                'disk_critical' => 90,
                'cpu_warning' => 80,
                'cpu_critical' => 100
            ],
            'timeouts' => [
                'network_test' => 5,
                'api_test' => 10
            ]
        ];
    }
    
    /**
     * Helper methods
     */
    
    private function parseMemoryLimit(string $limit): int
    {
        if ($limit === '-1') {
            return -1;
        }
        
        $unit = strtolower(substr($limit, -1));
        $value = (int)substr($limit, 0, -1);
        
        return match($unit) {
            'g' => $value * 1024 * 1024 * 1024,
            'm' => $value * 1024 * 1024,
            'k' => $value * 1024,
            default => (int)$limit
        };
    }
    
    private function getCpuCount(): int
    {
        if (is_file('/proc/cpuinfo')) {
            $cpuinfo = file_get_contents('/proc/cpuinfo');
            return substr_count($cpuinfo, 'processor');
        }
        
        return 1; // Default fallback
    }
    
    private function pingHost(string $host): bool
    {
        $command = sprintf('ping -c 1 -W 5 %s > /dev/null 2>&1', escapeshellarg($host));
        exec($command, $output, $returnCode);
        return $returnCode === 0;
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    $options = getopt('', ['format:', 'type:']);
    $format = $options['format'] ?? 'json';
    $type = $options['type'] ?? 'quick';
    
    $monitor = new SystemHealthMonitor();
    $exitCode = $monitor->run($type, $format);
    
    exit($exitCode);
}