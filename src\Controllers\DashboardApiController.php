<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Utils\AuthMiddleware;
use Skpassegna\GuardgeoApi\Utils\RoleManager;
use Skpassegna\GuardgeoApi\Services\DashboardService;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;

/**
 * Dashboard API Controller
 * 
 * Provides JSON API endpoints for dashboard data with proper
 * authentication and role-based access control.
 */
class DashboardApiController
{
    private AuthMiddleware $authMiddleware;
    private RoleManager $roleManager;
    private DashboardService $dashboardService;
    private LoggingService $logger;

    public function __construct(
        AuthMiddleware $authMiddleware,
        RoleManager $roleManager,
        DashboardService $dashboardService,
        LoggingService $logger
    ) {
        $this->authMiddleware = $authMiddleware;
        $this->roleManager = $roleManager;
        $this->dashboardService = $dashboardService;
        $this->logger = $logger;
    }

    /**
     * Get system overview data
     *
     * @return void
     */
    public function getSystemOverview(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'admin_dashboard')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            $overview = $this->dashboardService->getSystemOverview();
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => $overview
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Dashboard overview API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load system overview'
            ], 500);
        }
    }

    /**
     * Get recent activity data
     *
     * @return void
     */
    public function getRecentActivity(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'admin_dashboard')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            $limit = (int)($_GET['limit'] ?? 10);
            $limit = min(max($limit, 1), 50); // Limit between 1 and 50
            
            $activity = $this->dashboardService->getRecentActivity($limit);
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => $activity
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Recent activity API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load recent activity'
            ], 500);
        }
    }

    /**
     * Get API usage statistics
     *
     * @return void
     */
    public function getApiUsageStats(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'admin_dashboard')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            $period = $_GET['period'] ?? 'week';
            $validPeriods = ['today', 'week', 'month'];
            
            if (!in_array($period, $validPeriods)) {
                $period = 'week';
            }
            
            $stats = $this->dashboardService->getApiUsageStats($period);
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => $stats,
                'period' => $period
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('API usage stats API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load API usage statistics'
            ], 500);
        }
    }

    /**
     * Get IP intelligence statistics
     *
     * @return void
     */
    public function getIpIntelligenceStats(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'ip_intelligence_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            $stats = $this->dashboardService->getIpIntelligenceStats();
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('IP intelligence stats API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load IP intelligence statistics'
            ], 500);
        }
    }

    /**
     * Require authentication and return status
     *
     * @return bool
     */
    private function requireAuthentication(): bool
    {
        if (!$this->authMiddleware->isAuthenticated()) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Authentication required'
            ], 401);
            return false;
        }

        return true;
    }

    /**
     * Send JSON response
     *
     * @param array $data
     * @param int $statusCode
     * @return void
     */
    private function sendJsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Create instance with dependencies
     *
     * @return self
     */
    public static function create(): self
    {
        $logger = new LoggingService();
        $db = new DatabaseConnection();
        $sessionManager = new \Skpassegna\GuardgeoApi\Utils\SessionManager();
        $passwordValidator = new \Skpassegna\GuardgeoApi\Utils\PasswordValidator();
        $emailValidator = new \Skpassegna\GuardgeoApi\Utils\EmailDomainValidator();
        
        $authService = new \Skpassegna\GuardgeoApi\Services\AuthService(
            $db,
            $sessionManager,
            $passwordValidator,
            $emailValidator,
            $logger
        );
        
        $authMiddleware = new AuthMiddleware($authService);
        $roleManager = new RoleManager();
        $dashboardService = new DashboardService($db, $logger);

        return new self($authMiddleware, $roleManager, $dashboardService, $logger);
    }
}