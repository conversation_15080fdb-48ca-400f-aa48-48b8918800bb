<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Models\ProductModel;
use Skpassegna\GuardgeoApi\Models\InstallationModel;
use Skpassegna\GuardgeoApi\Database\ProductRepository;
use Skpassegna\GuardgeoApi\Database\InstallationRepository;
use Skpassegna\GuardgeoApi\Utils\Logger;
use DateTime;

/**
 * Freemius Validation Service
 * 
 * Handles installation validation logic using plugin_id and install_id,
 * manages caching mechanism for Freemius data with appropriate refresh intervals,
 * and provides data synchronization methods for Product and Installation models.
 */
class FreemiusService
{
    private FreemiusApiClient $apiClient;
    private ProductRepository $productRepository;
    private InstallationRepository $installationRepository;
    private Logger $logger;
    
    // Cache refresh intervals (in hours)
    private int $productCacheHours;
    private int $installationCacheHours;
    
    /**
     * Constructor
     */
    public function __construct(
        ?FreemiusApiClient $apiClient = null,
        ?ProductRepository $productRepository = null,
        ?InstallationRepository $installationRepository = null,
        int $productCacheHours = 24,
        int $installationCacheHours = 6
    ) {
        $this->apiClient = $apiClient ?? new FreemiusApiClient();
        $this->productRepository = $productRepository ?? new ProductRepository();
        $this->installationRepository = $installationRepository ?? new InstallationRepository();
        $this->logger = new Logger();
        
        $this->productCacheHours = $productCacheHours;
        $this->installationCacheHours = $installationCacheHours;
    }
    
    /**
     * Validate installation using plugin_id and install_id
     */
    public function validateInstallation(int $pluginId, int $installId): array
    {
        $this->logger->info("Starting installation validation", [
            'plugin_id' => $pluginId,
            'install_id' => $installId
        ]);
        
        try {
            // First, ensure we have the product data
            $product = $this->getProduct($pluginId);
            if (!$product) {
                return [
                    'valid' => false,
                    'reason' => 'Product not found or invalid',
                    'product' => null,
                    'installation' => null
                ];
            }
            
            // Get installation data
            $installation = $this->getInstallation($pluginId, $installId);
            if (!$installation) {
                return [
                    'valid' => false,
                    'reason' => 'Installation not found',
                    'product' => $product,
                    'installation' => null
                ];
            }
            
            // Validate installation status
            $validationResult = $this->validateInstallationStatus($installation);
            
            $this->logger->info("Installation validation completed", [
                'plugin_id' => $pluginId,
                'install_id' => $installId,
                'valid' => $validationResult['valid'],
                'reason' => $validationResult['reason']
            ]);
            
            return [
                'valid' => $validationResult['valid'],
                'reason' => $validationResult['reason'],
                'product' => $product,
                'installation' => $installation
            ];
            
        } catch (FreemiusApiException $e) {
            $this->logger->error("Installation validation failed due to API error", [
                'plugin_id' => $pluginId,
                'install_id' => $installId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'valid' => false,
                'reason' => 'API error: ' . $e->getMessage(),
                'product' => null,
                'installation' => null
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Installation validation failed due to unexpected error", [
                'plugin_id' => $pluginId,
                'install_id' => $installId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'valid' => false,
                'reason' => 'System error during validation',
                'product' => null,
                'installation' => null
            ];
        }
    }
    
    /**
     * Get product with caching
     */
    public function getProduct(int $productId, bool $forceRefresh = false): ?ProductModel
    {
        $this->logger->debug("Getting product", [
            'product_id' => $productId,
            'force_refresh' => $forceRefresh
        ]);
        
        // Try to get from cache first
        if (!$forceRefresh) {
            $cachedProduct = $this->productRepository->findByProductId($productId);
            if ($cachedProduct && !$cachedProduct->isExpired($this->productCacheHours)) {
                $this->logger->debug("Using cached product data", [
                    'product_id' => $productId,
                    'cached_at' => $cachedProduct->cached_at->format('Y-m-d H:i:s')
                ]);
                return $cachedProduct;
            }
        }
        
        // Fetch from API
        try {
            $this->logger->info("Fetching product from Freemius API", [
                'product_id' => $productId
            ]);
            
            $apiData = $this->apiClient->getProduct($productId);
            $product = ProductModel::fromFreemiusResponse($apiData);
            
            // Validate product data
            $validationErrors = $product->validate();
            if (!empty($validationErrors)) {
                $this->logger->warning("Product data validation failed", [
                    'product_id' => $productId,
                    'errors' => $validationErrors
                ]);
                return null;
            }
            
            // Save to cache
            $savedProduct = $this->productRepository->save($product);
            
            $this->logger->info("Product data cached successfully", [
                'product_id' => $productId,
                'title' => $product->title
            ]);
            
            return $savedProduct;
            
        } catch (FreemiusApiException $e) {
            $this->logger->error("Failed to fetch product from API", [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            
            // Return cached data if available, even if expired
            $cachedProduct = $this->productRepository->findByProductId($productId);
            if ($cachedProduct) {
                $this->logger->warning("Using expired cached product data due to API failure", [
                    'product_id' => $productId,
                    'cached_at' => $cachedProduct->cached_at->format('Y-m-d H:i:s')
                ]);
                return $cachedProduct;
            }
            
            return null;
        }
    }
    
    /**
     * Get installation with caching
     */
    public function getInstallation(int $pluginId, int $installId, bool $forceRefresh = false): ?InstallationModel
    {
        $this->logger->debug("Getting installation", [
            'plugin_id' => $pluginId,
            'install_id' => $installId,
            'force_refresh' => $forceRefresh
        ]);
        
        // Try to get from cache first
        if (!$forceRefresh) {
            $cachedInstallation = $this->installationRepository->findByPluginAndInstallId($pluginId, $installId);
            if ($cachedInstallation && !$cachedInstallation->isExpired($this->installationCacheHours)) {
                $this->logger->debug("Using cached installation data", [
                    'plugin_id' => $pluginId,
                    'install_id' => $installId,
                    'cached_at' => $cachedInstallation->cached_at->format('Y-m-d H:i:s')
                ]);
                return $cachedInstallation;
            }
        }
        
        // Fetch from API
        try {
            $this->logger->info("Fetching installation from Freemius API", [
                'plugin_id' => $pluginId,
                'install_id' => $installId
            ]);
            
            $apiData = $this->apiClient->getInstallation($pluginId, $installId);
            $installation = InstallationModel::fromFreemiusResponse($apiData);
            
            // Validate installation data
            $validationErrors = $installation->validate();
            if (!empty($validationErrors)) {
                $this->logger->warning("Installation data validation failed", [
                    'plugin_id' => $pluginId,
                    'install_id' => $installId,
                    'errors' => $validationErrors
                ]);
                return null;
            }
            
            // Save to cache
            $savedInstallation = $this->installationRepository->save($installation);
            
            $this->logger->info("Installation data cached successfully", [
                'plugin_id' => $pluginId,
                'install_id' => $installId,
                'url' => $installation->url
            ]);
            
            return $savedInstallation;
            
        } catch (FreemiusApiException $e) {
            $this->logger->error("Failed to fetch installation from API", [
                'plugin_id' => $pluginId,
                'install_id' => $installId,
                'error' => $e->getMessage()
            ]);
            
            // Return cached data if available, even if expired
            $cachedInstallation = $this->installationRepository->findByPluginAndInstallId($pluginId, $installId);
            if ($cachedInstallation) {
                $this->logger->warning("Using expired cached installation data due to API failure", [
                    'plugin_id' => $pluginId,
                    'install_id' => $installId,
                    'cached_at' => $cachedInstallation->cached_at->format('Y-m-d H:i:s')
                ]);
                return $cachedInstallation;
            }
            
            return null;
        }
    }
    
    /**
     * Validate installation status with comprehensive checks
     */
    private function validateInstallationStatus(InstallationModel $installation): array
    {
        $validationResults = [];
        
        // Check if installation is active
        if (!$installation->is_active) {
            return [
                'valid' => false,
                'reason' => 'Installation is not active',
                'details' => [
                    'is_active' => false,
                    'check_failed' => 'active_status'
                ]
            ];
        }
        $validationResults['is_active'] = true;
        
        // Check if installation is uninstalled
        if ($installation->is_uninstalled) {
            return [
                'valid' => false,
                'reason' => 'Installation has been uninstalled',
                'details' => [
                    'is_uninstalled' => true,
                    'check_failed' => 'uninstall_status'
                ]
            ];
        }
        $validationResults['is_not_uninstalled'] = true;
        
        // Check if installation is disconnected
        if ($installation->is_disconnected) {
            return [
                'valid' => false,
                'reason' => 'Installation is disconnected',
                'details' => [
                    'is_disconnected' => true,
                    'check_failed' => 'connection_status'
                ]
            ];
        }
        $validationResults['is_connected'] = true;
        
        // Check if installation is locked
        if ($installation->is_locked) {
            return [
                'valid' => false,
                'reason' => 'Installation is locked',
                'details' => [
                    'is_locked' => true,
                    'check_failed' => 'lock_status'
                ]
            ];
        }
        $validationResults['is_not_locked'] = true;
        
        // Check premium status (required for GuardGeo)
        if (!$installation->isPremiumInstallation()) {
            return [
                'valid' => false,
                'reason' => 'Installation does not have premium access',
                'details' => [
                    'is_premium' => false,
                    'has_license' => $installation->license_id !== null,
                    'has_subscription' => $installation->subscription_id !== null,
                    'check_failed' => 'premium_status'
                ]
            ];
        }
        $validationResults['has_premium_access'] = true;
        
        // Check if trial has expired (if applicable)
        if ($installation->trial_plan_id !== null && $installation->trial_ends !== null) {
            if ($installation->trial_ends <= new \DateTime()) {
                // Trial expired, check if there's a paid plan
                if (!$installation->is_premium && $installation->license_id === null && $installation->subscription_id === null) {
                    return [
                        'valid' => false,
                        'reason' => 'Trial period has expired and no premium plan is active',
                        'details' => [
                            'trial_expired' => true,
                            'trial_ends' => $installation->trial_ends->format('c'),
                            'has_premium_plan' => false,
                            'check_failed' => 'trial_expiration'
                        ]
                    ];
                }
            }
            $validationResults['trial_status'] = $installation->hasActiveTrial() ? 'active' : 'expired_with_premium';
        }
        
        // Check last seen date (warn if installation hasn't been seen recently)
        $daysSinceLastSeen = $installation->getDaysSinceLastSeen();
        if ($daysSinceLastSeen !== null && $daysSinceLastSeen > 30) {
            $this->logger->warning("Installation hasn't been seen recently", [
                'installation_id' => $installation->id,
                'days_since_last_seen' => $daysSinceLastSeen,
                'last_seen_at' => $installation->last_seen_at?->format('c')
            ]);
        }
        $validationResults['last_seen_days'] = $daysSinceLastSeen;
        
        // All checks passed
        return [
            'valid' => true,
            'reason' => 'Installation is valid and has premium access',
            'details' => $validationResults
        ];
    }
    
    /**
     * Synchronize product data from Freemius API
     */
    public function synchronizeProduct(int $productId): ?ProductModel
    {
        $this->logger->info("Synchronizing product data", [
            'product_id' => $productId
        ]);
        
        return $this->getProduct($productId, true);
    }
    
    /**
     * Synchronize installation data from Freemius API
     */
    public function synchronizeInstallation(int $pluginId, int $installId): ?InstallationModel
    {
        $this->logger->info("Synchronizing installation data", [
            'plugin_id' => $pluginId,
            'install_id' => $installId
        ]);
        
        return $this->getInstallation($pluginId, $installId, true);
    }
    
    /**
     * Synchronize all expired products with batch processing
     */
    public function synchronizeExpiredProducts(int $batchSize = 20): array
    {
        $this->logger->info("Starting batch synchronization of expired products", [
            'batch_size' => $batchSize
        ]);
        
        $expiredProducts = $this->productRepository->findExpiredProducts($this->productCacheHours);
        $totalProducts = count($expiredProducts);
        $results = [];
        $batchResults = [];
        
        // Process in batches to avoid memory issues and API rate limits
        $batches = array_chunk($expiredProducts, $batchSize);
        $batchNumber = 1;
        
        foreach ($batches as $batch) {
            $this->logger->info("Processing batch {$batchNumber} of " . count($batches), [
                'batch_size' => count($batch),
                'batch_number' => $batchNumber
            ]);
            
            $batchStartTime = microtime(true);
            $batchSuccessCount = 0;
            $batchFailureCount = 0;
            
            foreach ($batch as $product) {
                try {
                    $synchronized = $this->synchronizeProduct($product->id);
                    
                    $result = [
                        'product_id' => $product->id,
                        'success' => $synchronized !== null,
                        'error' => null,
                        'batch' => $batchNumber
                    ];
                    
                    if ($result['success']) {
                        $batchSuccessCount++;
                    } else {
                        $batchFailureCount++;
                        $result['error'] = 'Synchronization returned null';
                    }
                    
                    $results[] = $result;
                    
                } catch (FreemiusApiException $e) {
                    $batchFailureCount++;
                    $results[] = [
                        'product_id' => $product->id,
                        'success' => false,
                        'error' => 'API Error: ' . $e->getMessage(),
                        'error_code' => $e->getCode(),
                        'batch' => $batchNumber
                    ];
                    
                    $this->logger->warning("API error during product sync", [
                        'product_id' => $product->id,
                        'error' => $e->getMessage(),
                        'error_code' => $e->getCode()
                    ]);
                    
                } catch (\Exception $e) {
                    $batchFailureCount++;
                    $results[] = [
                        'product_id' => $product->id,
                        'success' => false,
                        'error' => 'System Error: ' . $e->getMessage(),
                        'batch' => $batchNumber
                    ];
                    
                    $this->logger->error("System error during product sync", [
                        'product_id' => $product->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
            
            $batchProcessingTime = round((microtime(true) - $batchStartTime) * 1000, 2);
            
            $batchResults[] = [
                'batch_number' => $batchNumber,
                'batch_size' => count($batch),
                'successful' => $batchSuccessCount,
                'failed' => $batchFailureCount,
                'processing_time_ms' => $batchProcessingTime
            ];
            
            $this->logger->info("Batch {$batchNumber} completed", [
                'successful' => $batchSuccessCount,
                'failed' => $batchFailureCount,
                'processing_time_ms' => $batchProcessingTime
            ]);
            
            // Add delay between batches to respect API rate limits
            if ($batchNumber < count($batches)) {
                sleep(2); // 2 second delay between batches for products (they're less frequent)
            }
            
            $batchNumber++;
        }
        
        $totalSuccessful = count(array_filter($results, fn($r) => $r['success']));
        $totalFailed = count(array_filter($results, fn($r) => !$r['success']));
        
        $this->logger->info("Expired products synchronization completed", [
            'total_products' => $totalProducts,
            'total_batches' => count($batches),
            'successful' => $totalSuccessful,
            'failed' => $totalFailed,
            'success_rate' => $totalProducts > 0 ? round(($totalSuccessful / $totalProducts) * 100, 2) : 0
        ]);
        
        return [
            'summary' => [
                'total_products' => $totalProducts,
                'total_batches' => count($batches),
                'successful' => $totalSuccessful,
                'failed' => $totalFailed,
                'success_rate_percent' => $totalProducts > 0 ? round(($totalSuccessful / $totalProducts) * 100, 2) : 0
            ],
            'batch_results' => $batchResults,
            'detailed_results' => $results
        ];
    }
    
    /**
     * Synchronize all expired installations with batch processing
     */
    public function synchronizeExpiredInstallations(int $batchSize = 50): array
    {
        $this->logger->info("Starting batch synchronization of expired installations", [
            'batch_size' => $batchSize
        ]);
        
        $expiredInstallations = $this->installationRepository->findExpiredInstallations($this->installationCacheHours);
        $totalInstallations = count($expiredInstallations);
        $results = [];
        $batchResults = [];
        
        // Process in batches to avoid memory issues and API rate limits
        $batches = array_chunk($expiredInstallations, $batchSize);
        $batchNumber = 1;
        
        foreach ($batches as $batch) {
            $this->logger->info("Processing batch {$batchNumber} of " . count($batches), [
                'batch_size' => count($batch),
                'batch_number' => $batchNumber
            ]);
            
            $batchStartTime = microtime(true);
            $batchSuccessCount = 0;
            $batchFailureCount = 0;
            
            foreach ($batch as $installation) {
                try {
                    $synchronized = $this->synchronizeInstallation($installation->plugin_id, $installation->id);
                    
                    $result = [
                        'plugin_id' => $installation->plugin_id,
                        'install_id' => $installation->id,
                        'success' => $synchronized !== null,
                        'error' => null,
                        'batch' => $batchNumber
                    ];
                    
                    if ($result['success']) {
                        $batchSuccessCount++;
                    } else {
                        $batchFailureCount++;
                        $result['error'] = 'Synchronization returned null';
                    }
                    
                    $results[] = $result;
                    
                } catch (FreemiusApiException $e) {
                    $batchFailureCount++;
                    $results[] = [
                        'plugin_id' => $installation->plugin_id,
                        'install_id' => $installation->id,
                        'success' => false,
                        'error' => 'API Error: ' . $e->getMessage(),
                        'error_code' => $e->getCode(),
                        'batch' => $batchNumber
                    ];
                    
                    $this->logger->warning("API error during installation sync", [
                        'plugin_id' => $installation->plugin_id,
                        'install_id' => $installation->id,
                        'error' => $e->getMessage(),
                        'error_code' => $e->getCode()
                    ]);
                    
                } catch (\Exception $e) {
                    $batchFailureCount++;
                    $results[] = [
                        'plugin_id' => $installation->plugin_id,
                        'install_id' => $installation->id,
                        'success' => false,
                        'error' => 'System Error: ' . $e->getMessage(),
                        'batch' => $batchNumber
                    ];
                    
                    $this->logger->error("System error during installation sync", [
                        'plugin_id' => $installation->plugin_id,
                        'install_id' => $installation->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
            
            $batchProcessingTime = round((microtime(true) - $batchStartTime) * 1000, 2);
            
            $batchResults[] = [
                'batch_number' => $batchNumber,
                'batch_size' => count($batch),
                'successful' => $batchSuccessCount,
                'failed' => $batchFailureCount,
                'processing_time_ms' => $batchProcessingTime
            ];
            
            $this->logger->info("Batch {$batchNumber} completed", [
                'successful' => $batchSuccessCount,
                'failed' => $batchFailureCount,
                'processing_time_ms' => $batchProcessingTime
            ]);
            
            // Add delay between batches to respect API rate limits
            if ($batchNumber < count($batches)) {
                sleep(1); // 1 second delay between batches
            }
            
            $batchNumber++;
        }
        
        $totalSuccessful = count(array_filter($results, fn($r) => $r['success']));
        $totalFailed = count(array_filter($results, fn($r) => !$r['success']));
        
        $this->logger->info("Expired installations synchronization completed", [
            'total_installations' => $totalInstallations,
            'total_batches' => count($batches),
            'successful' => $totalSuccessful,
            'failed' => $totalFailed,
            'success_rate' => $totalInstallations > 0 ? round(($totalSuccessful / $totalInstallations) * 100, 2) : 0
        ]);
        
        return [
            'summary' => [
                'total_installations' => $totalInstallations,
                'total_batches' => count($batches),
                'successful' => $totalSuccessful,
                'failed' => $totalFailed,
                'success_rate_percent' => $totalInstallations > 0 ? round(($totalSuccessful / $totalInstallations) * 100, 2) : 0
            ],
            'batch_results' => $batchResults,
            'detailed_results' => $results
        ];
    }
    
    /**
     * Get validation statistics
     */
    public function getValidationStatistics(): array
    {
        $productStats = $this->productRepository->getStatistics();
        $installationStats = $this->installationRepository->getStatistics();
        
        return [
            'products' => $productStats,
            'installations' => $installationStats,
            'cache_settings' => [
                'product_cache_hours' => $this->productCacheHours,
                'installation_cache_hours' => $this->installationCacheHours
            ]
        ];
    }
    
    /**
     * Clear expired cache entries
     */
    public function clearExpiredCache(): array
    {
        $this->logger->info("Clearing expired cache entries");
        
        $results = [
            'products_cleared' => 0,
            'installations_cleared' => 0
        ];
        
        // This would typically be implemented as a database cleanup job
        // For now, we'll just log the action
        $this->logger->info("Cache cleanup completed", $results);
        
        return $results;
    }
    
    /**
     * Comprehensive batch synchronization for all expired data
     */
    public function batchSynchronizeExpiredData(array $options = []): array
    {
        $startTime = microtime(true);
        
        $defaultOptions = [
            'product_batch_size' => 20,
            'installation_batch_size' => 50,
            'include_products' => true,
            'include_installations' => true,
            'max_execution_time' => 300, // 5 minutes
            'stop_on_high_failure_rate' => true,
            'failure_rate_threshold' => 50 // Stop if failure rate exceeds 50%
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        $this->logger->info("Starting comprehensive batch synchronization", [
            'options' => $options
        ]);
        
        $results = [
            'started_at' => date('c'),
            'products' => null,
            'installations' => null,
            'summary' => [
                'total_processed' => 0,
                'total_successful' => 0,
                'total_failed' => 0,
                'overall_success_rate' => 0,
                'execution_time_seconds' => 0,
                'stopped_early' => false,
                'stop_reason' => null
            ]
        ];
        
        try {
            // Synchronize products first if enabled
            if ($options['include_products']) {
                $this->logger->info("Starting product synchronization");
                $productResults = $this->synchronizeExpiredProducts($options['product_batch_size']);
                $results['products'] = $productResults;
                
                // Check if we should stop due to high failure rate
                if ($options['stop_on_high_failure_rate'] && 
                    $productResults['summary']['total_products'] > 0) {
                    
                    $failureRate = (1 - ($productResults['summary']['successful'] / $productResults['summary']['total_products'])) * 100;
                    
                    if ($failureRate > $options['failure_rate_threshold']) {
                        $results['summary']['stopped_early'] = true;
                        $results['summary']['stop_reason'] = "Product sync failure rate ({$failureRate}%) exceeded threshold ({$options['failure_rate_threshold']}%)";
                        
                        $this->logger->warning("Stopping batch sync due to high product failure rate", [
                            'failure_rate' => $failureRate,
                            'threshold' => $options['failure_rate_threshold']
                        ]);
                        
                        return $this->finalizeBatchResults($results, $startTime);
                    }
                }
                
                // Check execution time
                if ((microtime(true) - $startTime) > $options['max_execution_time']) {
                    $results['summary']['stopped_early'] = true;
                    $results['summary']['stop_reason'] = 'Maximum execution time exceeded';
                    return $this->finalizeBatchResults($results, $startTime);
                }
            }
            
            // Synchronize installations if enabled
            if ($options['include_installations']) {
                $this->logger->info("Starting installation synchronization");
                $installationResults = $this->synchronizeExpiredInstallations($options['installation_batch_size']);
                $results['installations'] = $installationResults;
                
                // Check execution time again
                if ((microtime(true) - $startTime) > $options['max_execution_time']) {
                    $results['summary']['stopped_early'] = true;
                    $results['summary']['stop_reason'] = 'Maximum execution time exceeded during installation sync';
                }
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Batch synchronization failed with exception", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $results['summary']['stopped_early'] = true;
            $results['summary']['stop_reason'] = 'Exception occurred: ' . $e->getMessage();
        }
        
        return $this->finalizeBatchResults($results, $startTime);
    }
    
    /**
     * Finalize batch synchronization results
     */
    private function finalizeBatchResults(array $results, float $startTime): array
    {
        $executionTime = microtime(true) - $startTime;
        
        // Calculate totals
        $totalProcessed = 0;
        $totalSuccessful = 0;
        $totalFailed = 0;
        
        if ($results['products']) {
            $totalProcessed += $results['products']['summary']['total_products'];
            $totalSuccessful += $results['products']['summary']['successful'];
            $totalFailed += $results['products']['summary']['failed'];
        }
        
        if ($results['installations']) {
            $totalProcessed += $results['installations']['summary']['total_installations'];
            $totalSuccessful += $results['installations']['summary']['successful'];
            $totalFailed += $results['installations']['summary']['failed'];
        }
        
        $results['summary']['total_processed'] = $totalProcessed;
        $results['summary']['total_successful'] = $totalSuccessful;
        $results['summary']['total_failed'] = $totalFailed;
        $results['summary']['overall_success_rate'] = $totalProcessed > 0 ? 
            round(($totalSuccessful / $totalProcessed) * 100, 2) : 0;
        $results['summary']['execution_time_seconds'] = round($executionTime, 2);
        $results['completed_at'] = date('c');
        
        $this->logger->info("Batch synchronization completed", [
            'summary' => $results['summary']
        ]);
        
        return $results;
    }
    
    /**
     * Enhanced health check for Freemius service
     */
    public function healthCheck(): array
    {
        $startTime = microtime(true);
        
        $results = [
            'overall_status' => 'unknown',
            'api_connection' => false,
            'database_connection' => false,
            'cache_status' => 'unknown',
            'webhook_handler' => 'unknown',
            'performance_metrics' => [],
            'errors' => [],
            'warnings' => []
        ];
        
        // Test API connection with timeout
        try {
            $apiStartTime = microtime(true);
            $results['api_connection'] = $this->apiClient->healthCheck();
            $apiResponseTime = round((microtime(true) - $apiStartTime) * 1000, 2);
            
            $results['performance_metrics']['api_response_time_ms'] = $apiResponseTime;
            
            if ($apiResponseTime > 5000) { // 5 seconds
                $results['warnings'][] = "API response time is slow ({$apiResponseTime}ms)";
            }
            
        } catch (FreemiusApiException $e) {
            $results['errors'][] = 'Freemius API error: ' . $e->getMessage();
        } catch (\Exception $e) {
            $results['errors'][] = 'API connection failed: ' . $e->getMessage();
        }
        
        // Test database connection
        try {
            $dbStartTime = microtime(true);
            $productCount = $this->productRepository->countBy([]);
            $installationCount = $this->installationRepository->countBy([]);
            $dbResponseTime = round((microtime(true) - $dbStartTime) * 1000, 2);
            
            $results['database_connection'] = true;
            $results['performance_metrics']['db_response_time_ms'] = $dbResponseTime;
            $results['performance_metrics']['cached_products'] = $productCount;
            $results['performance_metrics']['cached_installations'] = $installationCount;
            
            if ($dbResponseTime > 1000) { // 1 second
                $results['warnings'][] = "Database response time is slow ({$dbResponseTime}ms)";
            }
            
        } catch (\Exception $e) {
            $results['errors'][] = 'Database connection failed: ' . $e->getMessage();
        }
        
        // Check cache status and statistics
        try {
            $stats = $this->getValidationStatistics();
            $results['cache_status'] = 'healthy';
            $results['performance_metrics']['cache_statistics'] = $stats;
            
            // Check for expired data
            $expiredProducts = $this->productRepository->findExpiredProducts($this->productCacheHours);
            $expiredInstallations = $this->installationRepository->findExpiredInstallations($this->installationCacheHours);
            
            $results['performance_metrics']['expired_products'] = count($expiredProducts);
            $results['performance_metrics']['expired_installations'] = count($expiredInstallations);
            
            if (count($expiredProducts) > 10) {
                $results['warnings'][] = count($expiredProducts) . " products have expired cache data";
            }
            
            if (count($expiredInstallations) > 50) {
                $results['warnings'][] = count($expiredInstallations) . " installations have expired cache data";
            }
            
        } catch (\Exception $e) {
            $results['cache_status'] = 'error';
            $results['errors'][] = 'Cache status check failed: ' . $e->getMessage();
        }
        
        // Test webhook handler if available
        try {
            $webhookHandler = new FreemiusWebhookHandler($this->productRepository, $this->installationRepository);
            $webhookConfig = $webhookHandler->validateConfiguration();
            
            if ($webhookConfig['valid']) {
                $results['webhook_handler'] = 'configured';
            } else {
                $results['webhook_handler'] = 'misconfigured';
                $results['warnings'] = array_merge($results['warnings'], $webhookConfig['issues']);
            }
            
        } catch (\Exception $e) {
            $results['webhook_handler'] = 'error';
            $results['warnings'][] = 'Webhook handler check failed: ' . $e->getMessage();
        }
        
        // Determine overall status
        if (empty($results['errors'])) {
            if (empty($results['warnings'])) {
                $results['overall_status'] = 'healthy';
            } else {
                $results['overall_status'] = 'warning';
            }
        } else {
            $results['overall_status'] = 'error';
        }
        
        $results['performance_metrics']['health_check_time_ms'] = round((microtime(true) - $startTime) * 1000, 2);
        
        return $results;
    }
}