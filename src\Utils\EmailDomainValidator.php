<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Config\Environment;

/**
 * Email Domain Validator
 * 
 * Validates email addresses and checks against configured domain whitelist
 * for admin authentication.
 */
class EmailDomainValidator
{
    private array $allowedDomains;
    private bool $strictMode;

    public function __construct()
    {
        // Load allowed domains from environment configuration
        $this->allowedDomains = $this->loadAllowedDomains();
        $this->strictMode = Environment::get('EMAIL_DOMAIN_STRICT_MODE', true);
    }

    /**
     * Validate email format and domain
     *
     * @param string $email
     * @return bool True if email is valid and domain is allowed
     */
    public function isValidEmail(string $email): bool
    {
        // Basic email format validation
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false;
        }

        // Additional format checks
        if (strlen($email) > 254) { // RFC 5321 limit
            return false;
        }

        // Check for dangerous characters
        if (preg_match('/[<>"\']/', $email)) {
            return false;
        }

        return true;
    }

    /**
     * Check if email domain is in the allowed list
     *
     * @param string $email
     * @return bool True if domain is allowed
     */
    public function isAllowedDomain(string $email): bool
    {
        if (!$this->isValidEmail($email)) {
            return false;
        }

        $domain = $this->extractDomain($email);
        
        if (!$domain) {
            return false;
        }

        // If no domains configured and not in strict mode, allow all
        if (empty($this->allowedDomains) && !$this->strictMode) {
            return true;
        }

        // Check exact domain match
        if (in_array(strtolower($domain), array_map('strtolower', $this->allowedDomains))) {
            return true;
        }

        // Check subdomain matches (if domain starts with .)
        foreach ($this->allowedDomains as $allowedDomain) {
            if (str_starts_with($allowedDomain, '.')) {
                $baseDomain = substr($allowedDomain, 1);
                if (str_ends_with(strtolower($domain), '.' . strtolower($baseDomain))) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Extract domain from email address
     *
     * @param string $email
     * @return string|null Domain or null if invalid
     */
    public function extractDomain(string $email): ?string
    {
        $parts = explode('@', $email);
        
        if (count($parts) !== 2) {
            return null;
        }

        $domain = trim($parts[1]);
        
        if (empty($domain)) {
            return null;
        }

        // Remove any trailing dots
        $domain = rtrim($domain, '.');

        return $domain;
    }

    /**
     * Add domain to allowed list
     *
     * @param string $domain
     * @return void
     */
    public function addAllowedDomain(string $domain): void
    {
        $domain = strtolower(trim($domain));
        
        if (!in_array($domain, array_map('strtolower', $this->allowedDomains))) {
            $this->allowedDomains[] = $domain;
        }
    }

    /**
     * Remove domain from allowed list
     *
     * @param string $domain
     * @return void
     */
    public function removeAllowedDomain(string $domain): void
    {
        $domain = strtolower(trim($domain));
        
        $this->allowedDomains = array_filter(
            $this->allowedDomains,
            fn($allowed) => strtolower($allowed) !== $domain
        );
    }

    /**
     * Get list of allowed domains
     *
     * @return array Allowed domains
     */
    public function getAllowedDomains(): array
    {
        return $this->allowedDomains;
    }

    /**
     * Set allowed domains
     *
     * @param array $domains
     * @return void
     */
    public function setAllowedDomains(array $domains): void
    {
        $this->allowedDomains = array_map('trim', $domains);
    }

    /**
     * Validate email against business rules
     *
     * @param string $email
     * @return array Validation result with details
     */
    public function validateEmailForAdmin(string $email): array
    {
        $result = [
            'valid' => false,
            'email' => $email,
            'domain' => null,
            'errors' => []
        ];

        // Basic format validation
        if (!$this->isValidEmail($email)) {
            $result['errors'][] = 'Invalid email format';
            return $result;
        }

        $domain = $this->extractDomain($email);
        $result['domain'] = $domain;

        if (!$domain) {
            $result['errors'][] = 'Could not extract domain from email';
            return $result;
        }

        // Domain whitelist check
        if (!$this->isAllowedDomain($email)) {
            $result['errors'][] = 'Email domain is not authorized for admin access';
            return $result;
        }

        // Additional business rules
        if ($this->isDisposableEmailDomain($domain)) {
            $result['errors'][] = 'Disposable email addresses are not allowed';
            return $result;
        }

        if ($this->isBlockedDomain($domain)) {
            $result['errors'][] = 'Email domain is blocked';
            return $result;
        }

        $result['valid'] = true;
        return $result;
    }

    /**
     * Load allowed domains from environment configuration
     *
     * @return array Allowed domains
     */
    private function loadAllowedDomains(): array
    {
        $domains = Environment::get('ADMIN_ALLOWED_EMAIL_DOMAINS', '');
        
        if (empty($domains)) {
            // Default domains if none configured
            return [
                'skpassegna.me',
                'guardgeo.com',
                '.company.com' // Example subdomain wildcard
            ];
        }

        // Parse comma-separated domains
        $domainList = array_map('trim', explode(',', $domains));
        
        // Filter out empty values
        return array_filter($domainList, fn($domain) => !empty($domain));
    }

    /**
     * Check if domain is a known disposable email provider
     *
     * @param string $domain
     * @return bool True if disposable
     */
    private function isDisposableEmailDomain(string $domain): bool
    {
        $disposableDomains = [
            '10minutemail.com',
            'tempmail.org',
            'guerrillamail.com',
            'mailinator.com',
            'yopmail.com',
            'temp-mail.org',
            'throwaway.email'
        ];

        return in_array(strtolower($domain), $disposableDomains);
    }

    /**
     * Check if domain is explicitly blocked
     *
     * @param string $domain
     * @return bool True if blocked
     */
    private function isBlockedDomain(string $domain): bool
    {
        $blockedDomains = Environment::get('ADMIN_BLOCKED_EMAIL_DOMAINS', '');
        
        if (empty($blockedDomains)) {
            return false;
        }

        $blockedList = array_map('trim', explode(',', $blockedDomains));
        
        return in_array(strtolower($domain), array_map('strtolower', $blockedList));
    }

    /**
     * Get validation rules as human-readable text
     *
     * @return array Validation rules
     */
    public function getValidationRules(): array
    {
        $rules = [
            'Must be a valid email address format',
            'Maximum 254 characters in length',
            'Cannot contain dangerous characters (< > " \')',
        ];

        if (!empty($this->allowedDomains)) {
            $rules[] = 'Must use an authorized email domain: ' . implode(', ', $this->allowedDomains);
        }

        $rules[] = 'Cannot use disposable email addresses';
        
        return $rules;
    }
}