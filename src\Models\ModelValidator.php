<?php

namespace Skpassegna\GuardgeoApi\Models;

/**
 * Model Validator
 * 
 * Provides validation utilities for data models
 */
class ModelValidator
{
    /**
     * Freemius product types
     */
    public const PRODUCT_TYPES = ['plugin', 'theme', 'widget', 'template'];
    
    /**
     * Freemius refund policies
     */
    public const REFUND_POLICIES = ['flexible', 'moderate', 'strict'];
    
    /**
     * Freemius renewals discount types
     */
    public const RENEWALS_DISCOUNT_TYPES = ['percentage', 'dollar'];
    
    /**
     * Admin user roles
     */
    public const ADMIN_ROLES = ['super_admin', 'dev', 'marketing', 'sales'];
    
    /**
     * Log levels
     */
    public const LOG_LEVELS = ['debug', 'info', 'warning', 'error', 'critical'];
    
    /**
     * Log types
     */
    public const LOG_TYPES = ['api', 'admin', 'error', 'system'];
    
    /**
     * IP address types
     */
    public const IP_TYPES = ['IPv4', 'IPv6'];
    
    /**
     * Validate Freemius product type
     */
    public static function validateProductType(string $type): bool
    {
        return in_array($type, self::PRODUCT_TYPES);
    }
    
    /**
     * Validate Freemius refund policy
     */
    public static function validateRefundPolicy(string $policy): bool
    {
        return in_array($policy, self::REFUND_POLICIES);
    }
    
    /**
     * Validate Freemius renewals discount type
     */
    public static function validateRenewalsDiscountType(string $type): bool
    {
        return in_array($type, self::RENEWALS_DISCOUNT_TYPES);
    }
    
    /**
     * Validate admin user role
     */
    public static function validateAdminRole(string $role): bool
    {
        return in_array($role, self::ADMIN_ROLES);
    }
    
    /**
     * Validate log level
     */
    public static function validateLogLevel(string $level): bool
    {
        return in_array($level, self::LOG_LEVELS);
    }
    
    /**
     * Validate log type
     */
    public static function validateLogType(string $type): bool
    {
        return in_array($type, self::LOG_TYPES);
    }
    
    /**
     * Validate IP address type
     */
    public static function validateIpType(string $type): bool
    {
        return in_array($type, self::IP_TYPES);
    }
    
    /**
     * Validate IP address format
     */
    public static function validateIpAddress(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    /**
     * Validate IPv4 address
     */
    public static function validateIpv4Address(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false;
    }
    
    /**
     * Validate IPv6 address
     */
    public static function validateIpv6Address(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) !== false;
    }
    
    /**
     * Validate country code (ISO 3166-1 alpha-2)
     */
    public static function validateCountryCode(?string $code): bool
    {
        if ($code === null) {
            return true;
        }
        
        return strlen($code) === 2 && ctype_alpha($code);
    }
    
    /**
     * Validate language code (ISO 639-1 with optional country)
     */
    public static function validateLanguageCode(?string $code): bool
    {
        if ($code === null) {
            return true;
        }
        
        // Allow formats like 'en', 'en-US', 'en-GB'
        return preg_match('/^[a-z]{2}(-[A-Z]{2})?$/', $code) === 1;
    }
    
    /**
     * Validate version string
     */
    public static function validateVersion(string $version): bool
    {
        // Allow semantic versioning and other common formats
        return preg_match('/^[0-9]+(\.[0-9]+)*([a-zA-Z0-9\-\+\.]*)?$/', $version) === 1;
    }
    
    /**
     * Validate email address
     */
    public static function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate URL
     */
    public static function validateUrl(string $url): bool
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Validate password strength
     */
    public static function validatePassword(string $password): array
    {
        $errors = [];
        
        if (strlen($password) < 12) {
            $errors[] = 'Password must be at least 12 characters long';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }
        
        if (!preg_match('/[^a-zA-Z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }
        
        return $errors;
    }
    
    /**
     * Validate JSON string
     */
    public static function validateJson(?string $json): bool
    {
        if ($json === null) {
            return true;
        }
        
        json_decode($json);
        return json_last_error() === JSON_ERROR_NONE;
    }
    
    /**
     * Validate positive integer
     */
    public static function validatePositiveInteger(mixed $value): bool
    {
        return is_numeric($value) && (int) $value > 0;
    }
    
    /**
     * Validate non-negative integer
     */
    public static function validateNonNegativeInteger(mixed $value): bool
    {
        return is_numeric($value) && (int) $value >= 0;
    }
    
    /**
     * Validate non-negative float
     */
    public static function validateNonNegativeFloat(mixed $value): bool
    {
        return is_numeric($value) && (float) $value >= 0;
    }
    
    /**
     * Validate percentage (0-100)
     */
    public static function validatePercentage(mixed $value): bool
    {
        return is_numeric($value) && (float) $value >= 0 && (float) $value <= 100;
    }
    
    /**
     * Validate HTTP status code
     */
    public static function validateHttpStatusCode(mixed $code): bool
    {
        return is_numeric($code) && (int) $code >= 100 && (int) $code < 600;
    }
    
    /**
     * Get validation error message for enum
     */
    public static function getEnumErrorMessage(string $fieldName, array $allowedValues): string
    {
        $allowed = implode(', ', $allowedValues);
        return "$fieldName must be one of: $allowed";
    }
    
    /**
     * Sanitize string for database storage
     */
    public static function sanitizeString(?string $value): ?string
    {
        if ($value === null) {
            return null;
        }
        
        return trim($value);
    }
    
    /**
     * Sanitize email for database storage
     */
    public static function sanitizeEmail(?string $email): ?string
    {
        if ($email === null) {
            return null;
        }
        
        return strtolower(trim($email));
    }
    
    /**
     * Sanitize URL for database storage
     */
    public static function sanitizeUrl(?string $url): ?string
    {
        if ($url === null) {
            return null;
        }
        
        $url = trim($url);
        
        // Add protocol if missing
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = 'https://' . $url;
        }
        
        return $url;
    }
}