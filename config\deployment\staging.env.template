# GuardGeo Admin Platform - Staging Environment Configuration Template
# 
# Copy this file to .env and update all values with your staging settings.
# Staging should closely mirror production but with some debugging enabled.

# Application Settings
APP_ENV=staging
APP_DEBUG=true
APP_URL=https://staging.server-domain.tld
APP_ENCRYPTION_KEY=your_32_character_encryption_key_here

# Database Configuration
DB_HOST=your-staging-db-host
DB_PORT=5432
DB_NAME=guardgeo_staging
DB_USERNAME=guardgeo_staging
DB_PASSWORD=your_staging_database_password
DB_SSL_CA=/path/to/ssl/ca-certificate.crt
DB_SSL_VERIFY=true

# Redis Configuration (for caching and sessions)
REDIS_HOST=your-staging-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your_staging_redis_password
REDIS_DB=1

# External API Keys (use production keys for realistic testing)
FREEMIUS_API_TOKEN=your_production_freemius_api_token
IPREGISTRY_API_KEY=your_production_ipregistry_api_key

# Security Settings
SESSION_DOMAIN=staging.server-domain.tld
SESSION_SECURE=true
CSRF_PROTECTION=true
RATE_LIMITING_ENABLED=true

# Email Configuration
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-smtp-username
MAIL_PASSWORD=your-smtp-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="GuardGeo Staging"

# Monitoring and Alerting
ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK=https://your-staging-webhook-url.com/alerts
SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/STAGING/WEBHOOK

# Backup Configuration
BACKUP_S3_BUCKET=your-staging-backup-bucket
BACKUP_S3_REGION=us-east-1
BACKUP_S3_KEY=your-aws-access-key
BACKUP_S3_SECRET=your-aws-secret-key
BACKUP_ENCRYPTION_KEY=your_staging_backup_encryption_key

# Performance Monitoring
PHP_FPM_STATUS_URL=http://localhost/php-fpm-status?json

# Maintenance Mode
MAINTENANCE_SECRET=your_staging_maintenance_bypass_secret

# CDN Configuration (optional)
CDN_URL=https://staging-cdn.server-domain.tld

# Logging Configuration (more verbose for staging)
LOG_LEVEL=info
LOG_MAX_FILES=7
LOG_MAX_SIZE=20MB

# Feature Flags
ENABLE_IP_INTELLIGENCE_CACHING=true
ENABLE_FREEMIUS_SYNC=true
ENABLE_AUDIT_LOGGING=true
ENABLE_RATE_LIMITING=true
ENABLE_SECURITY_MONITORING=true
ENABLE_BACKUP_AUTOMATION=true
ENABLE_HEALTH_CHECKS=true
ENABLE_PERFORMANCE_MONITORING=true

# Admin User Domain Whitelist (comma-separated)
ADMIN_ALLOWED_DOMAINS=your-company.com,trusted-partner.com

# IP Security (comma-separated CIDR ranges)
ALLOWED_IP_RANGES=***********/24,10.0.0.0/8
BLOCKED_IP_RANGES=

# SSL/TLS Configuration
SSL_CERTIFICATE_PATH=/path/to/ssl/staging-certificate.crt
SSL_PRIVATE_KEY_PATH=/path/to/ssl/staging-private.key
SSL_CA_BUNDLE_PATH=/path/to/ssl/ca-bundle.crt

# Database Connection Pool Settings
DB_MIN_CONNECTIONS=3
DB_MAX_CONNECTIONS=10
DB_CONNECTION_TIMEOUT=30
DB_IDLE_TIMEOUT=300

# Cache TTL Settings (shorter for staging)
CACHE_TTL_IP_INTELLIGENCE=1800
CACHE_TTL_FREEMIUS_DATA=900
CACHE_TTL_SESSION_DATA=1800

# Rate Limiting Settings (more lenient for testing)
RATE_LIMIT_API_REQUESTS=2000
RATE_LIMIT_LOGIN_ATTEMPTS=10
RATE_LIMIT_TIME_WINDOW=3600

# Backup Retention Settings (shorter retention)
BACKUP_RETENTION_DAILY=3
BACKUP_RETENTION_WEEKLY=2
BACKUP_RETENTION_MONTHLY=3

# Performance Thresholds (more lenient)
ALERT_THRESHOLD_RESPONSE_TIME=3000
ALERT_THRESHOLD_MEMORY_USAGE=85
ALERT_THRESHOLD_ERROR_RATE=10
ALERT_THRESHOLD_DISK_USAGE=90

# External Service Timeouts (longer for debugging)
FREEMIUS_API_TIMEOUT=45
IPREGISTRY_API_TIMEOUT=15
WEBHOOK_TIMEOUT=15

# Compliance Settings
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=90
ANONYMIZE_LOGS=false
AUDIT_TRAIL=true
DATA_ENCRYPTION_AT_REST=true

# Staging-specific Settings
ENABLE_DEBUG_TOOLBAR=false
ENABLE_QUERY_LOGGING=true
ENABLE_PROFILING=true
MOCK_EXTERNAL_APIS=false
ENABLE_TEST_DATA=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_DETAILED_LOGGING=true
SIMULATE_LOAD_TESTING=false