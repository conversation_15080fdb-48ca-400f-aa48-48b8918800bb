<?php

namespace Skpassegna\GuardgeoApi\Config;

use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Centralized Configuration Manager
 * 
 * Manages all system configuration including environment-specific settings,
 * API key management, and operational parameters with secure storage.
 */
class ConfigManager
{
    private static ?ConfigManager $instance = null;
    private array $config = [];
    private array $runtimeConfig = [];
    private DatabaseConnection $db;
    private LoggingService $logger;
    private bool $loaded = false;
    
    /**
     * Private constructor for singleton pattern
     */
    private function __construct()
    {
        $this->db = DatabaseConnection::getInstance();
        $this->logger = LoggingService::getInstance();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance(): ConfigManager
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Load all configuration from various sources
     */
    public function load(): void
    {
        if ($this->loaded) {
            return;
        }
        
        try {
            // Load environment configuration
            Environment::load();
            
            // Load base configuration
            $this->loadBaseConfiguration();
            
            // Load database-stored configuration
            $this->loadDatabaseConfiguration();
            
            // Load environment-specific overrides
            $this->loadEnvironmentOverrides();
            
            // Validate critical configuration
            $this->validateConfiguration();
            
            $this->loaded = true;
            
            $this->logger->info('Configuration loaded successfully', [
                'environment' => $this->getEnvironment(),
                'config_sources' => ['environment', 'database', 'overrides']
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to load configuration', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
    
    /**
     * Load base configuration from Environment class
     */
    private function loadBaseConfiguration(): void
    {
        $this->config = [
            'app' => [
                'name' => 'GuardGeo Admin Platform',
                'version' => '1.0.0',
                'environment' => Environment::getEnvironment(),
                'debug' => Environment::get('APP_DEBUG', 'false') === 'true',
                'timezone' => Environment::get('APP_TIMEZONE', 'UTC'),
                'url' => Environment::get('APP_URL', 'http://localhost'),
            ],
            
            'database' => Environment::getDatabaseConfig(),
            'freemius' => Environment::getFreemiusConfig(),
            'ipregistry' => Environment::getIpRegistryConfig(),
            'logging' => Environment::getLoggingConfig(),
            'auth' => Environment::getAuthConfig(),
            
            'security' => [
                'encryption_key' => Environment::get('APP_ENCRYPTION_KEY'),
                'hash_algorithm' => Environment::get('HASH_ALGORITHM', 'sha256'),
                'session_secure' => Environment::get('SESSION_SECURE', 'true') === 'true',
                'session_httponly' => Environment::get('SESSION_HTTPONLY', 'true') === 'true',
                'csrf_protection' => Environment::get('CSRF_PROTECTION', 'true') === 'true',
            ],
            
            'api' => [
                'rate_limit_enabled' => Environment::get('API_RATE_LIMIT_ENABLED', 'true') === 'true',
                'rate_limit_requests' => (int) Environment::get('API_RATE_LIMIT_REQUESTS', 1000),
                'rate_limit_window' => (int) Environment::get('API_RATE_LIMIT_WINDOW', 3600),
                'request_timeout' => (int) Environment::get('API_REQUEST_TIMEOUT', 30),
                'max_request_size' => (int) Environment::get('API_MAX_REQUEST_SIZE', 1048576), // 1MB
            ],
            
            'cache' => [
                'enabled' => Environment::get('CACHE_ENABLED', 'true') === 'true',
                'driver' => Environment::get('CACHE_DRIVER', 'database'),
                'default_ttl' => (int) Environment::get('CACHE_DEFAULT_TTL', 3600),
            ],
            
            'monitoring' => [
                'enabled' => Environment::get('MONITORING_ENABLED', 'true') === 'true',
                'performance_tracking' => Environment::get('PERFORMANCE_TRACKING', 'true') === 'true',
                'error_reporting' => Environment::get('ERROR_REPORTING', 'true') === 'true',
                'health_check_interval' => (int) Environment::get('HEALTH_CHECK_INTERVAL', 300),
            ]
        ];
    }
    
    /**
     * Load configuration from database
     */
    private function loadDatabaseConfiguration(): void
    {
        try {
            $query = "SELECT config_key, config_value, config_type FROM system_config WHERE is_active = true";
            $result = $this->db->query($query);
            
            while ($row = $result->fetch(\PDO::FETCH_ASSOC)) {
                $value = $this->parseConfigValue($row['config_value'], $row['config_type']);
                $this->setNestedConfig($row['config_key'], $value);
            }
            
        } catch (\Exception $e) {
            // Database config is optional - log but don't fail
            $this->logger->warning('Could not load database configuration', [
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Load environment-specific configuration overrides
     */
    private function loadEnvironmentOverrides(): void
    {
        $environment = $this->getEnvironment();
        $overrideFile = __DIR__ . "/../../config/{$environment}.php";
        
        if (file_exists($overrideFile)) {
            $overrides = require $overrideFile;
            if (is_array($overrides)) {
                $this->config = array_merge_recursive($this->config, $overrides);
            }
        }
    }
    
    /**
     * Validate critical configuration
     */
    private function validateConfiguration(): void
    {
        $required = [
            'database.host',
            'database.database',
            'database.username',
            'freemius.api_token',
            'ipregistry.api_key'
        ];
        
        $missing = [];
        foreach ($required as $key) {
            if (empty($this->get($key))) {
                $missing[] = $key;
            }
        }
        
        if (!empty($missing)) {
            throw new \RuntimeException('Missing required configuration: ' . implode(', ', $missing));
        }
    }
    
    /**
     * Get configuration value using dot notation
     */
    public function get(string $key, $default = null)
    {
        $this->load();
        
        // Check runtime config first
        if (isset($this->runtimeConfig[$key])) {
            return $this->runtimeConfig[$key];
        }
        
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (!is_array($value) || !isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * Set configuration value using dot notation
     */
    public function set(string $key, $value): void
    {
        $this->runtimeConfig[$key] = $value;
    }
    
    /**
     * Set nested configuration value
     */
    private function setNestedConfig(string $key, $value): void
    {
        $keys = explode('.', $key);
        $config = &$this->config;
        
        foreach ($keys as $k) {
            if (!isset($config[$k]) || !is_array($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
    }
    
    /**
     * Parse configuration value based on type
     */
    private function parseConfigValue(string $value, string $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            case 'array':
                return explode(',', $value);
            default:
                return $value;
        }
    }
    
    /**
     * Get current environment
     */
    public function getEnvironment(): string
    {
        return $this->get('app.environment', 'development');
    }
    
    /**
     * Check if running in development
     */
    public function isDevelopment(): bool
    {
        return $this->getEnvironment() === 'development';
    }
    
    /**
     * Check if running in production
     */
    public function isProduction(): bool
    {
        return $this->getEnvironment() === 'production';
    }
    
    /**
     * Get all configuration
     */
    public function all(): array
    {
        $this->load();
        return array_merge($this->config, $this->runtimeConfig);
    }
    
    /**
     * Get configuration section
     */
    public function getSection(string $section): array
    {
        return $this->get($section, []);
    }
    
    /**
     * Save configuration to database
     */
    public function saveToDatabase(string $key, $value, string $type = 'string'): bool
    {
        try {
            $query = "
                INSERT INTO system_config (config_key, config_value, config_type, updated_at) 
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                ON CONFLICT (config_key) 
                DO UPDATE SET 
                    config_value = EXCLUDED.config_value,
                    config_type = EXCLUDED.config_type,
                    updated_at = CURRENT_TIMESTAMP
            ";
            
            $stmt = $this->db->prepare($query);
            $configValue = is_array($value) || is_object($value) ? json_encode($value) : (string) $value;
            
            $result = $stmt->execute([$key, $configValue, $type]);
            
            if ($result) {
                $this->logger->info('Configuration saved to database', [
                    'key' => $key,
                    'type' => $type
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to save configuration to database', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Delete configuration from database
     */
    public function deleteFromDatabase(string $key): bool
    {
        try {
            $query = "DELETE FROM system_config WHERE config_key = ?";
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([$key]);
            
            if ($result) {
                $this->logger->info('Configuration deleted from database', ['key' => $key]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to delete configuration from database', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Get secure API keys (masked for display)
     */
    public function getSecureKeys(): array
    {
        return [
            'freemius_api_token' => $this->maskApiKey($this->get('freemius.api_token')),
            'ipregistry_api_key' => $this->maskApiKey($this->get('ipregistry.api_key')),
            'encryption_key' => $this->maskApiKey($this->get('security.encryption_key')),
        ];
    }
    
    /**
     * Mask API key for display
     */
    private function maskApiKey(?string $key): string
    {
        if (empty($key)) {
            return 'Not configured';
        }
        
        if (strlen($key) <= 8) {
            return str_repeat('*', strlen($key));
        }
        
        return substr($key, 0, 4) . str_repeat('*', strlen($key) - 8) . substr($key, -4);
    }
    
    /**
     * Export configuration for backup
     */
    public function export(bool $includeSensitive = false): array
    {
        $config = $this->all();
        
        if (!$includeSensitive) {
            // Remove sensitive data
            unset($config['database']['password']);
            unset($config['freemius']['api_token']);
            unset($config['ipregistry']['api_key']);
            unset($config['security']['encryption_key']);
        }
        
        return $config;
    }
    
    /**
     * Get configuration health status
     */
    public function getHealthStatus(): array
    {
        $status = [
            'overall' => 'healthy',
            'checks' => []
        ];
        
        // Check database connection
        try {
            $this->db->query("SELECT 1");
            $status['checks']['database'] = 'connected';
        } catch (\Exception $e) {
            $status['checks']['database'] = 'failed';
            $status['overall'] = 'unhealthy';
        }
        
        // Check API keys
        $status['checks']['freemius_api'] = !empty($this->get('freemius.api_token')) ? 'configured' : 'missing';
        $status['checks']['ipregistry_api'] = !empty($this->get('ipregistry.api_key')) ? 'configured' : 'missing';
        
        if ($status['checks']['freemius_api'] === 'missing' || $status['checks']['ipregistry_api'] === 'missing') {
            $status['overall'] = 'degraded';
        }
        
        // Check logging
        $logPath = $this->get('logging.file_path');
        $status['checks']['logging'] = is_writable($logPath) ? 'writable' : 'not_writable';
        
        return $status;
    }
    
    /**
     * Reload configuration from all sources
     */
    public function reload(): void
    {
        $this->loaded = false;
        $this->config = [];
        $this->runtimeConfig = [];
        $this->load();
    }
}