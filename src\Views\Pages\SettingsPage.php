<?php

namespace Skpassegna\GuardgeoApi\Views\Pages;

use Skpassegna\GuardgeoApi\Views\Templates\BaseTemplate;
use Skpassegna\GuardgeoApi\Views\Components\Card;

/**
 * Settings Page Template
 * 
 * System settings and configuration page
 * using the component-based design system.
 */
class SettingsPage extends BaseTemplate
{
    public function render(): string
    {
        return $this->renderPlaceholder();
    }

    private function renderPlaceholder(): string
    {
        $placeholderCard = new Card([
            'title' => 'Settings',
            'content' => '<div class="text-center py-12">
                <i class="fas fa-cog text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Settings Interface</h3>
                <p class="text-gray-600">Settings interface will be implemented in task 9.1</p>
            </div>'
        ]);

        return $placeholderCard->render();
    }
}