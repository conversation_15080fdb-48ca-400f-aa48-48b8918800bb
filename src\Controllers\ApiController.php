<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Services\FreemiusService;
use Skpassegna\GuardgeoApi\Services\IpRegistryService;
use Skpassegna\GuardgeoApi\Services\ApiLogger;
use Skpassegna\GuardgeoApi\Services\ErrorHandlingService;
use Skpassegna\GuardgeoApi\Utils\RequestValidator;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;
use Skpassegna\GuardgeoApi\Utils\SecurityManager;
use Skpassegna\GuardgeoApi\Utils\SqlInjectionPrevention;
use Skpassegna\GuardgeoApi\Utils\PerformanceMonitor;

class ApiController
{
    private FreemiusService $freemiusService;
    private IpRegistryService $ipRegistryService;
    private ApiLogger $logger;
    private RequestValidator $validator;
    private ResponseFormatter $responseFormatter;
    private SecurityManager $securityManager;
    private SqlInjectionPrevention $sqlInjectionPrevention;
    private ErrorHandlingService $errorHandler;

    public function __construct(
        FreemiusService $freemiusService,
        IpRegistryService $ipRegistryService,
        ApiLogger $logger,
        RequestValidator $validator,
        ResponseFormatter $responseFormatter,
        ErrorHandlingService $errorHandler
    ) {
        $this->freemiusService = $freemiusService;
        $this->ipRegistryService = $ipRegistryService;
        $this->logger = $logger;
        $this->validator = $validator;
        $this->responseFormatter = $responseFormatter;
        $this->securityManager = new SecurityManager();
        $this->sqlInjectionPrevention = new SqlInjectionPrevention();
        $this->errorHandler = $errorHandler;
    }

    /**
     * Handle POST /api/analyze endpoint with comprehensive request handling
     * Validates WordPress plugin requests and returns enriched IP intelligence data
     */
    public function analyze(): void
    {
        $performanceMonitor = new \Skpassegna\GuardgeoApi\Utils\PerformanceMonitor();
        $performanceMonitor->startTimer('total_request');
        
        try {
            // Set comprehensive headers for world-class REST API
            $this->responseFormatter->setJsonHeaders();
            $this->responseFormatter->setSecurityHeaders();
            $this->responseFormatter->setCorsHeaders();

            // Handle preflight OPTIONS requests
            if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
                http_response_code(200);
                return;
            }

            // Validate HTTP method
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->sendErrorResponse(405, 'METHOD_NOT_ALLOWED', 'Only POST method is allowed', [
                    'allowed_methods' => ['POST'],
                    'received_method' => $_SERVER['REQUEST_METHOD']
                ], $performanceMonitor);
                return;
            }

            // Validate Content-Type
            if (!$this->validator->isJsonContentType()) {
                $this->sendErrorResponse(400, 'INVALID_CONTENT_TYPE', 'Content-Type must be application/json', [
                    'expected' => 'application/json',
                    'received' => $_SERVER['CONTENT_TYPE'] ?? 'not provided'
                ], $performanceMonitor);
                return;
            }

            // Get client IP for rate limiting and logging
            $clientIp = $this->validator->getClientIpAddress();
            $performanceMonitor->recordMetric('client_ip', $clientIp);
            
            // Enhanced rate limiting check
            $performanceMonitor->startTimer('rate_limit_check');
            if (!$this->checkApiRateLimit($clientIp)) {
                $performanceMonitor->stopTimer('rate_limit_check');
                $this->sendErrorResponse(429, 'RATE_LIMIT_EXCEEDED', 'Too many requests. Please try again later.', [
                    'retry_after' => 3600,
                    'ip' => $clientIp,
                    'limit_type' => 'api_requests'
                ], $performanceMonitor);
                return;
            }
            $performanceMonitor->stopTimer('rate_limit_check');

            // Get and validate request data with comprehensive validation
            $performanceMonitor->startTimer('request_validation');
            $requestData = $this->getRequestData();
            
            if (empty($requestData)) {
                $performanceMonitor->stopTimer('request_validation');
                $this->sendErrorResponse(400, 'INVALID_REQUEST', 'Request data validation failed', [
                    'possible_causes' => [
                        'Empty request body',
                        'Invalid JSON format',
                        'Security validation failed',
                        'SQL injection attempt detected'
                    ]
                ], $performanceMonitor);
                return;
            }
            
            $validationResult = $this->validator->validateAnalyzeRequest($requestData);

            if (!$validationResult['valid']) {
                $performanceMonitor->stopTimer('request_validation');
                echo $this->responseFormatter->formatValidationErrorDetailed($validationResult);
                $this->logRequestWithPerformance($requestData, 400, false, $performanceMonitor);
                return;
            }
            $performanceMonitor->stopTimer('request_validation');

            // Extract validated parameters
            $ip = $requestData['ip'];
            $visitorHash = $requestData['visitor_hash'];
            $pluginId = (int)$requestData['plugin_id'];
            $installId = (int)$requestData['install_id'];
            $url = $requestData['url'];

            // Log the incoming request with validation warnings
            $this->logger->logRequestReceived($requestData);
            if (!empty($validationResult['warnings'])) {
                $this->logger->warning('Request validation warnings', [
                    'warnings' => $validationResult['warnings'],
                    'ip' => $ip,
                    'plugin_id' => $pluginId
                ]);
            }

            // Validate Freemius installation with performance tracking
            $performanceMonitor->startTimer('freemius_validation');
            $freemiusValidation = $this->freemiusService->validateInstallation($pluginId, $installId);
            $performanceMonitor->stopTimer('freemius_validation');
            
            if (!$freemiusValidation['valid']) {
                $this->sendErrorResponse(401, 'INVALID_INSTALLATION', 'Installation not found or inactive', [
                    'plugin_id' => $pluginId,
                    'install_id' => $installId,
                    'validation_details' => $freemiusValidation['error'] ?? 'Unknown error',
                    'freemius_response_time_ms' => $performanceMonitor->getTimer('freemius_validation')['duration_ms'] ?? null
                ], $performanceMonitor);
                
                $this->logRequestWithPerformance($requestData, 401, false, $performanceMonitor);
                return;
            }

            // Log successful Freemius validation
            $this->logger->logFreemiusValidation($pluginId, $installId, true, $freemiusValidation);

            // Get IP intelligence data with performance tracking
            $performanceMonitor->startTimer('ip_intelligence_lookup');
            try {
                $ipIntelligenceModel = $this->ipRegistryService->getIpIntelligence($ip);
                $ipIntelligenceData = $ipIntelligenceModel->jsonSerialize();
                $performanceMonitor->stopTimer('ip_intelligence_lookup');
                
                // Log IP intelligence lookup
                $this->logger->logIpLookup($ip, $ipIntelligenceModel->isCached ?? false, $ipIntelligenceData);
                
            } catch (\Exception $e) {
                $performanceMonitor->stopTimer('ip_intelligence_lookup');
                
                $this->sendErrorResponse(502, 'IP_INTELLIGENCE_ERROR', 'Failed to retrieve IP intelligence data', [
                    'ip' => $ip,
                    'error_details' => $e->getMessage(),
                    'error_type' => get_class($e),
                    'lookup_time_ms' => $performanceMonitor->getTimer('ip_intelligence_lookup')['duration_ms'] ?? null
                ], $performanceMonitor);
                
                $this->logRequestWithPerformance($requestData, 502, true, $performanceMonitor);
                return;
            }

            // Build comprehensive response data
            $responseData = [
                'request' => [
                    'ip' => $ip,
                    'visitor_hash' => $visitorHash,
                    'plugin_id' => $pluginId,
                    'install_id' => $installId,
                    'url' => $url,
                    'processed_at' => date('c')
                ],
                'ip_intelligence' => $ipIntelligenceData,
                'freemius' => [
                    'product' => $freemiusValidation['product'],
                    'installation' => $freemiusValidation['installation'],
                    'validation_time' => date('c')
                ]
            ];

            // Stop total request timer and get performance metrics
            $performanceMonitor->stopTimer('total_request');
            $performanceMetrics = $performanceMonitor->getRequestPerformance();
            
            // Check performance thresholds
            $performanceCheck = $performanceMonitor->checkPerformanceThresholds();
            if (!$performanceCheck['within_thresholds']) {
                $this->logger->warning('Performance thresholds exceeded', [
                    'issues' => $performanceCheck['issues'],
                    'grade' => $performanceCheck['performance_grade'],
                    'ip' => $ip,
                    'plugin_id' => $pluginId
                ]);
            }

            // Send successful response with performance metrics
            http_response_code(200);
            echo $this->responseFormatter->formatSuccessWithPerformance($responseData, $performanceMetrics);
            
            // Log successful response with comprehensive details
            $this->logRequestWithPerformance($requestData, 200, true, $performanceMonitor);
            $this->logger->logResponseSent(200, $responseData, (int)$performanceMetrics['total_duration_ms']);

        } catch (\Exception $e) {
            // Stop any running timers
            $performanceMonitor->stopTimer('total_request');
            
            // Use comprehensive error handling service
            $this->errorHandler->handleException($e, [
                'request_data' => $requestData ?? null,
                'performance' => $performanceMonitor->getRequestPerformance(),
                'endpoint' => '/api/analyze',
                'method' => 'POST'
            ]);
        }
    }

    /**
     * Get and sanitize request data from JSON body with enhanced security
     */
    private function getRequestData(): array
    {
        $rawInput = file_get_contents('php://input');
        
        if (empty($rawInput)) {
            return [];
        }

        // Validate JSON structure
        $data = json_decode($rawInput, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->securityManager->logSecurityEvent('invalid_json_request', [
                'json_error' => json_last_error_msg(),
                'raw_input_length' => strlen($rawInput)
            ]);
            return [];
        }

        // Enhanced input validation and sanitization
        $validationRules = [
            'ip' => 'ip',
            'visitor_hash' => 'string',
            'plugin_id' => 'int',
            'install_id' => 'int',
            'url' => 'url'
        ];

        $validationResult = $this->securityManager->validateAndSanitizeInput($data, $validationRules);
        
        if (!$validationResult['valid']) {
            $this->securityManager->logSecurityEvent('input_validation_failed', [
                'errors' => $validationResult['errors'],
                'input_keys' => array_keys($data)
            ]);
            return [];
        }

        // Additional SQL injection prevention check
        foreach ($validationResult['sanitized'] as $key => $value) {
            if (!$this->sqlInjectionPrevention->validateParameter($value)) {
                $this->sqlInjectionPrevention->logInjectionAttempt((string)$value, "api_request_$key");
                $this->securityManager->logSecurityEvent('sql_injection_attempt', [
                    'field' => $key,
                    'value' => substr((string)$value, 0, 100)
                ]);
                return [];
            }
        }

        return $validationResult['sanitized'];
    }

    /**
     * Enhanced rate limiting check for API requests
     */
    private function checkApiRateLimit(string $ip): bool
    {
        // Check general API rate limit
        if (!$this->securityManager->checkAdvancedRateLimit($ip, 100, 3600, 'api_general')) {
            $this->securityManager->logSecurityEvent('api_rate_limit_exceeded', [
                'ip' => $ip,
                'limit_type' => 'general'
            ]);
            return false;
        }

        // Check analyze endpoint specific rate limit
        if (!$this->securityManager->checkAdvancedRateLimit($ip, 50, 3600, 'api_analyze')) {
            $this->securityManager->logSecurityEvent('api_rate_limit_exceeded', [
                'ip' => $ip,
                'limit_type' => 'analyze_endpoint'
            ]);
            return false;
        }

        return true;
    }

    /**
     * Send error response with comprehensive formatting and logging
     */
    private function sendErrorResponse(int $statusCode, string $errorCode, string $message, array $details, $performanceMonitor): void
    {
        http_response_code($statusCode);
        echo $this->responseFormatter->formatError($errorCode, $message, $details, $statusCode);
        
        // Log the error response
        $this->logger->logApiError($errorCode, $message, array_merge($details, [
            'status_code' => $statusCode,
            'performance' => $performanceMonitor->getRequestPerformance()
        ]));
    }

    /**
     * Log request with comprehensive performance metrics
     */
    private function logRequestWithPerformance(array $requestData, int $statusCode, bool $freemiusValid, $performanceMonitor): void
    {
        $performanceMetrics = $performanceMonitor->getRequestPerformance();
        
        // Log to API logger
        $this->logger->logApiResponse(
            $requestData['ip'] ?? 'unknown',
            (int)($requestData['plugin_id'] ?? 0),
            (int)($requestData['install_id'] ?? 0),
            $statusCode,
            $freemiusValid
        );

        // Log detailed performance metrics
        $this->logger->info('API request performance', [
            'ip' => $requestData['ip'] ?? 'unknown',
            'plugin_id' => $requestData['plugin_id'] ?? null,
            'install_id' => $requestData['install_id'] ?? null,
            'status_code' => $statusCode,
            'performance' => $performanceMetrics,
            'performance_grade' => $performanceMonitor->checkPerformanceThresholds()['performance_grade'] ?? 'N/A'
        ]);

        // Log to specialized API request table with performance data
        $responseData = [
            'status' => $statusCode,
            'freemius_valid' => $freemiusValid,
            'error_message' => $statusCode >= 400 ? 'Request failed' : null
        ];

        $this->logger->logApiRequest(
            $requestData,
            $responseData,
            (int)$performanceMetrics['total_duration_ms']
        );
    }
}