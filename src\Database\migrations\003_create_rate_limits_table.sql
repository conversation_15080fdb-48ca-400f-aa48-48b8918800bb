-- Rate Limits Table
-- Stores rate limiting data for advanced rate limiting with sliding window algorithm

CREATE TABLE IF NOT EXISTS rate_limits (
    id SERIAL PRIMARY KEY,
    rate_key VARCHAR(64) NOT NULL,
    request_time INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for efficient lookups
CREATE INDEX IF NOT EXISTS idx_rate_limits_key_time ON rate_limits(rate_key, request_time);

-- Index for cleanup operations
CREATE INDEX IF NOT EXISTS idx_rate_limits_time ON rate_limits(request_time);

-- Comment on table
COMMENT ON TABLE rate_limits IS 'Stores rate limiting data for sliding window rate limiting algorithm';
COMMENT ON COLUMN rate_limits.rate_key IS 'Hashed key combining identifier and action type';
COMMENT ON COLUMN rate_limits.request_time IS 'Unix timestamp of the request';