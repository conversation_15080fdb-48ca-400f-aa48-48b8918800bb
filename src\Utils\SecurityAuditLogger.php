<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;

/**
 * Security Audit Logger
 * 
 * Specialized logging for security events with structured data,
 * threat classification, and audit trail capabilities.
 */
class SecurityAuditLogger
{
    private LoggingService $logger;
    private DatabaseConnection $db;
    private array $config;

    // Security event types
    public const EVENT_AUTH_SUCCESS = 'auth_success';
    public const EVENT_AUTH_FAILURE = 'auth_failure';
    public const EVENT_RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded';
    public const EVENT_THREAT_DETECTED = 'threat_detected';
    public const EVENT_SQL_INJECTION_ATTEMPT = 'sql_injection_attempt';
    public const EVENT_XSS_ATTEMPT = 'xss_attempt';
    public const EVENT_CSRF_FAILURE = 'csrf_failure';
    public const EVENT_SESSION_HIJACK = 'session_hijack';
    public const EVENT_SUSPICIOUS_ACTIVITY = 'suspicious_activity';
    public const EVENT_ADMIN_ACTION = 'admin_action';

    // Severity levels
    public const SEVERITY_LOW = 'low';
    public const SEVERITY_MEDIUM = 'medium';
    public const SEVERITY_HIGH = 'high';
    public const SEVERITY_CRITICAL = 'critical';

    public function __construct(
        LoggingService $logger,
        DatabaseConnection $db,
        array $config = []
    ) {
        $this->logger = $logger;
        $this->db = $db;
        $this->config = array_merge([
            'store_in_database' => true,
            'log_to_file' => true,
            'alert_on_critical' => true,
            'retention_days' => 90
        ], $config);
    }

    /**
     * Log security event with structured data
     *
     * @param string $eventType Event type constant
     * @param string $severity Severity level
     * @param array $data Event data
     * @param string|null $userId User ID if applicable
     * @return void
     */
    public function logSecurityEvent(
        string $eventType,
        string $severity,
        array $data = [],
        ?string $userId = null
    ): void {
        $eventData = $this->prepareEventData($eventType, $severity, $data, $userId);

        // Log to file
        if ($this->config['log_to_file']) {
            $this->logToFile($eventData);
        }

        // Store in database
        if ($this->config['store_in_database']) {
            $this->storeInDatabase($eventData);
        }

        // Send alerts for critical events
        if ($this->config['alert_on_critical'] && $severity === self::SEVERITY_CRITICAL) {
            $this->sendCriticalAlert($eventData);
        }
    }

    /**
     * Log authentication event
     *
     * @param bool $success Authentication success
     * @param string $email Email address
     * @param array $additionalData Additional event data
     * @return void
     */
    public function logAuthenticationEvent(bool $success, string $email, array $additionalData = []): void
    {
        $eventType = $success ? self::EVENT_AUTH_SUCCESS : self::EVENT_AUTH_FAILURE;
        $severity = $success ? self::SEVERITY_LOW : self::SEVERITY_MEDIUM;

        $data = array_merge([
            'email' => $email,
            'success' => $success,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ], $additionalData);

        $this->logSecurityEvent($eventType, $severity, $data);
    }

    /**
     * Log threat detection event
     *
     * @param array $threats Detected threats
     * @param int $riskScore Risk score
     * @param string $action Action taken
     * @return void
     */
    public function logThreatDetection(array $threats, int $riskScore, string $action): void
    {
        $severity = $this->calculateSeverityFromRiskScore($riskScore);

        $data = [
            'threats' => $threats,
            'risk_score' => $riskScore,
            'action_taken' => $action,
            'threat_count' => count($threats),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
        ];

        $this->logSecurityEvent(self::EVENT_THREAT_DETECTED, $severity, $data);
    }

    /**
     * Log rate limiting event
     *
     * @param string $identifier Rate limit identifier
     * @param string $action Action type
     * @param int $currentCount Current request count
     * @param int $limit Rate limit
     * @return void
     */
    public function logRateLimitEvent(
        string $identifier,
        string $action,
        int $currentCount,
        int $limit
    ): void {
        $data = [
            'identifier' => $identifier,
            'action' => $action,
            'current_count' => $currentCount,
            'limit' => $limit,
            'exceeded_by' => $currentCount - $limit,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];

        $this->logSecurityEvent(self::EVENT_RATE_LIMIT_EXCEEDED, self::SEVERITY_MEDIUM, $data);
    }

    /**
     * Log admin action
     *
     * @param string $action Action performed
     * @param string $userId User ID
     * @param array $details Action details
     * @return void
     */
    public function logAdminAction(string $action, string $userId, array $details = []): void
    {
        $data = array_merge([
            'action' => $action,
            'user_id' => $userId,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ], $details);

        $this->logSecurityEvent(self::EVENT_ADMIN_ACTION, self::SEVERITY_LOW, $data, $userId);
    }

    /**
     * Log SQL injection attempt
     *
     * @param string $parameter Suspicious parameter
     * @param string $context Context where injection was detected
     * @return void
     */
    public function logSqlInjectionAttempt(string $parameter, string $context): void
    {
        $data = [
            'parameter' => substr($parameter, 0, 500), // Limit size
            'context' => $context,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ];

        $this->logSecurityEvent(self::EVENT_SQL_INJECTION_ATTEMPT, self::SEVERITY_HIGH, $data);
    }

    /**
     * Prepare event data with standard fields
     *
     * @param string $eventType
     * @param string $severity
     * @param array $data
     * @param string|null $userId
     * @return array Prepared event data
     */
    private function prepareEventData(
        string $eventType,
        string $severity,
        array $data,
        ?string $userId
    ): array {
        return [
            'event_id' => uniqid('sec_', true),
            'event_type' => $eventType,
            'severity' => $severity,
            'timestamp' => time(),
            'datetime' => date('Y-m-d H:i:s'),
            'user_id' => $userId,
            'session_id' => session_id() ?: null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
            'data' => $data
        ];
    }

    /**
     * Log event to file
     *
     * @param array $eventData
     * @return void
     */
    private function logToFile(array $eventData): void
    {
        $logLevel = match ($eventData['severity']) {
            self::SEVERITY_LOW => 'info',
            self::SEVERITY_MEDIUM => 'warning',
            self::SEVERITY_HIGH => 'error',
            self::SEVERITY_CRITICAL => 'critical',
            default => 'info'
        };

        $message = sprintf(
            'Security Event: %s [%s] - %s',
            $eventData['event_type'],
            strtoupper($eventData['severity']),
            json_encode($eventData['data'])
        );

        $this->logger->log($logLevel, $message, $eventData);
    }

    /**
     * Store event in database
     *
     * @param array $eventData
     * @return void
     */
    private function storeInDatabase(array $eventData): void
    {
        try {
            $query = "
                INSERT INTO security_audit_log (
                    event_id, event_type, severity, user_id, session_id,
                    ip_address, user_agent, request_uri, request_method,
                    event_data, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";

            $this->db->query($query, [
                $eventData['event_id'],
                $eventData['event_type'],
                $eventData['severity'],
                $eventData['user_id'],
                $eventData['session_id'],
                $eventData['ip_address'],
                substr($eventData['user_agent'], 0, 500), // Limit length
                $eventData['request_uri'],
                $eventData['request_method'],
                json_encode($eventData['data']),
                $eventData['datetime']
            ]);
        } catch (\Exception $e) {
            // Fallback to file logging if database fails
            $this->logger->logError('Failed to store security event in database', [
                'error' => $e->getMessage(),
                'event_id' => $eventData['event_id']
            ]);
        }
    }

    /**
     * Send critical alert
     *
     * @param array $eventData
     * @return void
     */
    private function sendCriticalAlert(array $eventData): void
    {
        // This could be enhanced to send email alerts, Slack notifications, etc.
        $this->logger->logCritical('CRITICAL SECURITY EVENT', $eventData);
        
        // Log to system error log as well
        error_log('CRITICAL SECURITY EVENT: ' . json_encode($eventData));
    }

    /**
     * Calculate severity from risk score
     *
     * @param int $riskScore
     * @return string Severity level
     */
    private function calculateSeverityFromRiskScore(int $riskScore): string
    {
        if ($riskScore >= 90) {
            return self::SEVERITY_CRITICAL;
        } elseif ($riskScore >= 70) {
            return self::SEVERITY_HIGH;
        } elseif ($riskScore >= 40) {
            return self::SEVERITY_MEDIUM;
        } else {
            return self::SEVERITY_LOW;
        }
    }

    /**
     * Get security events by criteria
     *
     * @param array $criteria Search criteria
     * @param int $limit Result limit
     * @param int $offset Result offset
     * @return array Security events
     */
    public function getSecurityEvents(array $criteria = [], int $limit = 100, int $offset = 0): array
    {
        try {
            $whereConditions = [];
            $parameters = [];

            // Build WHERE conditions
            if (!empty($criteria['event_type'])) {
                $whereConditions[] = 'event_type = ?';
                $parameters[] = $criteria['event_type'];
            }

            if (!empty($criteria['severity'])) {
                $whereConditions[] = 'severity = ?';
                $parameters[] = $criteria['severity'];
            }

            if (!empty($criteria['user_id'])) {
                $whereConditions[] = 'user_id = ?';
                $parameters[] = $criteria['user_id'];
            }

            if (!empty($criteria['ip_address'])) {
                $whereConditions[] = 'ip_address = ?';
                $parameters[] = $criteria['ip_address'];
            }

            if (!empty($criteria['date_from'])) {
                $whereConditions[] = 'created_at >= ?';
                $parameters[] = $criteria['date_from'];
            }

            if (!empty($criteria['date_to'])) {
                $whereConditions[] = 'created_at <= ?';
                $parameters[] = $criteria['date_to'];
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            $query = "
                SELECT * FROM security_audit_log
                {$whereClause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ";

            $parameters[] = $limit;
            $parameters[] = $offset;

            $result = $this->db->query($query, $parameters);
            return $result->fetchAll(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            $this->logger->logError('Failed to retrieve security events', [
                'error' => $e->getMessage(),
                'criteria' => $criteria
            ]);
            return [];
        }
    }

    /**
     * Get security statistics
     *
     * @param int $days Number of days to analyze
     * @return array Security statistics
     */
    public function getSecurityStatistics(int $days = 7): array
    {
        try {
            $dateFrom = date('Y-m-d H:i:s', strtotime("-{$days} days"));

            $query = "
                SELECT 
                    event_type,
                    severity,
                    COUNT(*) as count,
                    COUNT(DISTINCT ip_address) as unique_ips,
                    COUNT(DISTINCT user_id) as unique_users
                FROM security_audit_log 
                WHERE created_at >= ?
                GROUP BY event_type, severity
                ORDER BY count DESC
            ";

            $result = $this->db->query($query, [$dateFrom]);
            return $result->fetchAll(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            $this->logger->logError('Failed to retrieve security statistics', [
                'error' => $e->getMessage(),
                'days' => $days
            ]);
            return [];
        }
    }

    /**
     * Cleanup old security events
     *
     * @param int $retentionDays Number of days to retain
     * @return int Number of deleted records
     */
    public function cleanupOldEvents(int $retentionDays = null): int
    {
        $retentionDays = $retentionDays ?? $this->config['retention_days'];
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$retentionDays} days"));

        try {
            $query = "DELETE FROM security_audit_log WHERE created_at < ?";
            $result = $this->db->query($query, [$cutoffDate]);
            $deletedCount = $result->rowCount();

            $this->logger->logInfo('Security audit log cleanup completed', [
                'deleted_records' => $deletedCount,
                'cutoff_date' => $cutoffDate,
                'retention_days' => $retentionDays
            ]);

            return $deletedCount;

        } catch (\Exception $e) {
            $this->logger->logError('Failed to cleanup security audit log', [
                'error' => $e->getMessage(),
                'cutoff_date' => $cutoffDate
            ]);
            return 0;
        }
    }
}