<?php

namespace Skpassegna\GuardgeoApi\Database;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Database Migration Manager
 * 
 * Handles database schema updates and migrations for the GuardGeo platform.
 * Provides version control for database changes and rollback capabilities.
 */
class MigrationManager
{
    private DatabaseConnection $db;
    private LoggingService $logger;
    private string $migrationsPath;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->db = DatabaseConnection::getInstance();
        $this->logger = LoggingService::getInstance();
        $this->migrationsPath = __DIR__ . '/migrations';
        
        $this->ensureMigrationsTable();
    }
    
    /**
     * Ensure migrations tracking table exists
     */
    private function ensureMigrationsTable(): void
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS schema_migrations (
                id SERIAL PRIMARY KEY,
                migration_name VARCHAR(255) UNIQUE NOT NULL,
                version VARCHAR(50) NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                execution_time_ms INTEGER,
                checksum VARCHAR(64),
                rollback_sql TEXT
            );
            
            CREATE INDEX IF NOT EXISTS idx_schema_migrations_version ON schema_migrations(version);
            CREATE INDEX IF NOT EXISTS idx_schema_migrations_executed ON schema_migrations(executed_at);
        ";
        
        try {
            $this->db->exec($sql);
        } catch (\Exception $e) {
            $this->logger->error('Failed to create migrations table', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Run pending migrations
     */
    public function migrate(): array
    {
        $results = [];
        
        try {
            $pendingMigrations = $this->getPendingMigrations();
            
            if (empty($pendingMigrations)) {
                $this->logger->info('No pending migrations found');
                return ['status' => 'success', 'message' => 'No pending migrations', 'migrations' => []];
            }
            
            $this->logger->info('Starting migration process', [
                'pending_count' => count($pendingMigrations)
            ]);
            
            foreach ($pendingMigrations as $migration) {
                $result = $this->executeMigration($migration);
                $results[] = $result;
                
                if (!$result['success']) {
                    $this->logger->error('Migration failed, stopping process', [
                        'failed_migration' => $migration['name']
                    ]);
                    break;
                }
            }
            
            $successCount = count(array_filter($results, fn($r) => $r['success']));
            
            $this->logger->info('Migration process completed', [
                'total_migrations' => count($results),
                'successful' => $successCount,
                'failed' => count($results) - $successCount
            ]);
            
            return [
                'status' => $successCount === count($results) ? 'success' : 'partial',
                'message' => "Executed {$successCount} of " . count($results) . " migrations",
                'migrations' => $results
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Migration process failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'status' => 'error',
                'message' => 'Migration process failed: ' . $e->getMessage(),
                'migrations' => $results
            ];
        }
    }
    
    /**
     * Execute a single migration
     */
    private function executeMigration(array $migration): array
    {
        $startTime = microtime(true);
        
        try {
            $this->db->beginTransaction();
            
            // Execute the migration SQL
            $this->db->exec($migration['sql']);
            
            // Calculate execution time
            $executionTime = (int) ((microtime(true) - $startTime) * 1000);
            
            // Record the migration
            $this->recordMigration($migration, $executionTime);
            
            $this->db->commit();
            
            $this->logger->info('Migration executed successfully', [
                'migration' => $migration['name'],
                'version' => $migration['version'],
                'execution_time_ms' => $executionTime
            ]);
            
            return [
                'success' => true,
                'migration' => $migration['name'],
                'version' => $migration['version'],
                'execution_time_ms' => $executionTime,
                'message' => 'Migration executed successfully'
            ];
            
        } catch (\Exception $e) {
            $this->db->rollback();
            
            $this->logger->error('Migration execution failed', [
                'migration' => $migration['name'],
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'migration' => $migration['name'],
                'version' => $migration['version'],
                'error' => $e->getMessage(),
                'message' => 'Migration failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Record migration execution
     */
    private function recordMigration(array $migration, int $executionTime): void
    {
        $sql = "
            INSERT INTO schema_migrations (migration_name, version, execution_time_ms, checksum, rollback_sql)
            VALUES (?, ?, ?, ?, ?)
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $migration['name'],
            $migration['version'],
            $executionTime,
            $migration['checksum'],
            $migration['rollback_sql'] ?? null
        ]);
    }
    
    /**
     * Get pending migrations
     */
    private function getPendingMigrations(): array
    {
        $allMigrations = $this->getAllMigrationFiles();
        $executedMigrations = $this->getExecutedMigrations();
        
        $pending = [];
        
        foreach ($allMigrations as $migration) {
            if (!in_array($migration['name'], $executedMigrations)) {
                $pending[] = $migration;
            }
        }
        
        // Sort by version
        usort($pending, fn($a, $b) => version_compare($a['version'], $b['version']));
        
        return $pending;
    }
    
    /**
     * Get all migration files
     */
    private function getAllMigrationFiles(): array
    {
        $migrations = [];
        
        if (!is_dir($this->migrationsPath)) {
            return $migrations;
        }
        
        $files = glob($this->migrationsPath . '/*.sql');
        
        foreach ($files as $file) {
            $migration = $this->parseMigrationFile($file);
            if ($migration) {
                $migrations[] = $migration;
            }
        }
        
        return $migrations;
    }
    
    /**
     * Parse migration file
     */
    private function parseMigrationFile(string $filePath): ?array
    {
        $filename = basename($filePath, '.sql');
        $content = file_get_contents($filePath);
        
        if ($content === false) {
            return null;
        }
        
        // Extract version from filename (format: YYYY_MM_DD_HHMMSS_description.sql)
        if (preg_match('/^(\d{4}_\d{2}_\d{2}_\d{6})_(.+)$/', $filename, $matches)) {
            $version = $matches[1];
            $description = str_replace('_', ' ', $matches[2]);
        } else {
            // Fallback to filename as version
            $version = $filename;
            $description = $filename;
        }
        
        // Look for rollback SQL in comments
        $rollbackSql = null;
        if (preg_match('/-- ROLLBACK:\s*\n(.*?)(?=\n--|$)/s', $content, $matches)) {
            $rollbackSql = trim($matches[1]);
        }
        
        return [
            'name' => $filename,
            'version' => $version,
            'description' => $description,
            'sql' => $content,
            'checksum' => hash('sha256', $content),
            'rollback_sql' => $rollbackSql,
            'file_path' => $filePath
        ];
    }
    
    /**
     * Get executed migrations
     */
    private function getExecutedMigrations(): array
    {
        try {
            $sql = "SELECT migration_name FROM schema_migrations ORDER BY executed_at";
            $result = $this->db->query($sql);
            
            return $result->fetchAll(\PDO::FETCH_COLUMN);
            
        } catch (\Exception $e) {
            $this->logger->warning('Could not get executed migrations', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get migration status
     */
    public function getStatus(): array
    {
        try {
            $allMigrations = $this->getAllMigrationFiles();
            $executedMigrations = $this->getExecutedMigrations();
            $pendingMigrations = $this->getPendingMigrations();
            
            return [
                'total_migrations' => count($allMigrations),
                'executed_migrations' => count($executedMigrations),
                'pending_migrations' => count($pendingMigrations),
                'pending_list' => array_map(fn($m) => [
                    'name' => $m['name'],
                    'version' => $m['version'],
                    'description' => $m['description']
                ], $pendingMigrations),
                'last_migration' => $this->getLastMigration()
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to get migration status', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'error' => 'Failed to get migration status: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get last executed migration
     */
    private function getLastMigration(): ?array
    {
        try {
            $sql = "
                SELECT migration_name, version, executed_at, execution_time_ms
                FROM schema_migrations 
                ORDER BY executed_at DESC 
                LIMIT 1
            ";
            
            $result = $this->db->query($sql);
            $row = $result->fetch(\PDO::FETCH_ASSOC);
            
            return $row ?: null;
            
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Rollback last migration
     */
    public function rollback(): array
    {
        try {
            $lastMigration = $this->getLastMigration();
            
            if (!$lastMigration) {
                return [
                    'status' => 'error',
                    'message' => 'No migrations to rollback'
                ];
            }
            
            // Get rollback SQL
            $sql = "SELECT rollback_sql FROM schema_migrations WHERE migration_name = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$lastMigration['migration_name']]);
            $rollbackSql = $stmt->fetchColumn();
            
            if (empty($rollbackSql)) {
                return [
                    'status' => 'error',
                    'message' => 'No rollback SQL available for migration: ' . $lastMigration['migration_name']
                ];
            }
            
            $this->db->beginTransaction();
            
            try {
                // Execute rollback SQL
                $this->db->exec($rollbackSql);
                
                // Remove migration record
                $deleteSql = "DELETE FROM schema_migrations WHERE migration_name = ?";
                $deleteStmt = $this->db->prepare($deleteSql);
                $deleteStmt->execute([$lastMigration['migration_name']]);
                
                $this->db->commit();
                
                $this->logger->info('Migration rolled back successfully', [
                    'migration' => $lastMigration['migration_name']
                ]);
                
                return [
                    'status' => 'success',
                    'message' => 'Migration rolled back successfully',
                    'migration' => $lastMigration['migration_name']
                ];
                
            } catch (\Exception $e) {
                $this->db->rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            $this->logger->error('Migration rollback failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'status' => 'error',
                'message' => 'Rollback failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Create a new migration file
     */
    public function createMigration(string $description): string
    {
        if (!is_dir($this->migrationsPath)) {
            mkdir($this->migrationsPath, 0755, true);
        }
        
        $timestamp = date('Y_m_d_His');
        $filename = $timestamp . '_' . str_replace(' ', '_', strtolower($description)) . '.sql';
        $filePath = $this->migrationsPath . '/' . $filename;
        
        $template = "-- Migration: {$description}\n";
        $template .= "-- Created: " . date('Y-m-d H:i:s') . "\n";
        $template .= "-- Version: {$timestamp}\n\n";
        $template .= "-- Add your migration SQL here\n\n";
        $template .= "-- ROLLBACK:\n";
        $template .= "-- Add rollback SQL here (optional)\n";
        
        file_put_contents($filePath, $template);
        
        $this->logger->info('Migration file created', [
            'filename' => $filename,
            'path' => $filePath
        ]);
        
        return $filePath;
    }
    
    /**
     * Validate migration files
     */
    public function validateMigrations(): array
    {
        $migrations = $this->getAllMigrationFiles();
        $issues = [];
        
        foreach ($migrations as $migration) {
            // Check for SQL syntax (basic validation)
            if (empty(trim($migration['sql']))) {
                $issues[] = [
                    'migration' => $migration['name'],
                    'issue' => 'Empty migration file'
                ];
            }
            
            // Check for potentially dangerous operations
            $dangerousPatterns = [
                '/DROP\s+DATABASE/i',
                '/TRUNCATE\s+TABLE/i',
                '/DELETE\s+FROM\s+\w+\s*;?\s*$/i' // DELETE without WHERE
            ];
            
            foreach ($dangerousPatterns as $pattern) {
                if (preg_match($pattern, $migration['sql'])) {
                    $issues[] = [
                        'migration' => $migration['name'],
                        'issue' => 'Contains potentially dangerous operation',
                        'pattern' => $pattern
                    ];
                }
            }
        }
        
        return [
            'total_migrations' => count($migrations),
            'issues_found' => count($issues),
            'issues' => $issues
        ];
    }
}