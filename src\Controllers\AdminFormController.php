<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Utils\AuthMiddleware;
use Skpassegna\GuardgeoApi\Utils\FormValidator;
use Skpassegna\GuardgeoApi\Utils\AjaxFormHandler;
use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Admin Form Controller
 * 
 * Demonstrates comprehensive form handling capabilities for admin interface
 * with proper validation, CSRF protection, and AJAX support.
 */
class AdminFormController
{
    private AuthMiddleware $authMiddleware;
    private FormValidator $formValidator;
    private AjaxFormHandler $ajaxHandler;
    private LoggingService $logger;

    public function __construct(
        AuthMiddleware $authMiddleware,
        FormValidator $formValidator,
        AjaxFormHandler $ajaxHandler,
        LoggingService $logger
    ) {
        $this->authMiddleware = $authMiddleware;
        $this->formValidator = $formValidator;
        $this->ajaxHandler = $ajaxHandler;
        $this->logger = $logger;
    }

    /**
     * Handle user creation form
     */
    public function handleUserCreation(): void
    {
        $validationRules = [
            'email' => 'required|email|unique:admin_users,email',
            'password' => 'required|password',
            'password_confirmation' => 'required|confirmed',
            'role' => 'required|role',
            'is_active' => 'boolean'
        ];

        $customMessages = [
            'email.unique' => 'This email address is already registered',
            'password.password' => 'Password must meet security requirements (minimum 12 characters, mixed case, numbers, symbols)',
            'password_confirmation.confirmed' => 'Password confirmation does not match',
            'role.role' => 'Please select a valid user role'
        ];

        $this->ajaxHandler->handleSubmission(
            $validationRules,
            [$this, 'processUserCreation'],
            [
                'customMessages' => $customMessages,
                'requireAuth' => true,
                'requireCsrf' => true
            ]
        );
    }

    /**
     * Process user creation after validation
     */
    public function processUserCreation(array $validatedData, array $currentUser): array
    {
        // Check if current user has permission to create users
        $roleManager = new \Skpassegna\GuardgeoApi\Utils\RoleManager();
        if (!$roleManager->canAccessFeature($currentUser['role'], 'user_management')) {
            throw new \Exception('Insufficient permissions to create users');
        }

        // Create user in database
        $db = new \Skpassegna\GuardgeoApi\Database\DatabaseConnection();
        $userRepository = new \Skpassegna\GuardgeoApi\Database\AdminUserRepository($db);

        $userData = [
            'email' => $validatedData['email'],
            'password_hash' => password_hash($validatedData['password'], PASSWORD_DEFAULT),
            'role' => $validatedData['role'],
            'is_active' => $validatedData['is_active'] ?? true,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $userModel = new \Skpassegna\GuardgeoApi\Models\AdminUserModel($userData);
        $savedUser = $userRepository->save($userModel);

        if (!$savedUser) {
            throw new \Exception('Failed to create user in database');
        }

        // Log the action
        $this->logger->logAdminAction('user_created', [
            'created_user_id' => $savedUser->id,
            'created_user_email' => $savedUser->email,
            'created_user_role' => $savedUser->role,
            'created_by' => $currentUser['email']
        ]);

        return [
            'id' => $savedUser->id,
            'email' => $savedUser->email,
            'role' => $savedUser->role,
            'is_active' => $savedUser->is_active,
            'message' => 'User created successfully',
            'refresh' => true // Trigger page data refresh
        ];
    }

    /**
     * Handle user update form
     */
    public function handleUserUpdate(): void
    {
        $validationRules = [
            'id' => 'required|integer',
            'email' => 'required|email|unique:admin_users,email,{id}',
            'role' => 'required|role',
            'is_active' => 'boolean'
        ];

        // Password is optional for updates
        $requestData = $this->getRequestData();
        if (!empty($requestData['password'])) {
            $validationRules['password'] = 'password';
            $validationRules['password_confirmation'] = 'confirmed';
        }

        $this->ajaxHandler->handleSubmission(
            $validationRules,
            [$this, 'processUserUpdate'],
            [
                'requireAuth' => true,
                'requireCsrf' => true
            ]
        );
    }

    /**
     * Process user update after validation
     */
    public function processUserUpdate(array $validatedData, array $currentUser): array
    {
        // Check permissions
        $roleManager = new \Skpassegna\GuardgeoApi\Utils\RoleManager();
        if (!$roleManager->canAccessFeature($currentUser['role'], 'user_management')) {
            throw new \Exception('Insufficient permissions to update users');
        }

        // Get existing user
        $db = new \Skpassegna\GuardgeoApi\Database\DatabaseConnection();
        $userRepository = new \Skpassegna\GuardgeoApi\Database\AdminUserRepository($db);
        $existingUser = $userRepository->getById($validatedData['id']);

        if (!$existingUser) {
            throw new \Exception('User not found');
        }

        // Prevent self-deactivation for super admins
        if ($currentUser['id'] == $validatedData['id'] && 
            $currentUser['role'] === 'super_admin' && 
            !($validatedData['is_active'] ?? true)) {
            throw new \Exception('Super admins cannot deactivate themselves');
        }

        // Update user data
        $updateData = [
            'email' => $validatedData['email'],
            'role' => $validatedData['role'],
            'is_active' => $validatedData['is_active'] ?? true,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update password if provided
        if (!empty($validatedData['password'])) {
            $updateData['password_hash'] = password_hash($validatedData['password'], PASSWORD_DEFAULT);
        }

        $success = $userRepository->update($validatedData['id'], $updateData);

        if (!$success) {
            throw new \Exception('Failed to update user');
        }

        // Log the action
        $this->logger->logAdminAction('user_updated', [
            'updated_user_id' => $validatedData['id'],
            'updated_user_email' => $validatedData['email'],
            'updated_user_role' => $validatedData['role'],
            'updated_by' => $currentUser['email'],
            'password_changed' => !empty($validatedData['password'])
        ]);

        return [
            'id' => $validatedData['id'],
            'email' => $validatedData['email'],
            'role' => $validatedData['role'],
            'is_active' => $updateData['is_active'],
            'message' => 'User updated successfully',
            'refresh' => true
        ];
    }

    /**
     * Handle settings form
     */
    public function handleSettingsUpdate(): void
    {
        $validationRules = [
            'site_name' => 'required|max:100',
            'admin_email' => 'required|email',
            'session_timeout' => 'required|integer|min:5|max:1440',
            'max_login_attempts' => 'required|integer|min:3|max:10',
            'allowed_domains' => 'required',
            'blocked_domains' => '',
            'enable_2fa' => 'boolean',
            'enable_audit_log' => 'boolean',
            'log_retention_days' => 'required|integer|min:7|max:365'
        ];

        $this->ajaxHandler->handleSubmission(
            $validationRules,
            [$this, 'processSettingsUpdate'],
            [
                'requireAuth' => true,
                'requireCsrf' => true
            ]
        );
    }

    /**
     * Process settings update
     */
    public function processSettingsUpdate(array $validatedData, array $currentUser): array
    {
        // Check permissions
        $roleManager = new \Skpassegna\GuardgeoApi\Utils\RoleManager();
        if (!$roleManager->canAccessFeature($currentUser['role'], 'system_settings')) {
            throw new \Exception('Insufficient permissions to update system settings');
        }

        // Update settings in database
        $db = new \Skpassegna\GuardgeoApi\Database\DatabaseConnection();
        
        foreach ($validatedData as $key => $value) {
            $stmt = $db->prepare("
                INSERT INTO system_settings (setting_key, setting_value, updated_at, updated_by) 
                VALUES (?, ?, ?, ?)
                ON CONFLICT (setting_key) 
                DO UPDATE SET 
                    setting_value = EXCLUDED.setting_value,
                    updated_at = EXCLUDED.updated_at,
                    updated_by = EXCLUDED.updated_by
            ");
            
            $stmt->execute([
                $key,
                is_bool($value) ? ($value ? '1' : '0') : (string)$value,
                date('Y-m-d H:i:s'),
                $currentUser['email']
            ]);
        }

        // Log the action
        $this->logger->logAdminAction('settings_updated', [
            'updated_settings' => array_keys($validatedData),
            'updated_by' => $currentUser['email']
        ]);

        return [
            'message' => 'Settings updated successfully',
            'refresh' => true
        ];
    }

    /**
     * Handle file upload (e.g., backup restore)
     */
    public function handleFileUpload(): void
    {
        $validationRules = [
            'upload_type' => 'required|in:backup,import',
            'description' => 'max:255'
        ];

        $fileRules = [
            'file' => ['required', 'max_size:50000', 'extensions:sql,json,csv'] // 50MB max
        ];

        $this->ajaxHandler->handleFileUpload(
            $validationRules,
            $fileRules,
            [$this, 'processFileUpload'],
            [
                'requireAuth' => true,
                'requireCsrf' => true
            ]
        );
    }

    /**
     * Process file upload
     */
    public function processFileUpload(array $validatedData, array $validatedFiles, array $currentUser): array
    {
        // Check permissions
        $roleManager = new \Skpassegna\GuardgeoApi\Utils\RoleManager();
        if (!$roleManager->canAccessFeature($currentUser['role'], 'system_maintenance')) {
            throw new \Exception('Insufficient permissions to upload files');
        }

        $uploadedFile = $validatedFiles['file'];
        $uploadType = $validatedData['upload_type'];
        
        // Create secure upload directory
        $uploadDir = __DIR__ . '/../../uploads/' . date('Y/m/');
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Generate secure filename
        $extension = pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
        $filename = uniqid('upload_') . '.' . $extension;
        $filepath = $uploadDir . $filename;

        // Move uploaded file
        if (!move_uploaded_file($uploadedFile['tmp_name'], $filepath)) {
            throw new \Exception('Failed to save uploaded file');
        }

        // Log the upload
        $this->logger->logAdminAction('file_uploaded', [
            'upload_type' => $uploadType,
            'filename' => $uploadedFile['name'],
            'filepath' => $filepath,
            'filesize' => $uploadedFile['size'],
            'description' => $validatedData['description'] ?? '',
            'uploaded_by' => $currentUser['email']
        ]);

        return [
            'filename' => $uploadedFile['name'],
            'filepath' => $filepath,
            'filesize' => $uploadedFile['size'],
            'upload_type' => $uploadType,
            'message' => 'File uploaded successfully'
        ];
    }

    /**
     * Get request data (POST or JSON)
     */
    private function getRequestData(): array
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        if (strpos($contentType, 'application/json') !== false) {
            $json = file_get_contents('php://input');
            return json_decode($json, true) ?: [];
        }
        
        return $_POST;
    }
}