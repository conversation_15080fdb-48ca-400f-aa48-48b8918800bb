<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Database\DatabaseException;
use Skpassegna\GuardgeoApi\Utils\PerformanceMonitor;

/**
 * API Request Logger
 * 
 * Specialized logger for API requests with comprehensive performance metrics,
 * request/response tracking, and detailed analytics for monitoring and debugging.
 */
class ApiRequestLogger
{
    private LoggingService $loggingService;
    
    public function __construct(LoggingService $loggingService = null)
    {
        $this->loggingService = $loggingService ?? LoggingServiceFactory::getLoggingService();
    }
    
    /**
     * Log complete API request with performance metrics
     */
    public function logApiRequest(
        array $requestData,
        array $responseData,
        PerformanceMonitor $performanceMonitor,
        int $statusCode,
        bool $success = null
    ): void {
        $success = $success ?? ($statusCode >= 200 && $statusCode < 300);
        $performanceMetrics = $performanceMonitor->getRequestPerformance();
        
        try {
            // Log to specialized API requests table
            $this->logToApiRequestsTable($requestData, $responseData, $performanceMetrics, $statusCode);
            
            // Log to general system logs
            $this->logToSystemLogs($requestData, $responseData, $performanceMetrics, $statusCode, $success);
            
            // Log performance issues if any
            $this->logPerformanceIssues($performanceMonitor, $requestData);
            
        } catch (\Exception $e) {
            // Fallback logging to prevent request failures due to logging issues
            error_log("ApiRequestLogger: Failed to log API request - " . $e->getMessage());
        }
    }
    
    /**
     * Log to specialized API requests table
     */
    private function logToApiRequestsTable(
        array $requestData,
        array $responseData,
        array $performanceMetrics,
        int $statusCode
    ): void {
        try {
            $sql = "INSERT INTO api_requests (
                ip, visitor_hash, plugin_id, install_id, url,
                response_status, response_time_ms, memory_usage_mb, peak_memory_mb,
                database_queries, database_time_ms, external_api_calls, external_api_time_ms,
                freemius_valid, error_message, request_size_bytes, response_size_bytes,
                user_agent, referer, request_id, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
            
            $params = [
                $requestData['ip'] ?? null,
                $requestData['visitor_hash'] ?? null,
                $requestData['plugin_id'] ?? null,
                $requestData['install_id'] ?? null,
                $requestData['url'] ?? null,
                $statusCode,
                $performanceMetrics['total_duration_ms'] ?? null,
                $performanceMetrics['memory_usage_mb'] ?? null,
                $performanceMetrics['peak_memory_mb'] ?? null,
                $performanceMetrics['database_performance']['total_queries'] ?? 0,
                $performanceMetrics['database_performance']['total_duration_ms'] ?? 0,
                $performanceMetrics['external_api_performance']['total_calls'] ?? 0,
                $performanceMetrics['external_api_performance']['total_duration_ms'] ?? 0,
                $responseData['freemius_valid'] ?? null,
                $responseData['error_message'] ?? null,
                $this->calculateRequestSize($requestData),
                $this->calculateResponseSize($responseData),
                $_SERVER['HTTP_USER_AGENT'] ?? null,
                $_SERVER['HTTP_REFERER'] ?? null,
                $this->getRequestId()
            ];
            
            DatabaseConnection::execute($sql, $params);
            
        } catch (DatabaseException $e) {
            error_log("ApiRequestLogger: Database logging failed - " . $e->getMessage());
        }
    }
    
    /**
     * Log to general system logs with structured data
     */
    private function logToSystemLogs(
        array $requestData,
        array $responseData,
        array $performanceMetrics,
        int $statusCode,
        bool $success
    ): void {
        $logLevel = $success ? LoggingService::LEVEL_INFO : LoggingService::LEVEL_WARNING;
        
        $message = sprintf(
            'API request %s - %d %s (%.2fms)',
            $success ? 'completed' : 'failed',
            $statusCode,
            $this->getStatusText($statusCode),
            $performanceMetrics['total_duration_ms'] ?? 0
        );
        
        $context = [
            'request' => [
                'ip' => $requestData['ip'] ?? null,
                'plugin_id' => $requestData['plugin_id'] ?? null,
                'install_id' => $requestData['install_id'] ?? null,
                'url' => $requestData['url'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ],
            'response' => [
                'status_code' => $statusCode,
                'success' => $success,
                'freemius_valid' => $responseData['freemius_valid'] ?? null,
                'error_message' => $responseData['error_message'] ?? null
            ],
            'performance' => $performanceMetrics,
            'request_id' => $this->getRequestId()
        ];
        
        $this->loggingService->log(LoggingService::TYPE_API, $logLevel, $message, $context);
    }
    
    /**
     * Log performance issues if thresholds are exceeded
     */
    private function logPerformanceIssues(PerformanceMonitor $performanceMonitor, array $requestData): void
    {
        $thresholdCheck = $performanceMonitor->checkPerformanceThresholds();
        
        if (!$thresholdCheck['within_thresholds']) {
            $message = sprintf(
                'Performance issues detected (Grade: %s) for IP %s',
                $thresholdCheck['performance_grade'],
                $requestData['ip'] ?? 'unknown'
            );
            
            $context = [
                'performance_grade' => $thresholdCheck['performance_grade'],
                'issues' => $thresholdCheck['issues'],
                'request_data' => $requestData,
                'performance_report' => $performanceMonitor->generateReport()
            ];
            
            $this->loggingService->log(
                LoggingService::TYPE_SYSTEM,
                LoggingService::LEVEL_WARNING,
                $message,
                $context
            );
        }
    }
    
    /**
     * Log API validation step with details
     */
    public function logValidationStep(string $step, bool $success, array $details = [], array $requestData = []): void
    {
        $message = sprintf('API validation %s: %s', $step, $success ? 'PASSED' : 'FAILED');
        
        $context = [
            'validation_step' => $step,
            'success' => $success,
            'details' => $details,
            'ip' => $requestData['ip'] ?? null,
            'plugin_id' => $requestData['plugin_id'] ?? null,
            'request_id' => $this->getRequestId()
        ];
        
        $level = $success ? LoggingService::LEVEL_DEBUG : LoggingService::LEVEL_WARNING;
        $this->loggingService->log(LoggingService::TYPE_API, $level, $message, $context);
    }
    
    /**
     * Log external API call with performance metrics
     */
    public function logExternalApiCall(
        string $service,
        string $endpoint,
        int $responseCode,
        float $duration,
        array $requestData = [],
        array $responseData = []
    ): void {
        $message = sprintf(
            'External API call to %s %s: %d in %.2fms',
            $service,
            $endpoint,
            $responseCode,
            $duration * 1000
        );
        
        $context = [
            'external_service' => $service,
            'endpoint' => $endpoint,
            'response_code' => $responseCode,
            'duration_ms' => round($duration * 1000, 2),
            'success' => $responseCode >= 200 && $responseCode < 300,
            'request_data_size' => $this->calculateDataSize($requestData),
            'response_data_size' => $this->calculateDataSize($responseData),
            'request_id' => $this->getRequestId()
        ];
        
        $level = $responseCode >= 400 ? LoggingService::LEVEL_WARNING : LoggingService::LEVEL_INFO;
        $this->loggingService->log(LoggingService::TYPE_API, $level, $message, $context);
    }
    
    /**
     * Log rate limiting event
     */
    public function logRateLimitEvent(string $ip, string $limitType, int $currentCount, int $limit): void
    {
        $exceeded = $currentCount > $limit;
        
        $message = sprintf(
            'Rate limit %s for IP %s: %d/%d requests (%s)',
            $limitType,
            $ip,
            $currentCount,
            $limit,
            $exceeded ? 'EXCEEDED' : 'OK'
        );
        
        $context = [
            'ip' => $ip,
            'limit_type' => $limitType,
            'current_count' => $currentCount,
            'limit' => $limit,
            'exceeded' => $exceeded,
            'percentage' => round(($currentCount / $limit) * 100, 2),
            'request_id' => $this->getRequestId()
        ];
        
        $level = $exceeded ? LoggingService::LEVEL_WARNING : LoggingService::LEVEL_DEBUG;
        $this->loggingService->log(LoggingService::TYPE_API, $level, $message, $context);
    }
    
    /**
     * Log security event related to API requests
     */
    public function logSecurityEvent(string $eventType, array $details, array $requestData = []): void
    {
        $message = sprintf('API security event: %s from IP %s', $eventType, $requestData['ip'] ?? 'unknown');
        
        $context = [
            'security_event_type' => $eventType,
            'details' => $details,
            'ip' => $requestData['ip'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'referer' => $_SERVER['HTTP_REFERER'] ?? null,
            'request_data' => $requestData,
            'request_id' => $this->getRequestId(),
            'severity' => $this->getSecurityEventSeverity($eventType)
        ];
        
        $level = $this->getLogLevelForSecurityEvent($eventType);
        $this->loggingService->log(LoggingService::TYPE_API, $level, $message, $context);
    }
    
    /**
     * Get API request statistics
     */
    public function getApiRequestStats(array $filters = []): array
    {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_requests,
                        COUNT(CASE WHEN response_status >= 200 AND response_status < 300 THEN 1 END) as successful_requests,
                        COUNT(CASE WHEN response_status >= 400 THEN 1 END) as failed_requests,
                        AVG(response_time_ms) as avg_response_time,
                        MAX(response_time_ms) as max_response_time,
                        AVG(memory_usage_mb) as avg_memory_usage,
                        MAX(memory_usage_mb) as max_memory_usage,
                        COUNT(DISTINCT ip) as unique_ips,
                        COUNT(DISTINCT plugin_id) as unique_plugins
                    FROM api_requests 
                    WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'";
            
            $params = [];
            
            // Apply filters
            if (!empty($filters['from_date'])) {
                $sql .= " AND created_at >= ?";
                $params[] = $filters['from_date'];
            }
            
            if (!empty($filters['to_date'])) {
                $sql .= " AND created_at <= ?";
                $params[] = $filters['to_date'];
            }
            
            if (!empty($filters['plugin_id'])) {
                $sql .= " AND plugin_id = ?";
                $params[] = $filters['plugin_id'];
            }
            
            $statement = DatabaseConnection::execute($sql, $params);
            $stats = $statement->fetch();
            
            // Calculate additional metrics
            $stats['success_rate'] = $stats['total_requests'] > 0 
                ? round(($stats['successful_requests'] / $stats['total_requests']) * 100, 2) 
                : 0;
            
            $stats['error_rate'] = $stats['total_requests'] > 0 
                ? round(($stats['failed_requests'] / $stats['total_requests']) * 100, 2) 
                : 0;
            
            return $stats;
            
        } catch (DatabaseException $e) {
            error_log("ApiRequestLogger: Failed to get API stats - " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Calculate request size in bytes
     */
    private function calculateRequestSize(array $requestData): int
    {
        return strlen(json_encode($requestData));
    }
    
    /**
     * Calculate response size in bytes
     */
    private function calculateResponseSize(array $responseData): int
    {
        return strlen(json_encode($responseData));
    }
    
    /**
     * Calculate data size in bytes
     */
    private function calculateDataSize(array $data): int
    {
        return strlen(json_encode($data));
    }
    
    /**
     * Get HTTP status text
     */
    private function getStatusText(int $statusCode): string
    {
        $statusTexts = [
            200 => 'OK',
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            405 => 'Method Not Allowed',
            429 => 'Too Many Requests',
            500 => 'Internal Server Error',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable'
        ];
        
        return $statusTexts[$statusCode] ?? 'Unknown';
    }
    
    /**
     * Get security event severity
     */
    private function getSecurityEventSeverity(string $eventType): string
    {
        $severityMap = [
            'sql_injection_attempt' => 'HIGH',
            'xss_attempt' => 'HIGH',
            'rate_limit_exceeded' => 'MEDIUM',
            'invalid_json_request' => 'LOW',
            'suspicious_user_agent' => 'LOW',
            'multiple_failed_requests' => 'MEDIUM'
        ];
        
        return $severityMap[$eventType] ?? 'MEDIUM';
    }
    
    /**
     * Get log level for security event
     */
    private function getLogLevelForSecurityEvent(string $eventType): string
    {
        $severity = $this->getSecurityEventSeverity($eventType);
        
        switch ($severity) {
            case 'HIGH':
                return LoggingService::LEVEL_CRITICAL;
            case 'MEDIUM':
                return LoggingService::LEVEL_WARNING;
            case 'LOW':
                return LoggingService::LEVEL_INFO;
            default:
                return LoggingService::LEVEL_WARNING;
        }
    }
    
    /**
     * Get or generate request ID
     */
    private function getRequestId(): string
    {
        static $requestId = null;
        
        if ($requestId === null) {
            $requestId = $_SERVER['HTTP_X_REQUEST_ID'] ?? uniqid('req_', true);
        }
        
        return $requestId;
    }
}