<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Models\IpDataModel;
use Skpassegna\GuardgeoApi\Database\IpIntelligenceRepository;
use Skpassegna\GuardgeoApi\Utils\Logger;
use Skpassegna\GuardgeoApi\Config\Environment;

/**
 * IP Registry Service
 * 
 * Main service class for managing IP intelligence data with caching,
 * deprecation rules, and batch processing capabilities.
 */
class IpRegistryService
{
    private IpRegistryApiClient $apiClient;
    private IpIntelligenceRepository $repository;
    private Logger $logger;
    
    /**
     * Constructor
     */
    public function __construct(
        ?IpRegistryApiClient $apiClient = null,
        ?IpIntelligenceRepository $repository = null
    ) {
        $this->apiClient = $apiClient ?? new IpRegistryApiClient();
        $this->repository = $repository ?? new IpIntelligenceRepository();
        $this->logger = new Logger();
    }
    
    /**
     * Get IP intelligence data with caching and deprecation rules
     */
    public function getIpIntelligence(string $ip): IpDataModel
    {
        $this->logger->info("Getting IP intelligence data", ['ip' => $ip]);
        
        // Validate IP address
        if (!$this->apiClient->isValidIp($ip)) {
            throw new IpRegistryServiceException("Invalid IP address: {$ip}");
        }
        
        // Check if we have cached data
        $cachedData = $this->repository->findByIp($ip);
        
        if ($cachedData !== null) {
            $this->logger->info("Found cached IP data", [
                'ip' => $ip,
                'cached_at' => $cachedData->cached_at->format('Y-m-d H:i:s'),
                'needs_refresh' => $cachedData->needsRefresh()
            ]);
            
            // Check if data needs refresh based on deprecation rules
            if (!$cachedData->needsRefresh()) {
                $this->logger->info("Using cached IP data (still fresh)", ['ip' => $ip]);
                return $cachedData;
            }
            
            // Data is stale, refresh from API
            $this->logger->info("Cached IP data is stale, refreshing from API", [
                'ip' => $ip,
                'expired_types' => $cachedData->getExpiredDataTypes()
            ]);
        } else {
            $this->logger->info("No cached IP data found, fetching from API", ['ip' => $ip]);
        }
        
        // Fetch fresh data from API with enhanced fallback
        return $this->fetchWithFallback($ip, $cachedData);
    }
    
    /**
     * Get IP intelligence data for multiple IPs (batch processing)
     */
    public function getBatchIpIntelligence(array $ips): array
    {
        $this->logger->info("Getting batch IP intelligence data", [
            'ip_count' => count($ips),
            'ips' => $ips
        ]);
        
        $results = [];
        $ipsToFetch = [];
        $invalidIps = [];
        
        // Validate and check cached data for each IP
        foreach ($ips as $ip) {
            if (!$this->apiClient->isValidIp($ip)) {
                $this->logger->warning("Invalid IP address in batch", ['ip' => $ip]);
                $invalidIps[] = $ip;
                continue;
            }
            
            $cachedData = $this->repository->findByIp($ip);
            
            if ($cachedData !== null && !$cachedData->needsRefresh()) {
                // Use cached data
                $results[$ip] = $cachedData;
                $this->logger->debug("Using cached data for IP", ['ip' => $ip]);
            } else {
                // Need to fetch from API
                $ipsToFetch[] = $ip;
                
                // Store stale cached data as potential fallback
                if ($cachedData !== null) {
                    $results[$ip . '_stale_fallback'] = $cachedData;
                }
            }
        }
        
        // Process IPs that need API fetching
        if (!empty($ipsToFetch)) {
            $results = array_merge($results, $this->processBatchApiFetch($ipsToFetch, $results));
        }
        
        // Clean up stale fallback entries that weren't needed
        foreach (array_keys($results) as $key) {
            if (strpos($key, '_stale_fallback') !== false) {
                unset($results[$key]);
            }
        }
        
        $this->logger->info("Batch IP intelligence processing complete", [
            'requested_count' => count($ips),
            'successful_count' => count($results),
            'invalid_count' => count($invalidIps),
            'failed_count' => count($ips) - count($results) - count($invalidIps)
        ]);
        
        return $results;
    }
    
    /**
     * Process batch API fetch with enhanced error handling and fallback
     */
    private function processBatchApiFetch(array $ipsToFetch, array $existingResults): array
    {
        $results = [];
        $maxBatchSize = (int) Environment::get('IPREGISTRY_MAX_BATCH_SIZE', 100);
        
        // Split into smaller batches if needed
        $batches = array_chunk($ipsToFetch, $maxBatchSize);
        
        foreach ($batches as $batchIndex => $batch) {
            $this->logger->info("Processing batch", [
                'batch_index' => $batchIndex + 1,
                'batch_size' => count($batch),
                'total_batches' => count($batches)
            ]);
            
            try {
                $batchResults = $this->processSingleBatch($batch, $existingResults);
                $results = array_merge($results, $batchResults);
                
            } catch (IpRegistryApiException $e) {
                $this->logger->error("Batch API call failed", [
                    'batch_index' => $batchIndex + 1,
                    'batch_ips' => $batch,
                    'error' => $e->getMessage()
                ]);
                
                // Apply fallback strategy for this batch
                $fallbackResults = $this->applyBatchFallback($batch, $existingResults);
                $results = array_merge($results, $fallbackResults);
            }
            
            // Add small delay between batches to avoid rate limiting
            if ($batchIndex < count($batches) - 1) {
                usleep(100000); // 100ms delay
            }
        }
        
        return $results;
    }
    
    /**
     * Process a single batch of IPs
     */
    private function processSingleBatch(array $batch, array $existingResults): array
    {
        $results = [];
        
        $batchApiData = $this->apiClient->getBatchIpData($batch);
        
        // Process batch response
        if (isset($batchApiData['results']) && is_array($batchApiData['results'])) {
            // Multiple IPs response format
            foreach ($batchApiData['results'] as $apiData) {
                if (isset($apiData['ip'])) {
                    $result = $this->processApiResponse($apiData);
                    if ($result !== null) {
                        $results[$apiData['ip']] = $result;
                    }
                }
            }
        } else {
            // Single IP response format (fallback)
            if (count($batch) === 1 && isset($batchApiData['ip'])) {
                $result = $this->processApiResponse($batchApiData);
                if ($result !== null) {
                    $results[$batchApiData['ip']] = $result;
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Process individual API response
     */
    private function processApiResponse(array $apiData): ?IpDataModel
    {
        try {
            $ipModel = IpDataModel::fromApiResponse($apiData);
            
            if (!$ipModel->isValid()) {
                $errors = implode(', ', $ipModel->validate());
                $this->logger->warning("Invalid IP data received from API", [
                    'ip' => $apiData['ip'] ?? 'unknown',
                    'errors' => $errors
                ]);
                return null;
            }
            
            // Save to cache
            $existingData = $this->repository->findByIp($ipModel->ip);
            if ($existingData !== null) {
                $this->repository->update($ipModel);
                $this->logger->debug("Updated cached IP data", ['ip' => $ipModel->ip]);
            } else {
                $this->repository->create($ipModel);
                $this->logger->debug("Created new cached IP data", ['ip' => $ipModel->ip]);
            }
            
            return $ipModel;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to process API response", [
                'ip' => $apiData['ip'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Apply fallback strategy for failed batch
     */
    private function applyBatchFallback(array $failedIps, array $existingResults): array
    {
        $fallbackResults = [];
        
        foreach ($failedIps as $ip) {
            // Try to use stale cached data
            $staleKey = $ip . '_stale_fallback';
            if (isset($existingResults[$staleKey])) {
                $fallbackResults[$ip] = $existingResults[$staleKey];
                $this->logger->warning("Using stale cached data as fallback", ['ip' => $ip]);
                continue;
            }
            
            // Try to fetch from cache again (in case it was updated)
            $cachedData = $this->repository->findByIp($ip);
            if ($cachedData !== null) {
                $fallbackResults[$ip] = $cachedData;
                $this->logger->warning("Using any available cached data as fallback", ['ip' => $ip]);
                continue;
            }
            
            // Try individual API call as last resort
            try {
                $individualResult = $this->getIpIntelligence($ip);
                $fallbackResults[$ip] = $individualResult;
                $this->logger->info("Successfully fetched IP data individually after batch failure", ['ip' => $ip]);
            } catch (IpRegistryServiceException $e) {
                $this->logger->error("All fallback methods failed for IP", [
                    'ip' => $ip,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        return $fallbackResults;
    }
    
    /**
     * Fetch IP data with comprehensive fallback mechanisms
     */
    private function fetchWithFallback(string $ip, ?IpDataModel $cachedData): IpDataModel
    {
        $maxRetries = (int) Environment::get('IPREGISTRY_MAX_RETRIES', 3);
        $retryDelay = (int) Environment::get('IPREGISTRY_RETRY_DELAY', 1);
        
        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                $apiData = $this->apiClient->getIpData($ip);
                $ipModel = IpDataModel::fromApiResponse($apiData);
                
                // Validate the model
                if (!$ipModel->isValid()) {
                    $errors = implode(', ', $ipModel->validate());
                    throw new IpRegistryServiceException("Invalid IP data received from API: {$errors}");
                }
                
                // Save to cache
                if ($cachedData !== null) {
                    // Update existing record
                    $this->repository->update($ipModel);
                    $this->logger->info("Updated cached IP data", ['ip' => $ip, 'attempt' => $attempt]);
                } else {
                    // Insert new record
                    $this->repository->create($ipModel);
                    $this->logger->info("Cached new IP data", ['ip' => $ip, 'attempt' => $attempt]);
                }
                
                return $ipModel;
                
            } catch (IpRegistryApiException $e) {
                $this->logger->warning("API call failed", [
                    'ip' => $ip,
                    'attempt' => $attempt,
                    'max_attempts' => $maxRetries,
                    'error' => $e->getMessage()
                ]);
                
                // If this is the last attempt, apply fallback strategies
                if ($attempt === $maxRetries) {
                    return $this->applyFallbackStrategies($ip, $cachedData, $e);
                }
                
                // Wait before retry (exponential backoff)
                if ($attempt < $maxRetries) {
                    sleep($retryDelay * $attempt);
                }
            }
        }
        
        // This should never be reached, but just in case
        throw new IpRegistryServiceException("Unexpected error in fetchWithFallback");
    }
    
    /**
     * Apply comprehensive fallback strategies when API fails
     */
    private function applyFallbackStrategies(string $ip, ?IpDataModel $cachedData, IpRegistryApiException $originalException): IpDataModel
    {
        $this->logger->info("Applying fallback strategies", ['ip' => $ip]);
        
        // Strategy 1: Use stale cached data if available
        if ($cachedData !== null) {
            $this->logger->warning("Using stale cached data as fallback", [
                'ip' => $ip,
                'cached_at' => $cachedData->cached_at->format('Y-m-d H:i:s'),
                'expired_types' => $cachedData->getExpiredDataTypes()
            ]);
            return $cachedData;
        }
        
        // Strategy 2: Try alternative API endpoints (if configured)
        $alternativeEndpoints = Environment::get('IPREGISTRY_ALTERNATIVE_ENDPOINTS', '');
        if (!empty($alternativeEndpoints)) {
            $endpoints = explode(',', $alternativeEndpoints);
            foreach ($endpoints as $endpoint) {
                try {
                    $this->logger->info("Trying alternative endpoint", [
                        'ip' => $ip,
                        'endpoint' => trim($endpoint)
                    ]);
                    
                    // This would require implementing alternative endpoint support
                    // For now, we'll skip this strategy
                    
                } catch (\Exception $e) {
                    $this->logger->warning("Alternative endpoint failed", [
                        'ip' => $ip,
                        'endpoint' => trim($endpoint),
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
        
        // Strategy 3: Return minimal IP data based on IP analysis
        $minimalData = $this->createMinimalIpData($ip);
        if ($minimalData !== null) {
            $this->logger->warning("Using minimal IP data as fallback", ['ip' => $ip]);
            
            // Cache the minimal data to avoid repeated API failures
            try {
                $this->repository->create($minimalData);
            } catch (\Exception $e) {
                $this->logger->error("Failed to cache minimal IP data", [
                    'ip' => $ip,
                    'error' => $e->getMessage()
                ]);
            }
            
            return $minimalData;
        }
        
        // All fallback strategies failed
        $this->logger->error("All fallback strategies failed for IP", [
            'ip' => $ip,
            'original_error' => $originalException->getMessage()
        ]);
        
        throw new IpRegistryServiceException(
            "Failed to fetch IP intelligence data and all fallback strategies failed: " . $originalException->getMessage(),
            0,
            $originalException
        );
    }
    
    /**
     * Create minimal IP data when API is unavailable
     */
    private function createMinimalIpData(string $ip): ?IpDataModel
    {
        try {
            // Basic IP analysis without external API
            $ipType = $this->apiClient->getIpType($ip);
            
            // Create minimal data structure
            $minimalData = [
                'ip' => $ip,
                'type' => $ipType,
                'hostname' => null,
                'carrier' => [],
                'company' => [],
                'connection' => [],
                'currency' => [],
                'location' => [
                    'country' => ['name' => 'Unknown', 'code' => 'XX'],
                    'region' => ['name' => 'Unknown'],
                    'city' => 'Unknown'
                ],
                'security' => [
                    'is_threat' => false,
                    'is_abuser' => false,
                    'is_attacker' => false,
                    'is_proxy' => false,
                    'is_vpn' => false,
                    'is_tor' => false,
                    'is_anonymous' => false
                ],
                'time_zone' => []
            ];
            
            $model = IpDataModel::fromApiResponse($minimalData);
            
            // Set shorter expiry times for minimal data
            $now = new DateTime();
            $model->location_expires_at = (clone $now)->modify('+1 day');
            $model->security_expires_at = (clone $now)->modify('+1 hour');
            $model->connection_expires_at = (clone $now)->modify('+1 day');
            $model->company_expires_at = (clone $now)->modify('+1 day');
            
            return $model;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to create minimal IP data", [
                'ip' => $ip,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Force refresh IP data from API (ignore cache)
     */
    public function refreshIpData(string $ip): IpDataModel
    {
        $this->logger->info("Force refreshing IP data from API", ['ip' => $ip]);
        
        if (!$this->apiClient->isValidIp($ip)) {
            throw new IpRegistryServiceException("Invalid IP address: {$ip}");
        }
        
        try {
            $apiData = $this->apiClient->getIpData($ip);
            $ipModel = IpDataModel::fromApiResponse($apiData);
            
            if (!$ipModel->isValid()) {
                $errors = implode(', ', $ipModel->validate());
                throw new IpRegistryServiceException("Invalid IP data received from API: {$errors}");
            }
            
            // Save to cache (update or create)
            $existingData = $this->repository->findByIp($ip);
            if ($existingData !== null) {
                $this->repository->update($ipModel);
                $this->logger->info("Force updated cached IP data", ['ip' => $ip]);
            } else {
                $this->repository->create($ipModel);
                $this->logger->info("Force cached new IP data", ['ip' => $ip]);
            }
            
            return $ipModel;
            
        } catch (IpRegistryApiException $e) {
            $this->logger->error("Failed to force refresh IP data", [
                'ip' => $ip,
                'error' => $e->getMessage()
            ]);
            
            throw new IpRegistryServiceException(
                "Failed to refresh IP intelligence data: " . $e->getMessage(),
                0,
                $e
            );
        }
    }
    
    /**
     * Get cached IP data without API call
     */
    public function getCachedIpData(string $ip): ?IpDataModel
    {
        if (!$this->apiClient->isValidIp($ip)) {
            throw new IpRegistryServiceException("Invalid IP address: {$ip}");
        }
        
        return $this->repository->findByIp($ip);
    }
    
    /**
     * Check if IP data exists in cache
     */
    public function hasCachedData(string $ip): bool
    {
        return $this->getCachedIpData($ip) !== null;
    }
    
    /**
     * Check if cached IP data is fresh (not expired)
     */
    public function isCachedDataFresh(string $ip): bool
    {
        $cachedData = $this->getCachedIpData($ip);
        return $cachedData !== null && !$cachedData->needsRefresh();
    }
    
    /**
     * Update cached IP data
     */
    public function updateCachedData(IpDataModel $model): bool
    {
        try {
            return $this->repository->update($model);
        } catch (\Exception $e) {
            $this->logger->error("Failed to update cached data", [
                'ip' => $model->ip,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Create new cached IP data
     */
    public function createCachedData(IpDataModel $model): bool
    {
        try {
            return $this->repository->create($model);
        } catch (\Exception $e) {
            $this->logger->error("Failed to create cached data", [
                'ip' => $model->ip,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Get all IPs that need refresh based on deprecation rules
     */
    public function getExpiredIps(int $limit = 100): array
    {
        return $this->repository->findExpiredIps($limit);
    }
    
    /**
     * Clean up old cached data beyond retention period
     */
    public function cleanupOldData(int $retentionDays = 90): int
    {
        $this->logger->info("Cleaning up old IP data", [
            'retention_days' => $retentionDays
        ]);
        
        $deletedCount = $this->repository->deleteOldData($retentionDays);
        
        $this->logger->info("Cleanup complete", [
            'deleted_count' => $deletedCount
        ]);
        
        return $deletedCount;
    }
    
    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        return [
            'total_cached_ips' => $this->repository->getTotalCount(),
            'fresh_ips' => $this->repository->getFreshCount(),
            'expired_ips' => $this->repository->getExpiredCount(),
            'cache_hit_rate' => $this->calculateCacheHitRate()
        ];
    }
    
    /**
     * Calculate cache hit rate (simplified)
     */
    private function calculateCacheHitRate(): float
    {
        // This would need to be implemented with proper metrics tracking
        // For now, return a placeholder
        return 0.0;
    }
    
    /**
     * Validate multiple IP addresses
     */
    public function validateIps(array $ips): array
    {
        $results = [
            'valid' => [],
            'invalid' => []
        ];
        
        foreach ($ips as $ip) {
            if ($this->apiClient->isValidIp($ip)) {
                $results['valid'][] = $ip;
            } else {
                $results['invalid'][] = $ip;
            }
        }
        
        return $results;
    }
    
    /**
     * Get IP type for an IP address
     */
    public function getIpType(string $ip): string
    {
        return $this->apiClient->getIpType($ip);
    }
    
    /**
     * Perform comprehensive cache maintenance
     */
    public function performCacheMaintenance(): array
    {
        $this->logger->info("Starting comprehensive cache maintenance");
        
        $results = [
            'cleanup_performed' => false,
            'refresh_performed' => false,
            'old_data_deleted' => 0,
            'expired_ips_refreshed' => 0,
            'errors' => [],
            'duration_seconds' => 0
        ];
        
        $startTime = microtime(true);
        
        try {
            // 1. Clean up old data beyond retention period
            $retentionDays = (int) Environment::get('IP_CACHE_RETENTION_DAYS', 90);
            $results['old_data_deleted'] = $this->cleanupOldData($retentionDays);
            $results['cleanup_performed'] = true;
            
            // 2. Refresh expired IPs
            $batchSize = (int) Environment::get('IP_CACHE_MAINTENANCE_BATCH_SIZE', 50);
            $refreshResults = $this->refreshExpiredIps($batchSize);
            $results['expired_ips_refreshed'] = $refreshResults['refreshed'];
            $results['refresh_performed'] = true;
            
            if (!empty($refreshResults['errors'])) {
                $results['errors'] = array_merge($results['errors'], $refreshResults['errors']);
            }
            
        } catch (\Exception $e) {
            $error = "Cache maintenance failed: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        $results['duration_seconds'] = round(microtime(true) - $startTime, 2);
        
        $this->logger->info("Cache maintenance completed", $results);
        
        return $results;
    }
    
    /**
     * Refresh expired IPs in batches
     */
    public function refreshExpiredIps(int $batchSize = 50): array
    {
        $this->logger->info("Refreshing expired IPs", ['batch_size' => $batchSize]);
        
        $results = [
            'processed' => 0,
            'refreshed' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        try {
            $expiredIps = $this->getExpiredIps($batchSize);
            $results['processed'] = count($expiredIps);
            
            if (empty($expiredIps)) {
                $this->logger->info("No expired IPs found for refresh");
                return $results;
            }
            
            // Extract IP addresses
            $ipsToRefresh = array_column($expiredIps, 'ip');
            
            // Use batch processing for efficiency
            $batchResults = $this->getBatchIpIntelligence($ipsToRefresh);
            $results['refreshed'] = count($batchResults);
            $results['failed'] = $results['processed'] - $results['refreshed'];
            
        } catch (\Exception $e) {
            $error = "Failed to refresh expired IPs: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        return $results;
    }
    
    /**
     * Perform cache warmup for commonly requested IPs
     */
    public function performCacheWarmup(array $commonIps): array
    {
        $this->logger->info("Performing cache warmup", [
            'ip_count' => count($commonIps)
        ]);
        
        $results = [
            'processed' => 0,
            'warmed' => 0,
            'already_cached' => 0,
            'failed' => 0
        ];
        
        foreach ($commonIps as $ip) {
            $results['processed']++;
            
            try {
                if ($this->hasCachedData($ip) && $this->isCachedDataFresh($ip)) {
                    $results['already_cached']++;
                    continue;
                }
                
                $this->getIpIntelligence($ip);
                $results['warmed']++;
                
            } catch (IpRegistryServiceException $e) {
                $results['failed']++;
                $this->logger->warning("Failed to warm cache for IP", [
                    'ip' => $ip,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        $this->logger->info("Cache warmup completed", $results);
        
        return $results;
    }
    
    /**
     * Get cache maintenance recommendations
     */
    public function getCacheMaintenanceRecommendations(): array
    {
        $stats = $this->getCacheStats();
        $recommendations = [];
        
        // Check expired data ratio
        $expiredRatio = $stats['total_cached_ips'] > 0 
            ? ($stats['expired_ips'] / $stats['total_cached_ips']) * 100 
            : 0;
            
        if ($expiredRatio > 30) {
            $recommendations[] = [
                'type' => 'refresh',
                'priority' => 'high',
                'message' => "High percentage of expired data ({$expiredRatio}%). Consider running batch refresh.",
                'action' => 'refresh_expired_ips'
            ];
        }
        
        // Check cache size
        $maxCacheSize = (int) Environment::get('IP_CACHE_MAX_SIZE', 100000);
        if ($stats['total_cached_ips'] > $maxCacheSize * 0.9) {
            $recommendations[] = [
                'type' => 'cleanup',
                'priority' => 'medium',
                'message' => "Cache size approaching limit. Consider cleanup or increasing max size.",
                'action' => 'cleanup_old_data'
            ];
        }
        
        // Check API health
        if (!$this->apiClient->healthCheck()) {
            $recommendations[] = [
                'type' => 'api',
                'priority' => 'critical',
                'message' => "ipRegistry API is not accessible. Check API key and connectivity.",
                'action' => 'check_api_connection'
            ];
        }
        
        return $recommendations;
    }
    
    /**
     * Health check for the service
     */
    public function healthCheck(): array
    {
        $results = [
            'api_client' => false,
            'database' => false,
            'cache_health' => 'unknown',
            'overall' => false
        ];
        
        // Check API client
        try {
            $results['api_client'] = $this->apiClient->healthCheck();
        } catch (\Exception $e) {
            $this->logger->error("API client health check failed", [
                'error' => $e->getMessage()
            ]);
        }
        
        // Check database
        try {
            $results['database'] = $this->repository->healthCheck();
        } catch (\Exception $e) {
            $this->logger->error("Database health check failed", [
                'error' => $e->getMessage()
            ]);
        }
        
        // Check cache health
        try {
            $stats = $this->getCacheStats();
            $expiredRatio = $stats['total_cached_ips'] > 0 
                ? ($stats['expired_ips'] / $stats['total_cached_ips']) * 100 
                : 0;
                
            if ($expiredRatio < 20) {
                $results['cache_health'] = 'good';
            } elseif ($expiredRatio < 50) {
                $results['cache_health'] = 'fair';
            } else {
                $results['cache_health'] = 'poor';
            }
        } catch (\Exception $e) {
            $this->logger->error("Cache health check failed", [
                'error' => $e->getMessage()
            ]);
        }
        
        $results['overall'] = $results['api_client'] && 
                             $results['database'] && 
                             in_array($results['cache_health'], ['good', 'fair']);
        
        return $results;
    }
}

/**
 * Custom exception for IP Registry Service errors
 */
class IpRegistryServiceException extends \Exception
{
    public function __construct(string $message, int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}