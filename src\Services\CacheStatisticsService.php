<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Database\IpIntelligenceRepository;
use Skpassegna\GuardgeoApi\Database\ApiRequestRepository;
use Skpassegna\GuardgeoApi\Utils\Logger;
use Skpassegna\GuardgeoApi\Config\Environment;
use DateTime;

/**
 * Cache Statistics Service
 * 
 * Provides comprehensive cache statistics, monitoring, and analytics
 * for performance optimization and system health assessment.
 */
class CacheStatisticsService
{
    private IpIntelligenceRepository $ipRepository;
    private ApiRequestRepository $apiRequestRepository;
    private Logger $logger;
    
    // Statistics cache
    private array $statisticsCache = [];
    private int $cacheValidityMinutes;
    
    /**
     * Constructor
     */
    public function __construct(
        ?IpIntelligenceRepository $ipRepository = null,
        ?ApiRequestRepository $apiRequestRepository = null
    ) {
        $this->ipRepository = $ipRepository ?? new IpIntelligenceRepository();
        $this->apiRequestRepository = $apiRequestRepository ?? new ApiRequestRepository();
        $this->logger = new Logger();
        
        $this->cacheValidityMinutes = (int) Environment::get('CACHE_STATS_VALIDITY_MINUTES', 5);
    }
    
    /**
     * Get comprehensive cache statistics
     */
    public function getComprehensiveStatistics(): array
    {
        $cacheKey = 'comprehensive_stats';
        
        if ($this->isStatisticsCached($cacheKey)) {
            return $this->statisticsCache[$cacheKey]['data'];
        }
        
        $this->logger->info("Generating comprehensive cache statistics");
        
        $startTime = microtime(true);
        
        $stats = [
            'overview' => $this->getOverviewStatistics(),
            'performance' => $this->getPerformanceStatistics(),
            'health' => $this->getHealthStatistics(),
            'usage_patterns' => $this->getUsagePatternStatistics(),
            'geographic_distribution' => $this->getGeographicDistribution(),
            'security_insights' => $this->getSecurityInsights(),
            'cache_efficiency' => $this->getCacheEfficiencyMetrics(),
            'trends' => $this->getTrendAnalysis(),
            'recommendations' => $this->getOptimizationRecommendations(),
            'generated_at' => (new DateTime())->format('c'),
            'generation_time_ms' => round((microtime(true) - $startTime) * 1000, 2)
        ];
        
        $this->cacheStatistics($cacheKey, $stats);
        
        $this->logger->info("Comprehensive cache statistics generated", [
            'generation_time_ms' => $stats['generation_time_ms']
        ]);
        
        return $stats;
    }
    
    /**
     * Get overview statistics
     */
    public function getOverviewStatistics(): array
    {
        $cacheKey = 'overview_stats';
        
        if ($this->isStatisticsCached($cacheKey)) {
            return $this->statisticsCache[$cacheKey]['data'];
        }
        
        try {
            $totalIps = $this->ipRepository->getTotalCount();
            $freshIps = $this->ipRepository->getFreshCount();
            $expiredIps = $this->ipRepository->getExpiredCount();
            
            $stats = [
                'total_cached_ips' => $totalIps,
                'fresh_ips' => $freshIps,
                'expired_ips' => $expiredIps,
                'fresh_percentage' => $totalIps > 0 ? round(($freshIps / $totalIps) * 100, 2) : 0,
                'expired_percentage' => $totalIps > 0 ? round(($expiredIps / $totalIps) * 100, 2) : 0,
                'cache_utilization' => $this->calculateCacheUtilization($totalIps),
                'data_types' => $this->getDataTypeBreakdown(),
                'storage_size' => $this->estimateStorageSize()
            ];
            
            $this->cacheStatistics($cacheKey, $stats);
            return $stats;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get overview statistics", [
                'error' => $e->getMessage()
            ]);
            return $this->getEmptyOverviewStats();
        }
    }
    
    /**
     * Get performance statistics
     */
    public function getPerformanceStatistics(): array
    {
        $cacheKey = 'performance_stats';
        
        if ($this->isStatisticsCached($cacheKey)) {
            return $this->statisticsCache[$cacheKey]['data'];
        }
        
        try {
            $stats = [
                'cache_hit_rate' => $this->calculateCacheHitRate(),
                'average_response_time' => $this->calculateAverageResponseTime(),
                'api_call_reduction' => $this->calculateApiCallReduction(),
                'bandwidth_savings' => $this->calculateBandwidthSavings(),
                'performance_by_hour' => $this->getPerformanceByHour(),
                'slowest_operations' => $this->getSlowestOperations(),
                'fastest_operations' => $this->getFastestOperations(),
                'error_rates' => $this->getErrorRates()
            ];
            
            $this->cacheStatistics($cacheKey, $stats);
            return $stats;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get performance statistics", [
                'error' => $e->getMessage()
            ]);
            return $this->getEmptyPerformanceStats();
        }
    }
    
    /**
     * Get health statistics
     */
    public function getHealthStatistics(): array
    {
        $cacheKey = 'health_stats';
        
        if ($this->isStatisticsCached($cacheKey)) {
            return $this->statisticsCache[$cacheKey]['data'];
        }
        
        try {
            $stats = [
                'overall_health' => 'healthy',
                'health_score' => 0,
                'issues' => [],
                'warnings' => [],
                'recommendations' => [],
                'database_health' => $this->checkDatabaseHealth(),
                'api_health' => $this->checkApiHealth(),
                'memory_usage' => $this->getMemoryUsage(),
                'disk_usage' => $this->getDiskUsage(),
                'connection_pool' => $this->getConnectionPoolStats()
            ];
            
            // Calculate overall health score
            $stats['health_score'] = $this->calculateHealthScore($stats);
            
            // Determine overall health status
            if ($stats['health_score'] >= 90) {
                $stats['overall_health'] = 'excellent';
            } elseif ($stats['health_score'] >= 75) {
                $stats['overall_health'] = 'good';
            } elseif ($stats['health_score'] >= 60) {
                $stats['overall_health'] = 'fair';
            } elseif ($stats['health_score'] >= 40) {
                $stats['overall_health'] = 'poor';
            } else {
                $stats['overall_health'] = 'critical';
            }
            
            $this->cacheStatistics($cacheKey, $stats);
            return $stats;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get health statistics", [
                'error' => $e->getMessage()
            ]);
            return $this->getEmptyHealthStats();
        }
    }
    
    /**
     * Get usage pattern statistics
     */
    public function getUsagePatternStatistics(): array
    {
        $cacheKey = 'usage_patterns';
        
        if ($this->isStatisticsCached($cacheKey)) {
            return $this->statisticsCache[$cacheKey]['data'];
        }
        
        try {
            $stats = [
                'hourly_patterns' => $this->getHourlyUsagePatterns(),
                'daily_patterns' => $this->getDailyUsagePatterns(),
                'weekly_patterns' => $this->getWeeklyUsagePatterns(),
                'popular_ips' => $this->getPopularIps(),
                'request_frequency' => $this->getRequestFrequencyDistribution(),
                'peak_hours' => $this->getPeakHours(),
                'low_activity_periods' => $this->getLowActivityPeriods(),
                'seasonal_trends' => $this->getSeasonalTrends()
            ];
            
            $this->cacheStatistics($cacheKey, $stats);
            return $stats;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get usage pattern statistics", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get geographic distribution statistics
     */
    public function getGeographicDistribution(): array
    {
        $cacheKey = 'geographic_distribution';
        
        if ($this->isStatisticsCached($cacheKey)) {
            return $this->statisticsCache[$cacheKey]['data'];
        }
        
        try {
            $stats = [
                'by_country' => $this->getCountryDistribution(),
                'by_region' => $this->getRegionDistribution(),
                'by_continent' => $this->getContinentDistribution(),
                'top_countries' => $this->getTopCountries(),
                'geographic_trends' => $this->getGeographicTrends(),
                'coverage_analysis' => $this->getCoverageAnalysis()
            ];
            
            $this->cacheStatistics($cacheKey, $stats);
            return $stats;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get geographic distribution", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get security insights
     */
    public function getSecurityInsights(): array
    {
        $cacheKey = 'security_insights';
        
        if ($this->isStatisticsCached($cacheKey)) {
            return $this->statisticsCache[$cacheKey]['data'];
        }
        
        try {
            $stats = [
                'threat_summary' => $this->getThreatSummary(),
                'threat_trends' => $this->getThreatTrends(),
                'proxy_vpn_stats' => $this->getProxyVpnStats(),
                'tor_usage' => $this->getTorUsageStats(),
                'abuser_patterns' => $this->getAbuserPatterns(),
                'security_score_distribution' => $this->getSecurityScoreDistribution(),
                'high_risk_ips' => $this->getHighRiskIps(),
                'security_recommendations' => $this->getSecurityRecommendations()
            ];
            
            $this->cacheStatistics($cacheKey, $stats);
            return $stats;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get security insights", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get cache efficiency metrics
     */
    public function getCacheEfficiencyMetrics(): array
    {
        $cacheKey = 'cache_efficiency';
        
        if ($this->isStatisticsCached($cacheKey)) {
            return $this->statisticsCache[$cacheKey]['data'];
        }
        
        try {
            $stats = [
                'hit_rate_by_data_type' => $this->getHitRateByDataType(),
                'refresh_frequency' => $this->getRefreshFrequency(),
                'cache_turnover' => $this->getCacheTurnover(),
                'storage_efficiency' => $this->getStorageEfficiency(),
                'access_patterns' => $this->getAccessPatterns(),
                'optimization_opportunities' => $this->getOptimizationOpportunities(),
                'cost_savings' => $this->calculateCostSavings(),
                'performance_impact' => $this->getPerformanceImpact()
            ];
            
            $this->cacheStatistics($cacheKey, $stats);
            return $stats;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get cache efficiency metrics", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get trend analysis
     */
    public function getTrendAnalysis(): array
    {
        $cacheKey = 'trend_analysis';
        
        if ($this->isStatisticsCached($cacheKey)) {
            return $this->statisticsCache[$cacheKey]['data'];
        }
        
        try {
            $stats = [
                'growth_trends' => $this->getGrowthTrends(),
                'usage_trends' => $this->getUsageTrends(),
                'performance_trends' => $this->getPerformanceTrends(),
                'capacity_trends' => $this->getCapacityTrends(),
                'forecasts' => $this->generateForecasts(),
                'anomaly_detection' => $this->detectAnomalies(),
                'seasonal_analysis' => $this->getSeasonalAnalysis(),
                'trend_recommendations' => $this->getTrendRecommendations()
            ];
            
            $this->cacheStatistics($cacheKey, $stats);
            return $stats;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get trend analysis", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get optimization recommendations
     */
    public function getOptimizationRecommendations(): array
    {
        try {
            $recommendations = [];
            
            // Analyze current statistics
            $overview = $this->getOverviewStatistics();
            $performance = $this->getPerformanceStatistics();
            $health = $this->getHealthStatistics();
            
            // Cache utilization recommendations
            if ($overview['cache_utilization'] > 90) {
                $recommendations[] = [
                    'type' => 'capacity',
                    'priority' => 'high',
                    'title' => 'Increase Cache Capacity',
                    'description' => 'Cache utilization is very high. Consider increasing max cache size.',
                    'impact' => 'high',
                    'effort' => 'low'
                ];
            }
            
            // Expired data recommendations
            if ($overview['expired_percentage'] > 30) {
                $recommendations[] = [
                    'type' => 'maintenance',
                    'priority' => 'high',
                    'title' => 'Increase Refresh Frequency',
                    'description' => 'High percentage of expired data. Consider more frequent cache refresh.',
                    'impact' => 'high',
                    'effort' => 'medium'
                ];
            }
            
            // Performance recommendations
            if ($performance['cache_hit_rate'] < 70) {
                $recommendations[] = [
                    'type' => 'performance',
                    'priority' => 'medium',
                    'title' => 'Improve Cache Hit Rate',
                    'description' => 'Cache hit rate is below optimal. Consider cache warming strategies.',
                    'impact' => 'medium',
                    'effort' => 'medium'
                ];
            }
            
            // Health recommendations
            if ($health['health_score'] < 75) {
                $recommendations[] = [
                    'type' => 'health',
                    'priority' => 'high',
                    'title' => 'Address Health Issues',
                    'description' => 'System health score is below optimal. Review health warnings.',
                    'impact' => 'high',
                    'effort' => 'varies'
                ];
            }
            
            return $recommendations;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get optimization recommendations", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Calculate cache utilization percentage
     */
    private function calculateCacheUtilization(int $totalIps): float
    {
        $maxCacheSize = (int) Environment::get('IP_CACHE_MAX_SIZE', 100000);
        return $maxCacheSize > 0 ? round(($totalIps / $maxCacheSize) * 100, 2) : 0;
    }
    
    /**
     * Get data type breakdown
     */
    private function getDataTypeBreakdown(): array
    {
        try {
            return $this->ipRepository->getDataTypeBreakdown();
        } catch (\Exception $e) {
            $this->logger->error("Failed to get data type breakdown", [
                'error' => $e->getMessage()
            ]);
            return ['IPv4' => 0, 'IPv6' => 0];
        }
    }
    
    /**
     * Estimate storage size
     */
    private function estimateStorageSize(): array
    {
        try {
            $totalIps = $this->ipRepository->getTotalCount();
            $avgRecordSize = 2048; // Estimated bytes per record
            
            return [
                'total_records' => $totalIps,
                'estimated_size_bytes' => $totalIps * $avgRecordSize,
                'estimated_size_mb' => round(($totalIps * $avgRecordSize) / 1024 / 1024, 2),
                'estimated_size_gb' => round(($totalIps * $avgRecordSize) / 1024 / 1024 / 1024, 3)
            ];
        } catch (\Exception $e) {
            return ['total_records' => 0, 'estimated_size_bytes' => 0, 'estimated_size_mb' => 0, 'estimated_size_gb' => 0];
        }
    }
    
    /**
     * Calculate cache hit rate
     */
    private function calculateCacheHitRate(): float
    {
        try {
            // This would require tracking cache hits/misses in API requests
            // For now, return a calculated estimate based on fresh vs expired data
            $overview = $this->getOverviewStatistics();
            return $overview['fresh_percentage'] ?? 0;
        } catch (\Exception $e) {
            return 0.0;
        }
    }
    
    /**
     * Calculate average response time
     */
    private function calculateAverageResponseTime(): float
    {
        try {
            return $this->apiRequestRepository->getAverageResponseTime();
        } catch (\Exception $e) {
            return 0.0;
        }
    }
    
    /**
     * Calculate API call reduction
     */
    private function calculateApiCallReduction(): array
    {
        try {
            $totalRequests = $this->apiRequestRepository->getTotalRequestCount();
            $cacheHits = $totalRequests * ($this->calculateCacheHitRate() / 100);
            $apiCalls = $totalRequests - $cacheHits;
            
            return [
                'total_requests' => $totalRequests,
                'cache_hits' => (int) $cacheHits,
                'api_calls' => (int) $apiCalls,
                'reduction_percentage' => $totalRequests > 0 ? round(($cacheHits / $totalRequests) * 100, 2) : 0
            ];
        } catch (\Exception $e) {
            return ['total_requests' => 0, 'cache_hits' => 0, 'api_calls' => 0, 'reduction_percentage' => 0];
        }
    }
    
    /**
     * Calculate bandwidth savings
     */
    private function calculateBandwidthSavings(): array
    {
        try {
            $reduction = $this->calculateApiCallReduction();
            $avgResponseSize = 1024; // Estimated bytes per API response
            
            $savedBytes = $reduction['cache_hits'] * $avgResponseSize;
            
            return [
                'saved_bytes' => $savedBytes,
                'saved_kb' => round($savedBytes / 1024, 2),
                'saved_mb' => round($savedBytes / 1024 / 1024, 2),
                'saved_gb' => round($savedBytes / 1024 / 1024 / 1024, 3)
            ];
        } catch (\Exception $e) {
            return ['saved_bytes' => 0, 'saved_kb' => 0, 'saved_mb' => 0, 'saved_gb' => 0];
        }
    }
    
    /**
     * Check if statistics are cached and valid
     */
    private function isStatisticsCached(string $key): bool
    {
        if (!isset($this->statisticsCache[$key])) {
            return false;
        }
        
        $cacheTime = $this->statisticsCache[$key]['timestamp'];
        $validUntil = $cacheTime + ($this->cacheValidityMinutes * 60);
        
        return time() < $validUntil;
    }
    
    /**
     * Cache statistics data
     */
    private function cacheStatistics(string $key, array $data): void
    {
        $this->statisticsCache[$key] = [
            'data' => $data,
            'timestamp' => time()
        ];
    }
    
    /**
     * Get empty overview stats (fallback)
     */
    private function getEmptyOverviewStats(): array
    {
        return [
            'total_cached_ips' => 0,
            'fresh_ips' => 0,
            'expired_ips' => 0,
            'fresh_percentage' => 0,
            'expired_percentage' => 0,
            'cache_utilization' => 0,
            'data_types' => ['IPv4' => 0, 'IPv6' => 0],
            'storage_size' => ['total_records' => 0, 'estimated_size_mb' => 0]
        ];
    }
    
    /**
     * Get empty performance stats (fallback)
     */
    private function getEmptyPerformanceStats(): array
    {
        return [
            'cache_hit_rate' => 0,
            'average_response_time' => 0,
            'api_call_reduction' => ['reduction_percentage' => 0],
            'bandwidth_savings' => ['saved_mb' => 0]
        ];
    }
    
    /**
     * Get empty health stats (fallback)
     */
    private function getEmptyHealthStats(): array
    {
        return [
            'overall_health' => 'unknown',
            'health_score' => 0,
            'issues' => ['Unable to retrieve health statistics'],
            'warnings' => [],
            'recommendations' => []
        ];
    }
    
    // Placeholder methods for complex statistics that would require additional implementation
    private function getPerformanceByHour(): array { return []; }
    private function getSlowestOperations(): array { return []; }
    private function getFastestOperations(): array { return []; }
    private function getErrorRates(): array { return []; }
    private function checkDatabaseHealth(): array { return ['status' => 'unknown']; }
    private function checkApiHealth(): array { return ['status' => 'unknown']; }
    private function getMemoryUsage(): array { return []; }
    private function getDiskUsage(): array { return []; }
    private function getConnectionPoolStats(): array { return []; }
    private function calculateHealthScore(array $stats): int { return 75; }
    private function getHourlyUsagePatterns(): array { return []; }
    private function getDailyUsagePatterns(): array { return []; }
    private function getWeeklyUsagePatterns(): array { return []; }
    private function getPopularIps(): array { return []; }
    private function getRequestFrequencyDistribution(): array { return []; }
    private function getPeakHours(): array { return []; }
    private function getLowActivityPeriods(): array { return []; }
    private function getSeasonalTrends(): array { return []; }
    private function getCountryDistribution(): array { return []; }
    private function getRegionDistribution(): array { return []; }
    private function getContinentDistribution(): array { return []; }
    private function getTopCountries(): array { return []; }
    private function getGeographicTrends(): array { return []; }
    private function getCoverageAnalysis(): array { return []; }
    private function getThreatSummary(): array { return []; }
    private function getThreatTrends(): array { return []; }
    private function getProxyVpnStats(): array { return []; }
    private function getTorUsageStats(): array { return []; }
    private function getAbuserPatterns(): array { return []; }
    private function getSecurityScoreDistribution(): array { return []; }
    private function getHighRiskIps(): array { return []; }
    private function getSecurityRecommendations(): array { return []; }
    private function getHitRateByDataType(): array { return []; }
    private function getRefreshFrequency(): array { return []; }
    private function getCacheTurnover(): array { return []; }
    private function getStorageEfficiency(): array { return []; }
    private function getAccessPatterns(): array { return []; }
    private function getOptimizationOpportunities(): array { return []; }
    private function calculateCostSavings(): array { return []; }
    private function getPerformanceImpact(): array { return []; }
    private function getGrowthTrends(): array { return []; }
    private function getUsageTrends(): array { return []; }
    private function getPerformanceTrends(): array { return []; }
    private function getCapacityTrends(): array { return []; }
    private function generateForecasts(): array { return []; }
    private function detectAnomalies(): array { return []; }
    private function getSeasonalAnalysis(): array { return []; }
    private function getTrendRecommendations(): array { return []; }
}