<?php

namespace Skpassegna\GuardgeoApi\Utils;

/**
 * Performance Monitor
 * 
 * Tracks and measures performance metrics for API requests and system operations.
 * Provides detailed timing, memory usage, and performance analytics.
 */
class PerformanceMonitor
{
    private array $timers = [];
    private array $metrics = [];
    private float $requestStartTime;
    private int $requestStartMemory;
    
    public function __construct()
    {
        $this->requestStartTime = microtime(true);
        $this->requestStartMemory = memory_get_usage(true);
    }
    
    /**
     * Start a performance timer
     */
    public function startTimer(string $name): void
    {
        $this->timers[$name] = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'end_time' => null,
            'end_memory' => null,
            'duration' => null,
            'memory_delta' => null
        ];
    }
    
    /**
     * Stop a performance timer
     */
    public function stopTimer(string $name): array
    {
        if (!isset($this->timers[$name])) {
            throw new \InvalidArgumentException("Timer '$name' was not started");
        }
        
        $timer = &$this->timers[$name];
        $timer['end_time'] = microtime(true);
        $timer['end_memory'] = memory_get_usage(true);
        $timer['duration'] = $timer['end_time'] - $timer['start_time'];
        $timer['memory_delta'] = $timer['end_memory'] - $timer['start_memory'];
        
        return [
            'duration_ms' => round($timer['duration'] * 1000, 2),
            'memory_delta_bytes' => $timer['memory_delta'],
            'memory_delta_mb' => round($timer['memory_delta'] / 1024 / 1024, 2)
        ];
    }
    
    /**
     * Get timer results
     */
    public function getTimer(string $name): ?array
    {
        if (!isset($this->timers[$name])) {
            return null;
        }
        
        $timer = $this->timers[$name];
        
        if ($timer['end_time'] === null) {
            // Timer is still running
            $currentTime = microtime(true);
            $currentMemory = memory_get_usage(true);
            
            return [
                'duration_ms' => round(($currentTime - $timer['start_time']) * 1000, 2),
                'memory_delta_bytes' => $currentMemory - $timer['start_memory'],
                'memory_delta_mb' => round(($currentMemory - $timer['start_memory']) / 1024 / 1024, 2),
                'status' => 'running'
            ];
        }
        
        return [
            'duration_ms' => round($timer['duration'] * 1000, 2),
            'memory_delta_bytes' => $timer['memory_delta'],
            'memory_delta_mb' => round($timer['memory_delta'] / 1024 / 1024, 2),
            'status' => 'completed'
        ];
    }
    
    /**
     * Get all timer results
     */
    public function getAllTimers(): array
    {
        $results = [];
        
        foreach (array_keys($this->timers) as $name) {
            $results[$name] = $this->getTimer($name);
        }
        
        return $results;
    }
    
    /**
     * Record a custom metric
     */
    public function recordMetric(string $name, $value, string $unit = null): void
    {
        $this->metrics[$name] = [
            'value' => $value,
            'unit' => $unit,
            'timestamp' => microtime(true)
        ];
    }
    
    /**
     * Get all recorded metrics
     */
    public function getMetrics(): array
    {
        return $this->metrics;
    }
    
    /**
     * Get overall request performance
     */
    public function getRequestPerformance(): array
    {
        $currentTime = microtime(true);
        $currentMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        return [
            'total_duration_ms' => round(($currentTime - $this->requestStartTime) * 1000, 2),
            'memory_usage_mb' => round($currentMemory / 1024 / 1024, 2),
            'peak_memory_mb' => round($peakMemory / 1024 / 1024, 2),
            'memory_delta_mb' => round(($currentMemory - $this->requestStartMemory) / 1024 / 1024, 2),
            'timers' => $this->getAllTimers(),
            'metrics' => $this->getMetrics()
        ];
    }
    
    /**
     * Get database query performance summary
     */
    public function getDatabasePerformance(): array
    {
        $dbTimers = array_filter($this->timers, function($key) {
            return strpos($key, 'db_') === 0 || strpos($key, 'query_') === 0;
        }, ARRAY_FILTER_USE_KEY);
        
        if (empty($dbTimers)) {
            return [
                'total_queries' => 0,
                'total_duration_ms' => 0,
                'average_duration_ms' => 0,
                'slowest_query_ms' => 0
            ];
        }
        
        $totalDuration = 0;
        $slowestQuery = 0;
        
        foreach ($dbTimers as $timer) {
            if ($timer['duration'] !== null) {
                $durationMs = $timer['duration'] * 1000;
                $totalDuration += $durationMs;
                $slowestQuery = max($slowestQuery, $durationMs);
            }
        }
        
        return [
            'total_queries' => count($dbTimers),
            'total_duration_ms' => round($totalDuration, 2),
            'average_duration_ms' => round($totalDuration / count($dbTimers), 2),
            'slowest_query_ms' => round($slowestQuery, 2)
        ];
    }
    
    /**
     * Get external API performance summary
     */
    public function getExternalApiPerformance(): array
    {
        $apiTimers = array_filter($this->timers, function($key) {
            return strpos($key, 'api_') === 0 || strpos($key, 'external_') === 0;
        }, ARRAY_FILTER_USE_KEY);
        
        if (empty($apiTimers)) {
            return [
                'total_calls' => 0,
                'total_duration_ms' => 0,
                'average_duration_ms' => 0,
                'slowest_call_ms' => 0
            ];
        }
        
        $totalDuration = 0;
        $slowestCall = 0;
        
        foreach ($apiTimers as $timer) {
            if ($timer['duration'] !== null) {
                $durationMs = $timer['duration'] * 1000;
                $totalDuration += $durationMs;
                $slowestCall = max($slowestCall, $durationMs);
            }
        }
        
        return [
            'total_calls' => count($apiTimers),
            'total_duration_ms' => round($totalDuration, 2),
            'average_duration_ms' => round($totalDuration / count($apiTimers), 2),
            'slowest_call_ms' => round($slowestCall, 2)
        ];
    }
    
    /**
     * Check if performance is within acceptable thresholds
     */
    public function checkPerformanceThresholds(): array
    {
        $performance = $this->getRequestPerformance();
        $issues = [];
        
        // Check total request time (should be under 2 seconds)
        if ($performance['total_duration_ms'] > 2000) {
            $issues[] = [
                'type' => 'slow_request',
                'message' => 'Request took longer than 2 seconds',
                'value' => $performance['total_duration_ms'],
                'threshold' => 2000
            ];
        }
        
        // Check memory usage (should be under 128MB)
        if ($performance['memory_usage_mb'] > 128) {
            $issues[] = [
                'type' => 'high_memory',
                'message' => 'Memory usage exceeded 128MB',
                'value' => $performance['memory_usage_mb'],
                'threshold' => 128
            ];
        }
        
        // Check database performance
        $dbPerf = $this->getDatabasePerformance();
        if ($dbPerf['slowest_query_ms'] > 1000) {
            $issues[] = [
                'type' => 'slow_query',
                'message' => 'Database query took longer than 1 second',
                'value' => $dbPerf['slowest_query_ms'],
                'threshold' => 1000
            ];
        }
        
        // Check external API performance
        $apiPerf = $this->getExternalApiPerformance();
        if ($apiPerf['slowest_call_ms'] > 5000) {
            $issues[] = [
                'type' => 'slow_external_api',
                'message' => 'External API call took longer than 5 seconds',
                'value' => $apiPerf['slowest_call_ms'],
                'threshold' => 5000
            ];
        }
        
        return [
            'within_thresholds' => empty($issues),
            'issues' => $issues,
            'performance_grade' => $this->calculatePerformanceGrade($performance, $issues)
        ];
    }
    
    /**
     * Calculate performance grade (A-F)
     */
    private function calculatePerformanceGrade(array $performance, array $issues): string
    {
        $score = 100;
        
        // Deduct points for issues
        foreach ($issues as $issue) {
            switch ($issue['type']) {
                case 'slow_request':
                    $score -= 30;
                    break;
                case 'high_memory':
                    $score -= 20;
                    break;
                case 'slow_query':
                    $score -= 25;
                    break;
                case 'slow_external_api':
                    $score -= 15;
                    break;
            }
        }
        
        // Grade based on score
        if ($score >= 90) return 'A';
        if ($score >= 80) return 'B';
        if ($score >= 70) return 'C';
        if ($score >= 60) return 'D';
        return 'F';
    }
    
    /**
     * Generate performance report
     */
    public function generateReport(): array
    {
        $performance = $this->getRequestPerformance();
        $thresholds = $this->checkPerformanceThresholds();
        $dbPerf = $this->getDatabasePerformance();
        $apiPerf = $this->getExternalApiPerformance();
        
        return [
            'summary' => [
                'grade' => $thresholds['performance_grade'],
                'within_thresholds' => $thresholds['within_thresholds'],
                'total_duration_ms' => $performance['total_duration_ms'],
                'memory_usage_mb' => $performance['memory_usage_mb']
            ],
            'request_performance' => $performance,
            'database_performance' => $dbPerf,
            'external_api_performance' => $apiPerf,
            'threshold_analysis' => $thresholds,
            'generated_at' => date('c')
        ];
    }
}