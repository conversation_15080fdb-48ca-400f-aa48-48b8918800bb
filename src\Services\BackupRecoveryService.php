<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Config\ConfigManager;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Models\AdminUserModel;

/**
 * Backup and Recovery Service
 * 
 * Provides database backup, configuration export, and system recovery
 * capabilities for the GuardGeo platform.
 */
class BackupRecoveryService
{
    private ConfigManager $configManager;
    private DatabaseConnection $db;
    private LoggingService $logger;
    private SystemSettingsService $settingsService;
    private string $backupPath;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->configManager = ConfigManager::getInstance();
        $this->db = DatabaseConnection::getInstance();
        $this->logger = LoggingService::getInstance();
        $this->settingsService = new SystemSettingsService();
        $this->backupPath = dirname(__DIR__, 2) . '/backups';
        
        $this->ensureBackupDirectory();
    }
    
    /**
     * Ensure backup directory exists
     */
    private function ensureBackupDirectory(): void
    {
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }
    
    /**
     * Create full system backup
     */
    public function createFullBackup(AdminUserModel $user, array $options = []): array
    {
        $startTime = microtime(true);
        $backupId = date('Y-m-d_H-i-s') . '_' . uniqid();
        $backupDir = $this->backupPath . '/' . $backupId;
        
        try {
            mkdir($backupDir, 0755, true);
            
            $this->logger->info('Starting full system backup', [
                'backup_id' => $backupId,
                'initiated_by' => $user->getEmail(),
                'options' => $options
            ]);
            
            $results = [];
            
            // Database backup
            if ($options['include_database'] ?? true) {
                $results['database'] = $this->createDatabaseBackup($backupDir);
            }
            
            // Configuration backup
            if ($options['include_configuration'] ?? true) {
                $results['configuration'] = $this->createConfigurationBackup($backupDir, $user);
            }
            
            // Logs backup
            if ($options['include_logs'] ?? false) {
                $results['logs'] = $this->createLogsBackup($backupDir);
            }
            
            // Create backup manifest
            $manifest = $this->createBackupManifest($backupId, $user, $results, $options);
            file_put_contents($backupDir . '/manifest.json', json_encode($manifest, JSON_PRETTY_PRINT));
            
            // Create backup archive if requested
            $archivePath = null;
            if ($options['create_archive'] ?? true) {
                $archivePath = $this->createBackupArchive($backupDir, $backupId);
            }
            
            $executionTime = (microtime(true) - $startTime) * 1000;
            
            $this->logger->info('Full system backup completed', [
                'backup_id' => $backupId,
                'execution_time_ms' => $executionTime,
                'archive_path' => $archivePath
            ]);
            
            return [
                'success' => true,
                'backup_id' => $backupId,
                'backup_path' => $backupDir,
                'archive_path' => $archivePath,
                'execution_time_ms' => round($executionTime, 2),
                'results' => $results,
                'manifest' => $manifest
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Full system backup failed', [
                'backup_id' => $backupId,
                'error' => $e->getMessage(),
                'initiated_by' => $user->getEmail()
            ]);
            
            // Cleanup failed backup
            if (is_dir($backupDir)) {
                $this->removeDirectory($backupDir);
            }
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'backup_id' => $backupId
            ];
        }
    }
    
    /**
     * Create database backup
     */
    private function createDatabaseBackup(string $backupDir): array
    {
        try {
            $dbConfig = $this->configManager->getSection('database');
            $backupFile = $backupDir . '/database.sql';
            
            // Use pg_dump for PostgreSQL backup
            $command = sprintf(
                'pg_dump -h %s -p %d -U %s -d %s --no-password --clean --if-exists > %s',
                escapeshellarg($dbConfig['host']),
                $dbConfig['port'],
                escapeshellarg($dbConfig['username']),
                escapeshellarg($dbConfig['database']),
                escapeshellarg($backupFile)
            );
            
            // Set PGPASSWORD environment variable
            $env = ['PGPASSWORD' => $dbConfig['password']];
            
            $output = [];
            $returnCode = 0;
            
            // Execute backup command
            exec($command, $output, $returnCode);
            
            if ($returnCode !== 0) {
                throw new \Exception('pg_dump failed with return code: ' . $returnCode);
            }
            
            if (!file_exists($backupFile) || filesize($backupFile) === 0) {
                throw new \Exception('Database backup file is empty or was not created');
            }
            
            // Create table-specific backups for critical data
            $this->createTableBackups($backupDir);
            
            return [
                'success' => true,
                'file' => $backupFile,
                'size' => filesize($backupFile),
                'tables_backed_up' => $this->getTableList()
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Create table-specific backups
     */
    private function createTableBackups(string $backupDir): void
    {
        $criticalTables = [
            'admin_users',
            'system_config',
            'freemius_products',
            'freemius_installations'
        ];
        
        $tableDir = $backupDir . '/tables';
        mkdir($tableDir, 0755, true);
        
        foreach ($criticalTables as $table) {
            try {
                $query = "SELECT * FROM {$table}";
                $result = $this->db->query($query);
                $data = $result->fetchAll(\PDO::FETCH_ASSOC);
                
                $tableFile = $tableDir . "/{$table}.json";
                file_put_contents($tableFile, json_encode($data, JSON_PRETTY_PRINT));
                
            } catch (\Exception $e) {
                $this->logger->warning("Failed to backup table {$table}", [
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
    
    /**
     * Create configuration backup
     */
    private function createConfigurationBackup(string $backupDir, AdminUserModel $user): array
    {
        try {
            $configDir = $backupDir . '/configuration';
            mkdir($configDir, 0755, true);
            
            // Export system settings
            $settings = $this->settingsService->exportSettings(true); // Include sensitive
            file_put_contents($configDir . '/system_settings.json', json_encode($settings, JSON_PRETTY_PRINT));
            
            // Export full configuration
            $fullConfig = $this->configManager->export(true); // Include sensitive
            file_put_contents($configDir . '/full_config.json', json_encode($fullConfig, JSON_PRETTY_PRINT));
            
            // Copy environment files
            $envFiles = ['.env', '.env.example'];
            foreach ($envFiles as $envFile) {
                $sourcePath = dirname(__DIR__, 2) . '/' . $envFile;
                if (file_exists($sourcePath)) {
                    copy($sourcePath, $configDir . '/' . $envFile);
                }
            }
            
            // Copy config directory
            $configSourceDir = dirname(__DIR__, 2) . '/config';
            if (is_dir($configSourceDir)) {
                $this->copyDirectory($configSourceDir, $configDir . '/config');
            }
            
            return [
                'success' => true,
                'files' => [
                    'system_settings.json',
                    'full_config.json',
                    '.env',
                    '.env.example',
                    'config/'
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Create logs backup
     */
    private function createLogsBackup(string $backupDir): array
    {
        try {
            $logsDir = $backupDir . '/logs';
            mkdir($logsDir, 0755, true);
            
            $logPath = $this->configManager->get('logging.file_path', 'logs');
            
            if (is_dir($logPath)) {
                $this->copyDirectory($logPath, $logsDir);
                
                return [
                    'success' => true,
                    'source_path' => $logPath,
                    'backup_path' => $logsDir
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Log directory not found: ' . $logPath
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Create backup manifest
     */
    private function createBackupManifest(string $backupId, AdminUserModel $user, array $results, array $options): array
    {
        return [
            'backup_id' => $backupId,
            'created_at' => date('c'),
            'created_by' => [
                'id' => $user->getId(),
                'email' => $user->getEmail(),
                'role' => $user->getRole()
            ],
            'system_info' => [
                'php_version' => PHP_VERSION,
                'platform' => PHP_OS,
                'application_version' => '1.0.0',
                'environment' => $this->configManager->getEnvironment()
            ],
            'backup_options' => $options,
            'backup_results' => $results,
            'file_structure' => $this->getBackupFileStructure($backupId)
        ];
    }
    
    /**
     * Create backup archive
     */
    private function createBackupArchive(string $backupDir, string $backupId): string
    {
        $archivePath = $this->backupPath . '/' . $backupId . '.tar.gz';
        
        $command = sprintf(
            'tar -czf %s -C %s .',
            escapeshellarg($archivePath),
            escapeshellarg($backupDir)
        );
        
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new \Exception('Failed to create backup archive');
        }
        
        // Remove uncompressed backup directory
        $this->removeDirectory($backupDir);
        
        return $archivePath;
    }
    
    /**
     * List available backups
     */
    public function listBackups(): array
    {
        $backups = [];
        
        try {
            $files = glob($this->backupPath . '/*');
            
            foreach ($files as $file) {
                if (is_dir($file)) {
                    $manifestFile = $file . '/manifest.json';
                    if (file_exists($manifestFile)) {
                        $manifest = json_decode(file_get_contents($manifestFile), true);
                        $backups[] = [
                            'backup_id' => basename($file),
                            'created_at' => $manifest['created_at'],
                            'created_by' => $manifest['created_by']['email'],
                            'type' => 'directory',
                            'path' => $file,
                            'size' => $this->getDirectorySize($file)
                        ];
                    }
                } elseif (pathinfo($file, PATHINFO_EXTENSION) === 'gz') {
                    $backups[] = [
                        'backup_id' => basename($file, '.tar.gz'),
                        'created_at' => date('c', filemtime($file)),
                        'created_by' => 'unknown',
                        'type' => 'archive',
                        'path' => $file,
                        'size' => filesize($file)
                    ];
                }
            }
            
            // Sort by creation date (newest first)
            usort($backups, fn($a, $b) => strtotime($b['created_at']) - strtotime($a['created_at']));
            
            return $backups;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to list backups', [
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }
    
    /**
     * Restore from backup
     */
    public function restoreFromBackup(string $backupId, AdminUserModel $user, array $options = []): array
    {
        try {
            $this->logger->info('Starting system restore', [
                'backup_id' => $backupId,
                'initiated_by' => $user->getEmail(),
                'options' => $options
            ]);
            
            $backupPath = $this->findBackupPath($backupId);
            if (!$backupPath) {
                throw new \Exception("Backup not found: {$backupId}");
            }
            
            // Extract archive if needed
            $workingDir = $backupPath;
            if (pathinfo($backupPath, PATHINFO_EXTENSION) === 'gz') {
                $workingDir = $this->extractBackupArchive($backupPath);
            }
            
            // Load manifest
            $manifestFile = $workingDir . '/manifest.json';
            if (!file_exists($manifestFile)) {
                throw new \Exception('Backup manifest not found');
            }
            
            $manifest = json_decode(file_get_contents($manifestFile), true);
            
            $results = [];
            
            // Restore database
            if ($options['restore_database'] ?? true) {
                $results['database'] = $this->restoreDatabase($workingDir);
            }
            
            // Restore configuration
            if ($options['restore_configuration'] ?? true) {
                $results['configuration'] = $this->restoreConfiguration($workingDir, $user);
            }
            
            $this->logger->info('System restore completed', [
                'backup_id' => $backupId,
                'results' => $results
            ]);
            
            return [
                'success' => true,
                'backup_id' => $backupId,
                'manifest' => $manifest,
                'results' => $results
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('System restore failed', [
                'backup_id' => $backupId,
                'error' => $e->getMessage(),
                'initiated_by' => $user->getEmail()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'backup_id' => $backupId
            ];
        }
    }
    
    /**
     * Delete backup
     */
    public function deleteBackup(string $backupId, AdminUserModel $user): bool
    {
        try {
            $backupPath = $this->findBackupPath($backupId);
            if (!$backupPath) {
                return false;
            }
            
            if (is_dir($backupPath)) {
                $this->removeDirectory($backupPath);
            } else {
                unlink($backupPath);
            }
            
            $this->logger->info('Backup deleted', [
                'backup_id' => $backupId,
                'deleted_by' => $user->getEmail()
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to delete backup', [
                'backup_id' => $backupId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * Cleanup old backups
     */
    public function cleanupOldBackups(int $keepDays = 30): array
    {
        $cutoffTime = time() - ($keepDays * 24 * 60 * 60);
        $deleted = [];
        $errors = [];
        
        try {
            $backups = $this->listBackups();
            
            foreach ($backups as $backup) {
                $backupTime = strtotime($backup['created_at']);
                
                if ($backupTime < $cutoffTime) {
                    if ($this->deleteBackup($backup['backup_id'], new AdminUserModel(['id' => 0, 'email' => 'system']))) {
                        $deleted[] = $backup['backup_id'];
                    } else {
                        $errors[] = $backup['backup_id'];
                    }
                }
            }
            
            $this->logger->info('Backup cleanup completed', [
                'keep_days' => $keepDays,
                'deleted_count' => count($deleted),
                'error_count' => count($errors)
            ]);
            
            return [
                'success' => true,
                'deleted' => $deleted,
                'errors' => $errors,
                'keep_days' => $keepDays
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Backup cleanup failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Helper methods
     */
    
    private function findBackupPath(string $backupId): ?string
    {
        $dirPath = $this->backupPath . '/' . $backupId;
        $archivePath = $this->backupPath . '/' . $backupId . '.tar.gz';
        
        if (is_dir($dirPath)) {
            return $dirPath;
        } elseif (file_exists($archivePath)) {
            return $archivePath;
        }
        
        return null;
    }
    
    private function extractBackupArchive(string $archivePath): string
    {
        $extractDir = $this->backupPath . '/temp_' . uniqid();
        mkdir($extractDir, 0755, true);
        
        $command = sprintf(
            'tar -xzf %s -C %s',
            escapeshellarg($archivePath),
            escapeshellarg($extractDir)
        );
        
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new \Exception('Failed to extract backup archive');
        }
        
        return $extractDir;
    }
    
    private function restoreDatabase(string $backupDir): array
    {
        try {
            $sqlFile = $backupDir . '/database.sql';
            if (!file_exists($sqlFile)) {
                throw new \Exception('Database backup file not found');
            }
            
            $dbConfig = $this->configManager->getSection('database');
            
            $command = sprintf(
                'psql -h %s -p %d -U %s -d %s -f %s',
                escapeshellarg($dbConfig['host']),
                $dbConfig['port'],
                escapeshellarg($dbConfig['username']),
                escapeshellarg($dbConfig['database']),
                escapeshellarg($sqlFile)
            );
            
            $env = ['PGPASSWORD' => $dbConfig['password']];
            
            exec($command, $output, $returnCode);
            
            if ($returnCode !== 0) {
                throw new \Exception('Database restore failed with return code: ' . $returnCode);
            }
            
            return [
                'success' => true,
                'message' => 'Database restored successfully'
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function restoreConfiguration(string $backupDir, AdminUserModel $user): array
    {
        try {
            $configDir = $backupDir . '/configuration';
            
            // Restore system settings
            $settingsFile = $configDir . '/system_settings.json';
            if (file_exists($settingsFile)) {
                $settings = json_decode(file_get_contents($settingsFile), true);
                $this->settingsService->importSettings($settings, $user);
            }
            
            return [
                'success' => true,
                'message' => 'Configuration restored successfully'
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function copyDirectory(string $source, string $destination): void
    {
        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }
        
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($source, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $destPath = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                mkdir($destPath, 0755, true);
            } else {
                copy($item, $destPath);
            }
        }
    }
    
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $item) {
            if ($item->isDir()) {
                rmdir($item);
            } else {
                unlink($item);
            }
        }
        
        rmdir($dir);
    }
    
    private function getDirectorySize(string $dir): int
    {
        $size = 0;
        
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            $size += $file->getSize();
        }
        
        return $size;
    }
    
    private function getTableList(): array
    {
        try {
            $query = "
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            ";
            
            $result = $this->db->query($query);
            return $result->fetchAll(\PDO::FETCH_COLUMN);
            
        } catch (\Exception $e) {
            return [];
        }
    }
    
    private function getBackupFileStructure(string $backupId): array
    {
        $backupDir = $this->backupPath . '/' . $backupId;
        $structure = [];
        
        if (is_dir($backupDir)) {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($backupDir, \RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                $relativePath = str_replace($backupDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
                $structure[] = [
                    'path' => $relativePath,
                    'size' => $file->getSize(),
                    'type' => $file->isDir() ? 'directory' : 'file'
                ];
            }
        }
        
        return $structure;
    }
}