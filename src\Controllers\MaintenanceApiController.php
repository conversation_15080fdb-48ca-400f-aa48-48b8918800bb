<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Services\AuthService;
use Skpassegna\GuardgeoApi\Services\SystemHealthService;
use Skpassegna\GuardgeoApi\Services\BackupRecoveryService;
use Skpassegna\GuardgeoApi\Database\MigrationManager;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;
use Skpassegna\GuardgeoApi\Utils\RequestValidator;

/**
 * Maintenance API Controller
 * 
 * Handles system maintenance operations including health checks,
 * database migrations, backups, and deployment tasks.
 */
class MaintenanceApiController
{
    private AuthService $authService;
    private SystemHealthService $healthService;
    private BackupRecoveryService $backupService;
    private MigrationManager $migrationManager;
    private ResponseFormatter $responseFormatter;
    private RequestValidator $requestValidator;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->authService = new AuthService();
        $this->healthService = new SystemHealthService();
        $this->backupService = new BackupRecoveryService();
        $this->migrationManager = new MigrationManager();
        $this->responseFormatter = new ResponseFormatter();
        $this->requestValidator = new RequestValidator();
    }
    
    /**
     * Perform system health check
     */
    public function healthCheck(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            $type = $_GET['type'] ?? 'full';
            
            if ($type === 'quick') {
                $result = $this->healthService->getQuickStatus();
            } else {
                $result = $this->healthService->performHealthCheck();
            }
            
            $this->responseFormatter->sendSuccess($result);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Health check failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Get migration status
     */
    public function getMigrationStatus(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            $status = $this->migrationManager->getStatus();
            $this->responseFormatter->sendSuccess($status);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to get migration status: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Run database migrations
     */
    public function runMigrations(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || $user->getRole() !== 'super_admin') {
                $this->responseFormatter->sendError('Super Admin access required', 403);
                return;
            }
            
            $result = $this->migrationManager->migrate();
            
            if ($result['status'] === 'success') {
                $this->responseFormatter->sendSuccess($result);
            } else {
                $this->responseFormatter->sendError($result['message'], 500, $result);
            }
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Migration failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Rollback last migration
     */
    public function rollbackMigration(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || $user->getRole() !== 'super_admin') {
                $this->responseFormatter->sendError('Super Admin access required', 403);
                return;
            }
            
            $result = $this->migrationManager->rollback();
            
            if ($result['status'] === 'success') {
                $this->responseFormatter->sendSuccess($result);
            } else {
                $this->responseFormatter->sendError($result['message'], 500, $result);
            }
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Migration rollback failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Validate migrations
     */
    public function validateMigrations(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            $validation = $this->migrationManager->validateMigrations();
            $this->responseFormatter->sendSuccess($validation);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Migration validation failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Create system backup
     */
    public function createBackup(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || !in_array($user->getRole(), ['super_admin', 'dev'])) {
                $this->responseFormatter->sendError('Super Admin or Dev access required', 403);
                return;
            }
            
            $input = json_decode(file_get_contents('php://input'), true) ?? [];
            
            $options = [
                'include_database' => $input['include_database'] ?? true,
                'include_configuration' => $input['include_configuration'] ?? true,
                'include_logs' => $input['include_logs'] ?? false,
                'create_archive' => $input['create_archive'] ?? true
            ];
            
            $result = $this->backupService->createFullBackup($user, $options);
            
            if ($result['success']) {
                $this->responseFormatter->sendSuccess($result);
            } else {
                $this->responseFormatter->sendError($result['error'], 500, $result);
            }
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Backup creation failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * List available backups
     */
    public function listBackups(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || !in_array($user->getRole(), ['super_admin', 'dev'])) {
                $this->responseFormatter->sendError('Super Admin or Dev access required', 403);
                return;
            }
            
            $backups = $this->backupService->listBackups();
            
            $this->responseFormatter->sendSuccess([
                'backups' => $backups,
                'total_count' => count($backups)
            ]);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to list backups: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Restore from backup
     */
    public function restoreBackup(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || $user->getRole() !== 'super_admin') {
                $this->responseFormatter->sendError('Super Admin access required', 403);
                return;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            $validation = $this->requestValidator->validate($input, [
                'backup_id' => 'required|string'
            ]);
            
            if (!$validation['valid']) {
                $this->responseFormatter->sendError('Validation failed', 400, $validation['errors']);
                return;
            }
            
            $options = [
                'restore_database' => $input['restore_database'] ?? true,
                'restore_configuration' => $input['restore_configuration'] ?? true
            ];
            
            $result = $this->backupService->restoreFromBackup($input['backup_id'], $user, $options);
            
            if ($result['success']) {
                $this->responseFormatter->sendSuccess($result);
            } else {
                $this->responseFormatter->sendError($result['error'], 500, $result);
            }
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Backup restore failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Delete backup
     */
    public function deleteBackup(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || $user->getRole() !== 'super_admin') {
                $this->responseFormatter->sendError('Super Admin access required', 403);
                return;
            }
            
            $backupId = $_GET['backup_id'] ?? '';
            if (empty($backupId)) {
                $this->responseFormatter->sendError('Backup ID is required', 400);
                return;
            }
            
            $result = $this->backupService->deleteBackup($backupId, $user);
            
            if ($result) {
                $this->responseFormatter->sendSuccess([
                    'message' => 'Backup deleted successfully',
                    'backup_id' => $backupId
                ]);
            } else {
                $this->responseFormatter->sendError('Failed to delete backup', 500);
            }
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Backup deletion failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Cleanup old backups
     */
    public function cleanupBackups(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || $user->getRole() !== 'super_admin') {
                $this->responseFormatter->sendError('Super Admin access required', 403);
                return;
            }
            
            $keepDays = (int) ($_GET['keep_days'] ?? 30);
            
            if ($keepDays < 1) {
                $this->responseFormatter->sendError('Keep days must be at least 1', 400);
                return;
            }
            
            $result = $this->backupService->cleanupOldBackups($keepDays);
            
            if ($result['success']) {
                $this->responseFormatter->sendSuccess($result);
            } else {
                $this->responseFormatter->sendError($result['error'], 500, $result);
            }
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Backup cleanup failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Get system information
     */
    public function getSystemInfo(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            $info = [
                'php' => [
                    'version' => PHP_VERSION,
                    'sapi' => PHP_SAPI,
                    'os' => PHP_OS,
                    'memory_limit' => ini_get('memory_limit'),
                    'max_execution_time' => ini_get('max_execution_time'),
                    'upload_max_filesize' => ini_get('upload_max_filesize'),
                    'post_max_size' => ini_get('post_max_size')
                ],
                'server' => [
                    'software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? '',
                    'server_name' => $_SERVER['SERVER_NAME'] ?? '',
                    'request_time' => $_SERVER['REQUEST_TIME'] ?? time()
                ],
                'application' => [
                    'name' => 'GuardGeo Admin Platform',
                    'version' => '1.0.0',
                    'environment' => \Skpassegna\GuardgeoApi\Config\ConfigManager::getInstance()->getEnvironment()
                ],
                'extensions' => [
                    'pdo' => extension_loaded('pdo'),
                    'pdo_pgsql' => extension_loaded('pdo_pgsql'),
                    'curl' => extension_loaded('curl'),
                    'json' => extension_loaded('json'),
                    'mbstring' => extension_loaded('mbstring'),
                    'openssl' => extension_loaded('openssl')
                ]
            ];
            
            $this->responseFormatter->sendSuccess($info);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to get system info: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Clear system caches
     */
    public function clearCaches(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || !in_array($user->getRole(), ['super_admin', 'dev'])) {
                $this->responseFormatter->sendError('Super Admin or Dev access required', 403);
                return;
            }
            
            $cacheType = $_GET['type'] ?? 'all';
            $results = [];
            
            switch ($cacheType) {
                case 'ip':
                    $results['ip_cache'] = $this->clearIpCache();
                    break;
                    
                case 'freemius':
                    $results['freemius_cache'] = $this->clearFreemiusCache();
                    break;
                    
                case 'opcache':
                    $results['opcache'] = $this->clearOpcache();
                    break;
                    
                case 'all':
                default:
                    $results['ip_cache'] = $this->clearIpCache();
                    $results['freemius_cache'] = $this->clearFreemiusCache();
                    $results['opcache'] = $this->clearOpcache();
                    break;
            }
            
            $this->responseFormatter->sendSuccess([
                'message' => 'Cache clearing completed',
                'cache_type' => $cacheType,
                'results' => $results
            ]);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Cache clearing failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Optimize database
     */
    public function optimizeDatabase(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || $user->getRole() !== 'super_admin') {
                $this->responseFormatter->sendError('Super Admin access required', 403);
                return;
            }
            
            $db = \Skpassegna\GuardgeoApi\Database\DatabaseConnection::getInstance();
            
            // Run VACUUM and ANALYZE on all tables
            $tables = [
                'admin_users',
                'freemius_products',
                'freemius_installations',
                'ip_intelligence',
                'system_logs',
                'api_requests',
                'system_config',
                'schema_migrations'
            ];
            
            $results = [];
            
            foreach ($tables as $table) {
                try {
                    $db->exec("VACUUM ANALYZE {$table}");
                    $results[$table] = 'optimized';
                } catch (\Exception $e) {
                    $results[$table] = 'error: ' . $e->getMessage();
                }
            }
            
            $this->responseFormatter->sendSuccess([
                'message' => 'Database optimization completed',
                'results' => $results
            ]);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Database optimization failed: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Helper methods for cache clearing
     */
    
    private function clearIpCache(): array
    {
        try {
            $db = \Skpassegna\GuardgeoApi\Database\DatabaseConnection::getInstance();
            
            // Clear expired IP cache entries
            $query = "DELETE FROM ip_intelligence WHERE 
                      security_expires_at < NOW() OR 
                      location_expires_at < NOW() OR 
                      connection_expires_at < NOW() OR 
                      company_expires_at < NOW()";
            
            $stmt = $db->prepare($query);
            $stmt->execute();
            $deletedCount = $stmt->rowCount();
            
            return [
                'success' => true,
                'deleted_entries' => $deletedCount,
                'message' => "Cleared {$deletedCount} expired IP cache entries"
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function clearFreemiusCache(): array
    {
        try {
            $db = \Skpassegna\GuardgeoApi\Database\DatabaseConnection::getInstance();
            
            // Update cached_at timestamp to force refresh
            $queries = [
                "UPDATE freemius_products SET cached_at = NOW() - INTERVAL '1 day'",
                "UPDATE freemius_installations SET cached_at = NOW() - INTERVAL '1 day'"
            ];
            
            $updatedCount = 0;
            
            foreach ($queries as $query) {
                $stmt = $db->prepare($query);
                $stmt->execute();
                $updatedCount += $stmt->rowCount();
            }
            
            return [
                'success' => true,
                'updated_entries' => $updatedCount,
                'message' => "Marked {$updatedCount} Freemius cache entries for refresh"
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function clearOpcache(): array
    {
        if (!function_exists('opcache_reset')) {
            return [
                'success' => false,
                'error' => 'OPcache extension not available'
            ];
        }
        
        try {
            $result = opcache_reset();
            
            return [
                'success' => $result,
                'message' => $result ? 'OPcache cleared successfully' : 'Failed to clear OPcache'
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}