<?php

namespace Skpassegna\GuardgeoApi\Database;

/**
 * System Configuration Repository
 * 
 * Handles database operations for system configuration settings.
 */
class SystemConfigRepository extends BaseRepository
{
    protected string $table = 'system_config';
    
    /**
     * Get configuration value by key
     */
    public function getConfigValue(string $key, $default = null)
    {
        try {
            $row = $this->findOneBy(['config_key' => $key, 'is_active' => true]);
            
            if (!$row) {
                return $default;
            }
            
            return $this->parseConfigValue($row['config_value'], $row['config_type']);
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get config value", [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return $default;
        }
    }
    
    /**
     * Set configuration value
     */
    public function setConfigValue(string $key, $value, string $type = 'string', ?string $description = null, bool $isSensitive = false, ?int $updatedBy = null): bool
    {
        try {
            $configValue = $this->formatConfigValue($value, $type);
            
            // Check if config exists
            $existing = $this->findOneBy(['config_key' => $key]);
            
            if ($existing) {
                // Update existing
                $data = [
                    'config_value' => $configValue,
                    'config_type' => $type,
                    'updated_by' => $updatedBy
                ];
                
                if ($description !== null) {
                    $data['description'] = $description;
                }
                
                return $this->update($existing['id'], $data);
            } else {
                // Insert new
                $data = [
                    'config_key' => $key,
                    'config_value' => $configValue,
                    'config_type' => $type,
                    'description' => $description,
                    'is_sensitive' => $isSensitive,
                    'updated_by' => $updatedBy
                ];
                
                $this->insert($data);
                return true;
            }
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to set config value", [
                'key' => $key,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Get all configuration values
     */
    public function getAllConfig(bool $includeSensitive = false): array
    {
        try {
            $conditions = ['is_active' => true];
            if (!$includeSensitive) {
                $conditions['is_sensitive'] = false;
            }
            
            $rows = $this->findBy($conditions, ['order' => ['config_key' => 'ASC']]);
            
            $config = [];
            foreach ($rows as $row) {
                $value = $this->parseConfigValue($row['config_value'], $row['config_type']);
                
                if ($row['is_sensitive'] && !$includeSensitive) {
                    $value = $this->maskSensitiveValue($value);
                }
                
                $config[$row['config_key']] = [
                    'value' => $value,
                    'type' => $row['config_type'],
                    'description' => $row['description'],
                    'is_sensitive' => (bool)$row['is_sensitive'],
                    'updated_at' => $row['updated_at']
                ];
            }
            
            return $config;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get all config", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get configuration by section (prefix)
     */
    public function getConfigBySection(string $section): array
    {
        try {
            $sql = "
                SELECT * FROM {$this->table} 
                WHERE config_key LIKE :section AND is_active = true
                ORDER BY config_key ASC
            ";
            
            $statement = $this->executeQuery($sql, ['section' => $section . '.%']);
            $rows = $statement->fetchAll();
            
            $config = [];
            foreach ($rows as $row) {
                $value = $this->parseConfigValue($row['config_value'], $row['config_type']);
                $config[$row['config_key']] = $value;
            }
            
            return $config;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get config by section", [
                'section' => $section,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Delete configuration key
     */
    public function deleteConfig(string $key): bool
    {
        try {
            $sql = "DELETE FROM {$this->table} WHERE config_key = :key";
            $statement = $this->executeQuery($sql, ['key' => $key]);
            
            return $statement->rowCount() > 0;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to delete config", [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Deactivate configuration key (soft delete)
     */
    public function deactivateConfig(string $key): bool
    {
        try {
            $existing = $this->findOneBy(['config_key' => $key]);
            
            if (!$existing) {
                return false;
            }
            
            return $this->update($existing['id'], ['is_active' => false]);
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to deactivate config", [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Get configuration with pagination for admin interface
     */
    public function getConfigPaginated(int $page, int $limit, array $filters = []): array
    {
        $offset = ($page - 1) * $limit;
        
        $whereConditions = ['is_active = true'];
        $params = [];
        
        // Apply filters
        if (!empty($filters['search'])) {
            $whereConditions[] = '(config_key ILIKE :search OR description ILIKE :search)';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['type'])) {
            $whereConditions[] = 'config_type = :type';
            $params['type'] = $filters['type'];
        }
        
        if (isset($filters['is_sensitive'])) {
            $whereConditions[] = 'is_sensitive = :is_sensitive';
            $params['is_sensitive'] = (bool)$filters['is_sensitive'];
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        
        $sql = "
            SELECT c.*, u.email as updated_by_email
            FROM {$this->table} c
            LEFT JOIN admin_users u ON c.updated_by = u.id
            {$whereClause}
            ORDER BY config_key ASC
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        try {
            $statement = $this->executeQuery($sql, $params);
            $configs = $statement->fetchAll();
            
            // Parse values and mask sensitive ones
            foreach ($configs as &$config) {
                $config['parsed_value'] = $this->parseConfigValue($config['config_value'], $config['config_type']);
                
                if ($config['is_sensitive']) {
                    $config['display_value'] = $this->maskSensitiveValue($config['parsed_value']);
                } else {
                    $config['display_value'] = $config['parsed_value'];
                }
            }
            
            return [
                'configs' => $configs,
                'total' => $this->getFilteredConfigCount($filters)
            ];
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get paginated config", [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [
                'configs' => [],
                'total' => 0
            ];
        }
    }
    
    /**
     * Parse configuration value based on type
     */
    private function parseConfigValue(string $value, string $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            case 'array':
                return explode(',', $value);
            default:
                return $value;
        }
    }
    
    /**
     * Format configuration value for storage
     */
    private function formatConfigValue($value, string $type): string
    {
        switch ($type) {
            case 'boolean':
                return $value ? 'true' : 'false';
            case 'integer':
            case 'float':
                return (string) $value;
            case 'json':
                return json_encode($value);
            case 'array':
                return is_array($value) ? implode(',', $value) : (string) $value;
            default:
                return (string) $value;
        }
    }
    
    /**
     * Mask sensitive values for display
     */
    private function maskSensitiveValue($value): string
    {
        if (is_string($value) && strlen($value) > 8) {
            return substr($value, 0, 4) . str_repeat('*', strlen($value) - 8) . substr($value, -4);
        }
        
        return str_repeat('*', is_string($value) ? strlen($value) : 8);
    }
    
    /**
     * Get filtered count for pagination
     */
    private function getFilteredConfigCount(array $filters): int
    {
        $whereConditions = ['is_active = true'];
        $params = [];
        
        // Apply same filters as main query
        if (!empty($filters['search'])) {
            $whereConditions[] = '(config_key ILIKE :search OR description ILIKE :search)';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['type'])) {
            $whereConditions[] = 'config_type = :type';
            $params['type'] = $filters['type'];
        }
        
        if (isset($filters['is_sensitive'])) {
            $whereConditions[] = 'is_sensitive = :is_sensitive';
            $params['is_sensitive'] = (bool)$filters['is_sensitive'];
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        $sql = "SELECT COUNT(*) as count FROM {$this->table} {$whereClause}";
        
        try {
            $statement = $this->executeQuery($sql, $params);
            $row = $statement->fetch();
            return (int)($row['count'] ?? 0);
        } catch (DatabaseException $e) {
            return 0;
        }
    }
    
    /**
     * Backup configuration to array
     */
    public function backupConfig(): array
    {
        try {
            $configs = $this->getAllConfig(true); // Include sensitive data
            
            return [
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0',
                'configs' => $configs
            ];
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to backup config", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Restore configuration from backup
     */
    public function restoreConfig(array $backup, ?int $updatedBy = null): bool
    {
        try {
            $this->beginTransaction();
            
            foreach ($backup['configs'] as $key => $config) {
                $this->setConfigValue(
                    $key,
                    $config['value'],
                    $config['type'],
                    $config['description'],
                    $config['is_sensitive'],
                    $updatedBy
                );
            }
            
            $this->commit();
            return true;
            
        } catch (DatabaseException $e) {
            $this->rollback();
            $this->logger->error("Failed to restore config", [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}