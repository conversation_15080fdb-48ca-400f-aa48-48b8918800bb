<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Database\IpIntelligenceRepository;
use Skpassegna\GuardgeoApi\Database\FreemiusRepository;
use Skpassegna\GuardgeoApi\Utils\Logger;
use Skpassegna\GuardgeoApi\Config\Environment;
use DateTime;

/**
 * Cache Invalidation Service
 * 
 * Implements intelligent cache invalidation strategies to maintain
 * data consistency while optimizing performance.
 */
class CacheInvalidationService
{
    private IpIntelligenceRepository $ipRepository;
    private FreemiusRepository $freemiusRepository;
    private Logger $logger;
    
    // Invalidation strategies
    private array $invalidationStrategies;
    private array $invalidationRules;
    
    /**
     * Constructor
     */
    public function __construct(
        ?IpIntelligenceRepository $ipRepository = null,
        ?FreemiusRepository $freemiusRepository = null
    ) {
        $this->ipRepository = $ipRepository ?? new IpIntelligenceRepository();
        $this->freemiusRepository = $freemiusRepository ?? new FreemiusRepository();
        $this->logger = new Logger();
        
        $this->initializeInvalidationStrategies();
        $this->initializeInvalidationRules();
    }
    
    /**
     * Initialize invalidation strategies
     */
    private function initializeInvalidationStrategies(): void
    {
        $this->invalidationStrategies = [
            'immediate' => [
                'description' => 'Immediate invalidation for critical data changes',
                'delay' => 0,
                'batch_size' => 1
            ],
            'delayed' => [
                'description' => 'Delayed invalidation for non-critical changes',
                'delay' => (int) Environment::get('CACHE_DELAYED_INVALIDATION_SECONDS', 300),
                'batch_size' => (int) Environment::get('CACHE_INVALIDATION_BATCH_SIZE', 100)
            ],
            'scheduled' => [
                'description' => 'Scheduled invalidation during low-traffic periods',
                'delay' => 0,
                'batch_size' => (int) Environment::get('CACHE_SCHEDULED_BATCH_SIZE', 500)
            ],
            'conditional' => [
                'description' => 'Conditional invalidation based on data age and usage',
                'delay' => 0,
                'batch_size' => (int) Environment::get('CACHE_CONDITIONAL_BATCH_SIZE', 200)
            ]
        ];
    }
    
    /**
     * Initialize invalidation rules
     */
    private function initializeInvalidationRules(): void
    {
        $this->invalidationRules = [
            'security_data' => [
                'strategy' => 'immediate',
                'reason' => 'Security data changes require immediate invalidation',
                'max_age_hours' => 1
            ],
            'location_data' => [
                'strategy' => 'delayed',
                'reason' => 'Location data can tolerate some delay',
                'max_age_hours' => 24
            ],
            'connection_data' => [
                'strategy' => 'delayed',
                'reason' => 'Connection data changes are less critical',
                'max_age_hours' => 12
            ],
            'company_data' => [
                'strategy' => 'scheduled',
                'reason' => 'Company data changes infrequently',
                'max_age_hours' => 168 // 7 days
            ],
            'freemius_data' => [
                'strategy' => 'immediate',
                'reason' => 'Subscription changes affect access control',
                'max_age_hours' => 1
            ]
        ];
    }
    
    /**
     * Invalidate cache for specific IP
     */
    public function invalidateIp(string $ip, string $reason = 'manual', array $dataTypes = []): array
    {
        $this->logger->info("Invalidating cache for IP", [
            'ip' => $ip,
            'reason' => $reason,
            'data_types' => $dataTypes
        ]);
        
        $results = [
            'ip' => $ip,
            'invalidated' => false,
            'strategy_used' => null,
            'data_types_invalidated' => [],
            'errors' => []
        ];
        
        try {
            // Get current cached data
            $cachedData = $this->ipRepository->findByIp($ip);
            
            if ($cachedData === null) {
                $this->logger->info("No cached data found for IP", ['ip' => $ip]);
                return $results;
            }
            
            // Determine invalidation strategy
            $strategy = $this->determineInvalidationStrategy($dataTypes, $reason);
            $results['strategy_used'] = $strategy;
            
            // Apply invalidation based on strategy
            switch ($strategy) {
                case 'immediate':
                    $results = array_merge($results, $this->performImmediateInvalidation($ip, $dataTypes));
                    break;
                    
                case 'delayed':
                    $results = array_merge($results, $this->scheduleDelayedInvalidation($ip, $dataTypes, $reason));
                    break;
                    
                case 'scheduled':
                    $results = array_merge($results, $this->scheduleInvalidation($ip, $dataTypes, $reason));
                    break;
                    
                case 'conditional':
                    $results = array_merge($results, $this->performConditionalInvalidation($ip, $dataTypes, $cachedData));
                    break;
                    
                default:
                    $results = array_merge($results, $this->performImmediateInvalidation($ip, $dataTypes));
            }
            
        } catch (\Exception $e) {
            $error = "Failed to invalidate cache for IP {$ip}: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        return $results;
    }
    
    /**
     * Invalidate cache for multiple IPs
     */
    public function invalidateMultipleIps(array $ips, string $reason = 'batch', array $dataTypes = []): array
    {
        $this->logger->info("Invalidating cache for multiple IPs", [
            'ip_count' => count($ips),
            'reason' => $reason,
            'data_types' => $dataTypes
        ]);
        
        $results = [
            'total_ips' => count($ips),
            'invalidated_count' => 0,
            'failed_count' => 0,
            'skipped_count' => 0,
            'strategy_used' => null,
            'results' => [],
            'errors' => []
        ];
        
        // Determine batch strategy
        $strategy = $this->determineBatchInvalidationStrategy(count($ips), $dataTypes, $reason);
        $results['strategy_used'] = $strategy;
        
        // Process in batches
        $batchSize = $this->invalidationStrategies[$strategy]['batch_size'];
        $batches = array_chunk($ips, $batchSize);
        
        foreach ($batches as $batchIndex => $batch) {
            $this->logger->debug("Processing invalidation batch", [
                'batch_index' => $batchIndex + 1,
                'batch_size' => count($batch),
                'total_batches' => count($batches)
            ]);
            
            foreach ($batch as $ip) {
                try {
                    $ipResult = $this->invalidateIp($ip, $reason, $dataTypes);
                    $results['results'][$ip] = $ipResult;
                    
                    if ($ipResult['invalidated']) {
                        $results['invalidated_count']++;
                    } elseif (!empty($ipResult['errors'])) {
                        $results['failed_count']++;
                    } else {
                        $results['skipped_count']++;
                    }
                    
                } catch (\Exception $e) {
                    $results['failed_count']++;
                    $error = "Failed to invalidate IP {$ip}: " . $e->getMessage();
                    $results['errors'][] = $error;
                    $this->logger->error($error);
                }
            }
            
            // Add delay between batches if configured
            $delay = $this->invalidationStrategies[$strategy]['delay'];
            if ($delay > 0 && $batchIndex < count($batches) - 1) {
                sleep($delay);
            }
        }
        
        $this->logger->info("Batch invalidation completed", [
            'total_ips' => $results['total_ips'],
            'invalidated' => $results['invalidated_count'],
            'failed' => $results['failed_count'],
            'skipped' => $results['skipped_count']
        ]);
        
        return $results;
    }
    
    /**
     * Invalidate cache based on data age
     */
    public function invalidateByAge(array $ageRules = []): array
    {
        $this->logger->info("Invalidating cache based on data age");
        
        $results = [
            'processed' => 0,
            'invalidated' => 0,
            'failed' => 0,
            'rules_applied' => [],
            'errors' => []
        ];
        
        try {
            // Use default rules if none provided
            if (empty($ageRules)) {
                $ageRules = $this->getDefaultAgeRules();
            }
            
            foreach ($ageRules as $dataType => $rule) {
                $this->logger->debug("Applying age rule", [
                    'data_type' => $dataType,
                    'max_age_hours' => $rule['max_age_hours']
                ]);
                
                $ruleResults = $this->applyAgeRule($dataType, $rule);
                $results['rules_applied'][] = [
                    'data_type' => $dataType,
                    'processed' => $ruleResults['processed'],
                    'invalidated' => $ruleResults['invalidated']
                ];
                
                $results['processed'] += $ruleResults['processed'];
                $results['invalidated'] += $ruleResults['invalidated'];
                $results['failed'] += $ruleResults['failed'];
                
                if (!empty($ruleResults['errors'])) {
                    $results['errors'] = array_merge($results['errors'], $ruleResults['errors']);
                }
            }
            
        } catch (\Exception $e) {
            $error = "Failed to invalidate by age: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        return $results;
    }
    
    /**
     * Invalidate cache based on usage patterns
     */
    public function invalidateByUsagePattern(): array
    {
        $this->logger->info("Invalidating cache based on usage patterns");
        
        $results = [
            'strategies_applied' => [],
            'total_invalidated' => 0,
            'errors' => []
        ];
        
        try {
            // Strategy 1: Invalidate rarely accessed data
            $rarelyAccessedResults = $this->invalidateRarelyAccessedData();
            $results['strategies_applied'][] = 'rarely_accessed';
            $results['total_invalidated'] += $rarelyAccessedResults['invalidated'];
            
            // Strategy 2: Invalidate data with high error rates
            $highErrorResults = $this->invalidateHighErrorData();
            $results['strategies_applied'][] = 'high_error_rate';
            $results['total_invalidated'] += $highErrorResults['invalidated'];
            
            // Strategy 3: Invalidate data from inactive regions
            $inactiveRegionResults = $this->invalidateInactiveRegionData();
            $results['strategies_applied'][] = 'inactive_regions';
            $results['total_invalidated'] += $inactiveRegionResults['invalidated'];
            
        } catch (\Exception $e) {
            $error = "Failed to invalidate by usage pattern: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        return $results;
    }
    
    /**
     * Perform immediate invalidation
     */
    private function performImmediateInvalidation(string $ip, array $dataTypes = []): array
    {
        $results = [
            'invalidated' => false,
            'data_types_invalidated' => [],
            'errors' => []
        ];
        
        try {
            if (empty($dataTypes)) {
                // Invalidate entire record
                $success = $this->ipRepository->deleteByIp($ip);
                if ($success) {
                    $results['invalidated'] = true;
                    $results['data_types_invalidated'] = ['all'];
                    $this->logger->info("Immediately invalidated all data for IP", ['ip' => $ip]);
                }
            } else {
                // Invalidate specific data types by updating expiry dates
                $success = $this->invalidateSpecificDataTypes($ip, $dataTypes);
                if ($success) {
                    $results['invalidated'] = true;
                    $results['data_types_invalidated'] = $dataTypes;
                    $this->logger->info("Immediately invalidated specific data types for IP", [
                        'ip' => $ip,
                        'data_types' => $dataTypes
                    ]);
                }
            }
            
        } catch (\Exception $e) {
            $error = "Immediate invalidation failed: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error);
        }
        
        return $results;
    }
    
    /**
     * Schedule delayed invalidation
     */
    private function scheduleDelayedInvalidation(string $ip, array $dataTypes, string $reason): array
    {
        $results = [
            'invalidated' => false,
            'scheduled' => true,
            'data_types_invalidated' => [],
            'errors' => []
        ];
        
        try {
            // For now, perform immediate invalidation
            // In a full implementation, this would queue the invalidation
            $immediateResults = $this->performImmediateInvalidation($ip, $dataTypes);
            $results = array_merge($results, $immediateResults);
            
            $this->logger->info("Scheduled delayed invalidation for IP", [
                'ip' => $ip,
                'data_types' => $dataTypes,
                'reason' => $reason
            ]);
            
        } catch (\Exception $e) {
            $error = "Delayed invalidation scheduling failed: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error);
        }
        
        return $results;
    }
    
    /**
     * Schedule invalidation for low-traffic periods
     */
    private function scheduleInvalidation(string $ip, array $dataTypes, string $reason): array
    {
        $results = [
            'invalidated' => false,
            'scheduled' => true,
            'data_types_invalidated' => [],
            'errors' => []
        ];
        
        try {
            // For now, perform immediate invalidation
            // In a full implementation, this would schedule for off-peak hours
            $immediateResults = $this->performImmediateInvalidation($ip, $dataTypes);
            $results = array_merge($results, $immediateResults);
            
            $this->logger->info("Scheduled invalidation for IP", [
                'ip' => $ip,
                'data_types' => $dataTypes,
                'reason' => $reason
            ]);
            
        } catch (\Exception $e) {
            $error = "Invalidation scheduling failed: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error);
        }
        
        return $results;
    }
    
    /**
     * Perform conditional invalidation based on data age and usage
     */
    private function performConditionalInvalidation(string $ip, array $dataTypes, $cachedData): array
    {
        $results = [
            'invalidated' => false,
            'condition_met' => false,
            'data_types_invalidated' => [],
            'errors' => []
        ];
        
        try {
            // Check conditions for invalidation
            $shouldInvalidate = $this->evaluateInvalidationConditions($cachedData, $dataTypes);
            $results['condition_met'] = $shouldInvalidate;
            
            if ($shouldInvalidate) {
                $immediateResults = $this->performImmediateInvalidation($ip, $dataTypes);
                $results = array_merge($results, $immediateResults);
                
                $this->logger->info("Conditional invalidation performed for IP", [
                    'ip' => $ip,
                    'data_types' => $dataTypes
                ]);
            } else {
                $this->logger->info("Conditional invalidation skipped for IP", [
                    'ip' => $ip,
                    'reason' => 'conditions not met'
                ]);
            }
            
        } catch (\Exception $e) {
            $error = "Conditional invalidation failed: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error);
        }
        
        return $results;
    }
    
    /**
     * Determine invalidation strategy based on data types and reason
     */
    private function determineInvalidationStrategy(array $dataTypes, string $reason): string
    {
        // If no specific data types, use immediate strategy
        if (empty($dataTypes)) {
            return 'immediate';
        }
        
        // Check if any data type requires immediate invalidation
        foreach ($dataTypes as $dataType) {
            if (isset($this->invalidationRules[$dataType]) && 
                $this->invalidationRules[$dataType]['strategy'] === 'immediate') {
                return 'immediate';
            }
        }
        
        // Check reason-based overrides
        if (in_array($reason, ['security_threat', 'fraud_detected', 'manual_urgent'])) {
            return 'immediate';
        }
        
        // Default to delayed strategy
        return 'delayed';
    }
    
    /**
     * Determine batch invalidation strategy
     */
    private function determineBatchInvalidationStrategy(int $ipCount, array $dataTypes, string $reason): string
    {
        // Large batches use scheduled strategy
        if ($ipCount > 1000) {
            return 'scheduled';
        }
        
        // Medium batches use delayed strategy
        if ($ipCount > 100) {
            return 'delayed';
        }
        
        // Small batches can use immediate strategy
        return $this->determineInvalidationStrategy($dataTypes, $reason);
    }
    
    /**
     * Invalidate specific data types by updating expiry dates
     */
    private function invalidateSpecificDataTypes(string $ip, array $dataTypes): bool
    {
        try {
            $cachedData = $this->ipRepository->findByIp($ip);
            if ($cachedData === null) {
                return false;
            }
            
            $now = new DateTime();
            $updated = false;
            
            foreach ($dataTypes as $dataType) {
                switch ($dataType) {
                    case 'security':
                        $cachedData->security_expires_at = $now;
                        $updated = true;
                        break;
                    case 'location':
                        $cachedData->location_expires_at = $now;
                        $updated = true;
                        break;
                    case 'connection':
                        $cachedData->connection_expires_at = $now;
                        $updated = true;
                        break;
                    case 'company':
                        $cachedData->company_expires_at = $now;
                        $updated = true;
                        break;
                }
            }
            
            if ($updated) {
                return $this->ipRepository->update($cachedData);
            }
            
            return false;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to invalidate specific data types", [
                'ip' => $ip,
                'data_types' => $dataTypes,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Get default age rules for invalidation
     */
    private function getDefaultAgeRules(): array
    {
        return [
            'security' => ['max_age_hours' => 1],
            'location' => ['max_age_hours' => 24],
            'connection' => ['max_age_hours' => 12],
            'company' => ['max_age_hours' => 168]
        ];
    }
    
    /**
     * Apply age rule for specific data type
     */
    private function applyAgeRule(string $dataType, array $rule): array
    {
        $results = [
            'processed' => 0,
            'invalidated' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        try {
            $cutoffTime = (new DateTime())->modify("-{$rule['max_age_hours']} hours");
            $expiredIps = $this->ipRepository->findExpiredByDataType($dataType, $cutoffTime);
            
            $results['processed'] = count($expiredIps);
            
            foreach ($expiredIps as $ip) {
                try {
                    $success = $this->invalidateSpecificDataTypes($ip, [$dataType]);
                    if ($success) {
                        $results['invalidated']++;
                    } else {
                        $results['failed']++;
                    }
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Failed to invalidate {$ip}: " . $e->getMessage();
                }
            }
            
        } catch (\Exception $e) {
            $error = "Failed to apply age rule for {$dataType}: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error);
        }
        
        return $results;
    }
    
    /**
     * Evaluate conditions for conditional invalidation
     */
    private function evaluateInvalidationConditions($cachedData, array $dataTypes): bool
    {
        // Example conditions - in a full implementation, these would be more sophisticated
        
        // Condition 1: Data is very old
        $now = new DateTime();
        $cacheAge = $now->diff($cachedData->cached_at)->days;
        if ($cacheAge > 30) {
            return true;
        }
        
        // Condition 2: Data has been accessed recently (would need access tracking)
        // This would require additional implementation
        
        // Condition 3: Data quality issues detected
        // This would require data quality analysis
        
        return false;
    }
    
    /**
     * Get invalidation statistics
     */
    public function getInvalidationStatistics(): array
    {
        return [
            'strategies' => $this->invalidationStrategies,
            'rules' => $this->invalidationRules,
            'recent_invalidations' => $this->getRecentInvalidations(),
            'invalidation_patterns' => $this->getInvalidationPatterns(),
            'recommendations' => $this->getInvalidationRecommendations()
        ];
    }
    
    // Placeholder methods for complex functionality
    private function invalidateRarelyAccessedData(): array { return ['invalidated' => 0]; }
    private function invalidateHighErrorData(): array { return ['invalidated' => 0]; }
    private function invalidateInactiveRegionData(): array { return ['invalidated' => 0]; }
    private function getRecentInvalidations(): array { return []; }
    private function getInvalidationPatterns(): array { return []; }
    private function getInvalidationRecommendations(): array { return []; }
}