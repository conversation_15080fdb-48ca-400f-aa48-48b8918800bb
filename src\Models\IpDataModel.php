<?php

namespace Skpassegna\GuardgeoApi\Models;

use DateTime;

/**
 * IP Data Model
 * 
 * Represents IP intelligence data from ipRegistry API with caching metadata
 * and deprecation rules for different data types.
 */
class IpDataModel extends BaseModel
{
    public string $ip;
    public string $type; // IPv4, IPv6
    public ?string $hostname;
    
    // Carrier information
    public array $carrier; // name, mcc, mnc
    
    // Company information
    public array $company; // domain, name, type
    
    // Connection information
    public array $connection; // asn, domain, organization, route, type
    
    // Currency information
    public array $currency; // code, name, name_native, plural, symbol, format
    
    // Location information
    public array $location; // continent, country, region, city, postal, coordinates, language, in_eu
    
    // Security assessment
    public array $security; // is_abuser, is_attacker, is_bogon, is_cloud_provider, is_proxy, is_relay, is_tor, is_tor_exit, is_vpn, is_anonymous, is_threat
    
    // Time zone information
    public array $time_zone; // id, abbreviation, current_time, name, offset, in_daylight_saving
    
    // Caching metadata
    public DateTime $cached_at;
    public DateTime $location_expires_at;
    public DateTime $security_expires_at;
    public DateTime $connection_expires_at;
    public DateTime $company_expires_at;
    
    // Raw API response for debugging
    public ?array $raw_data;
    
    /**
     * Constructor
     */
    public function __construct(array $data = [])
    {
        $this->ip = $this->toString($data['ip'] ?? '');
        $this->type = $this->toString($data['type'] ?? '');
        $this->hostname = $this->toNullableString($data['hostname'] ?? null);
        
        // Initialize arrays with proper handling of JSON data
        $this->carrier = $this->parseJsonField($data['carrier'] ?? $data['carrier_data'] ?? []);
        $this->company = $this->parseJsonField($data['company'] ?? $data['company_data'] ?? []);
        $this->connection = $this->parseJsonField($data['connection'] ?? $data['connection_data'] ?? []);
        $this->currency = $this->parseJsonField($data['currency'] ?? $data['currency_data'] ?? []);
        $this->location = $this->parseJsonField($data['location'] ?? $data['location_data'] ?? []);
        $this->security = $this->parseJsonField($data['security'] ?? $data['security_data'] ?? []);
        $this->time_zone = $this->parseJsonField($data['time_zone'] ?? $data['time_zone_data'] ?? []);
        
        // Set caching metadata
        $this->cached_at = isset($data['cached_at']) 
            ? $this->dbToDateTime($data['cached_at']) ?? new DateTime()
            : new DateTime();
            
        $this->location_expires_at = isset($data['location_expires_at'])
            ? $this->dbToDateTime($data['location_expires_at']) ?? $this->calculateLocationExpiry()
            : $this->calculateLocationExpiry();
            
        $this->security_expires_at = isset($data['security_expires_at'])
            ? $this->dbToDateTime($data['security_expires_at']) ?? $this->calculateSecurityExpiry()
            : $this->calculateSecurityExpiry();
            
        $this->connection_expires_at = isset($data['connection_expires_at'])
            ? $this->dbToDateTime($data['connection_expires_at']) ?? $this->calculateConnectionExpiry()
            : $this->calculateConnectionExpiry();
            
        $this->company_expires_at = isset($data['company_expires_at'])
            ? $this->dbToDateTime($data['company_expires_at']) ?? $this->calculateCompanyExpiry()
            : $this->calculateCompanyExpiry();
        
        $this->raw_data = $this->parseJsonField($data['raw_data'] ?? null);
    }

    /**
     * Parse JSON field data
     */
    private function parseJsonField($data): array
    {
        if (is_string($data)) {
            $decoded = json_decode($data, true);
            return is_array($decoded) ? $decoded : [];
        }
        
        return is_array($data) ? $data : [];
    }
    
    /**
     * Create from ipRegistry API response
     */
    public static function fromApiResponse(array $apiData): self
    {
        $processedData = $apiData;
        $processedData['cached_at'] = (new DateTime())->format('Y-m-d H:i:s');
        $processedData['raw_data'] = $apiData;
        
        return new self($processedData);
    }

    /**
     * Create from database row
     */
    public static function fromDatabaseRow(array $row): self
    {
        return new self($row);
    }
    
    /**
     * Calculate location data expiry (10 days from now)
     */
    private function calculateLocationExpiry(): DateTime
    {
        return (new DateTime())->modify('+10 days');
    }
    
    /**
     * Calculate security data expiry (3 days from now)
     */
    private function calculateSecurityExpiry(): DateTime
    {
        return (new DateTime())->modify('+3 days');
    }
    
    /**
     * Calculate connection data expiry (7 days from now)
     */
    private function calculateConnectionExpiry(): DateTime
    {
        return (new DateTime())->modify('+7 days');
    }
    
    /**
     * Calculate company data expiry (30 days from now)
     */
    private function calculateCompanyExpiry(): DateTime
    {
        return (new DateTime())->modify('+30 days');
    }
    
    /**
     * Check if location data is expired
     */
    public function isLocationExpired(): bool
    {
        return new DateTime() > $this->location_expires_at;
    }
    
    /**
     * Check if security data is expired
     */
    public function isSecurityExpired(): bool
    {
        return new DateTime() > $this->security_expires_at;
    }
    
    /**
     * Check if connection data is expired
     */
    public function isConnectionExpired(): bool
    {
        return new DateTime() > $this->connection_expires_at;
    }
    
    /**
     * Check if company data is expired
     */
    public function isCompanyExpired(): bool
    {
        return new DateTime() > $this->company_expires_at;
    }
    
    /**
     * Check if any data is expired and needs refresh
     */
    public function needsRefresh(): bool
    {
        return $this->isLocationExpired() || 
               $this->isSecurityExpired() || 
               $this->isConnectionExpired() || 
               $this->isCompanyExpired();
    }
    
    /**
     * Get list of expired data types
     */
    public function getExpiredDataTypes(): array
    {
        $expired = [];
        
        if ($this->isLocationExpired()) {
            $expired[] = 'location';
        }
        
        if ($this->isSecurityExpired()) {
            $expired[] = 'security';
        }
        
        if ($this->isConnectionExpired()) {
            $expired[] = 'connection';
        }
        
        if ($this->isCompanyExpired()) {
            $expired[] = 'company';
        }
        
        return $expired;
    }
    
    /**
     * Comprehensive validation
     */
    public function validate(): array
    {
        $errors = [];
        
        // Validate required fields
        if ($error = $this->validateRequired($this->ip, 'ip')) {
            $errors[] = $error;
        } elseif (!ModelValidator::validateIpAddress($this->ip)) {
            $errors[] = 'ip must be a valid IP address';
        }
        
        if ($error = $this->validateRequired($this->type, 'type')) {
            $errors[] = $error;
        } elseif ($error = $this->validateEnum($this->type, ModelValidator::IP_TYPES, 'type')) {
            $errors[] = $error;
        }
        
        // Validate IP type consistency
        if ($this->ip && $this->type) {
            $isIpv4 = ModelValidator::validateIpv4Address($this->ip);
            $isIpv6 = ModelValidator::validateIpv6Address($this->ip);
            
            if ($this->type === 'IPv4' && !$isIpv4) {
                $errors[] = 'IP address type mismatch: expected IPv4';
            } elseif ($this->type === 'IPv6' && !$isIpv6) {
                $errors[] = 'IP address type mismatch: expected IPv6';
            }
        }
        
        // Validate hostname if provided
        if ($this->hostname !== null && !empty($this->hostname)) {
            if ($error = $this->validateStringLength($this->hostname, 255, 'hostname')) {
                $errors[] = $error;
            }
            
            // Basic hostname validation
            if (!preg_match('/^[a-zA-Z0-9.-]+$/', $this->hostname)) {
                $errors[] = 'hostname contains invalid characters';
            }
        }
        
        // Validate data structure integrity
        if (!is_array($this->carrier)) {
            $errors[] = 'carrier data must be an array';
        }
        
        if (!is_array($this->company)) {
            $errors[] = 'company data must be an array';
        }
        
        if (!is_array($this->connection)) {
            $errors[] = 'connection data must be an array';
        }
        
        if (!is_array($this->currency)) {
            $errors[] = 'currency data must be an array';
        }
        
        if (!is_array($this->location)) {
            $errors[] = 'location data must be an array';
        }
        
        if (!is_array($this->security)) {
            $errors[] = 'security data must be an array';
        }
        
        if (!is_array($this->time_zone)) {
            $errors[] = 'time_zone data must be an array';
        }
        
        return $errors;
    }
    
    /**
     * Convert to database array
     */
    public function toDatabaseArray(): array
    {
        return [
            'ip' => $this->ip,
            'type' => $this->type,
            'hostname' => $this->hostname,
            'carrier_data' => json_encode($this->carrier),
            'company_data' => json_encode($this->company),
            'connection_data' => json_encode($this->connection),
            'currency_data' => json_encode($this->currency),
            'location_data' => json_encode($this->location),
            'security_data' => json_encode($this->security),
            'time_zone_data' => json_encode($this->time_zone),
            'cached_at' => $this->dateTimeToDb($this->cached_at),
            'location_expires_at' => $this->dateTimeToDb($this->location_expires_at),
            'security_expires_at' => $this->dateTimeToDb($this->security_expires_at),
            'connection_expires_at' => $this->dateTimeToDb($this->connection_expires_at),
            'company_expires_at' => $this->dateTimeToDb($this->company_expires_at),
            'raw_data' => $this->raw_data ? json_encode($this->raw_data) : null
        ];
    }
    
    /**
     * JSON serialization
     */
    public function jsonSerialize(): array
    {
        return [
            'ip' => $this->ip,
            'type' => $this->type,
            'hostname' => $this->hostname,
            'carrier' => $this->carrier,
            'company' => $this->company,
            'connection' => $this->connection,
            'currency' => $this->currency,
            'location' => $this->location,
            'security' => $this->security,
            'time_zone' => $this->time_zone,
            'cached_at' => $this->dateTimeToJson($this->cached_at),
            'location_expires_at' => $this->dateTimeToJson($this->location_expires_at),
            'security_expires_at' => $this->dateTimeToJson($this->security_expires_at),
            'connection_expires_at' => $this->dateTimeToJson($this->connection_expires_at),
            'company_expires_at' => $this->dateTimeToJson($this->company_expires_at),
            'needs_refresh' => $this->needsRefresh(),
            'expired_data_types' => $this->getExpiredDataTypes(),
            'threat_indicators' => $this->getThreatIndicators(),
            'is_high_risk' => $this->isHighRisk(),
            'is_anonymized' => $this->isAnonymized(),
            'location_summary' => $this->getLocationSummary(),
            'organization' => $this->getOrganization(),
            'isp' => $this->getIsp()
        ];
    }

    /**
     * Transform for external API integration (ipRegistry format)
     */
    public function toIpRegistryApiFormat(): array
    {
        return [
            'ip' => $this->ip,
            'type' => $this->type,
            'hostname' => $this->hostname,
            'carrier' => $this->carrier,
            'company' => $this->company,
            'connection' => $this->connection,
            'currency' => $this->currency,
            'location' => $this->location,
            'security' => $this->security,
            'time_zone' => $this->time_zone
        ];
    }

    /**
     * Get safe array for API responses (excludes internal metadata)
     */
    public function toSafeArray(): array
    {
        return [
            'ip' => $this->ip,
            'type' => $this->type,
            'hostname' => $this->hostname,
            'carrier' => $this->carrier,
            'company' => $this->company,
            'connection' => $this->connection,
            'currency' => $this->currency,
            'location' => $this->location,
            'security' => $this->security,
            'time_zone' => $this->time_zone,
            'threat_indicators' => $this->getThreatIndicators(),
            'is_high_risk' => $this->isHighRisk(),
            'is_anonymized' => $this->isAnonymized(),
            'location_summary' => $this->getLocationSummary()
        ];
    }

    /**
     * Handle foreign key relationships
     */
    public function getRelatedApiRequests(): array
    {
        // This would be implemented by the repository layer
        return [];
    }

    /**
     * Validate foreign key constraints
     */
    public function validateForeignKeys(): array
    {
        $errors = [];
        
        // IP data doesn't have foreign key dependencies
        // but we validate data integrity
        
        return $errors;
    }
    
    /**
     * Get security threat indicators
     */
    public function getThreatIndicators(): array
    {
        $threats = [];
        
        $threatFields = [
            'is_abuser', 'is_attacker', 'is_bogon', 'is_proxy', 
            'is_relay', 'is_tor', 'is_tor_exit', 'is_vpn', 
            'is_anonymous', 'is_threat'
        ];
        
        foreach ($threatFields as $field) {
            if (isset($this->security[$field]) && $this->security[$field] === true) {
                $threats[] = $field;
            }
        }
        
        return $threats;
    }
    
    /**
     * Check if IP is considered high risk
     */
    public function isHighRisk(): bool
    {
        $highRiskIndicators = ['is_abuser', 'is_attacker', 'is_threat'];
        
        foreach ($highRiskIndicators as $indicator) {
            if (isset($this->security[$indicator]) && $this->security[$indicator] === true) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if IP is using anonymization services
     */
    public function isAnonymized(): bool
    {
        $anonymizationIndicators = ['is_proxy', 'is_vpn', 'is_tor', 'is_anonymous'];
        
        foreach ($anonymizationIndicators as $indicator) {
            if (isset($this->security[$indicator]) && $this->security[$indicator] === true) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get location summary
     */
    public function getLocationSummary(): string
    {
        $parts = [];
        
        if (isset($this->location['city']) && !empty($this->location['city'])) {
            $parts[] = $this->location['city'];
        }
        
        if (isset($this->location['region']['name']) && !empty($this->location['region']['name'])) {
            $parts[] = $this->location['region']['name'];
        }
        
        if (isset($this->location['country']['name']) && !empty($this->location['country']['name'])) {
            $parts[] = $this->location['country']['name'];
        }
        
        return implode(', ', $parts) ?: 'Unknown Location';
    }
    
    /**
     * Get organization name from connection data
     */
    public function getOrganization(): ?string
    {
        return $this->connection['organization'] ?? null;
    }
    
    /**
     * Get ISP name from connection data
     */
    public function getIsp(): ?string
    {
        return $this->connection['domain'] ?? null;
    }
}