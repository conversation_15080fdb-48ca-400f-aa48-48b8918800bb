<?php

namespace Skpassegna\GuardgeoApi\Config;

/**
 * IP Cache Configuration
 * 
 * Manages configuration settings for IP intelligence caching system
 * including deprecation rules and batch processing parameters.
 */
class IpCacheConfig
{
    // Default deprecation periods (in days)
    public const DEFAULT_LOCATION_DAYS = 10;
    public const DEFAULT_SECURITY_DAYS = 3;
    public const DEFAULT_CONNECTION_DAYS = 7;
    public const DEFAULT_COMPANY_DAYS = 30;
    
    // Default cache limits
    public const DEFAULT_MAX_CACHE_SIZE = 100000;
    public const DEFAULT_RETENTION_DAYS = 90;
    
    // Default batch processing settings
    public const DEFAULT_BATCH_SIZE = 100;
    public const DEFAULT_CLEANUP_BATCH_SIZE = 1000;
    public const DEFAULT_REFRESH_BATCH_SIZE = 100;
    public const DEFAULT_BATCH_DELAY = 0.5;
    
    private array $config;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->loadConfiguration();
    }
    
    /**
     * Load configuration from environment
     */
    private function loadConfiguration(): void
    {
        $this->config = [
            // Deprecation settings
            'deprecation' => [
                'location_days' => (int) Environment::get('IP_CACHE_LOCATION_DAYS', self::DEFAULT_LOCATION_DAYS),
                'security_days' => (int) Environment::get('IP_CACHE_SECURITY_DAYS', self::DEFAULT_SECURITY_DAYS),
                'connection_days' => (int) Environment::get('IP_CACHE_CONNECTION_DAYS', self::DEFAULT_CONNECTION_DAYS),
                'company_days' => (int) Environment::get('IP_CACHE_COMPANY_DAYS', self::DEFAULT_COMPANY_DAYS),
            ],
            
            // Cache size and retention
            'cache' => [
                'max_size' => (int) Environment::get('IP_CACHE_MAX_SIZE', self::DEFAULT_MAX_CACHE_SIZE),
                'retention_days' => (int) Environment::get('IP_CACHE_RETENTION_DAYS', self::DEFAULT_RETENTION_DAYS),
            ],
            
            // Batch processing
            'batch' => [
                'max_batch_size' => (int) Environment::get('IP_CACHE_BATCH_SIZE', self::DEFAULT_BATCH_SIZE),
                'cleanup_batch_size' => (int) Environment::get('IP_CACHE_CLEANUP_BATCH_SIZE', self::DEFAULT_CLEANUP_BATCH_SIZE),
                'refresh_batch_size' => (int) Environment::get('IP_CACHE_REFRESH_BATCH_SIZE', self::DEFAULT_REFRESH_BATCH_SIZE),
                'delay_between_batches' => (float) Environment::get('IP_CACHE_BATCH_DELAY', self::DEFAULT_BATCH_DELAY),
            ],
            
            // Performance settings
            'performance' => [
                'enable_batch_processing' => Environment::get('IP_CACHE_ENABLE_BATCH', 'true') === 'true',
                'enable_auto_cleanup' => Environment::get('IP_CACHE_ENABLE_AUTO_CLEANUP', 'true') === 'true',
                'enable_auto_refresh' => Environment::get('IP_CACHE_ENABLE_AUTO_REFRESH', 'true') === 'true',
            ]
        ];
    }
    
    /**
     * Get deprecation configuration
     */
    public function getDeprecationConfig(): array
    {
        return $this->config['deprecation'];
    }
    
    /**
     * Get cache configuration
     */
    public function getCacheConfig(): array
    {
        return $this->config['cache'];
    }
    
    /**
     * Get batch processing configuration
     */
    public function getBatchConfig(): array
    {
        return $this->config['batch'];
    }
    
    /**
     * Get performance configuration
     */
    public function getPerformanceConfig(): array
    {
        return $this->config['performance'];
    }
    
    /**
     * Get all configuration
     */
    public function getAllConfig(): array
    {
        return $this->config;
    }
    
    /**
     * Get specific configuration value
     */
    public function get(string $section, string $key, $default = null)
    {
        return $this->config[$section][$key] ?? $default;
    }
    
    /**
     * Set configuration value
     */
    public function set(string $section, string $key, $value): void
    {
        if (!isset($this->config[$section])) {
            $this->config[$section] = [];
        }
        
        $this->config[$section][$key] = $value;
    }
    
    /**
     * Update deprecation configuration
     */
    public function updateDeprecationConfig(array $config): void
    {
        $this->config['deprecation'] = array_merge($this->config['deprecation'], $config);
    }
    
    /**
     * Update cache configuration
     */
    public function updateCacheConfig(array $config): void
    {
        $this->config['cache'] = array_merge($this->config['cache'], $config);
    }
    
    /**
     * Update batch configuration
     */
    public function updateBatchConfig(array $config): void
    {
        $this->config['batch'] = array_merge($this->config['batch'], $config);
    }
    
    /**
     * Validate configuration values
     */
    public function validate(): array
    {
        $errors = [];
        
        // Validate deprecation days
        foreach ($this->config['deprecation'] as $key => $days) {
            if (!is_int($days) || $days < 1) {
                $errors[] = "Deprecation {$key} must be a positive integer";
            }
        }
        
        // Validate cache settings
        if (!is_int($this->config['cache']['max_size']) || $this->config['cache']['max_size'] < 1000) {
            $errors[] = "Cache max_size must be at least 1000";
        }
        
        if (!is_int($this->config['cache']['retention_days']) || $this->config['cache']['retention_days'] < 7) {
            $errors[] = "Cache retention_days must be at least 7";
        }
        
        // Validate batch settings
        if (!is_int($this->config['batch']['max_batch_size']) || $this->config['batch']['max_batch_size'] < 1) {
            $errors[] = "Batch max_batch_size must be a positive integer";
        }
        
        if (!is_float($this->config['batch']['delay_between_batches']) || $this->config['batch']['delay_between_batches'] < 0) {
            $errors[] = "Batch delay_between_batches must be a non-negative number";
        }
        
        return $errors;
    }
    
    /**
     * Check if configuration is valid
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }
    
    /**
     * Get configuration summary for logging
     */
    public function getSummary(): array
    {
        return [
            'deprecation_periods' => [
                'location' => $this->config['deprecation']['location_days'] . ' days',
                'security' => $this->config['deprecation']['security_days'] . ' days',
                'connection' => $this->config['deprecation']['connection_days'] . ' days',
                'company' => $this->config['deprecation']['company_days'] . ' days',
            ],
            'cache_limits' => [
                'max_size' => number_format($this->config['cache']['max_size']),
                'retention' => $this->config['cache']['retention_days'] . ' days',
            ],
            'batch_settings' => [
                'max_batch_size' => $this->config['batch']['max_batch_size'],
                'delay' => $this->config['batch']['delay_between_batches'] . 's',
            ],
            'features_enabled' => [
                'batch_processing' => $this->config['performance']['enable_batch_processing'],
                'auto_cleanup' => $this->config['performance']['enable_auto_cleanup'],
                'auto_refresh' => $this->config['performance']['enable_auto_refresh'],
            ]
        ];
    }
    
    /**
     * Export configuration for backup
     */
    public function export(): string
    {
        return json_encode($this->config, JSON_PRETTY_PRINT);
    }
    
    /**
     * Import configuration from JSON
     */
    public function import(string $json): bool
    {
        $imported = json_decode($json, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        
        // Validate imported configuration
        $tempConfig = $this->config;
        $this->config = array_merge($this->config, $imported);
        
        if (!$this->isValid()) {
            $this->config = $tempConfig; // Restore original
            return false;
        }
        
        return true;
    }
}