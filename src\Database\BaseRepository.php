<?php

namespace Skpassegna\GuardgeoApi\Database;

use PDO;
use PDOStatement;
use Skpassegna\GuardgeoApi\Utils\Logger;

/**
 * Base Repository Pattern
 * 
 * Provides common database operations with prepared statement support
 * and comprehensive error handling.
 */
abstract class BaseRepository
{
    protected Logger $logger;
    protected string $table;
    protected string $primaryKey = 'id';
    
    public function __construct()
    {
        $this->logger = new Logger();
    }
    
    /**
     * Find a record by ID
     */
    public function findById(int|string $id): ?array
    {
        try {
            $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
            $statement = DatabaseConnection::execute($sql, ['id' => $id]);
            
            $result = $statement->fetch();
            return $result ?: null;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to find record by ID in {$this->table}", [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Find records by criteria
     */
    public function findBy(array $criteria, array $options = []): array
    {
        try {
            $whereClause = $this->buildWhereClause($criteria);
            $orderClause = $this->buildOrderClause($options['order'] ?? []);
            $limitClause = $this->buildLimitClause($options['limit'] ?? null, $options['offset'] ?? null);
            
            $sql = "SELECT * FROM {$this->table} {$whereClause} {$orderClause} {$limitClause}";
            $statement = DatabaseConnection::execute($sql, array_values($criteria));
            
            return $statement->fetchAll();
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to find records in {$this->table}", [
                'criteria' => $criteria,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Find one record by criteria
     */
    public function findOneBy(array $criteria): ?array
    {
        $results = $this->findBy($criteria, ['limit' => 1]);
        return $results[0] ?? null;
    }
    
    /**
     * Get all records
     */
    public function findAll(array $options = []): array
    {
        return $this->findBy([], $options);
    }
    
    /**
     * Count records by criteria
     */
    public function countBy(array $criteria = []): int
    {
        try {
            $whereClause = $this->buildWhereClause($criteria);
            $sql = "SELECT COUNT(*) as count FROM {$this->table} {$whereClause}";
            $statement = DatabaseConnection::execute($sql, array_values($criteria));
            
            $result = $statement->fetch();
            return (int) $result['count'];
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to count records in {$this->table}", [
                'criteria' => $criteria,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Insert a new record
     */
    public function insert(array $data): int|string
    {
        try {
            // Add timestamps if columns exist
            $data = $this->addTimestamps($data, 'insert');
            
            $columns = array_keys($data);
            $placeholders = array_map(fn($col) => ":$col", $columns);
            
            $sql = sprintf(
                "INSERT INTO %s (%s) VALUES (%s)",
                $this->table,
                implode(', ', $columns),
                implode(', ', $placeholders)
            );
            
            DatabaseConnection::execute($sql, $data);
            
            $insertId = DatabaseConnection::lastInsertId($this->getSequenceName());
            
            $this->logger->info("Record inserted into {$this->table}", [
                'id' => $insertId,
                'columns' => count($columns)
            ]);
            
            return $insertId;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to insert record into {$this->table}", [
                'data' => array_keys($data),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Update a record by ID
     */
    public function update(int|string $id, array $data): bool
    {
        try {
            // Add timestamps if columns exist
            $data = $this->addTimestamps($data, 'update');
            
            $setPairs = array_map(fn($col) => "$col = :$col", array_keys($data));
            $data['id'] = $id; // Add ID to parameters
            
            $sql = sprintf(
                "UPDATE %s SET %s WHERE %s = :id",
                $this->table,
                implode(', ', $setPairs),
                $this->primaryKey
            );
            
            $statement = DatabaseConnection::execute($sql, $data);
            $rowCount = $statement->rowCount();
            
            $this->logger->info("Record updated in {$this->table}", [
                'id' => $id,
                'affected_rows' => $rowCount
            ]);
            
            return $rowCount > 0;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to update record in {$this->table}", [
                'id' => $id,
                'data' => array_keys($data),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Delete a record by ID
     */
    public function delete(int|string $id): bool
    {
        try {
            $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
            $statement = DatabaseConnection::execute($sql, ['id' => $id]);
            $rowCount = $statement->rowCount();
            
            $this->logger->info("Record deleted from {$this->table}", [
                'id' => $id,
                'affected_rows' => $rowCount
            ]);
            
            return $rowCount > 0;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to delete record from {$this->table}", [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Execute custom SQL query
     */
    protected function executeQuery(string $sql, array $params = []): PDOStatement
    {
        try {
            return DatabaseConnection::execute($sql, $params);
        } catch (DatabaseException $e) {
            $this->logger->error("Custom query execution failed in {$this->table}", [
                'sql' => $sql,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Build WHERE clause from criteria
     */
    protected function buildWhereClause(array $criteria): string
    {
        if (empty($criteria)) {
            return '';
        }
        
        $conditions = array_map(fn($col) => "$col = ?", array_keys($criteria));
        return 'WHERE ' . implode(' AND ', $conditions);
    }
    
    /**
     * Build ORDER BY clause
     */
    protected function buildOrderClause(array $order): string
    {
        if (empty($order)) {
            return '';
        }
        
        $orderPairs = [];
        foreach ($order as $column => $direction) {
            $direction = strtoupper($direction) === 'DESC' ? 'DESC' : 'ASC';
            $orderPairs[] = "$column $direction";
        }
        
        return 'ORDER BY ' . implode(', ', $orderPairs);
    }
    
    /**
     * Build LIMIT clause
     */
    protected function buildLimitClause(?int $limit, ?int $offset): string
    {
        $clause = '';
        
        if ($limit !== null) {
            $clause .= "LIMIT $limit";
        }
        
        if ($offset !== null) {
            $clause .= " OFFSET $offset";
        }
        
        return $clause;
    }
    
    /**
     * Add timestamps to data if columns exist
     */
    protected function addTimestamps(array $data, string $operation): array
    {
        $now = date('Y-m-d H:i:s');
        
        if ($operation === 'insert' && !isset($data['created_at'])) {
            $data['created_at'] = $now;
        }
        
        if (!isset($data['updated_at'])) {
            $data['updated_at'] = $now;
        }
        
        return $data;
    }
    
    /**
     * Get sequence name for PostgreSQL
     */
    protected function getSequenceName(): string
    {
        return $this->table . '_' . $this->primaryKey . '_seq';
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction(): bool
    {
        return DatabaseConnection::beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit(): bool
    {
        return DatabaseConnection::commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback(): bool
    {
        return DatabaseConnection::rollback();
    }
}