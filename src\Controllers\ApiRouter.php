<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Services\FreemiusService;
use Skpassegna\GuardgeoApi\Services\IpRegistryService;
use Skpassegna\GuardgeoApi\Services\LoggingServiceFactory;
use Skpassegna\GuardgeoApi\Utils\RequestValidator;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;

class ApiRouter
{
    private ResponseFormatter $responseFormatter;

    public function __construct()
    {
        $this->responseFormatter = new ResponseFormatter();
    }

    /**
     * Route API requests to appropriate controllers with comprehensive error handling
     */
    public function route(): void
    {
        $startTime = microtime(true);
        
        try {
            // Set comprehensive headers for world-class REST API
            $this->responseFormatter->setSecurityHeaders();
            $this->responseFormatter->setJsonHeaders();
            $this->responseFormatter->setCorsHeaders();

            // Handle preflight OPTIONS requests
            if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
                http_response_code(200);
                return;
            }

            // Get and validate the request path
            $requestUri = $_SERVER['REQUEST_URI'] ?? '';
            $path = parse_url($requestUri, PHP_URL_PATH);

            if ($path === false) {
                $this->handleBadRequest('Invalid request URI format');
                return;
            }

            // Remove leading slash and split path
            $pathSegments = array_filter(explode('/', trim($path, '/')));

            // Validate API path structure
            if (empty($pathSegments) || $pathSegments[0] !== 'api') {
                $this->handleNotFound('API endpoint not found');
                return;
            }

            if (count($pathSegments) < 2) {
                $this->handleBadRequest('API endpoint not specified', [
                    'available_endpoints' => ['analyze'],
                    'format' => '/api/{endpoint}'
                ]);
                return;
            }

            $endpoint = $pathSegments[1];
            
            // Route to appropriate endpoint with comprehensive validation
            switch ($endpoint) {
                case 'analyze':
                    $this->handleAnalyzeEndpoint();
                    break;
                
                case 'health':
                    $this->handleHealthCheck();
                    break;
                    
                case 'status':
                    $this->handleStatusCheck();
                    break;
                
                case 'webhook':
                    $this->handleWebhookEndpoint($pathSegments);
                    break;
                
                default:
                    $this->handleNotFound('Unknown API endpoint', [
                        'requested_endpoint' => $endpoint,
                        'available_endpoints' => ['analyze', 'health', 'status', 'webhook']
                    ]);
                    break;
            }

        } catch (\Exception $e) {
            $this->handleInternalError($e, $startTime);
        }
    }

    /**
     * Handle /api/analyze endpoint
     */
    private function handleAnalyzeEndpoint(): void
    {
        try {
            // Create dependencies
            $freemiusService = $this->createFreemiusService();
            $ipRegistryService = $this->createIpRegistryService();
            $apiLogger = $this->createApiLogger();
            $requestValidator = new RequestValidator();
            $responseFormatter = new ResponseFormatter();

            // Create and execute controller
            $controller = new ApiController(
                $freemiusService,
                $ipRegistryService,
                $apiLogger,
                $requestValidator,
                $responseFormatter
            );

            $controller->analyze();

        } catch (\Exception $e) {
            error_log("Analyze Endpoint Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            http_response_code(500);
            echo $this->responseFormatter->formatInternalError(uniqid('analyze_err_'));
        }
    }

    /**
     * Handle 404 Not Found with detailed information
     */
    private function handleNotFound(string $message = 'API endpoint not found', array $details = []): void
    {
        http_response_code(404);
        echo $this->responseFormatter->formatError('NOT_FOUND', $message, $details, 404);
    }

    /**
     * Handle 400 Bad Request
     */
    private function handleBadRequest(string $message, array $details = []): void
    {
        http_response_code(400);
        echo $this->responseFormatter->formatError('BAD_REQUEST', $message, $details, 400);
    }

    /**
     * Handle 500 Internal Server Error
     */
    private function handleInternalError(\Exception $e, float $startTime): void
    {
        $errorId = uniqid('router_err_');
        $duration = round((microtime(true) - $startTime) * 1000, 2);
        
        // Log the error with comprehensive details
        error_log("Router Error [{$errorId}]: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        
        http_response_code(500);
        echo $this->responseFormatter->formatError('INTERNAL_ERROR', 'An internal server error occurred', [
            'error_id' => $errorId,
            'duration_ms' => $duration,
            'timestamp' => date('c')
        ], 500);
    }

    /**
     * Handle health check endpoint with comprehensive system health monitoring
     */
    private function handleHealthCheck(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            http_response_code(405);
            echo $this->responseFormatter->formatError('METHOD_NOT_ALLOWED', 'Only GET method is allowed', [
                'allowed_methods' => ['GET']
            ], 405);
            return;
        }

        try {
            // Get health check type from query parameter
            $type = $_GET['type'] ?? 'basic';
            
            switch ($type) {
                case 'comprehensive':
                    $healthService = new \Skpassegna\GuardgeoApi\Services\SystemHealthService();
                    $healthData = $healthService->performHealthCheck();
                    break;
                    
                case 'quick':
                    $healthService = new \Skpassegna\GuardgeoApi\Services\SystemHealthService();
                    $healthData = $healthService->getQuickStatus();
                    break;
                    
                case 'basic':
                default:
                    $healthData = $this->getBasicHealthCheck();
                    break;
            }

            // Set appropriate HTTP status based on overall health
            $httpStatus = match($healthData['overall_status'] ?? 'healthy') {
                'healthy' => 200,
                'degraded' => 200, // Still operational but with warnings
                'error' => 503,    // Service unavailable
                default => 200
            };

            http_response_code($httpStatus);
            echo $this->responseFormatter->formatSuccess($healthData);
            
        } catch (\Exception $e) {
            error_log("Health check error: " . $e->getMessage());
            
            http_response_code(503);
            echo $this->responseFormatter->formatError('HEALTH_CHECK_FAILED', 'Health check failed', [
                'error' => 'Unable to perform health check',
                'timestamp' => date('c')
            ], 503);
        }
    }
    
    /**
     * Get basic health check data
     */
    private function getBasicHealthCheck(): array
    {
        return [
            'status' => 'healthy',
            'overall_status' => 'healthy',
            'timestamp' => date('c'),
            'version' => '1.0.0',
            'environment' => \Skpassegna\GuardgeoApi\Config\Environment::get('APP_ENV', 'production'),
            'uptime' => $this->getUptime(),
            'basic_checks' => [
                'php_version' => PHP_VERSION,
                'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
                'server_time' => date('c')
            ]
        ];
    }

    /**
     * Handle status check endpoint
     */
    private function handleStatusCheck(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            http_response_code(405);
            echo $this->responseFormatter->formatError('METHOD_NOT_ALLOWED', 'Only GET method is allowed', [
                'allowed_methods' => ['GET']
            ], 405);
            return;
        }

        $statusData = [
            'api_status' => 'operational',
            'endpoints' => [
                'analyze' => 'operational',
                'health' => 'operational',
                'status' => 'operational'
            ],
            'timestamp' => date('c'),
            'server_time' => date('c'),
            'timezone' => date_default_timezone_get()
        ];

        http_response_code(200);
        echo $this->responseFormatter->formatSuccess($statusData);
    }

    /**
     * Get system uptime (simplified version)
     */
    private function getUptime(): array
    {
        return [
            'server_start' => date('c', $_SERVER['REQUEST_TIME'] ?? time()),
            'current_time' => date('c'),
            'php_version' => PHP_VERSION
        ];
    }

    /**
     * Create FreemiusService instance
     */
    private function createFreemiusService(): FreemiusService
    {
        return new FreemiusService();
    }

    /**
     * Create IpRegistryService instance
     */
    private function createIpRegistryService(): IpRegistryService
    {
        return new IpRegistryService();
    }

    /**
     * Create ApiLogger instance
     */
    private function createApiLogger()
    {
        return LoggingServiceFactory::getApiLogger();
    }

    /**
     * Get request method
     */
    public function getRequestMethod(): string
    {
        return $_SERVER['REQUEST_METHOD'] ?? 'GET';
    }

    /**
     * Get request path
     */
    public function getRequestPath(): string
    {
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        return parse_url($requestUri, PHP_URL_PATH) ?? '/';
    }

    /**
     * Handle webhook endpoints
     */
    private function handleWebhookEndpoint(array $pathSegments): void
    {
        try {
            // Webhook endpoints require at least 3 segments: api/webhook/{service}
            if (count($pathSegments) < 3) {
                $this->handleBadRequest('Webhook service not specified', [
                    'available_services' => ['freemius'],
                    'format' => '/api/webhook/{service}'
                ]);
                return;
            }
            
            $service = $pathSegments[2];
            $action = $pathSegments[3] ?? null;
            
            $webhookController = new WebhookController();
            
            switch ($service) {
                case 'freemius':
                    $this->handleFreemiusWebhook($webhookController, $action);
                    break;
                    
                default:
                    $this->handleNotFound('Unknown webhook service', [
                        'requested_service' => $service,
                        'available_services' => ['freemius']
                    ]);
                    break;
            }
            
        } catch (\Exception $e) {
            error_log("Webhook Endpoint Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            http_response_code(500);
            echo $this->responseFormatter->formatInternalError(uniqid('webhook_err_'));
        }
    }
    
    /**
     * Handle Freemius webhook requests
     */
    private function handleFreemiusWebhook(WebhookController $controller, ?string $action): void
    {
        switch ($action) {
            case null:
            case 'receive':
                // Main webhook endpoint: POST /api/webhook/freemius
                $controller->handleFreemiusWebhook();
                break;
                
            case 'status':
                // Webhook status endpoint: GET /api/webhook/freemius/status
                $controller->getWebhookStatus();
                break;
                
            case 'test':
                // Webhook test endpoint: GET /api/webhook/freemius/test
                $controller->testWebhookConfiguration();
                break;
                
            default:
                $this->handleNotFound('Unknown webhook action', [
                    'requested_action' => $action,
                    'available_actions' => ['receive', 'status', 'test']
                ]);
                break;
        }
    }
    
    /**
     * Check if request is for API
     */
    public function isApiRequest(): bool
    {
        $path = $this->getRequestPath();
        return strpos(trim($path, '/'), 'api/') === 0;
    }
}