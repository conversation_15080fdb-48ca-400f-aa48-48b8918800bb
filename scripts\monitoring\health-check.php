<?php
/**
 * GuardGeo Admin Platform Health Check Script
 * 
 * This script performs comprehensive health checks on the platform
 * and can be used for monitoring and alerting systems.
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Skpassegna\GuardgeoApi\Config\ConfigManager;
use Skpassegna\GuardgeoApi\Database\DatabaseManager;
use Skpassegna\GuardgeoApi\Services\FreemiusService;
use Skpassegna\GuardgeoApi\Services\IpRegistryService;

class HealthChecker
{
    private $config;
    private $results = [];
    private $overallStatus = 'healthy';

    public function __construct()
    {
        $this->config = ConfigManager::getInstance();
    }

    public function runAllChecks(): array
    {
        $this->checkDatabase();
        $this->checkDiskSpace();
        $this->checkMemoryUsage();
        $this->checkLogFiles();
        $this->checkExternalAPIs();
        $this->checkSSLCertificate();
        $this->checkCacheSystem();
        $this->checkFilePermissions();

        return [
            'status' => $this->overallStatus,
            'timestamp' => date('c'),
            'checks' => $this->results,
            'summary' => $this->generateSummary()
        ];
    }

    private function checkDatabase(): void
    {
        try {
            $db = DatabaseManager::getInstance();
            $connection = $db->getConnection();
            
            // Test basic connectivity
            $stmt = $connection->query('SELECT 1 as test');
            $result = $stmt->fetch();
            
            if ($result['test'] !== 1) {
                throw new Exception('Database connectivity test failed');
            }

            // Check database size and performance
            $stmt = $connection->query("
                SELECT 
                    pg_size_pretty(pg_database_size(current_database())) as db_size,
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections
            ");
            $dbStats = $stmt->fetch();

            // Check table health
            $stmt = $connection->query("
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes
                FROM pg_stat_user_tables 
                ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC 
                LIMIT 5
            ");
            $tableStats = $stmt->fetchAll();

            $this->addResult('database', 'healthy', [
                'connection' => 'OK',
                'size' => $dbStats['db_size'],
                'active_connections' => $dbStats['active_connections'],
                'top_tables' => $tableStats
            ]);

        } catch (Exception $e) {
            $this->addResult('database', 'critical', [
                'error' => $e->getMessage()
            ]);
            $this->overallStatus = 'critical';
        }
    }

    private function checkDiskSpace(): void
    {
        $rootPath = __DIR__ . '/../../';
        $freeBytes = disk_free_space($rootPath);
        $totalBytes = disk_total_space($rootPath);
        $usedBytes = $totalBytes - $freeBytes;
        $usagePercent = ($usedBytes / $totalBytes) * 100;

        $status = 'healthy';
        if ($usagePercent > 90) {
            $status = 'critical';
            $this->overallStatus = 'critical';
        } elseif ($usagePercent > 80) {
            $status = 'warning';
            if ($this->overallStatus === 'healthy') {
                $this->overallStatus = 'warning';
            }
        }

        $this->addResult('disk_space', $status, [
            'free_space' => $this->formatBytes($freeBytes),
            'total_space' => $this->formatBytes($totalBytes),
            'used_space' => $this->formatBytes($usedBytes),
            'usage_percent' => round($usagePercent, 2)
        ]);
    }

    private function checkMemoryUsage(): void
    {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        $usagePercent = ($memoryUsage / $memoryLimit) * 100;
        $peakPercent = ($memoryPeak / $memoryLimit) * 100;

        $status = 'healthy';
        if ($usagePercent > 90) {
            $status = 'critical';
            $this->overallStatus = 'critical';
        } elseif ($usagePercent > 80) {
            $status = 'warning';
            if ($this->overallStatus === 'healthy') {
                $this->overallStatus = 'warning';
            }
        }

        $this->addResult('memory', $status, [
            'current_usage' => $this->formatBytes($memoryUsage),
            'peak_usage' => $this->formatBytes($memoryPeak),
            'memory_limit' => $this->formatBytes($memoryLimit),
            'usage_percent' => round($usagePercent, 2),
            'peak_percent' => round($peakPercent, 2)
        ]);
    }

    private function checkLogFiles(): void
    {
        $logDir = __DIR__ . '/../../logs/';
        $logFiles = ['combined.log', 'error.log'];
        $logStatus = [];

        foreach ($logFiles as $logFile) {
            $filePath = $logDir . $logFile;
            
            if (!file_exists($filePath)) {
                $logStatus[$logFile] = ['status' => 'missing', 'size' => 0];
                continue;
            }

            $fileSize = filesize($filePath);
            $isWritable = is_writable($filePath);
            $lastModified = filemtime($filePath);
            $timeSinceModified = time() - $lastModified;

            $status = 'healthy';
            if (!$isWritable) {
                $status = 'critical';
            } elseif ($fileSize > 100 * 1024 * 1024) { // 100MB
                $status = 'warning';
            } elseif ($timeSinceModified > 3600 && $logFile === 'combined.log') { // No activity in 1 hour
                $status = 'warning';
            }

            $logStatus[$logFile] = [
                'status' => $status,
                'size' => $this->formatBytes($fileSize),
                'writable' => $isWritable,
                'last_modified' => date('c', $lastModified),
                'minutes_since_modified' => round($timeSinceModified / 60)
            ];
        }

        $overallLogStatus = 'healthy';
        foreach ($logStatus as $status) {
            if ($status['status'] === 'critical') {
                $overallLogStatus = 'critical';
                $this->overallStatus = 'critical';
                break;
            } elseif ($status['status'] === 'warning') {
                $overallLogStatus = 'warning';
                if ($this->overallStatus === 'healthy') {
                    $this->overallStatus = 'warning';
                }
            }
        }

        $this->addResult('log_files', $overallLogStatus, $logStatus);
    }

    private function checkExternalAPIs(): void
    {
        $apiResults = [];

        // Check Freemius API
        try {
            $freemiusService = new FreemiusService();
            $startTime = microtime(true);
            
            // Simple ping test - adjust based on actual Freemius API capabilities
            $response = $freemiusService->testConnection();
            $responseTime = (microtime(true) - $startTime) * 1000;

            $apiResults['freemius'] = [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'last_check' => date('c')
            ];

        } catch (Exception $e) {
            $apiResults['freemius'] = [
                'status' => 'critical',
                'error' => $e->getMessage(),
                'last_check' => date('c')
            ];
            $this->overallStatus = 'critical';
        }

        // Check ipRegistry API
        try {
            $ipRegistryService = new IpRegistryService();
            $startTime = microtime(true);
            
            // Test with a known IP
            $response = $ipRegistryService->lookupIp('*******');
            $responseTime = (microtime(true) - $startTime) * 1000;

            $apiResults['ipregistry'] = [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'last_check' => date('c')
            ];

        } catch (Exception $e) {
            $apiResults['ipregistry'] = [
                'status' => 'critical',
                'error' => $e->getMessage(),
                'last_check' => date('c')
            ];
            $this->overallStatus = 'critical';
        }

        $this->addResult('external_apis', 
            in_array('critical', array_column($apiResults, 'status')) ? 'critical' : 'healthy',
            $apiResults
        );
    }

    private function checkSSLCertificate(): void
    {
        $url = $this->config->get('app.url');
        
        if (!str_starts_with($url, 'https://')) {
            $this->addResult('ssl_certificate', 'warning', [
                'message' => 'HTTPS not configured'
            ]);
            return;
        }

        $host = parse_url($url, PHP_URL_HOST);
        $port = parse_url($url, PHP_URL_PORT) ?: 443;

        try {
            $context = stream_context_create([
                'ssl' => [
                    'capture_peer_cert' => true,
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]);

            $socket = stream_socket_client(
                "ssl://{$host}:{$port}",
                $errno,
                $errstr,
                30,
                STREAM_CLIENT_CONNECT,
                $context
            );

            if (!$socket) {
                throw new Exception("Failed to connect: {$errstr}");
            }

            $cert = stream_context_get_params($socket)['options']['ssl']['peer_certificate'];
            $certInfo = openssl_x509_parse($cert);
            
            $expiryDate = $certInfo['validTo_time_t'];
            $daysUntilExpiry = ($expiryDate - time()) / (24 * 3600);

            $status = 'healthy';
            if ($daysUntilExpiry < 7) {
                $status = 'critical';
                $this->overallStatus = 'critical';
            } elseif ($daysUntilExpiry < 30) {
                $status = 'warning';
                if ($this->overallStatus === 'healthy') {
                    $this->overallStatus = 'warning';
                }
            }

            $this->addResult('ssl_certificate', $status, [
                'subject' => $certInfo['subject']['CN'] ?? 'Unknown',
                'issuer' => $certInfo['issuer']['CN'] ?? 'Unknown',
                'expires' => date('c', $expiryDate),
                'days_until_expiry' => round($daysUntilExpiry, 1)
            ]);

            fclose($socket);

        } catch (Exception $e) {
            $this->addResult('ssl_certificate', 'critical', [
                'error' => $e->getMessage()
            ]);
            $this->overallStatus = 'critical';
        }
    }

    private function checkCacheSystem(): void
    {
        // Check if cache directory exists and is writable
        $cacheDir = __DIR__ . '/../../storage/cache';
        
        if (!is_dir($cacheDir)) {
            $this->addResult('cache_system', 'warning', [
                'message' => 'Cache directory does not exist',
                'path' => $cacheDir
            ]);
            return;
        }

        if (!is_writable($cacheDir)) {
            $this->addResult('cache_system', 'critical', [
                'message' => 'Cache directory is not writable',
                'path' => $cacheDir
            ]);
            $this->overallStatus = 'critical';
            return;
        }

        // Check cache size
        $cacheSize = $this->getDirectorySize($cacheDir);
        $status = $cacheSize > 500 * 1024 * 1024 ? 'warning' : 'healthy'; // 500MB threshold

        $this->addResult('cache_system', $status, [
            'directory' => $cacheDir,
            'writable' => true,
            'size' => $this->formatBytes($cacheSize)
        ]);
    }

    private function checkFilePermissions(): void
    {
        $criticalPaths = [
            'logs/' => 0755,
            'storage/' => 0755,
            '.env' => 0600
        ];

        $permissionIssues = [];
        $basePath = __DIR__ . '/../../';

        foreach ($criticalPaths as $path => $expectedPerms) {
            $fullPath = $basePath . $path;
            
            if (!file_exists($fullPath)) {
                $permissionIssues[] = [
                    'path' => $path,
                    'issue' => 'File/directory does not exist'
                ];
                continue;
            }

            $actualPerms = fileperms($fullPath) & 0777;
            
            if ($actualPerms !== $expectedPerms) {
                $permissionIssues[] = [
                    'path' => $path,
                    'expected' => sprintf('%o', $expectedPerms),
                    'actual' => sprintf('%o', $actualPerms),
                    'issue' => 'Incorrect permissions'
                ];
            }
        }

        $status = empty($permissionIssues) ? 'healthy' : 'warning';
        if ($status === 'warning' && $this->overallStatus === 'healthy') {
            $this->overallStatus = 'warning';
        }

        $this->addResult('file_permissions', $status, [
            'issues' => $permissionIssues,
            'checked_paths' => array_keys($criticalPaths)
        ]);
    }

    private function addResult(string $check, string $status, array $details): void
    {
        $this->results[$check] = [
            'status' => $status,
            'timestamp' => date('c'),
            'details' => $details
        ];
    }

    private function generateSummary(): array
    {
        $statusCounts = ['healthy' => 0, 'warning' => 0, 'critical' => 0];
        
        foreach ($this->results as $result) {
            $statusCounts[$result['status']]++;
        }

        return [
            'total_checks' => count($this->results),
            'healthy' => $statusCounts['healthy'],
            'warnings' => $statusCounts['warning'],
            'critical' => $statusCounts['critical'],
            'overall_status' => $this->overallStatus
        ];
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;

        switch ($last) {
            case 'g':
                $limit *= 1024;
            case 'm':
                $limit *= 1024;
            case 'k':
                $limit *= 1024;
        }

        return $limit;
    }

    private function getDirectorySize(string $directory): int
    {
        $size = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            $size += $file->getSize();
        }

        return $size;
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    $checker = new HealthChecker();
    $results = $checker->runAllChecks();
    
    echo json_encode($results, JSON_PRETTY_PRINT) . PHP_EOL;
    
    // Exit with appropriate code
    exit($results['status'] === 'critical' ? 1 : 0);
}