<?php

namespace Skpassegna\GuardgeoApi\Services;

/**
 * Admin Logger
 * 
 * Specialized logger for administrative actions and user activities.
 * Provides audit trail functionality with user attribution and detailed context.
 */
class AdminLogger implements LoggerInterface
{
    private LoggingService $loggingService;
    
    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }
    
    /**
     * Log debug message
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_DEBUG, $message, $context);
    }
    
    /**
     * Log info message
     */
    public function info(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_INFO, $message, $context);
    }
    
    /**
     * Log warning message
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_WARNING, $message, $context);
    }
    
    /**
     * Log error message
     */
    public function error(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_ERROR, $message, $context);
    }
    
    /**
     * Log critical message
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_CRITICAL, $message, $context);
    }
    
    /**
     * Log admin login attempt
     */
    public function logLoginAttempt(string $email, bool $success, string $reason = ''): void
    {
        $message = sprintf(
            'Admin login attempt for %s: %s',
            $email,
            $success ? 'SUCCESS' : 'FAILED'
        );
        
        $context = [
            'email' => $email,
            'success' => $success,
            'reason' => $reason,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        $level = $success ? LoggingService::LEVEL_INFO : LoggingService::LEVEL_WARNING;
        $this->log($level, $message, $context);
    }
    
    /**
     * Log admin logout
     */
    public function logLogout(int $userId, string $email): void
    {
        $message = sprintf('Admin logout: %s (ID: %d)', $email, $userId);
        
        $context = [
            'user_id' => $userId,
            'email' => $email,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log user management action
     */
    public function logUserManagement(int $adminUserId, string $action, array $targetUser, array $changes = []): void
    {
        $message = sprintf(
            'User management: %s performed %s on user %s',
            $adminUserId,
            $action,
            $targetUser['email'] ?? $targetUser['id'] ?? 'unknown'
        );
        
        $context = [
            'admin_user_id' => $adminUserId,
            'action' => $action,
            'target_user' => $targetUser,
            'changes' => $changes,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log role change
     */
    public function logRoleChange(int $adminUserId, int $targetUserId, string $oldRole, string $newRole): void
    {
        $message = sprintf(
            'Role change: Admin %d changed user %d role from %s to %s',
            $adminUserId,
            $targetUserId,
            $oldRole,
            $newRole
        );
        
        $context = [
            'admin_user_id' => $adminUserId,
            'target_user_id' => $targetUserId,
            'old_role' => $oldRole,
            'new_role' => $newRole,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log data access
     */
    public function logDataAccess(int $userId, string $dataType, string $action, array $filters = []): void
    {
        $message = sprintf(
            'Data access: User %d performed %s on %s',
            $userId,
            $action,
            $dataType
        );
        
        $context = [
            'user_id' => $userId,
            'data_type' => $dataType,
            'action' => $action,
            'filters' => $filters,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log IP intelligence management
     */
    public function logIpManagement(int $userId, string $action, string $ip, array $details = []): void
    {
        $message = sprintf(
            'IP management: User %d performed %s on IP %s',
            $userId,
            $action,
            $ip
        );
        
        $context = [
            'user_id' => $userId,
            'action' => $action,
            'target_ip' => $ip,
            'details' => $details,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log Freemius data management
     */
    public function logFreemiusManagement(int $userId, string $action, array $details = []): void
    {
        $message = sprintf(
            'Freemius management: User %d performed %s',
            $userId,
            $action
        );
        
        $context = [
            'user_id' => $userId,
            'action' => $action,
            'details' => $details,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log system configuration change
     */
    public function logConfigurationChange(int $userId, string $setting, $oldValue, $newValue): void
    {
        $message = sprintf(
            'Configuration change: User %d changed %s',
            $userId,
            $setting
        );
        
        $context = [
            'user_id' => $userId,
            'setting' => $setting,
            'old_value' => $this->sanitizeValue($oldValue),
            'new_value' => $this->sanitizeValue($newValue),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log dashboard access
     */
    public function logDashboardAccess(int $userId, string $section, array $filters = []): void
    {
        $message = sprintf(
            'Dashboard access: User %d accessed %s section',
            $userId,
            $section
        );
        
        $context = [
            'user_id' => $userId,
            'section' => $section,
            'filters' => $filters,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->debug($message, $context);
    }
    
    /**
     * Log export action
     */
    public function logExport(int $userId, string $dataType, array $filters, int $recordCount): void
    {
        $message = sprintf(
            'Data export: User %d exported %d %s records',
            $userId,
            $recordCount,
            $dataType
        );
        
        $context = [
            'user_id' => $userId,
            'data_type' => $dataType,
            'filters' => $filters,
            'record_count' => $recordCount,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log bulk action
     */
    public function logBulkAction(int $userId, string $action, string $dataType, array $targetIds): void
    {
        $message = sprintf(
            'Bulk action: User %d performed %s on %d %s records',
            $userId,
            $action,
            count($targetIds),
            $dataType
        );
        
        $context = [
            'user_id' => $userId,
            'action' => $action,
            'data_type' => $dataType,
            'target_ids' => $targetIds,
            'affected_count' => count($targetIds),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log security event
     */
    public function logSecurityEvent(int $userId, string $event, string $severity, array $details = []): void
    {
        $message = sprintf(
            'Security event: %s for user %d (severity: %s)',
            $event,
            $userId,
            $severity
        );
        
        $context = [
            'user_id' => $userId,
            'security_event' => $event,
            'severity' => $severity,
            'details' => $details,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        $level = match($severity) {
            'critical' => LoggingService::LEVEL_CRITICAL,
            'high' => LoggingService::LEVEL_ERROR,
            'medium' => LoggingService::LEVEL_WARNING,
            default => LoggingService::LEVEL_INFO
        };
        
        $this->log($level, $message, $context);
    }
    
    /**
     * Log permission check
     */
    public function logPermissionCheck(int $userId, string $permission, bool $granted, string $resource = ''): void
    {
        $message = sprintf(
            'Permission check: %s for user %d on %s: %s',
            $permission,
            $userId,
            $resource ?: 'system',
            $granted ? 'GRANTED' : 'DENIED'
        );
        
        $context = [
            'user_id' => $userId,
            'permission' => $permission,
            'resource' => $resource,
            'granted' => $granted,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $level = $granted ? LoggingService::LEVEL_DEBUG : LoggingService::LEVEL_WARNING;
        $this->log($level, $message, $context);
    }
    
    /**
     * Sanitize sensitive values for logging
     */
    private function sanitizeValue($value): string
    {
        if (is_string($value)) {
            // Hide passwords, tokens, keys
            if (preg_match('/password|token|key|secret/i', $value)) {
                return '[REDACTED]';
            }
            
            // Truncate long strings
            if (strlen($value) > 100) {
                return substr($value, 0, 97) . '...';
            }
        }
        
        return is_scalar($value) ? (string)$value : json_encode($value);
    }
    
    /**
     * Internal log method
     */
    private function log(string $level, string $message, array $context): void
    {
        $this->loggingService->log(LoggingService::TYPE_ADMIN, $level, $message, $context);
    }
}