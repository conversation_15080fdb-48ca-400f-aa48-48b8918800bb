<?php

namespace Skpassegna\GuardgeoApi\Utils;

/**
 * SQL Injection Prevention Utility
 * 
 * Provides comprehensive protection against SQL injection attacks
 * through parameter validation, query analysis, and secure escaping.
 */
class SqlInjectionPrevention
{
    // Common SQL injection patterns
    private const INJECTION_PATTERNS = [
        '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute|sp_|xp_)/i',
        '/(\s|^)(or|and)\s+[\w\'"]+\s*=\s*[\w\'"]+/i',
        '/(\s|^)(or|and)\s+\d+\s*=\s*\d+/i',
        '/(\s|^)(or|and)\s+[\w\'"]+\s+(like|in|between)/i',
        '/(\s|^)(having|group\s+by|order\s+by)\s+/i',
        '/(\s|^)(information_schema|sys\.|mysql\.|pg_)/i',
        '/(\s|^)(load_file|into\s+outfile|into\s+dumpfile)/i',
        '/(\s|^)(benchmark|sleep|waitfor\s+delay)/i',
        '/(\s|^)(concat|char|ascii|substring|mid|left|right)/i',
        '/(\s|^)(cast|convert|hex|unhex|md5|sha1)/i',
        '/(\s|^)(@@|@)/i',
        '/(\s|^)(--|#|\/\*|\*\/)/i',
        '/(\s|^)(;|\||&)/i',
        '/(\s|^)(0x[0-9a-f]+)/i',
        '/(\s|^)(null|true|false)\s*[=!<>]/i'
    ];

    // Dangerous SQL keywords
    private const DANGEROUS_KEYWORDS = [
        'union', 'select', 'insert', 'update', 'delete', 'drop', 'create', 'alter',
        'exec', 'execute', 'sp_', 'xp_', 'information_schema', 'sys.', 'mysql.',
        'pg_', 'load_file', 'outfile', 'dumpfile', 'benchmark', 'sleep', 'waitfor',
        'concat', 'char', 'ascii', 'substring', 'mid', 'left', 'right', 'cast',
        'convert', 'hex', 'unhex', 'md5', 'sha1', 'script', 'javascript', 'vbscript'
    ];

    /**
     * Validate parameter against SQL injection patterns
     */
    public function validateParameter($parameter): bool
    {
        if (is_null($parameter)) {
            return true;
        }

        if (is_numeric($parameter)) {
            return $this->validateNumericParameter($parameter);
        }

        if (is_string($parameter)) {
            return $this->validateStringParameter($parameter);
        }

        if (is_array($parameter)) {
            return $this->validateArrayParameter($parameter);
        }

        if (is_bool($parameter)) {
            return true;
        }

        // Unknown type, reject
        return false;
    }

    /**
     * Validate numeric parameter
     */
    private function validateNumericParameter($parameter): bool
    {
        $stringValue = (string)$parameter;
        
        // Check for SQL injection patterns in numeric values
        foreach (self::INJECTION_PATTERNS as $pattern) {
            if (preg_match($pattern, $stringValue)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Validate string parameter
     */
    private function validateStringParameter(string $parameter): bool
    {
        // Check length (prevent extremely long inputs)
        if (strlen($parameter) > 10000) {
            return false;
        }

        // Check for null bytes
        if (strpos($parameter, "\0") !== false) {
            return false;
        }

        // Check for SQL injection patterns
        foreach (self::INJECTION_PATTERNS as $pattern) {
            if (preg_match($pattern, $parameter)) {
                return false;
            }
        }

        // Check for dangerous keywords in suspicious contexts
        $lowerParam = strtolower($parameter);
        foreach (self::DANGEROUS_KEYWORDS as $keyword) {
            if (strpos($lowerParam, $keyword) !== false) {
                // Additional context checking
                if ($this->isDangerousContext($lowerParam, $keyword)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Validate array parameter recursively
     */
    private function validateArrayParameter(array $parameter): bool
    {
        foreach ($parameter as $key => $value) {
            if (!$this->validateParameter($key) || !$this->validateParameter($value)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if keyword appears in dangerous context
     */
    private function isDangerousContext(string $input, string $keyword): bool
    {
        $keywordPos = strpos($input, $keyword);
        if ($keywordPos === false) {
            return false;
        }

        // Get context around the keyword
        $contextStart = max(0, $keywordPos - 20);
        $contextEnd = min(strlen($input), $keywordPos + strlen($keyword) + 20);
        $context = substr($input, $contextStart, $contextEnd - $contextStart);

        // Check for SQL-like patterns around the keyword
        $dangerousPatterns = [
            '/\b' . preg_quote($keyword, '/') . '\s+(from|where|into|values|set)\b/i',
            '/\b(from|where|into|values|set)\s+' . preg_quote($keyword, '/') . '\b/i',
            '/\b' . preg_quote($keyword, '/') . '\s*\(/i',
            '/\b' . preg_quote($keyword, '/') . '\s*[=<>!]/i',
            '/[=<>!]\s*' . preg_quote($keyword, '/') . '\b/i'
        ];

        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $context)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Sanitize parameter for safe database usage
     */
    public function sanitizeParameter($parameter)
    {
        if (is_null($parameter)) {
            return null;
        }

        if (is_bool($parameter)) {
            return $parameter;
        }

        if (is_numeric($parameter)) {
            return $parameter;
        }

        if (is_string($parameter)) {
            return $this->sanitizeString($parameter);
        }

        if (is_array($parameter)) {
            return $this->sanitizeArray($parameter);
        }

        // Unknown type, return null
        return null;
    }

    /**
     * Sanitize string parameter
     */
    private function sanitizeString(string $parameter): string
    {
        // Remove null bytes
        $parameter = str_replace("\0", '', $parameter);
        
        // Remove or escape dangerous characters
        $parameter = str_replace(['\\', '"', "'", ';', '--', '/*', '*/', '|', '&'], '', $parameter);
        
        // Limit length
        if (strlen($parameter) > 10000) {
            $parameter = substr($parameter, 0, 10000);
        }

        return trim($parameter);
    }

    /**
     * Sanitize array parameter recursively
     */
    private function sanitizeArray(array $parameter): array
    {
        $sanitized = [];
        foreach ($parameter as $key => $value) {
            $sanitizedKey = $this->sanitizeParameter($key);
            $sanitizedValue = $this->sanitizeParameter($value);
            
            if ($sanitizedKey !== null) {
                $sanitized[$sanitizedKey] = $sanitizedValue;
            }
        }

        return $sanitized;
    }

    /**
     * Validate SQL query structure (for dynamic queries)
     */
    public function validateQuery(string $query): bool
    {
        // Remove comments and normalize whitespace
        $normalizedQuery = $this->normalizeQuery($query);
        
        // Check for multiple statements (semicolon outside of quotes)
        if ($this->hasMultipleStatements($normalizedQuery)) {
            return false;
        }

        // Check for dangerous patterns
        foreach (self::INJECTION_PATTERNS as $pattern) {
            if (preg_match($pattern, $normalizedQuery)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Normalize query for analysis
     */
    private function normalizeQuery(string $query): string
    {
        // Remove SQL comments
        $query = preg_replace('/--.*$/m', '', $query);
        $query = preg_replace('/\/\*.*?\*\//s', '', $query);
        
        // Normalize whitespace
        $query = preg_replace('/\s+/', ' ', $query);
        
        return trim($query);
    }

    /**
     * Check if query contains multiple statements
     */
    private function hasMultipleStatements(string $query): bool
    {
        $inQuotes = false;
        $quoteChar = null;
        $escaped = false;
        
        for ($i = 0; $i < strlen($query); $i++) {
            $char = $query[$i];
            
            if ($escaped) {
                $escaped = false;
                continue;
            }
            
            if ($char === '\\') {
                $escaped = true;
                continue;
            }
            
            if (!$inQuotes && ($char === '"' || $char === "'")) {
                $inQuotes = true;
                $quoteChar = $char;
                continue;
            }
            
            if ($inQuotes && $char === $quoteChar) {
                $inQuotes = false;
                $quoteChar = null;
                continue;
            }
            
            if (!$inQuotes && $char === ';') {
                // Check if there's more content after the semicolon
                $remaining = trim(substr($query, $i + 1));
                if (!empty($remaining)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Create safe parameter placeholder for prepared statements
     */
    public function createParameterPlaceholder(string $name): string
    {
        // Ensure parameter name is safe
        $safeName = preg_replace('/[^a-zA-Z0-9_]/', '', $name);
        
        if (empty($safeName)) {
            $safeName = 'param_' . uniqid();
        }
        
        return ':' . $safeName;
    }

    /**
     * Validate table/column name for dynamic queries
     */
    public function validateIdentifier(string $identifier): bool
    {
        // Identifiers should only contain alphanumeric characters, underscores, and dots
        if (!preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$/', $identifier)) {
            return false;
        }
        
        // Check length
        if (strlen($identifier) > 64) {
            return false;
        }
        
        // Check against dangerous keywords
        $lowerIdentifier = strtolower($identifier);
        foreach (self::DANGEROUS_KEYWORDS as $keyword) {
            if ($lowerIdentifier === $keyword) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Escape identifier for safe usage in dynamic queries
     */
    public function escapeIdentifier(string $identifier): string
    {
        if (!$this->validateIdentifier($identifier)) {
            throw new \InvalidArgumentException('Invalid identifier: ' . $identifier);
        }
        
        // Use double quotes for PostgreSQL identifier escaping
        return '"' . str_replace('"', '""', $identifier) . '"';
    }

    /**
     * Log potential SQL injection attempt
     */
    public function logInjectionAttempt(string $parameter, string $context = ''): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => 'sql_injection_attempt',
            'parameter' => substr($parameter, 0, 500), // Limit log size
            'context' => $context,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ];

        error_log('SQL_INJECTION_ATTEMPT: ' . json_encode($logData));
    }

    /**
     * Comprehensive parameter validation and sanitization
     */
    public function processParameter($parameter, string $context = '')
    {
        if (!$this->validateParameter($parameter)) {
            $this->logInjectionAttempt(
                is_string($parameter) ? $parameter : json_encode($parameter),
                $context
            );
            throw new \InvalidArgumentException('Parameter failed SQL injection validation');
        }

        return $this->sanitizeParameter($parameter);
    }
}