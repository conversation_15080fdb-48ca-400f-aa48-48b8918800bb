# GuardGeo Database Infrastructure

This document describes the complete database infrastructure for the GuardGeo Admin Platform, including repositories, migrations, and configuration management.

## Overview

The database infrastructure follows a layered architecture with:
- **Repository Pattern**: Data access layer with CRUD operations
- **Migration System**: Version-controlled database schema changes
- **Configuration Management**: Environment-specific settings
- **Health Monitoring**: Database performance and integrity checks

## Database Schema

### Core Tables

#### admin_users
Administrative users with role-based access control.
- **Roles**: super_admin, dev, marketing, sales
- **Features**: Password hashing, session tracking, email validation

#### freemius_products
Cached Freemius product data from API.
- **Caching**: 24-hour refresh cycle
- **Data**: Complete product information, pricing, features

#### freemius_installations
WordPress installation data for subscription validation.
- **Caching**: 6-hour refresh cycle
- **Validation**: Active, premium, not uninstalled checks

#### ip_intelligence
IP analysis data with intelligent caching.
- **Deprecation Rules**:
  - Security data: 3 days
  - Location data: 10 days
  - Connection data: 7 days
  - Company data: 30 days

#### system_logs
Comprehensive system activity logging.
- **Types**: api, admin, error, system
- **Levels**: debug, info, warning, error, critical

#### api_requests
API request tracking and performance monitoring.
- **Metrics**: Response time, status codes, Freemius validation

#### system_config
Runtime configuration storage.
- **Types**: string, integer, float, boolean, json, array
- **Features**: Sensitive data masking, audit trail

## Repository Classes

### BaseRepository
Abstract base class providing common CRUD operations:
- `findById()`, `findBy()`, `findOneBy()`, `findAll()`
- `insert()`, `update()`, `delete()`
- `countBy()`, transaction support
- Automatic timestamp management

### Specific Repositories

#### AdminUserRepository
- User authentication and management
- Role-based access control
- Password updates and session tracking
- User statistics and pagination

#### ProductRepository
- Freemius product data management
- Cache expiration handling
- Search and filtering capabilities
- Statistics and analytics

#### InstallationRepository
- WordPress installation tracking
- Subscription validation
- Active/inactive status management
- Performance metrics

#### IpIntelligenceRepository
- IP data caching with deprecation rules
- Batch processing capabilities
- Security threat detection
- Geographic and network analysis

#### SystemLogRepository
- Structured logging with context
- Log level filtering and search
- Automatic cleanup and archival
- Performance monitoring

#### ApiRequestRepository
- Request tracking and analytics
- Performance metrics calculation
- Rate limiting support
- Error analysis

#### SystemConfigRepository
- Runtime configuration management
- Type-safe value parsing
- Sensitive data protection
- Configuration backup/restore

## Migration System

### MigrationManager
Handles database schema evolution:
- **Version Control**: Timestamp-based migration files
- **Rollback Support**: Automatic rollback SQL generation
- **Integrity Checks**: Validation before execution
- **Logging**: Complete audit trail

### Migration Files
Located in `src/Database/migrations/`:
- `2025_01_30_120000_add_system_config_table.sql`
- `2025_01_30_130000_add_missing_indexes.sql`
- `2025_01_30_140000_add_database_functions.sql`

### Database Functions
Custom PostgreSQL functions for maintenance:
- `cleanup_expired_ip_data()`: Remove stale IP records
- `update_ip_expiration_dates()`: Apply deprecation rules
- `get_system_health_stats()`: System health metrics
- `archive_old_logs()`: Log cleanup and archival
- `get_api_performance_metrics()`: API performance analysis
- `validate_database_integrity()`: Data consistency checks
- `run_maintenance()`: Automated maintenance tasks

## Configuration Management

### Environment Class
Basic environment variable loading:
- `.env` file parsing
- Environment detection
- Configuration sections for database, APIs, logging

### ConfigManager
Centralized configuration management:
- **Multi-source Loading**: Environment, database, file overrides
- **Caching**: In-memory configuration cache
- **Validation**: Required field checking
- **Security**: Sensitive data masking

### EnvironmentLoader
Advanced environment-specific configuration:
- **Environment Detection**: development, staging, production
- **Configuration Merging**: Base + environment + local overrides
- **Validation**: Environment-specific requirements
- **Security Checks**: Production safety validations

### ConfigValidator
Comprehensive configuration validation:
- **Field Validation**: Type checking, format validation
- **Security Validation**: Weak passwords, default values
- **Environment Compliance**: Production safety checks
- **Detailed Reporting**: Errors and warnings with context

## Database Connection Management

### DatabaseConnection
Singleton connection manager:
- **Connection Pooling**: Persistent connections
- **Health Monitoring**: Connection status and performance
- **Error Handling**: Automatic reconnection
- **Performance Metrics**: Query timing and statistics

### Features
- **Health Checks**: Database connectivity and performance
- **Maintenance Operations**: Automated cleanup and optimization
- **Performance Monitoring**: Table sizes, index usage
- **Integrity Validation**: Data consistency checks

## Usage Examples

### Basic Repository Usage
```php
use Skpassegna\GuardgeoApi\Database\AdminUserRepository;

$userRepo = new AdminUserRepository();

// Find user by email
$user = $userRepo->findByEmail('<EMAIL>');

// Create new user
$newUser = new AdminUserModel([
    'email' => '<EMAIL>',
    'password_hash' => password_hash('password', PASSWORD_DEFAULT),
    'role' => 'dev'
]);
$createdUser = $userRepo->create($newUser);

// Update user
$user->setRole('marketing');
$userRepo->updateUser($user);
```

### Migration Management
```php
use Skpassegna\GuardgeoApi\Database\MigrationManager;

$migrationManager = new MigrationManager();

// Check migration status
$status = $migrationManager->getStatus();

// Run pending migrations
$result = $migrationManager->migrate();

// Create new migration
$filePath = $migrationManager->createMigration('add_new_feature');
```

### Configuration Management
```php
use Skpassegna\GuardgeoApi\Config\ConfigManager;

$config = ConfigManager::getInstance();
$config->load();

// Get configuration values
$dbHost = $config->get('database.host');
$apiTimeout = $config->get('api.timeout', 30);

// Set runtime configuration
$config->set('cache.enabled', true);

// Save to database
$config->saveToDatabase('feature.enabled', true, 'boolean');
```

### Health Monitoring
```php
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;

// Database health check
$health = DatabaseConnection::healthCheck();
if ($health['status'] === 'healthy') {
    echo "Database is healthy, response time: {$health['response_time_ms']}ms";
}

// Run maintenance
$maintenance = DatabaseConnection::runMaintenance();
echo "Cleaned {$maintenance['operations']['cleaned_ip_records']} IP records";

// Validate integrity
$integrity = DatabaseConnection::validateIntegrity();
if ($integrity['issues_found'] > 0) {
    echo "Found {$integrity['issues_found']} integrity issues";
}
```

## Performance Considerations

### Indexing Strategy
- **Primary Keys**: All tables have optimized primary keys
- **Foreign Keys**: Proper relationships with cascading
- **Composite Indexes**: Multi-column indexes for common queries
- **Partial Indexes**: Conditional indexes for filtered queries

### Caching Strategy
- **IP Intelligence**: Multi-tier caching with deprecation rules
- **Freemius Data**: Time-based cache refresh
- **Configuration**: In-memory caching with database fallback
- **Query Results**: Repository-level result caching

### Maintenance Tasks
- **Automated Cleanup**: Expired data removal
- **Log Archival**: Automatic log rotation
- **Index Maintenance**: Regular ANALYZE operations
- **Health Monitoring**: Continuous performance tracking

## Security Features

### Data Protection
- **Password Hashing**: Secure password storage
- **Sensitive Data Masking**: Configuration value protection
- **SQL Injection Prevention**: Prepared statements
- **Input Validation**: Comprehensive data validation

### Access Control
- **Role-Based Access**: User role management
- **Session Security**: Secure session handling
- **Audit Logging**: Complete activity tracking
- **Configuration Security**: Environment-specific validation

## Testing and Validation

### Infrastructure Testing
Run the infrastructure test script:
```bash
php test_infrastructure.php
```

This validates:
- Environment configuration loading
- Database connectivity and health
- Migration system functionality
- Repository pattern implementation
- Configuration management
- Database functions and procedures

### Configuration Validation
```php
use Skpassegna\GuardgeoApi\Config\ConfigValidator;

$validator = new ConfigValidator();
$result = $validator->validate($config, 'production');

if (!$result['valid']) {
    foreach ($result['errors'] as $error) {
        echo "Error in {$error['section']}: {$error['message']}\n";
    }
}
```

## Deployment Checklist

### Development Setup
1. Copy `.env.example` to `.env`
2. Configure database connection
3. Run migrations: `php -r "require 'vendor/autoload.php'; (new Skpassegna\GuardgeoApi\Database\MigrationManager())->migrate();"`
4. Create super admin user
5. Test infrastructure: `php test_infrastructure.php`

### Production Deployment
1. Validate configuration for production environment
2. Ensure all required environment variables are set
3. Run database migrations
4. Verify database health and performance
5. Set up automated maintenance tasks
6. Configure monitoring and alerting

## Troubleshooting

### Common Issues

#### Database Connection Failures
- Check database credentials and connectivity
- Verify PostgreSQL service is running
- Check firewall and network configuration

#### Migration Failures
- Review migration logs for specific errors
- Check database permissions
- Verify migration file syntax

#### Configuration Issues
- Run configuration validation
- Check environment variable values
- Verify file permissions for configuration files

#### Performance Issues
- Monitor database query performance
- Check index usage statistics
- Review cache hit rates
- Analyze slow query logs

### Monitoring and Alerts
- Database health checks every 5 minutes
- Performance metric collection
- Automated error reporting
- Capacity planning alerts

This infrastructure provides a robust foundation for the GuardGeo Admin Platform with comprehensive data management, configuration handling, and monitoring capabilities.