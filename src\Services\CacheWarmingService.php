<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Database\IpIntelligenceRepository;
use Skpassegna\GuardgeoApi\Database\ApiRequestRepository;
use Skpassegna\GuardgeoApi\Utils\Logger;
use Skpassegna\GuardgeoApi\Config\Environment;
use DateTime;

/**
 * Cache Warming Service
 * 
 * Implements intelligent cache warming and preloading strategies
 * to improve API response times and reduce external API calls.
 */
class CacheWarmingService
{
    private IpIntelligenceRepository $ipRepository;
    private ApiRequestRepository $apiRequestRepository;
    private IpRegistryService $ipRegistryService;
    private Logger $logger;
    
    // Configuration
    private int $warmupBatchSize;
    private int $popularIpThreshold;
    private int $recentDays;
    private int $maxWarmupIps;
    
    /**
     * Constructor
     */
    public function __construct(
        ?IpIntelligenceRepository $ipRepository = null,
        ?ApiRequestRepository $apiRequestRepository = null,
        ?IpRegistryService $ipRegistryService = null
    ) {
        $this->ipRepository = $ipRepository ?? new IpIntelligenceRepository();
        $this->apiRequestRepository = $apiRequestRepository ?? new ApiRequestRepository();
        $this->ipRegistryService = $ipRegistryService ?? new IpRegistryService();
        $this->logger = new Logger();
        
        // Load configuration
        $this->warmupBatchSize = (int) Environment::get('CACHE_WARMUP_BATCH_SIZE', 50);
        $this->popularIpThreshold = (int) Environment::get('CACHE_POPULAR_IP_THRESHOLD', 5);
        $this->recentDays = (int) Environment::get('CACHE_WARMUP_RECENT_DAYS', 7);
        $this->maxWarmupIps = (int) Environment::get('CACHE_MAX_WARMUP_IPS', 1000);
    }
    
    /**
     * Perform intelligent cache warming based on usage patterns
     */
    public function performIntelligentWarmup(): array
    {
        $this->logger->info("Starting intelligent cache warmup");
        
        $results = [
            'strategies_executed' => [],
            'total_ips_processed' => 0,
            'total_ips_warmed' => 0,
            'total_ips_skipped' => 0,
            'total_failures' => 0,
            'duration_seconds' => 0,
            'errors' => []
        ];
        
        $startTime = microtime(true);
        
        try {
            // Strategy 1: Warm popular IPs from recent requests
            $popularResults = $this->warmPopularIps();
            $results['strategies_executed'][] = 'popular_ips';
            $this->mergeResults($results, $popularResults);
            
            // Strategy 2: Warm IPs that are about to expire
            $expiringResults = $this->warmExpiringIps();
            $results['strategies_executed'][] = 'expiring_ips';
            $this->mergeResults($results, $expiringResults);
            
            // Strategy 3: Warm IPs from geographic regions with high activity
            $geoResults = $this->warmGeographicRegions();
            $results['strategies_executed'][] = 'geographic_regions';
            $this->mergeResults($results, $geoResults);
            
            // Strategy 4: Warm IPs from security threat patterns
            $securityResults = $this->warmSecurityPatterns();
            $results['strategies_executed'][] = 'security_patterns';
            $this->mergeResults($results, $securityResults);
            
        } catch (\Exception $e) {
            $error = "Intelligent warmup failed: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        $results['duration_seconds'] = round(microtime(true) - $startTime, 2);
        
        $this->logger->info("Intelligent cache warmup completed", $results);
        
        return $results;
    }
    
    /**
     * Warm cache for popular IPs based on recent request patterns
     */
    public function warmPopularIps(): array
    {
        $this->logger->info("Warming popular IPs from recent requests");
        
        $results = [
            'processed' => 0,
            'warmed' => 0,
            'skipped' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        try {
            // Get popular IPs from recent API requests
            $popularIps = $this->getPopularIpsFromRequests();
            
            if (empty($popularIps)) {
                $this->logger->info("No popular IPs found for warming");
                return $results;
            }
            
            $this->logger->info("Found popular IPs for warming", [
                'count' => count($popularIps),
                'ips' => array_slice($popularIps, 0, 10) // Log first 10 for debugging
            ]);
            
            // Process in batches
            $batches = array_chunk($popularIps, $this->warmupBatchSize);
            
            foreach ($batches as $batchIndex => $batch) {
                $batchResults = $this->processBatchWarmup($batch, 'popular');
                $this->mergeResults($results, $batchResults);
                
                // Add delay between batches to avoid overwhelming the API
                if ($batchIndex < count($batches) - 1) {
                    usleep(200000); // 200ms delay
                }
            }
            
        } catch (\Exception $e) {
            $error = "Failed to warm popular IPs: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        return $results;
    }
    
    /**
     * Warm cache for IPs that are about to expire
     */
    public function warmExpiringIps(): array
    {
        $this->logger->info("Warming IPs that are about to expire");
        
        $results = [
            'processed' => 0,
            'warmed' => 0,
            'skipped' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        try {
            // Get IPs that will expire within the next 24 hours
            $expiringIps = $this->getExpiringIps(24);
            
            if (empty($expiringIps)) {
                $this->logger->info("No expiring IPs found for warming");
                return $results;
            }
            
            $this->logger->info("Found expiring IPs for warming", [
                'count' => count($expiringIps)
            ]);
            
            // Process in batches
            $batches = array_chunk($expiringIps, $this->warmupBatchSize);
            
            foreach ($batches as $batch) {
                $batchResults = $this->processBatchWarmup($batch, 'expiring');
                $this->mergeResults($results, $batchResults);
            }
            
        } catch (\Exception $e) {
            $error = "Failed to warm expiring IPs: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        return $results;
    }
    
    /**
     * Warm cache for IPs from high-activity geographic regions
     */
    public function warmGeographicRegions(): array
    {
        $this->logger->info("Warming IPs from high-activity geographic regions");
        
        $results = [
            'processed' => 0,
            'warmed' => 0,
            'skipped' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        try {
            // Get IPs from high-activity regions
            $regionIps = $this->getHighActivityRegionIps();
            
            if (empty($regionIps)) {
                $this->logger->info("No region IPs found for warming");
                return $results;
            }
            
            $this->logger->info("Found region IPs for warming", [
                'count' => count($regionIps)
            ]);
            
            // Process in batches
            $batches = array_chunk($regionIps, $this->warmupBatchSize);
            
            foreach ($batches as $batch) {
                $batchResults = $this->processBatchWarmup($batch, 'geographic');
                $this->mergeResults($results, $batchResults);
            }
            
        } catch (\Exception $e) {
            $error = "Failed to warm geographic region IPs: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        return $results;
    }
    
    /**
     * Warm cache for IPs with security threat patterns
     */
    public function warmSecurityPatterns(): array
    {
        $this->logger->info("Warming IPs with security threat patterns");
        
        $results = [
            'processed' => 0,
            'warmed' => 0,
            'skipped' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        try {
            // Get IPs with security threats that need fresh data
            $securityIps = $this->getSecurityThreatIps();
            
            if (empty($securityIps)) {
                $this->logger->info("No security threat IPs found for warming");
                return $results;
            }
            
            $this->logger->info("Found security threat IPs for warming", [
                'count' => count($securityIps)
            ]);
            
            // Process in batches
            $batches = array_chunk($securityIps, $this->warmupBatchSize);
            
            foreach ($batches as $batch) {
                $batchResults = $this->processBatchWarmup($batch, 'security');
                $this->mergeResults($results, $batchResults);
            }
            
        } catch (\Exception $e) {
            $error = "Failed to warm security pattern IPs: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        return $results;
    }
    
    /**
     * Process a batch of IPs for warming
     */
    private function processBatchWarmup(array $ips, string $strategy): array
    {
        $results = [
            'processed' => 0,
            'warmed' => 0,
            'skipped' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        foreach ($ips as $ip) {
            $results['processed']++;
            
            try {
                // Check if IP already has fresh data
                if ($this->ipRegistryService->hasCachedData($ip) && 
                    $this->ipRegistryService->isCachedDataFresh($ip)) {
                    $results['skipped']++;
                    continue;
                }
                
                // Warm the cache
                $this->ipRegistryService->getIpIntelligence($ip);
                $results['warmed']++;
                
                $this->logger->debug("Warmed cache for IP", [
                    'ip' => $ip,
                    'strategy' => $strategy
                ]);
                
            } catch (\Exception $e) {
                $results['failed']++;
                $error = "Failed to warm IP {$ip}: " . $e->getMessage();
                $results['errors'][] = $error;
                
                $this->logger->warning($error, [
                    'ip' => $ip,
                    'strategy' => $strategy
                ]);
            }
        }
        
        return $results;
    }
    
    /**
     * Get popular IPs from recent API requests
     */
    private function getPopularIpsFromRequests(): array
    {
        try {
            $cutoffDate = (new DateTime())->modify("-{$this->recentDays} days");
            
            // This would require implementing the method in ApiRequestRepository
            // For now, we'll use a simplified approach
            $popularIps = $this->apiRequestRepository->getPopularIps(
                $cutoffDate,
                $this->popularIpThreshold,
                $this->maxWarmupIps
            );
            
            return $popularIps;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get popular IPs from requests", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get IPs that are about to expire within specified hours
     */
    private function getExpiringIps(int $hoursUntilExpiry): array
    {
        try {
            $expiryThreshold = (new DateTime())->modify("+{$hoursUntilExpiry} hours");
            
            return $this->ipRepository->findExpiringIps($expiryThreshold, $this->maxWarmupIps);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get expiring IPs", [
                'hours' => $hoursUntilExpiry,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get IPs from high-activity geographic regions
     */
    private function getHighActivityRegionIps(): array
    {
        try {
            // Get regions with high request activity
            $activeRegions = $this->getActiveGeographicRegions();
            
            if (empty($activeRegions)) {
                return [];
            }
            
            // Get IPs from these regions that need refreshing
            return $this->ipRepository->findIpsByRegions($activeRegions, $this->maxWarmupIps);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get high-activity region IPs", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get IPs with security threats that need fresh data
     */
    private function getSecurityThreatIps(): array
    {
        try {
            // Get IPs with security threats where security data is stale
            return $this->ipRepository->findSecurityThreatIps($this->maxWarmupIps);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get security threat IPs", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get active geographic regions based on recent requests
     */
    private function getActiveGeographicRegions(): array
    {
        try {
            $cutoffDate = (new DateTime())->modify("-{$this->recentDays} days");
            
            return $this->apiRequestRepository->getActiveRegions($cutoffDate, 10);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get active geographic regions", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Merge results from different warming strategies
     */
    private function mergeResults(array &$mainResults, array $strategyResults): void
    {
        $mainResults['total_ips_processed'] += $strategyResults['processed'] ?? 0;
        $mainResults['total_ips_warmed'] += $strategyResults['warmed'] ?? 0;
        $mainResults['total_ips_skipped'] += $strategyResults['skipped'] ?? 0;
        $mainResults['total_failures'] += $strategyResults['failed'] ?? 0;
        
        if (!empty($strategyResults['errors'])) {
            $mainResults['errors'] = array_merge($mainResults['errors'], $strategyResults['errors']);
        }
    }
    
    /**
     * Perform preloading for specific IP lists
     */
    public function preloadIpList(array $ips, string $source = 'manual'): array
    {
        $this->logger->info("Preloading IP list", [
            'count' => count($ips),
            'source' => $source
        ]);
        
        $results = [
            'processed' => 0,
            'preloaded' => 0,
            'already_cached' => 0,
            'failed' => 0,
            'invalid_ips' => 0,
            'errors' => []
        ];
        
        foreach ($ips as $ip) {
            $results['processed']++;
            
            // Validate IP
            if (!$this->ipRegistryService->getApiClient()->isValidIp($ip)) {
                $results['invalid_ips']++;
                continue;
            }
            
            try {
                // Check if already cached and fresh
                if ($this->ipRegistryService->hasCachedData($ip) && 
                    $this->ipRegistryService->isCachedDataFresh($ip)) {
                    $results['already_cached']++;
                    continue;
                }
                
                // Preload the data
                $this->ipRegistryService->getIpIntelligence($ip);
                $results['preloaded']++;
                
            } catch (\Exception $e) {
                $results['failed']++;
                $error = "Failed to preload IP {$ip}: " . $e->getMessage();
                $results['errors'][] = $error;
                
                $this->logger->warning($error, [
                    'ip' => $ip,
                    'source' => $source
                ]);
            }
        }
        
        $this->logger->info("IP list preloading completed", array_merge($results, [
            'source' => $source
        ]));
        
        return $results;
    }
    
    /**
     * Get cache warming recommendations based on current state
     */
    public function getWarmingRecommendations(): array
    {
        $recommendations = [];
        
        try {
            // Check expired data ratio
            $stats = $this->ipRepository->getCacheStatistics();
            $expiredRatio = $stats['total_cached_ips'] > 0 
                ? ($stats['expired_ips'] / $stats['total_cached_ips']) * 100 
                : 0;
            
            if ($expiredRatio > 20) {
                $recommendations[] = [
                    'type' => 'expiring_warmup',
                    'priority' => 'high',
                    'message' => "High percentage of expired data ({$expiredRatio}%). Run expiring IP warmup.",
                    'action' => 'warm_expiring_ips',
                    'estimated_ips' => $stats['expired_ips']
                ];
            }
            
            // Check popular IP patterns
            $popularIpCount = count($this->getPopularIpsFromRequests());
            if ($popularIpCount > 0) {
                $recommendations[] = [
                    'type' => 'popular_warmup',
                    'priority' => 'medium',
                    'message' => "Found {$popularIpCount} popular IPs that could benefit from warming.",
                    'action' => 'warm_popular_ips',
                    'estimated_ips' => $popularIpCount
                ];
            }
            
            // Check security threat patterns
            $securityIpCount = count($this->getSecurityThreatIps());
            if ($securityIpCount > 0) {
                $recommendations[] = [
                    'type' => 'security_warmup',
                    'priority' => 'high',
                    'message' => "Found {$securityIpCount} security threat IPs with stale data.",
                    'action' => 'warm_security_patterns',
                    'estimated_ips' => $securityIpCount
                ];
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to generate warming recommendations", [
                'error' => $e->getMessage()
            ]);
        }
        
        return $recommendations;
    }
    
    /**
     * Schedule automatic cache warming
     */
    public function scheduleAutomaticWarmup(): array
    {
        $this->logger->info("Scheduling automatic cache warmup");
        
        $results = [
            'scheduled_tasks' => [],
            'recommendations' => [],
            'next_warmup' => null
        ];
        
        try {
            // Get recommendations
            $recommendations = $this->getWarmingRecommendations();
            $results['recommendations'] = $recommendations;
            
            // Schedule high-priority tasks
            foreach ($recommendations as $recommendation) {
                if ($recommendation['priority'] === 'high') {
                    $results['scheduled_tasks'][] = [
                        'task' => $recommendation['action'],
                        'estimated_ips' => $recommendation['estimated_ips'],
                        'scheduled_for' => 'immediate'
                    ];
                }
            }
            
            // Calculate next warmup time
            $nextWarmup = new DateTime();
            $warmupInterval = (int) Environment::get('CACHE_WARMUP_INTERVAL_HOURS', 6);
            $nextWarmup->modify("+{$warmupInterval} hours");
            $results['next_warmup'] = $nextWarmup->format('c');
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to schedule automatic warmup", [
                'error' => $e->getMessage()
            ]);
        }
        
        return $results;
    }
    
    /**
     * Get warming statistics
     */
    public function getWarmingStatistics(): array
    {
        return [
            'configuration' => [
                'batch_size' => $this->warmupBatchSize,
                'popular_threshold' => $this->popularIpThreshold,
                'recent_days' => $this->recentDays,
                'max_warmup_ips' => $this->maxWarmupIps
            ],
            'potential_targets' => [
                'popular_ips' => count($this->getPopularIpsFromRequests()),
                'expiring_ips' => count($this->getExpiringIps(24)),
                'security_ips' => count($this->getSecurityThreatIps()),
                'region_ips' => count($this->getHighActivityRegionIps())
            ],
            'recommendations' => $this->getWarmingRecommendations()
        ];
    }
}