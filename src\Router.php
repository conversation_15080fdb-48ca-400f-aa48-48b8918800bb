<?php
namespace Skpassegna\GuardgeoApi;

use Skpassegna\GuardgeoApi\Controllers\AdminRouter;
use Skpassegna\GuardgeoApi\Controllers\ApiRouter;
use Skpassegna\GuardgeoApi\Controllers\AdminApiController;
use Skpassegna\GuardgeoApi\Controllers\WebhookController;

class Router {
    public function dispatch(string $uri, string $method): void {
        $path = parse_url($uri, PHP_URL_PATH) ?: '/';

        if (str_starts_with($path, '/admin')) {
            (new AdminRouter())->handleRequest($path, $method);
            return;
        }
        if (str_starts_with($path, '/api/v1')) {
            (new ApiRouter())->route($path, $method);
            return;
        }
        if (str_starts_with($path, '/webhooks')) {
            (new WebhookController())->handleRequest($path, $method);
            return;
        }
        http_response_code(404);
        echo 'Not Found';
    }
}