<?php

/**
 * Production Environment Configuration
 * 
 * This file contains production-specific configuration settings
 * for the GuardGeo Admin Platform.
 */

return [
    // Application Settings
    'app' => [
        'name' => 'GuardGeo Admin Platform',
        'env' => 'production',
        'debug' => false,
        'url' => 'https://server-domain.tld',
        'timezone' => 'UTC',
    ],

    // Database Configuration
    'database' => [
        'host' => $_ENV['DB_HOST'] ?? 'localhost',
        'port' => $_ENV['DB_PORT'] ?? 5432,
        'database' => $_ENV['DB_NAME'] ?? 'guardgeo_production',
        'username' => $_ENV['DB_USERNAME'] ?? 'guardgeo_user',
        'password' => $_ENV['DB_PASSWORD'] ?? '',
        'charset' => 'utf8',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::ATTR_PERSISTENT => true,
            PDO::MYSQL_ATTR_SSL_CA => $_ENV['DB_SSL_CA'] ?? null,
            PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => true,
        ],
        'pool' => [
            'min_connections' => 5,
            'max_connections' => 20,
            'connection_timeout' => 30,
            'idle_timeout' => 300,
        ],
    ],

    // Logging Configuration
    'logging' => [
        'default' => 'combined',
        'channels' => [
            'combined' => [
                'driver' => 'file',
                'path' => __DIR__ . '/../logs/combined.log',
                'level' => 'info',
                'max_files' => 30,
                'max_size' => '100MB',
            ],
            'error' => [
                'driver' => 'file',
                'path' => __DIR__ . '/../logs/error.log',
                'level' => 'error',
                'max_files' => 90,
                'max_size' => '50MB',
            ],
            'security' => [
                'driver' => 'file',
                'path' => __DIR__ . '/../logs/security.log',
                'level' => 'warning',
                'max_files' => 365,
                'max_size' => '200MB',
            ],
            'audit' => [
                'driver' => 'file',
                'path' => __DIR__ . '/../logs/audit.log',
                'level' => 'info',
                'max_files' => 365,
                'max_size' => '500MB',
            ],
        ],
    ],

    // Cache Configuration
    'cache' => [
        'default' => 'redis',
        'stores' => [
            'redis' => [
                'driver' => 'redis',
                'host' => $_ENV['REDIS_HOST'] ?? '127.0.0.1',
                'port' => $_ENV['REDIS_PORT'] ?? 6379,
                'password' => $_ENV['REDIS_PASSWORD'] ?? null,
                'database' => $_ENV['REDIS_DB'] ?? 0,
                'prefix' => 'guardgeo:',
                'serializer' => 'php',
                'compression' => true,
            ],
            'file' => [
                'driver' => 'file',
                'path' => __DIR__ . '/../storage/cache',
            ],
        ],
        'ttl' => [
            'ip_intelligence' => 86400, // 24 hours
            'freemius_data' => 3600,    // 1 hour
            'session_data' => 1800,     // 30 minutes
        ],
    ],

    // Session Configuration
    'session' => [
        'driver' => 'redis',
        'lifetime' => 3600, // 1 hour
        'expire_on_close' => true,
        'encrypt' => true,
        'files' => __DIR__ . '/../storage/sessions',
        'connection' => 'session',
        'table' => 'sessions',
        'store' => 'redis',
        'lottery' => [2, 100],
        'cookie' => 'guardgeo_session',
        'path' => '/',
        'domain' => $_ENV['SESSION_DOMAIN'] ?? null,
        'secure' => true,
        'http_only' => true,
        'same_site' => 'strict',
    ],

    // External API Configuration
    'apis' => [
        'freemius' => [
            'base_url' => 'https://api.freemius.com',
            'timeout' => 30,
            'retry_attempts' => 3,
            'retry_delay' => 1000, // milliseconds
            'verify_ssl' => true,
            'user_agent' => 'GuardGeo/1.0',
        ],
        'ipregistry' => [
            'base_url' => 'https://api.ipregistry.co',
            'timeout' => 10,
            'retry_attempts' => 2,
            'retry_delay' => 500, // milliseconds
            'verify_ssl' => true,
            'user_agent' => 'GuardGeo/1.0',
        ],
    ],

    // Security Configuration
    'security' => [
        'encryption_key' => $_ENV['APP_KEY'] ?? '',
        'hash_algorithm' => 'sha256',
        'cipher' => 'AES-256-CBC',
        'allowed_ips' => [
            // Add your allowed IP ranges here
        ],
        'blocked_ips' => [
            // Add blocked IP ranges here
        ],
        'rate_limiting' => [
            'enabled' => true,
            'store' => 'redis',
            'key_prefix' => 'rate_limit:',
        ],
        'csrf' => [
            'enabled' => true,
            'token_lifetime' => 3600,
        ],
        'headers' => [
            'hsts' => 'max-age=31536000; includeSubDomains; preload',
            'csp' => "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests",
        ],
    ],

    // Email Configuration
    'mail' => [
        'driver' => 'smtp',
        'host' => $_ENV['MAIL_HOST'] ?? 'localhost',
        'port' => $_ENV['MAIL_PORT'] ?? 587,
        'username' => $_ENV['MAIL_USERNAME'] ?? '',
        'password' => $_ENV['MAIL_PASSWORD'] ?? '',
        'encryption' => $_ENV['MAIL_ENCRYPTION'] ?? 'tls',
        'from' => [
            'address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
            'name' => $_ENV['MAIL_FROM_NAME'] ?? 'GuardGeo Admin Platform',
        ],
    ],

    // Monitoring Configuration
    'monitoring' => [
        'enabled' => true,
        'metrics' => [
            'response_time' => true,
            'memory_usage' => true,
            'database_queries' => true,
            'cache_hits' => true,
            'error_rates' => true,
        ],
        'alerts' => [
            'email' => $_ENV['ALERT_EMAIL'] ?? '<EMAIL>',
            'webhook' => $_ENV['ALERT_WEBHOOK'] ?? null,
            'thresholds' => [
                'response_time' => 2000, // milliseconds
                'memory_usage' => 80,    // percentage
                'error_rate' => 5,       // percentage
                'disk_usage' => 85,      // percentage
            ],
        ],
        'health_checks' => [
            'database' => true,
            'redis' => true,
            'external_apis' => true,
            'disk_space' => true,
            'ssl_certificate' => true,
        ],
    ],

    // Backup Configuration
    'backup' => [
        'enabled' => true,
        'schedule' => '0 2 * * *', // Daily at 2 AM
        'retention' => [
            'daily' => 7,
            'weekly' => 4,
            'monthly' => 12,
        ],
        'destinations' => [
            'local' => [
                'driver' => 'local',
                'path' => '/var/backups/guardgeo',
            ],
            's3' => [
                'driver' => 's3',
                'bucket' => $_ENV['BACKUP_S3_BUCKET'] ?? '',
                'region' => $_ENV['BACKUP_S3_REGION'] ?? 'us-east-1',
                'key' => $_ENV['BACKUP_S3_KEY'] ?? '',
                'secret' => $_ENV['BACKUP_S3_SECRET'] ?? '',
            ],
        ],
        'encryption' => [
            'enabled' => true,
            'key' => $_ENV['BACKUP_ENCRYPTION_KEY'] ?? '',
        ],
        'compression' => true,
        'verify_integrity' => true,
    ],

    // Performance Configuration
    'performance' => [
        'opcache' => [
            'enabled' => true,
            'memory_consumption' => 256,
            'max_accelerated_files' => 20000,
            'validate_timestamps' => false,
        ],
        'compression' => [
            'enabled' => true,
            'level' => 6,
            'types' => ['text/html', 'text/css', 'application/javascript', 'application/json'],
        ],
        'cdn' => [
            'enabled' => false,
            'url' => $_ENV['CDN_URL'] ?? '',
        ],
    ],

    // Error Handling
    'error_handling' => [
        'display_errors' => false,
        'log_errors' => true,
        'error_reporting' => E_ERROR | E_WARNING | E_PARSE,
        'max_execution_time' => 60,
        'memory_limit' => '256M',
        'post_max_size' => '10M',
        'upload_max_filesize' => '5M',
    ],

    // Maintenance Mode
    'maintenance' => [
        'enabled' => false,
        'secret' => $_ENV['MAINTENANCE_SECRET'] ?? '',
        'template' => __DIR__ . '/../resources/views/maintenance.html',
        'allowed_ips' => [
            // Add IPs that can bypass maintenance mode
        ],
    ],

    // Feature Flags
    'features' => [
        'ip_intelligence_caching' => true,
        'freemius_sync' => true,
        'audit_logging' => true,
        'rate_limiting' => true,
        'security_monitoring' => true,
        'backup_automation' => true,
        'health_checks' => true,
        'performance_monitoring' => true,
    ],
];