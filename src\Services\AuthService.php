<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Models\AdminUserModel;
use Skpassegna\GuardgeoApi\Utils\SessionManager;
use Skpassegna\GuardgeoApi\Utils\PasswordValidator;
use Skpassegna\GuardgeoApi\Utils\EmailDomainValidator;
use Skpassegna\GuardgeoApi\Utils\RateLimiter;
use Skpassegna\GuardgeoApi\Config\SecurityConfig;

/**
 * Authentication Service
 * 
 * Handles admin user authentication with email domain validation,
 * password complexity requirements, and secure session management.
 */
class AuthService
{
    private DatabaseConnection $db;
    private SessionManager $sessionManager;
    private PasswordValidator $passwordValidator;
    private EmailDomainValidator $emailValidator;
    private RateLimiter $rateLimiter;
    private LoggingService $logger;

    public function __construct(
        DatabaseConnection $db,
        SessionManager $sessionManager,
        PasswordValidator $passwordValidator,
        EmailDomainValidator $emailValidator,
        RateLimiter $rateLimiter,
        LoggingService $logger
    ) {
        $this->db = $db;
        $this->sessionManager = $sessionManager;
        $this->passwordValidator = $passwordValidator;
        $this->emailValidator = $emailValidator;
        $this->rateLimiter = $rateLimiter;
        $this->logger = $logger;
    }

    /**
     * Authenticate admin user with email and password
     *
     * @param string $email
     * @param string $password
     * @return array Authentication result with user data or error
     */
    public function authenticate(string $email, string $password): array
    {
        $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        
        // Check rate limiting for login attempts
        if (!$this->rateLimiter->checkLimit($clientIp, 'login_attempt')) {
            $this->logger->logAdminAction('auth_rate_limited', [
                'email' => $email,
                'ip' => $clientIp,
                'attempts' => $this->rateLimiter->getCurrentRequestCount($clientIp, 'login_attempt')
            ]);
            
            return [
                'success' => false,
                'error' => 'Too many login attempts. Please try again later.'
            ];
        }

        try {
            // Validate email format and domain
            if (!$this->emailValidator->isValidEmail($email)) {
                $this->logger->logAdminAction('auth_failed', [
                    'email' => $email,
                    'reason' => 'invalid_email_format',
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
                
                return [
                    'success' => false,
                    'error' => 'Invalid email format'
                ];
            }

            if (!$this->emailValidator->isAllowedDomain($email)) {
                $this->logger->logAdminAction('auth_failed', [
                    'email' => $email,
                    'reason' => 'domain_not_whitelisted',
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
                
                return [
                    'success' => false,
                    'error' => 'Email domain not authorized'
                ];
            }

            // Find user by email
            $user = $this->findUserByEmail($email);
            if (!$user) {
                $this->logger->logAdminAction('auth_failed', [
                    'email' => $email,
                    'reason' => 'user_not_found',
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
                
                return [
                    'success' => false,
                    'error' => 'Invalid credentials'
                ];
            }

            // Check if user is active
            if (!$user['is_active']) {
                $this->logger->logAdminAction('auth_failed', [
                    'email' => $email,
                    'user_id' => $user['id'],
                    'reason' => 'user_inactive',
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
                
                return [
                    'success' => false,
                    'error' => 'Account is inactive'
                ];
            }

            // Verify password
            if (!password_verify($password, $user['password_hash'])) {
                $this->logger->logAdminAction('auth_failed', [
                    'email' => $email,
                    'user_id' => $user['id'],
                    'reason' => 'invalid_password',
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
                
                return [
                    'success' => false,
                    'error' => 'Invalid credentials'
                ];
            }

            // Update last login timestamp
            $this->updateLastLogin($user['id']);

            // Create secure session
            $sessionData = [
                'user_id' => $user['id'],
                'email' => $user['email'],
                'role' => $user['role'],
                'login_time' => time(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ];

            $sessionId = $this->sessionManager->createSession($sessionData);

            $this->logger->logAdminAction('auth_success', [
                'email' => $email,
                'user_id' => $user['id'],
                'role' => $user['role'],
                'session_id' => $sessionId,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            return [
                'success' => true,
                'user' => [
                    'id' => $user['id'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'session_id' => $sessionId
                ]
            ];

        } catch (\Exception $e) {
            $this->logger->logError('Authentication error', [
                'email' => $email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Authentication system error'
            ];
        }
    }

    /**
     * Validate current session and return user data
     *
     * @return array|null User data if session is valid, null otherwise
     */
    public function validateSession(): ?array
    {
        try {
            $sessionData = $this->sessionManager->getSession();
            
            if (!$sessionData) {
                return null;
            }

            // Verify user still exists and is active
            $user = $this->findUserById($sessionData['user_id']);
            if (!$user || !$user['is_active']) {
                $this->sessionManager->destroySession();
                return null;
            }

            // Check session timeout (24 hours)
            if (time() - $sessionData['login_time'] > 86400) {
                $this->sessionManager->destroySession();
                return null;
            }

            return [
                'id' => $user['id'],
                'email' => $user['email'],
                'role' => $user['role']
            ];

        } catch (\Exception $e) {
            $this->logger->logError('Session validation error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return null;
        }
    }

    /**
     * Logout user and destroy session
     *
     * @return bool Success status
     */
    public function logout(): bool
    {
        try {
            $sessionData = $this->sessionManager->getSession();
            
            if ($sessionData) {
                $this->logger->logAdminAction('logout', [
                    'user_id' => $sessionData['user_id'],
                    'email' => $sessionData['email'],
                    'session_duration' => time() - $sessionData['login_time'],
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
            }

            return $this->sessionManager->destroySession();

        } catch (\Exception $e) {
            $this->logger->logError('Logout error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return false;
        }
    }

    /**
     * Create new admin user (Super Admin only)
     *
     * @param string $email
     * @param string $password
     * @param string $role
     * @param int $createdBy Super Admin user ID
     * @return array Creation result
     */
    public function createAdminUser(string $email, string $password, string $role, int $createdBy): array
    {
        try {
            // Validate email
            if (!$this->emailValidator->isValidEmail($email)) {
                return [
                    'success' => false,
                    'error' => 'Invalid email format'
                ];
            }

            if (!$this->emailValidator->isAllowedDomain($email)) {
                return [
                    'success' => false,
                    'error' => 'Email domain not authorized'
                ];
            }

            // Validate password
            $passwordValidation = $this->passwordValidator->validate($password);
            if (!$passwordValidation['valid']) {
                return [
                    'success' => false,
                    'error' => 'Password requirements not met: ' . implode(', ', $passwordValidation['errors'])
                ];
            }

            // Validate role
            $allowedRoles = ['super_admin', 'dev', 'marketing', 'sales'];
            if (!in_array($role, $allowedRoles)) {
                return [
                    'success' => false,
                    'error' => 'Invalid role specified'
                ];
            }

            // Check if user already exists
            if ($this->findUserByEmail($email)) {
                return [
                    'success' => false,
                    'error' => 'User with this email already exists'
                ];
            }

            // Hash password
            $passwordHash = password_hash($password, PASSWORD_ARGON2ID, [
                'memory_cost' => 65536,
                'time_cost' => 4,
                'threads' => 3
            ]);

            // Insert user
            $query = "
                INSERT INTO admin_users (email, password_hash, role, created_at, updated_at, is_active)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, true)
                RETURNING id, email, role, created_at
            ";

            $result = $this->db->query($query, [$email, $passwordHash, $role]);
            $newUser = $result->fetch(\PDO::FETCH_ASSOC);

            $this->logger->logAdminAction('user_created', [
                'created_user_id' => $newUser['id'],
                'created_user_email' => $newUser['email'],
                'created_user_role' => $newUser['role'],
                'created_by' => $createdBy,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            return [
                'success' => true,
                'user' => $newUser
            ];

        } catch (\Exception $e) {
            $this->logger->logError('User creation error', [
                'email' => $email,
                'role' => $role,
                'created_by' => $createdBy,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create user'
            ];
        }
    }

    /**
     * Find user by email
     *
     * @param string $email
     * @return array|null User data or null if not found
     */
    private function findUserByEmail(string $email): ?array
    {
        $query = "SELECT id, email, password_hash, role, is_active, last_login FROM admin_users WHERE email = ?";
        $result = $this->db->query($query, [$email]);
        
        $user = $result->fetch(\PDO::FETCH_ASSOC);
        return $user ?: null;
    }

    /**
     * Find user by ID
     *
     * @param int $id
     * @return array|null User data or null if not found
     */
    private function findUserById(int $id): ?array
    {
        $query = "SELECT id, email, role, is_active, last_login FROM admin_users WHERE id = ?";
        $result = $this->db->query($query, [$id]);
        
        $user = $result->fetch(\PDO::FETCH_ASSOC);
        return $user ?: null;
    }

    /**
     * Update last login timestamp
     *
     * @param int $userId
     * @return void
     */
    private function updateLastLogin(int $userId): void
    {
        $query = "UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = ?";
        $this->db->query($query, [$userId]);
    }
}