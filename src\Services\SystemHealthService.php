<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Config\ConfigManager;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Database\MigrationManager;

/**
 * System Health Check Service
 * 
 * Provides comprehensive system health monitoring including database connectivity,
 * external API status, disk space, memory usage, and service availability.
 */
class SystemHealthService
{
    private ConfigManager $configManager;
    private DatabaseConnection $db;
    private LoggingService $logger;
    private MigrationManager $migrationManager;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->configManager = ConfigManager::getInstance();
        $this->db = DatabaseConnection::getInstance();
        $this->logger = LoggingService::getInstance();
        $this->migrationManager = new MigrationManager();
    }
    
    /**
     * Perform comprehensive health check
     */
    public function performHealthCheck(): array
    {
        $startTime = microtime(true);
        
        $checks = [
            'database' => $this->checkDatabase(),
            'external_apis' => $this->checkExternalApis(),
            'file_system' => $this->checkFileSystem(),
            'system_resources' => $this->checkSystemResources(),
            'configuration' => $this->checkConfiguration(),
            'migrations' => $this->checkMigrations(),
            'logging' => $this->checkLogging(),
            'cache' => $this->checkCache(),
        ];
        
        $overallStatus = $this->calculateOverallStatus($checks);
        $executionTime = (microtime(true) - $startTime) * 1000;
        
        $result = [
            'timestamp' => date('c'),
            'overall_status' => $overallStatus,
            'execution_time_ms' => round($executionTime, 2),
            'checks' => $checks,
            'summary' => $this->generateSummary($checks)
        ];
        
        // Log health check results
        $this->logger->info('System health check completed', [
            'overall_status' => $overallStatus,
            'execution_time_ms' => $result['execution_time_ms'],
            'failed_checks' => array_keys(array_filter($checks, fn($check) => $check['status'] === 'error'))
        ]);
        
        return $result;
    }
    
    /**
     * Check database connectivity and performance
     */
    private function checkDatabase(): array
    {
        $startTime = microtime(true);
        
        try {
            // Test basic connectivity
            $result = $this->db->query("SELECT 1 as test, NOW() as current_time");
            $row = $result->fetch(\PDO::FETCH_ASSOC);
            
            if (!$row || $row['test'] !== 1) {
                throw new \Exception('Database query returned unexpected result');
            }
            
            // Test write capability
            $testQuery = "
                CREATE TEMP TABLE health_check_test (id SERIAL, test_data VARCHAR(50));
                INSERT INTO health_check_test (test_data) VALUES ('health_check');
                SELECT COUNT(*) as count FROM health_check_test;
                DROP TABLE health_check_test;
            ";
            
            $writeResult = $this->db->query($testQuery);
            $writeRow = $writeResult->fetch(\PDO::FETCH_ASSOC);
            
            // Check table counts
            $tableStats = $this->getTableStatistics();
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => 'healthy',
                'message' => 'Database is accessible and responsive',
                'response_time_ms' => round($responseTime, 2),
                'details' => [
                    'server_time' => $row['current_time'],
                    'write_test' => 'passed',
                    'table_statistics' => $tableStats
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database check failed: ' . $e->getMessage(),
                'response_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * Check external API connectivity
     */
    private function checkExternalApis(): array
    {
        $apis = [
            'freemius' => $this->checkFreemiusApi(),
            'ipregistry' => $this->checkIpRegistryApi()
        ];
        
        $overallStatus = 'healthy';
        $failedApis = [];
        
        foreach ($apis as $name => $status) {
            if ($status['status'] === 'error') {
                $overallStatus = 'degraded';
                $failedApis[] = $name;
            }
        }
        
        if (count($failedApis) === count($apis)) {
            $overallStatus = 'error';
        }
        
        return [
            'status' => $overallStatus,
            'message' => empty($failedApis) ? 'All external APIs are accessible' : 
                        'Some external APIs are unavailable: ' . implode(', ', $failedApis),
            'details' => $apis
        ];
    }
    
    /**
     * Check Freemius API
     */
    private function checkFreemiusApi(): array
    {
        $startTime = microtime(true);
        
        try {
            $apiToken = $this->configManager->get('freemius.api_token');
            $baseUrl = $this->configManager->get('freemius.base_url');
            $timeout = $this->configManager->get('freemius.timeout', 30);
            
            if (empty($apiToken)) {
                return [
                    'status' => 'error',
                    'message' => 'Freemius API token not configured',
                    'response_time_ms' => 0
                ];
            }
            
            // Simple connectivity test
            $client = new \GuzzleHttp\Client(['timeout' => $timeout]);
            $response = $client->get($baseUrl . '/ping', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiToken,
                    'User-Agent' => 'GuardGeo-Admin/1.0'
                ]
            ]);
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => $response->getStatusCode() === 200 ? 'healthy' : 'degraded',
                'message' => 'Freemius API is accessible',
                'response_time_ms' => round($responseTime, 2),
                'details' => [
                    'status_code' => $response->getStatusCode(),
                    'base_url' => $baseUrl
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Freemius API check failed: ' . $e->getMessage(),
                'response_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * Check ipRegistry API
     */
    private function checkIpRegistryApi(): array
    {
        $startTime = microtime(true);
        
        try {
            $apiKey = $this->configManager->get('ipregistry.api_key');
            $baseUrl = $this->configManager->get('ipregistry.base_url');
            $timeout = $this->configManager->get('ipregistry.timeout', 30);
            
            if (empty($apiKey)) {
                return [
                    'status' => 'error',
                    'message' => 'ipRegistry API key not configured',
                    'response_time_ms' => 0
                ];
            }
            
            // Test with a known IP address
            $client = new \GuzzleHttp\Client(['timeout' => $timeout]);
            $response = $client->get($baseUrl . '/*******', [
                'query' => ['key' => $apiKey],
                'headers' => [
                    'User-Agent' => 'GuardGeo-Admin/1.0'
                ]
            ]);
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => $response->getStatusCode() === 200 ? 'healthy' : 'degraded',
                'message' => 'ipRegistry API is accessible',
                'response_time_ms' => round($responseTime, 2),
                'details' => [
                    'status_code' => $response->getStatusCode(),
                    'base_url' => $baseUrl
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'ipRegistry API check failed: ' . $e->getMessage(),
                'response_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * Check file system health
     */
    private function checkFileSystem(): array
    {
        try {
            $checks = [];
            
            // Check log directory
            $logPath = $this->configManager->get('logging.file_path', 'logs');
            $checks['log_directory'] = $this->checkDirectoryHealth($logPath);
            
            // Check temp directory
            $tempPath = sys_get_temp_dir();
            $checks['temp_directory'] = $this->checkDirectoryHealth($tempPath);
            
            // Check application root
            $appRoot = dirname(__DIR__, 2);
            $checks['app_root'] = $this->checkDirectoryHealth($appRoot, false); // Read-only check
            
            $overallStatus = 'healthy';
            $issues = [];
            
            foreach ($checks as $name => $check) {
                if ($check['status'] === 'error') {
                    $overallStatus = 'error';
                    $issues[] = $name;
                } elseif ($check['status'] === 'warning') {
                    if ($overallStatus === 'healthy') {
                        $overallStatus = 'degraded';
                    }
                    $issues[] = $name;
                }
            }
            
            return [
                'status' => $overallStatus,
                'message' => empty($issues) ? 'File system is healthy' : 
                            'File system issues detected: ' . implode(', ', $issues),
                'details' => $checks
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'File system check failed: ' . $e->getMessage(),
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * Check directory health
     */
    private function checkDirectoryHealth(string $path, bool $checkWrite = true): array
    {
        if (!is_dir($path)) {
            return [
                'status' => 'error',
                'message' => 'Directory does not exist',
                'path' => $path
            ];
        }
        
        if (!is_readable($path)) {
            return [
                'status' => 'error',
                'message' => 'Directory is not readable',
                'path' => $path
            ];
        }
        
        $details = [
            'path' => $path,
            'readable' => true,
            'writable' => is_writable($path),
            'free_space' => disk_free_space($path),
            'total_space' => disk_total_space($path)
        ];
        
        if ($checkWrite && !$details['writable']) {
            return [
                'status' => 'error',
                'message' => 'Directory is not writable',
                'details' => $details
            ];
        }
        
        // Check disk space (warn if less than 1GB free)
        $freeSpaceGB = $details['free_space'] / (1024 * 1024 * 1024);
        if ($freeSpaceGB < 1) {
            return [
                'status' => 'warning',
                'message' => 'Low disk space: ' . round($freeSpaceGB, 2) . 'GB free',
                'details' => $details
            ];
        }
        
        return [
            'status' => 'healthy',
            'message' => 'Directory is accessible',
            'details' => $details
        ];
    }
    
    /**
     * Check system resources
     */
    private function checkSystemResources(): array
    {
        try {
            $memoryLimit = ini_get('memory_limit');
            $memoryUsage = memory_get_usage(true);
            $memoryPeak = memory_get_peak_usage(true);
            
            $details = [
                'memory_limit' => $memoryLimit,
                'memory_usage' => $memoryUsage,
                'memory_peak' => $memoryPeak,
                'memory_usage_mb' => round($memoryUsage / (1024 * 1024), 2),
                'memory_peak_mb' => round($memoryPeak / (1024 * 1024), 2),
                'php_version' => PHP_VERSION,
                'server_load' => function_exists('sys_getloadavg') ? sys_getloadavg() : null
            ];
            
            // Convert memory limit to bytes for comparison
            $memoryLimitBytes = $this->convertToBytes($memoryLimit);
            $memoryUsagePercent = ($memoryUsage / $memoryLimitBytes) * 100;
            
            $status = 'healthy';
            $message = 'System resources are within normal limits';
            
            if ($memoryUsagePercent > 80) {
                $status = 'warning';
                $message = 'High memory usage: ' . round($memoryUsagePercent, 1) . '%';
            } elseif ($memoryUsagePercent > 95) {
                $status = 'error';
                $message = 'Critical memory usage: ' . round($memoryUsagePercent, 1) . '%';
            }
            
            $details['memory_usage_percent'] = round($memoryUsagePercent, 1);
            
            return [
                'status' => $status,
                'message' => $message,
                'details' => $details
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'System resources check failed: ' . $e->getMessage(),
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * Check configuration health
     */
    private function checkConfiguration(): array
    {
        try {
            $configHealth = $this->configManager->getHealthStatus();
            
            return [
                'status' => $configHealth['overall'] === 'healthy' ? 'healthy' : 
                           ($configHealth['overall'] === 'degraded' ? 'degraded' : 'error'),
                'message' => 'Configuration status: ' . $configHealth['overall'],
                'details' => $configHealth['checks']
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Configuration check failed: ' . $e->getMessage(),
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * Check migration status
     */
    private function checkMigrations(): array
    {
        try {
            $migrationStatus = $this->migrationManager->getStatus();
            
            if (isset($migrationStatus['error'])) {
                return [
                    'status' => 'error',
                    'message' => $migrationStatus['error'],
                    'details' => $migrationStatus
                ];
            }
            
            $status = 'healthy';
            $message = 'All migrations are up to date';
            
            if ($migrationStatus['pending_migrations'] > 0) {
                $status = 'warning';
                $message = $migrationStatus['pending_migrations'] . ' pending migrations';
            }
            
            return [
                'status' => $status,
                'message' => $message,
                'details' => $migrationStatus
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Migration check failed: ' . $e->getMessage(),
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * Check logging system
     */
    private function checkLogging(): array
    {
        try {
            $logPath = $this->configManager->get('logging.file_path', 'logs');
            $combinedLog = $logPath . '/combined.log';
            $errorLog = $logPath . '/error.log';
            
            $details = [
                'log_directory' => $logPath,
                'combined_log_exists' => file_exists($combinedLog),
                'error_log_exists' => file_exists($errorLog),
                'combined_log_writable' => is_writable($combinedLog),
                'error_log_writable' => is_writable($errorLog),
                'combined_log_size' => file_exists($combinedLog) ? filesize($combinedLog) : 0,
                'error_log_size' => file_exists($errorLog) ? filesize($errorLog) : 0
            ];
            
            $status = 'healthy';
            $issues = [];
            
            if (!$details['combined_log_writable']) {
                $status = 'error';
                $issues[] = 'combined log not writable';
            }
            
            if (!$details['error_log_writable']) {
                $status = 'error';
                $issues[] = 'error log not writable';
            }
            
            $message = empty($issues) ? 'Logging system is functional' : 
                      'Logging issues: ' . implode(', ', $issues);
            
            return [
                'status' => $status,
                'message' => $message,
                'details' => $details
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Logging check failed: ' . $e->getMessage(),
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * Check cache system
     */
    private function checkCache(): array
    {
        try {
            $cacheEnabled = $this->configManager->get('cache.enabled', true);
            
            if (!$cacheEnabled) {
                return [
                    'status' => 'healthy',
                    'message' => 'Cache is disabled',
                    'details' => ['enabled' => false]
                ];
            }
            
            // Test cache functionality by checking IP intelligence cache
            $query = "SELECT COUNT(*) as count FROM ip_intelligence WHERE cached_at > NOW() - INTERVAL '1 hour'";
            $result = $this->db->query($query);
            $row = $result->fetch(\PDO::FETCH_ASSOC);
            
            $recentCacheCount = $row['count'];
            
            return [
                'status' => 'healthy',
                'message' => 'Cache system is operational',
                'details' => [
                    'enabled' => true,
                    'recent_cache_entries' => $recentCacheCount,
                    'driver' => $this->configManager->get('cache.driver', 'database')
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Cache check failed: ' . $e->getMessage(),
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * Get table statistics
     */
    private function getTableStatistics(): array
    {
        try {
            $tables = [
                'admin_users',
                'freemius_products',
                'freemius_installations',
                'ip_intelligence',
                'system_logs',
                'api_requests'
            ];
            
            $stats = [];
            
            foreach ($tables as $table) {
                try {
                    $query = "SELECT COUNT(*) as count FROM {$table}";
                    $result = $this->db->query($query);
                    $row = $result->fetch(\PDO::FETCH_ASSOC);
                    $stats[$table] = (int) $row['count'];
                } catch (\Exception $e) {
                    $stats[$table] = 'error: ' . $e->getMessage();
                }
            }
            
            return $stats;
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Calculate overall status from individual checks
     */
    private function calculateOverallStatus(array $checks): string
    {
        $statuses = array_column($checks, 'status');
        
        if (in_array('error', $statuses)) {
            return 'error';
        }
        
        if (in_array('degraded', $statuses) || in_array('warning', $statuses)) {
            return 'degraded';
        }
        
        return 'healthy';
    }
    
    /**
     * Generate summary of health check results
     */
    private function generateSummary(array $checks): array
    {
        $summary = [
            'total_checks' => count($checks),
            'healthy' => 0,
            'degraded' => 0,
            'errors' => 0,
            'failed_checks' => []
        ];
        
        foreach ($checks as $name => $check) {
            switch ($check['status']) {
                case 'healthy':
                    $summary['healthy']++;
                    break;
                case 'degraded':
                case 'warning':
                    $summary['degraded']++;
                    break;
                case 'error':
                    $summary['errors']++;
                    $summary['failed_checks'][] = $name;
                    break;
            }
        }
        
        return $summary;
    }
    
    /**
     * Convert memory limit string to bytes
     */
    private function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
    
    /**
     * Get quick health status (lightweight check)
     */
    public function getQuickStatus(): array
    {
        try {
            // Quick database check
            $this->db->query("SELECT 1");
            $dbStatus = 'healthy';
        } catch (\Exception $e) {
            $dbStatus = 'error';
        }
        
        // Quick configuration check
        $configStatus = $this->configManager->getHealthStatus()['overall'];
        
        // Quick file system check
        $logPath = $this->configManager->get('logging.file_path', 'logs');
        $fsStatus = is_writable($logPath) ? 'healthy' : 'error';
        
        $overallStatus = 'healthy';
        if ($dbStatus === 'error' || $configStatus === 'unhealthy' || $fsStatus === 'error') {
            $overallStatus = 'error';
        } elseif ($configStatus === 'degraded') {
            $overallStatus = 'degraded';
        }
        
        return [
            'timestamp' => date('c'),
            'overall_status' => $overallStatus,
            'quick_checks' => [
                'database' => $dbStatus,
                'configuration' => $configStatus,
                'file_system' => $fsStatus
            ]
        ];
    }
}