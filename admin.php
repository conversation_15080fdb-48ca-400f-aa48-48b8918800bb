<?php

/**
 * GuardGeo Admin Interface Entry Point
 * 
 * Handles all admin interface requests with proper routing,
 * authentication, and role-based access control.
 */

require_once __DIR__ . '/vendor/autoload.php';

use Skpassegna\GuardgeoApi\Controllers\AdminRouter;
use Skpassegna\GuardgeoApi\Config\EnvironmentManager;
use Skpassegna\GuardgeoApi\Utils\SSLEnforcementMiddleware;

// Initialize environment manager
$envManager = EnvironmentManager::getInstance();
$envManager->loadConfiguration();

// Set error reporting for production
if ($envManager->isProduction()) {
    error_reporting(0);
    ini_set('display_errors', 0);
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Set timezone
date_default_timezone_set($envManager->get('app.timezone', 'UTC'));

// Initialize SSL enforcement middleware
$sslMiddleware = new SSLEnforcementMiddleware();
$sslMiddleware->process();

// Initialize security middleware
$logger = \Skpassegna\GuardgeoApi\Services\LoggingService::getInstance();
$securityMiddleware = new \Skpassegna\GuardgeoApi\Utils\SecurityMiddleware($logger);

// Apply comprehensive security measures
$securityResult = $securityMiddleware->applySecurityMeasures();

if (!$securityResult['allowed']) {
    // Security check failed - show security error page
    http_response_code(403);
    header('Content-Type: text/html; charset=utf-8');
    
    echo <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Denied - GuardGeo Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full text-center">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
            <p class="text-gray-600 mb-6">Your request has been blocked by our security system. If you believe this is an error, please contact support.</p>
            <button onclick="window.history.back()" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Go Back
            </button>
        </div>
    </div>
</body>
</html>
HTML;
    exit;
}

// HTTPS enforcement is now handled by SSLEnforcementMiddleware

try {
    // Get request path and method
    $requestUri = $_SERVER['REQUEST_URI'] ?? '/';
    $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    
    // Parse the path
    $parsedUrl = parse_url($requestUri);
    $path = $parsedUrl['path'] ?? '/';
    
    // Initialize and handle the request
    $router = new AdminRouter();
    $router->handleRequest($path, $method);

} catch (\Exception $e) {
    // Log the error
    error_log("Admin interface error: " . $e->getMessage());
    
    // Show generic error page
    http_response_code(500);
    header('Content-Type: text/html; charset=utf-8');
    
    echo <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Error - GuardGeo Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full text-center">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">System Error</h1>
            <p class="text-gray-600 mb-6">The system encountered an unexpected error. Please try again later or contact support if the problem persists.</p>
            <button onclick="window.location.reload()" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Try Again
            </button>
        </div>
    </div>
</body>
</html>
HTML;
}