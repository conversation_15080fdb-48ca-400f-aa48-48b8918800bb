<?php

namespace Skpassegna\GuardgeoApi\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Skpassegna\GuardgeoApi\Utils\Logger;
use Skpassegna\GuardgeoApi\Config\Environment;

/**
 * ipRegistry API Client
 * 
 * Handles HTTP communication with the ipRegistry API for IP intelligence data.
 * Provides comprehensive error handling for API failures and supports both
 * single IP and batch IP lookups.
 */
class IpRegistryApiClient
{
    private Client $httpClient;
    private Logger $logger;
    private string $baseUrl;
    private string $apiKey;
    private int $timeout;
    private int $retryAttempts;
    private int $retryDelay;
    
    /**
     * Constructor
     */
    public function __construct(
        ?string $apiKey = null,
        ?string $baseUrl = null,
        int $timeout = 30,
        int $retryAttempts = 3,
        int $retryDelay = 1
    ) {
        $this->apiKey = $apiKey ?? Environment::get('IPREGISTRY_API_KEY');
        $this->baseUrl = $baseUrl ?? Environment::get('IPREGISTRY_API_BASE_URL', 'https://api.ipregistry.co');
        $this->timeout = $timeout;
        $this->retryAttempts = $retryAttempts;
        $this->retryDelay = $retryDelay;
        
        $this->logger = new Logger();
        
        // Initialize Guzzle HTTP client
        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => $this->timeout,
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'User-Agent' => 'GuardGeo-API/1.0'
            ]
        ]);
        
        if (empty($this->apiKey)) {
            throw new \InvalidArgumentException('ipRegistry API key is required');
        }
    }
    
    /**
     * Get IP intelligence data for a single IP address
     */
    public function getIpData(string $ip, array $fields = []): array
    {
        $this->logger->info("Fetching IP intelligence data", [
            'ip' => $ip,
            'fields' => $fields
        ]);
        
        // Validate IP address
        if (!filter_var($ip, FILTER_VALIDATE_IP)) {
            throw new IpRegistryApiException("Invalid IP address: {$ip}");
        }
        
        $endpoint = "/{$ip}";
        $params = ['key' => $this->apiKey];
        
        // Add specific fields if requested
        if (!empty($fields)) {
            $params['fields'] = implode(',', $fields);
        }
        
        return $this->makeRequest('GET', $endpoint, $params);
    }
    
    /**
     * Get IP intelligence data for multiple IP addresses (batch lookup)
     */
    public function getBatchIpData(array $ips, array $fields = []): array
    {
        $this->logger->info("Fetching batch IP intelligence data", [
            'ip_count' => count($ips),
            'ips' => $ips,
            'fields' => $fields
        ]);
        
        // Validate all IP addresses
        foreach ($ips as $ip) {
            if (!filter_var($ip, FILTER_VALIDATE_IP)) {
                throw new IpRegistryApiException("Invalid IP address in batch: {$ip}");
            }
        }
        
        // ipRegistry batch endpoint expects comma-separated IPs
        $endpoint = "/" . implode(',', $ips);
        $params = ['key' => $this->apiKey];
        
        // Add specific fields if requested
        if (!empty($fields)) {
            $params['fields'] = implode(',', $fields);
        }
        
        return $this->makeRequest('GET', $endpoint, $params);
    }
    
    /**
     * Enhanced batch processing with automatic chunking and error handling
     */
    public function getEnhancedBatchIpData(array $ips, array $options = []): array
    {
        $defaultOptions = [
            'fields' => [],
            'chunk_size' => 100, // ipRegistry supports up to 100 IPs per batch
            'delay_between_chunks_ms' => 100,
            'max_retries_per_chunk' => 2,
            'include_failed_ips' => true,
            'fallback_to_individual' => true
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        $this->logger->info("Enhanced batch IP processing started", [
            'total_ips' => count($ips),
            'chunk_size' => $options['chunk_size'],
            'options' => $options
        ]);
        
        $results = [
            'successful' => [],
            'failed' => [],
            'summary' => [
                'total_requested' => count($ips),
                'successful_count' => 0,
                'failed_count' => 0,
                'chunks_processed' => 0,
                'processing_time_ms' => 0,
                'average_chunk_time_ms' => 0
            ]
        ];
        
        $startTime = microtime(true);
        $invalidIps = [];
        $validIps = [];
        
        // Validate all IPs first
        foreach ($ips as $ip) {
            if ($this->isValidIp($ip)) {
                $validIps[] = $ip;
            } else {
                $invalidIps[] = $ip;
                if ($options['include_failed_ips']) {
                    $results['failed'][$ip] = [
                        'error' => 'Invalid IP address format',
                        'error_code' => 'INVALID_IP'
                    ];
                }
            }
        }
        
        if (!empty($invalidIps)) {
            $this->logger->warning("Invalid IP addresses found in batch", [
                'invalid_count' => count($invalidIps),
                'invalid_ips' => $invalidIps
            ]);
        }
        
        if (empty($validIps)) {
            $results['summary']['failed_count'] = count($invalidIps);
            return $results;
        }
        
        // Process in chunks
        $chunks = array_chunk($validIps, $options['chunk_size']);
        $chunkTimes = [];
        
        foreach ($chunks as $chunkIndex => $chunk) {
            $chunkStartTime = microtime(true);
            $results['summary']['chunks_processed']++;
            
            $this->logger->debug("Processing chunk", [
                'chunk_index' => $chunkIndex + 1,
                'chunk_size' => count($chunk),
                'total_chunks' => count($chunks)
            ]);
            
            $chunkResult = $this->processChunkWithRetry($chunk, $options);
            
            // Merge results
            $results['successful'] = array_merge($results['successful'], $chunkResult['successful']);
            $results['failed'] = array_merge($results['failed'], $chunkResult['failed']);
            
            $chunkTime = round((microtime(true) - $chunkStartTime) * 1000, 2);
            $chunkTimes[] = $chunkTime;
            
            // Add delay between chunks if specified
            if ($chunkIndex < count($chunks) - 1 && $options['delay_between_chunks_ms'] > 0) {
                usleep($options['delay_between_chunks_ms'] * 1000);
            }
        }
        
        // Calculate final summary
        $results['summary']['successful_count'] = count($results['successful']);
        $results['summary']['failed_count'] = count($results['failed']);
        $results['summary']['processing_time_ms'] = round((microtime(true) - $startTime) * 1000, 2);
        $results['summary']['average_chunk_time_ms'] = !empty($chunkTimes) ? round(array_sum($chunkTimes) / count($chunkTimes), 2) : 0;
        
        $this->logger->info("Enhanced batch IP processing completed", $results['summary']);
        
        return $results;
    }
    
    /**
     * Process a single chunk with retry logic
     */
    private function processChunkWithRetry(array $chunk, array $options): array
    {
        $result = [
            'successful' => [],
            'failed' => []
        ];
        
        $attempt = 0;
        $maxRetries = $options['max_retries_per_chunk'];
        
        while ($attempt <= $maxRetries) {
            $attempt++;
            
            try {
                $batchData = $this->getBatchIpData($chunk, $options['fields']);
                
                // Process successful batch response
                if (isset($batchData['results']) && is_array($batchData['results'])) {
                    // Multiple IPs response format
                    foreach ($batchData['results'] as $ipData) {
                        if (isset($ipData['ip'])) {
                            $result['successful'][$ipData['ip']] = $ipData;
                        }
                    }
                } else {
                    // Single IP response format (when chunk has only one IP)
                    if (count($chunk) === 1 && isset($batchData['ip'])) {
                        $result['successful'][$batchData['ip']] = $batchData;
                    }
                }
                
                // Check if all IPs were processed successfully
                $processedIps = array_keys($result['successful']);
                $missingIps = array_diff($chunk, $processedIps);
                
                if (empty($missingIps)) {
                    // All IPs processed successfully
                    break;
                } else {
                    // Some IPs missing, mark as failed
                    foreach ($missingIps as $missingIp) {
                        $result['failed'][$missingIp] = [
                            'error' => 'IP not found in batch response',
                            'error_code' => 'MISSING_FROM_RESPONSE'
                        ];
                    }
                    break;
                }
                
            } catch (IpRegistryApiException $e) {
                $this->logger->warning("Chunk processing failed", [
                    'attempt' => $attempt,
                    'max_attempts' => $maxRetries + 1,
                    'chunk_size' => count($chunk),
                    'error' => $e->getMessage()
                ]);
                
                if ($attempt > $maxRetries) {
                    // Max retries exceeded, handle fallback
                    if ($options['fallback_to_individual']) {
                        $fallbackResult = $this->processFallbackIndividual($chunk, $options);
                        $result['successful'] = array_merge($result['successful'], $fallbackResult['successful']);
                        $result['failed'] = array_merge($result['failed'], $fallbackResult['failed']);
                    } else {
                        // Mark all IPs in chunk as failed
                        foreach ($chunk as $ip) {
                            $result['failed'][$ip] = [
                                'error' => $e->getMessage(),
                                'error_code' => 'BATCH_FAILED'
                            ];
                        }
                    }
                    break;
                }
                
                // Wait before retry
                usleep(500000 * $attempt); // Exponential backoff: 500ms, 1s, 1.5s
            }
        }
        
        return $result;
    }
    
    /**
     * Fallback to individual IP processing when batch fails
     */
    private function processFallbackIndividual(array $ips, array $options): array
    {
        $this->logger->info("Falling back to individual IP processing", [
            'ip_count' => count($ips)
        ]);
        
        $result = [
            'successful' => [],
            'failed' => []
        ];
        
        foreach ($ips as $ip) {
            try {
                $ipData = $this->getIpData($ip, $options['fields']);
                $result['successful'][$ip] = $ipData;
                
                // Small delay between individual requests
                usleep(50000); // 50ms delay
                
            } catch (IpRegistryApiException $e) {
                $result['failed'][$ip] = [
                    'error' => $e->getMessage(),
                    'error_code' => 'INDIVIDUAL_FAILED'
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * Get IP geolocation data only
     */
    public function getGeolocation(string $ip): array
    {
        $fields = [
            'location.continent',
            'location.country',
            'location.region',
            'location.city',
            'location.postal',
            'location.latitude',
            'location.longitude',
            'location.language',
            'location.in_eu'
        ];
        
        return $this->getIpData($ip, $fields);
    }
    
    /**
     * Get IP security/threat data only
     */
    public function getSecurityData(string $ip): array
    {
        $fields = [
            'security.is_abuser',
            'security.is_attacker',
            'security.is_bogon',
            'security.is_cloud_provider',
            'security.is_proxy',
            'security.is_relay',
            'security.is_tor',
            'security.is_tor_exit',
            'security.is_vpn',
            'security.is_anonymous',
            'security.is_threat'
        ];
        
        return $this->getIpData($ip, $fields);
    }
    
    /**
     * Get IP connection data only
     */
    public function getConnectionData(string $ip): array
    {
        $fields = [
            'connection.asn',
            'connection.domain',
            'connection.organization',
            'connection.route',
            'connection.type'
        ];
        
        return $this->getIpData($ip, $fields);
    }
    
    /**
     * Get IP company data only
     */
    public function getCompanyData(string $ip): array
    {
        $fields = [
            'company.domain',
            'company.name',
            'company.type'
        ];
        
        return $this->getIpData($ip, $fields);
    }
    
    /**
     * Make HTTP request to ipRegistry API with retry logic
     */
    private function makeRequest(string $method, string $endpoint, array $params = []): array
    {
        $attempt = 0;
        $lastException = null;
        
        while ($attempt < $this->retryAttempts) {
            $attempt++;
            
            try {
                $options = [];
                
                // Add query parameters for GET requests
                if ($method === 'GET' && !empty($params)) {
                    $options['query'] = $params;
                }
                
                // Add JSON body for POST/PUT requests
                if (in_array($method, ['POST', 'PUT']) && !empty($params)) {
                    $options['json'] = $params;
                }
                
                $startTime = microtime(true);
                $response = $this->httpClient->request($method, $endpoint, $options);
                $responseTime = round((microtime(true) - $startTime) * 1000, 2);
                
                $statusCode = $response->getStatusCode();
                $body = $response->getBody()->getContents();
                $data = json_decode($body, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new IpRegistryApiException(
                        'Invalid JSON response from ipRegistry API: ' . json_last_error_msg(),
                        $statusCode
                    );
                }
                
                $this->logger->info("ipRegistry API request successful", [
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'status_code' => $statusCode,
                    'response_time_ms' => $responseTime,
                    'attempt' => $attempt
                ]);
                
                return $data;
                
            } catch (ClientException $e) {
                // 4xx errors - don't retry
                $statusCode = $e->getResponse()->getStatusCode();
                $responseBody = $e->getResponse()->getBody()->getContents();
                
                $this->logger->error("ipRegistry API client error", [
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'status_code' => $statusCode,
                    'response_body' => $responseBody,
                    'attempt' => $attempt
                ]);
                
                // Handle specific error codes
                if ($statusCode === 401) {
                    throw new IpRegistryApiException(
                        "Invalid API key or unauthorized access",
                        $statusCode,
                        $e
                    );
                } elseif ($statusCode === 403) {
                    throw new IpRegistryApiException(
                        "API key quota exceeded or access forbidden",
                        $statusCode,
                        $e
                    );
                } elseif ($statusCode === 404) {
                    throw new IpRegistryApiException(
                        "IP address not found or invalid endpoint",
                        $statusCode,
                        $e
                    );
                } elseif ($statusCode === 429) {
                    throw new IpRegistryApiException(
                        "Rate limit exceeded - too many requests",
                        $statusCode,
                        $e
                    );
                }
                
                throw new IpRegistryApiException(
                    $this->parseErrorMessage($responseBody, "HTTP {$statusCode} error"),
                    $statusCode,
                    $e
                );
                
            } catch (ServerException $e) {
                // 5xx errors - retry
                $statusCode = $e->getResponse()->getStatusCode();
                $responseBody = $e->getResponse()->getBody()->getContents();
                $lastException = $e;
                
                $this->logger->warning("ipRegistry API server error, retrying", [
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'status_code' => $statusCode,
                    'response_body' => $responseBody,
                    'attempt' => $attempt,
                    'max_attempts' => $this->retryAttempts
                ]);
                
                if ($attempt < $this->retryAttempts) {
                    sleep($this->retryDelay * $attempt); // Exponential backoff
                    continue;
                }
                
                throw new IpRegistryApiException(
                    $this->parseErrorMessage($responseBody, "HTTP {$statusCode} server error after {$attempt} attempts"),
                    $statusCode,
                    $e
                );
                
            } catch (RequestException $e) {
                // Network errors - retry
                $lastException = $e;
                
                $this->logger->warning("ipRegistry API network error, retrying", [
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'error' => $e->getMessage(),
                    'attempt' => $attempt,
                    'max_attempts' => $this->retryAttempts
                ]);
                
                if ($attempt < $this->retryAttempts) {
                    sleep($this->retryDelay * $attempt); // Exponential backoff
                    continue;
                }
                
                throw new IpRegistryApiException(
                    "Network error after {$attempt} attempts: " . $e->getMessage(),
                    0,
                    $e
                );
                
            } catch (GuzzleException $e) {
                // Other Guzzle errors
                $lastException = $e;
                
                $this->logger->error("ipRegistry API Guzzle error", [
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'error' => $e->getMessage(),
                    'attempt' => $attempt
                ]);
                
                throw new IpRegistryApiException(
                    "HTTP client error: " . $e->getMessage(),
                    0,
                    $e
                );
            }
        }
        
        // This should never be reached, but just in case
        throw new IpRegistryApiException(
            "Maximum retry attempts ({$this->retryAttempts}) exceeded",
            0,
            $lastException
        );
    }
    
    /**
     * Parse error message from API response
     */
    private function parseErrorMessage(string $responseBody, string $fallback): string
    {
        $data = json_decode($responseBody, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            // ipRegistry error format
            if (isset($data['error'])) {
                if (is_string($data['error'])) {
                    return $data['error'];
                }
                
                if (is_array($data['error'])) {
                    if (isset($data['error']['message'])) {
                        return $data['error']['message'];
                    }
                    
                    if (isset($data['error']['code'])) {
                        return "Error code: " . $data['error']['code'];
                    }
                    
                    return json_encode($data['error']);
                }
            }
            
            // Alternative error format
            if (isset($data['message'])) {
                return $data['message'];
            }
        }
        
        return $fallback;
    }
    
    /**
     * Comprehensive health check with detailed status information
     */
    public function healthCheck(): array
    {
        $healthStatus = [
            'api_reachable' => false,
            'api_key_valid' => false,
            'response_time_ms' => null,
            'quota_available' => null,
            'last_error' => null,
            'timestamp' => date('c'),
            'test_ip' => '*******',
            'details' => []
        ];
        
        try {
            $startTime = microtime(true);
            
            // Use a known public IP for health check
            $testResult = $this->getIpData('*******', ['ip', 'location.country']);
            
            $healthStatus['response_time_ms'] = round((microtime(true) - $startTime) * 1000, 2);
            $healthStatus['api_reachable'] = true;
            $healthStatus['api_key_valid'] = true;
            
            // Extract additional information from response if available
            if (isset($testResult['ip']) && $testResult['ip'] === '*******') {
                $healthStatus['details']['test_response_valid'] = true;
                $healthStatus['details']['test_country'] = $testResult['location']['country']['name'] ?? 'Unknown';
            }
            
            $healthStatus['details'] = array_merge($healthStatus['details'], [
                'base_url' => $this->baseUrl,
                'timeout' => $this->timeout,
                'retry_attempts' => $this->retryAttempts,
                'api_key_configured' => !empty($this->apiKey),
                'api_key_masked' => $this->getApiKeyMasked()
            ]);
            
            $this->logger->info("ipRegistry API health check passed", $healthStatus);
            
        } catch (IpRegistryApiException $e) {
            $healthStatus['response_time_ms'] = round((microtime(true) - $startTime) * 1000, 2);
            $healthStatus['last_error'] = $e->getMessage();
            
            // Determine what failed based on error code
            if ($e->getCode() === 401 || $e->getCode() === 403) {
                $healthStatus['api_reachable'] = true;
                $healthStatus['api_key_valid'] = false;
            } elseif ($e->getCode() >= 400 && $e->getCode() < 500) {
                $healthStatus['api_reachable'] = true;
                $healthStatus['api_key_valid'] = true; // API is reachable, might be other client error
            }
            
            $this->logger->error("ipRegistry API health check failed", [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'health_status' => $healthStatus
            ]);
        }
        
        return $healthStatus;
    }
    
    /**
     * Get API usage statistics and quota information
     */
    public function getUsageStats(): array
    {
        $usageStats = [
            'quota_limit' => null,
            'quota_used' => null,
            'quota_remaining' => null,
            'quota_reset_date' => null,
            'requests_today' => null,
            'api_key_masked' => $this->getApiKeyMasked(),
            'timestamp' => date('c'),
            'source' => 'estimated' // Since ipRegistry doesn't have a dedicated usage endpoint
        ];
        
        try {
            // ipRegistry doesn't have a dedicated usage endpoint, but we can estimate from headers
            // Make a lightweight request to get headers
            $testResult = $this->getIpData('*******', ['ip']);
            
            // Note: This would need to be implemented based on actual ipRegistry response headers
            // For now, we'll return the basic structure
            $usageStats['source'] = 'api_response_headers';
            
            $this->logger->info("Retrieved ipRegistry usage stats", $usageStats);
            
        } catch (IpRegistryApiException $e) {
            $usageStats['last_error'] = $e->getMessage();
            $this->logger->error("Failed to get ipRegistry usage stats", [
                'error' => $e->getMessage()
            ]);
        }
        
        return $usageStats;
    }
    
    /**
     * Get API connection statistics
     */
    public function getConnectionStats(): array
    {
        return [
            'base_url' => $this->baseUrl,
            'timeout_seconds' => $this->timeout,
            'retry_attempts' => $this->retryAttempts,
            'retry_delay_seconds' => $this->retryDelay,
            'api_key_configured' => !empty($this->apiKey),
            'api_key_masked' => $this->getApiKeyMasked()
        ];
    }
    
    /**
     * Test specific endpoint or IP lookup
     */
    public function testIpLookup(string $testIp, array $fields = []): array
    {
        $testResult = [
            'test_ip' => $testIp,
            'fields_requested' => $fields,
            'success' => false,
            'response_time_ms' => null,
            'error' => null,
            'response_data' => null,
            'timestamp' => date('c')
        ];
        
        if (!$this->isValidIp($testIp)) {
            $testResult['error'] = 'Invalid IP address format';
            return $testResult;
        }
        
        try {
            $startTime = microtime(true);
            $response = $this->getIpData($testIp, $fields);
            $testResult['response_time_ms'] = round((microtime(true) - $startTime) * 1000, 2);
            $testResult['success'] = true;
            $testResult['response_data'] = $response;
            
        } catch (IpRegistryApiException $e) {
            $testResult['response_time_ms'] = round((microtime(true) - $startTime) * 1000, 2);
            $testResult['error'] = $e->getMessage();
        }
        
        return $testResult;
    }
    
    /**
     * Perform comprehensive API monitoring check
     */
    public function performMonitoringCheck(): array
    {
        $monitoringResult = [
            'overall_status' => 'unknown',
            'timestamp' => date('c'),
            'checks' => [],
            'summary' => [
                'total_checks' => 0,
                'passed_checks' => 0,
                'failed_checks' => 0,
                'average_response_time_ms' => 0
            ]
        ];
        
        // Define monitoring checks
        $testIps = [
            'google_dns' => '*******',
            'cloudflare_dns' => '*******',
            'ipv6_test' => '2001:4860:4860::8888'
        ];
        
        $checks = [
            'health_check' => ['method' => 'healthCheck', 'critical' => true],
            'usage_stats' => ['method' => 'getUsageStats', 'critical' => false]
        ];
        
        // Add IP lookup tests
        foreach ($testIps as $testName => $testIp) {
            $checks["ip_lookup_{$testName}"] = [
                'method' => 'testIpLookup',
                'params' => [$testIp, ['ip', 'location.country']],
                'critical' => $testName === 'google_dns' // Only Google DNS test is critical
            ];
        }
        
        $totalResponseTime = 0;
        $responseTimeCount = 0;
        
        foreach ($checks as $checkName => $checkConfig) {
            $monitoringResult['checks'][$checkName] = [
                'name' => $checkName,
                'critical' => $checkConfig['critical'],
                'status' => 'unknown',
                'details' => []
            ];
            
            try {
                $result = null;
                
                switch ($checkConfig['method']) {
                    case 'healthCheck':
                        $result = $this->healthCheck();
                        $monitoringResult['checks'][$checkName]['status'] = 
                            ($result['api_reachable'] && $result['api_key_valid']) ? 'passed' : 'failed';
                        
                        if ($result['response_time_ms'] !== null) {
                            $totalResponseTime += $result['response_time_ms'];
                            $responseTimeCount++;
                        }
                        break;
                        
                    case 'getUsageStats':
                        $result = $this->getUsageStats();
                        $monitoringResult['checks'][$checkName]['status'] = 
                            isset($result['last_error']) ? 'failed' : 'passed';
                        break;
                        
                    case 'testIpLookup':
                        $result = $this->testIpLookup(...$checkConfig['params']);
                        $monitoringResult['checks'][$checkName]['status'] = $result['success'] ? 'passed' : 'failed';
                        
                        if ($result['response_time_ms'] !== null) {
                            $totalResponseTime += $result['response_time_ms'];
                            $responseTimeCount++;
                        }
                        break;
                }
                
                $monitoringResult['checks'][$checkName]['details'] = $result;
                
            } catch (\Exception $e) {
                $monitoringResult['checks'][$checkName]['status'] = 'failed';
                $monitoringResult['checks'][$checkName]['details'] = [
                    'error' => $e->getMessage(),
                    'exception_type' => get_class($e)
                ];
            }
        }
        
        // Calculate summary
        $monitoringResult['summary']['total_checks'] = count($checks);
        $monitoringResult['summary']['passed_checks'] = count(array_filter(
            $monitoringResult['checks'], 
            fn($check) => $check['status'] === 'passed'
        ));
        $monitoringResult['summary']['failed_checks'] = count(array_filter(
            $monitoringResult['checks'], 
            fn($check) => $check['status'] === 'failed'
        ));
        
        if ($responseTimeCount > 0) {
            $monitoringResult['summary']['average_response_time_ms'] = round($totalResponseTime / $responseTimeCount, 2);
        }
        
        // Determine overall status
        $criticalChecks = array_filter($checks, fn($check) => $check['critical']);
        $criticalChecksPassed = count(array_filter(
            $monitoringResult['checks'],
            fn($check) => $check['critical'] && $check['status'] === 'passed'
        ));
        
        if ($criticalChecksPassed === count($criticalChecks)) {
            $monitoringResult['overall_status'] = 'healthy';
        } elseif ($criticalChecksPassed > 0) {
            $monitoringResult['overall_status'] = 'degraded';
        } else {
            $monitoringResult['overall_status'] = 'unhealthy';
        }
        
        $this->logger->info("ipRegistry API monitoring check completed", [
            'overall_status' => $monitoringResult['overall_status'],
            'passed_checks' => $monitoringResult['summary']['passed_checks'],
            'failed_checks' => $monitoringResult['summary']['failed_checks'],
            'average_response_time' => $monitoringResult['summary']['average_response_time_ms']
        ]);
        
        return $monitoringResult;
    }
    
    /**
     * Batch validate multiple IP addresses
     */
    public function batchValidateIps(array $ips): array
    {
        $results = [
            'valid_ipv4' => [],
            'valid_ipv6' => [],
            'invalid' => [],
            'summary' => [
                'total' => count($ips),
                'valid_ipv4_count' => 0,
                'valid_ipv6_count' => 0,
                'invalid_count' => 0
            ]
        ];
        
        foreach ($ips as $ip) {
            if ($this->isIpv4($ip)) {
                $results['valid_ipv4'][] = $ip;
            } elseif ($this->isIpv6($ip)) {
                $results['valid_ipv6'][] = $ip;
            } else {
                $results['invalid'][] = $ip;
            }
        }
        
        $results['summary']['valid_ipv4_count'] = count($results['valid_ipv4']);
        $results['summary']['valid_ipv6_count'] = count($results['valid_ipv6']);
        $results['summary']['invalid_count'] = count($results['invalid']);
        
        return $results;
    }
    
    /**
     * Set API key
     */
    public function setApiKey(string $apiKey): void
    {
        $this->apiKey = $apiKey;
    }
    
    /**
     * Get current API key (masked for security)
     */
    public function getApiKeyMasked(): string
    {
        if (strlen($this->apiKey) <= 8) {
            return str_repeat('*', strlen($this->apiKey));
        }
        
        return substr($this->apiKey, 0, 4) . str_repeat('*', strlen($this->apiKey) - 8) . substr($this->apiKey, -4);
    }
    
    /**
     * Get API client configuration
     */
    public function getClientConfiguration(): array
    {
        return [
            'base_url' => $this->baseUrl,
            'timeout_seconds' => $this->timeout,
            'retry_attempts' => $this->retryAttempts,
            'retry_delay_seconds' => $this->retryDelay,
            'api_key_configured' => !empty($this->apiKey),
            'api_key_masked' => $this->getApiKeyMasked(),
            'supported_ip_types' => ['IPv4', 'IPv6'],
            'max_batch_size' => 100
        ];
    }
    
    /**
     * Reset client configuration
     */
    public function updateConfiguration(array $config): void
    {
        if (isset($config['timeout'])) {
            $this->timeout = (int) $config['timeout'];
        }
        
        if (isset($config['retry_attempts'])) {
            $this->retryAttempts = (int) $config['retry_attempts'];
        }
        
        if (isset($config['retry_delay'])) {
            $this->retryDelay = (int) $config['retry_delay'];
        }
        
        if (isset($config['api_key'])) {
            $this->setApiKey($config['api_key']);
        }
        
        // Recreate HTTP client with new configuration
        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => $this->timeout,
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'User-Agent' => 'GuardGeo-API/1.0'
            ]
        ]);
        
        $this->logger->info("ipRegistry API client configuration updated", $config);
    }
    
    /**
     * Validate IP address format
     */
    public function isValidIp(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    /**
     * Check if IP is IPv4
     */
    public function isIpv4(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false;
    }
    
    /**
     * Check if IP is IPv6
     */
    public function isIpv6(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) !== false;
    }
    
    /**
     * Get IP type (IPv4 or IPv6)
     */
    public function getIpType(string $ip): string
    {
        if ($this->isIpv4($ip)) {
            return 'IPv4';
        } elseif ($this->isIpv6($ip)) {
            return 'IPv6';
        } else {
            throw new IpRegistryApiException("Invalid IP address: {$ip}");
        }
    }
}

/**
 * Custom exception for ipRegistry API errors
 */
class IpRegistryApiException extends \Exception
{
    public function __construct(string $message, int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}