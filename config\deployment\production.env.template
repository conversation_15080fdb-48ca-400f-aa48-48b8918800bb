# GuardGeo Admin Platform - Production Environment Configuration Template
# 
# Copy this file to .env and update all values with your production settings.
# DO NOT commit the actual .env file to version control.

# Application Settings
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-production-domain.com
APP_ENCRYPTION_KEY=your_32_character_encryption_key_here

# Database Configuration
DB_HOST=your-production-db-host
DB_PORT=5432
DB_NAME=guardgeo_production
DB_USERNAME=guardgeo_prod
DB_PASSWORD=your_secure_database_password
DB_SSL_CA=/path/to/ssl/ca-certificate.crt
DB_SSL_VERIFY=true

# Redis Configuration (for caching and sessions)
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# External API Keys
FREEMIUS_API_TOKEN=your_production_freemius_api_token
IPREGISTRY_API_KEY=your_production_ipregistry_api_key

# Security Settings
SESSION_DOMAIN=your-production-domain.com
SESSION_SECURE=true
CSRF_PROTECTION=true
RATE_LIMITING_ENABLED=true

# Email Configuration
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-smtp-username
MAIL_PASSWORD=your-smtp-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="GuardGeo Admin Platform"

# Monitoring and Alerting
ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK=https://your-webhook-url.com/alerts
SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Backup Configuration
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
BACKUP_S3_KEY=your-aws-access-key
BACKUP_S3_SECRET=your-aws-secret-key
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key

# Performance Monitoring
PHP_FPM_STATUS_URL=http://localhost/php-fpm-status?json

# Maintenance Mode
MAINTENANCE_SECRET=your_maintenance_bypass_secret

# CDN Configuration (optional)
CDN_URL=https://cdn.server-domain.tld

# Logging Configuration
LOG_LEVEL=error
LOG_MAX_FILES=30
LOG_MAX_SIZE=100MB

# Feature Flags
ENABLE_IP_INTELLIGENCE_CACHING=true
ENABLE_FREEMIUS_SYNC=true
ENABLE_AUDIT_LOGGING=true
ENABLE_RATE_LIMITING=true
ENABLE_SECURITY_MONITORING=true
ENABLE_BACKUP_AUTOMATION=true
ENABLE_HEALTH_CHECKS=true
ENABLE_PERFORMANCE_MONITORING=true

# Admin User Domain Whitelist (comma-separated)
ADMIN_ALLOWED_DOMAINS=your-company.com,trusted-partner.com

# IP Security (comma-separated CIDR ranges)
ALLOWED_IP_RANGES=***********/24,10.0.0.0/8
BLOCKED_IP_RANGES=

# SSL/TLS Configuration
SSL_CERTIFICATE_PATH=/path/to/ssl/certificate.crt
SSL_PRIVATE_KEY_PATH=/path/to/ssl/private.key
SSL_CA_BUNDLE_PATH=/path/to/ssl/ca-bundle.crt

# Database Connection Pool Settings
DB_MIN_CONNECTIONS=5
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=30
DB_IDLE_TIMEOUT=300

# Cache TTL Settings (in seconds)
CACHE_TTL_IP_INTELLIGENCE=86400
CACHE_TTL_FREEMIUS_DATA=3600
CACHE_TTL_SESSION_DATA=1800

# Rate Limiting Settings
RATE_LIMIT_API_REQUESTS=100
RATE_LIMIT_LOGIN_ATTEMPTS=5
RATE_LIMIT_TIME_WINDOW=3600

# Backup Retention Settings
BACKUP_RETENTION_DAILY=7
BACKUP_RETENTION_WEEKLY=4
BACKUP_RETENTION_MONTHLY=12

# Performance Thresholds
ALERT_THRESHOLD_RESPONSE_TIME=2000
ALERT_THRESHOLD_MEMORY_USAGE=80
ALERT_THRESHOLD_ERROR_RATE=5
ALERT_THRESHOLD_DISK_USAGE=85

# External Service Timeouts (in seconds)
FREEMIUS_API_TIMEOUT=30
IPREGISTRY_API_TIMEOUT=10
WEBHOOK_TIMEOUT=10

# Compliance Settings
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=365
ANONYMIZE_LOGS=true
AUDIT_TRAIL=true
DATA_ENCRYPTION_AT_REST=true

# Development/Debug Settings (should be false in production)
ENABLE_DEBUG_TOOLBAR=false
ENABLE_QUERY_LOGGING=false
ENABLE_PROFILING=false
MOCK_EXTERNAL_APIS=false