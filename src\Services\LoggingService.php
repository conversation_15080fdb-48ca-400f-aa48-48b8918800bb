<?php

namespace Skpassegna\GuardgeoApi\Services;

use DateTime;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Database\DatabaseException;
use Skpassegna\GuardgeoApi\Utils\Logger;

/**
 * Comprehensive Logging Service
 * 
 * Provides multi-level logging with file and database output support.
 * Implements separate loggers for API requests, admin actions, and system errors
 * with structured logging, categorization, and filtering capabilities.
 */
class LoggingService
{
    private Logger $fileLogger;
    private array $loggers = [];
    private bool $databaseLoggingEnabled = true;
    
    // Log types for categorization
    public const TYPE_API = 'api';
    public const TYPE_ADMIN = 'admin';
    public const TYPE_ERROR = 'error';
    public const TYPE_SYSTEM = 'system';
    
    // Log levels
    public const LEVEL_DEBUG = 'debug';
    public const LEVEL_INFO = 'info';
    public const LEVEL_WARNING = 'warning';
    public const LEVEL_ERROR = 'error';
    public const LEVEL_CRITICAL = 'critical';
    
    private const LOG_LEVELS = [
        self::LEVEL_DEBUG => 0,
        self::LEVEL_INFO => 1,
        self::LEVEL_WARNING => 2,
        self::LEVEL_ERROR => 3,
        self::LEVEL_CRITICAL => 4
    ];
    
    public function __construct(bool $enableDatabaseLogging = true)
    {
        $this->fileLogger = new Logger();
        $this->databaseLoggingEnabled = $enableDatabaseLogging;
        
        // Initialize specialized loggers
        $this->initializeLoggers();
    }
    
    /**
     * Initialize specialized loggers for different types
     */
    private function initializeLoggers(): void
    {
        $this->loggers = [
            self::TYPE_API => new ApiLogger($this),
            self::TYPE_ADMIN => new AdminLogger($this),
            self::TYPE_ERROR => new ErrorLogger($this),
            self::TYPE_SYSTEM => new SystemLogger($this)
        ];
    }
    
    /**
     * Get specialized logger by type
     */
    public function getLogger(string $type): LoggerInterface
    {
        if (!isset($this->loggers[$type])) {
            throw new \InvalidArgumentException("Unknown logger type: $type");
        }
        
        return $this->loggers[$type];
    }
    
    /**
     * Log message with specified type and level
     */
    public function log(string $type, string $level, string $message, array $context = []): void
    {
        // Validate inputs
        if (!$this->isValidType($type)) {
            throw new \InvalidArgumentException("Invalid log type: $type");
        }
        
        if (!$this->isValidLevel($level)) {
            throw new \InvalidArgumentException("Invalid log level: $level");
        }
        
        // Add metadata to context
        $enrichedContext = $this->enrichContext($context, $type);
        
        // Log to file
        $this->logToFile($type, $level, $message, $enrichedContext);
        
        // Log to database if enabled
        if ($this->databaseLoggingEnabled) {
            $this->logToDatabase($type, $level, $message, $enrichedContext);
        }
    }
    
    /**
     * Log API request/response
     */
    public function logApiRequest(array $requestData, array $responseData, int $responseTime = null): void
    {
        $context = [
            'request' => $requestData,
            'response' => $responseData,
            'response_time_ms' => $responseTime,
            'ip' => $requestData['ip'] ?? null,
            'plugin_id' => $requestData['plugin_id'] ?? null,
            'install_id' => $requestData['install_id'] ?? null
        ];
        
        $message = sprintf(
            'API request processed - Status: %d, IP: %s, Plugin: %s',
            $responseData['status'] ?? 'unknown',
            $requestData['ip'] ?? 'unknown',
            $requestData['plugin_id'] ?? 'unknown'
        );
        
        $this->log(self::TYPE_API, self::LEVEL_INFO, $message, $context);
        
        // Also log to specialized API request table
        $this->logApiRequestToDatabase($requestData, $responseData, $responseTime);
    }
    
    /**
     * Log admin action with comprehensive context and timestamps
     */
    public function logAdminAction(int $userId, string $action, array $details = []): void
    {
        $context = [
            'user_id' => $userId,
            'action' => $action,
            'details' => $details,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'session_id' => session_id() ?: null,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? null,
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? null,
            'timestamp' => (new \DateTime())->format('c'),
            'server_time' => date('Y-m-d H:i:s'),
            'timezone' => date_default_timezone_get()
        ];
        
        $message = sprintf('Admin action: %s by user %d', $action, $userId);
        
        $this->log(self::TYPE_ADMIN, self::LEVEL_INFO, $message, $context);
    }
    
    /**
     * Log comprehensive admin action with before/after states
     */
    public function logComprehensiveAdminAction(int $userId, string $action, array $beforeState = [], array $afterState = [], array $metadata = []): void
    {
        $context = [
            'user_id' => $userId,
            'action' => $action,
            'before_state' => $this->sanitizeStateData($beforeState),
            'after_state' => $this->sanitizeStateData($afterState),
            'changes' => $this->calculateChanges($beforeState, $afterState),
            'metadata' => $metadata,
            'request_info' => [
                'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'session_id' => session_id() ?: null,
                'request_uri' => $_SERVER['REQUEST_URI'] ?? null,
                'request_method' => $_SERVER['REQUEST_METHOD'] ?? null,
                'referer' => $_SERVER['HTTP_REFERER'] ?? null
            ],
            'timing' => [
                'timestamp' => (new \DateTime())->format('c'),
                'server_time' => date('Y-m-d H:i:s'),
                'timezone' => date_default_timezone_get(),
                'execution_time' => $metadata['execution_time'] ?? null
            ],
            'system_info' => [
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true),
                'php_version' => PHP_VERSION
            ]
        ];
        
        $changesCount = count($context['changes']);
        $message = sprintf(
            'Admin action: %s by user %d (%d changes made)',
            $action,
            $userId,
            $changesCount
        );
        
        $this->log(self::TYPE_ADMIN, self::LEVEL_INFO, $message, $context);
    }
    
    /**
     * Sanitize state data for logging (remove sensitive information)
     */
    private function sanitizeStateData(array $data): array
    {
        $sanitized = [];
        $sensitiveFields = ['password', 'password_hash', 'token', 'secret', 'key', 'api_key'];
        
        foreach ($data as $key => $value) {
            if (in_array(strtolower($key), $sensitiveFields)) {
                $sanitized[$key] = '[REDACTED]';
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitizeStateData($value);
            } elseif (is_string($value) && strlen($value) > 500) {
                $sanitized[$key] = substr($value, 0, 497) . '...';
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Calculate changes between before and after states
     */
    private function calculateChanges(array $before, array $after): array
    {
        $changes = [];
        
        // Find added fields
        foreach ($after as $key => $value) {
            if (!array_key_exists($key, $before)) {
                $changes[] = [
                    'type' => 'added',
                    'field' => $key,
                    'new_value' => $this->sanitizeValue($value)
                ];
            }
        }
        
        // Find removed fields
        foreach ($before as $key => $value) {
            if (!array_key_exists($key, $after)) {
                $changes[] = [
                    'type' => 'removed',
                    'field' => $key,
                    'old_value' => $this->sanitizeValue($value)
                ];
            }
        }
        
        // Find modified fields
        foreach ($before as $key => $oldValue) {
            if (array_key_exists($key, $after) && $after[$key] !== $oldValue) {
                $changes[] = [
                    'type' => 'modified',
                    'field' => $key,
                    'old_value' => $this->sanitizeValue($oldValue),
                    'new_value' => $this->sanitizeValue($after[$key])
                ];
            }
        }
        
        return $changes;
    }
    
    /**
     * Sanitize individual values for logging
     */
    private function sanitizeValue($value): string
    {
        if (is_null($value)) {
            return 'null';
        }
        
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }
        
        if (is_array($value) || is_object($value)) {
            return json_encode($value);
        }
        
        $stringValue = (string)$value;
        
        // Truncate long values
        if (strlen($stringValue) > 100) {
            return substr($stringValue, 0, 97) . '...';
        }
        
        return $stringValue;
    }
    
    /**
     * Log system error with categorization and severity
     */
    public function logError(\Throwable $exception, array $context = []): void
    {
        $category = $this->categorizeError($exception);
        $severity = $this->determineSeverity($exception, $context);
        
        $errorContext = [
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $this->formatStackTrace($exception->getTrace()),
            'category' => $category,
            'severity' => $severity,
            'context' => $context,
            'error_code' => $exception->getCode(),
            'previous_exception' => $exception->getPrevious() ? [
                'class' => get_class($exception->getPrevious()),
                'message' => $exception->getPrevious()->getMessage(),
                'file' => $exception->getPrevious()->getFile(),
                'line' => $exception->getPrevious()->getLine()
            ] : null
        ];
        
        $message = sprintf(
            '[%s] %s: %s in %s:%d',
            strtoupper($category),
            strtoupper($severity),
            $exception->getMessage(),
            basename($exception->getFile()),
            $exception->getLine()
        );
        
        $level = $this->mapSeverityToLevel($severity);
        $this->log(self::TYPE_ERROR, $level, $message, $errorContext);
    }
    
    /**
     * Log system event
     */
    public function logSystemEvent(string $event, array $details = []): void
    {
        $context = [
            'event' => $event,
            'details' => $details,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
        
        $message = sprintf('System event: %s', $event);
        
        $this->log(self::TYPE_SYSTEM, self::LEVEL_INFO, $message, $context);
    }
    
    /**
     * Log performance metrics
     */
    public function logPerformance(string $operation, float $duration, array $metrics = []): void
    {
        $context = [
            'operation' => $operation,
            'duration_ms' => round($duration * 1000, 2),
            'metrics' => $metrics,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
        
        $message = sprintf(
            'Performance: %s completed in %.2fms',
            $operation,
            $context['duration_ms']
        );
        
        $level = $duration > 1.0 ? self::LEVEL_WARNING : self::LEVEL_INFO;
        $this->log(self::TYPE_SYSTEM, $level, $message, $context);
    }
    
    /**
     * Enrich context with metadata
     */
    private function enrichContext(array $context, string $type): array
    {
        return array_merge($context, [
            'timestamp' => (new DateTime())->format('c'),
            'type' => $type,
            'request_id' => $this->getRequestId(),
            'process_id' => getmypid(),
            'memory_usage' => memory_get_usage(true)
        ]);
    }
    
    /**
     * Log to file using existing Logger utility
     */
    private function logToFile(string $type, string $level, string $message, array $context): void
    {
        try {
            $formattedMessage = sprintf('[%s] %s', strtoupper($type), $message);
            $this->fileLogger->log($level, $formattedMessage, $context);
        } catch (\Exception $e) {
            // Fallback to error_log if file logging fails
            error_log("LoggingService: File logging failed - " . $e->getMessage());
        }
    }
    
    /**
     * Log to database
     */
    private function logToDatabase(string $type, string $level, string $message, array $context): void
    {
        try {
            // Avoid infinite recursion by checking if this is already a database operation
            if ($this->isDatabaseOperation($context)) {
                return;
            }
            
            $sql = "INSERT INTO system_logs (type, level, message, context, ip, user_id, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
            
            $params = [
                $type,
                $level,
                $message,
                json_encode($context, JSON_UNESCAPED_SLASHES),
                $context['ip'] ?? null,
                $context['user_id'] ?? null
            ];
            
            DatabaseConnection::execute($sql, $params);
            
        } catch (DatabaseException $e) {
            // Silently fail database logging to avoid infinite loops
            // Log to file instead
            error_log("LoggingService: Database logging failed - " . $e->getMessage());
        } catch (\Exception $e) {
            error_log("LoggingService: Unexpected error in database logging - " . $e->getMessage());
        }
    }
    
    /**
     * Log API request to specialized table with comprehensive fields
     */
    private function logApiRequestToDatabase(array $requestData, array $responseData, int $responseTime = null): void
    {
        try {
            $sql = "INSERT INTO api_requests (ip, visitor_hash, plugin_id, install_id, url, 
                    response_status, response_time_ms, freemius_valid, error_message, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
            
            $params = [
                $requestData['ip'] ?? null,
                $requestData['visitor_hash'] ?? null,
                $requestData['plugin_id'] ?? null,
                $requestData['install_id'] ?? null,
                $requestData['url'] ?? null,
                $responseData['status'] ?? null,
                $responseTime,
                $responseData['freemius_valid'] ?? null,
                $responseData['error_message'] ?? null
            ];
            
            DatabaseConnection::execute($sql, $params);
            
        } catch (DatabaseException $e) {
            error_log("LoggingService: API request logging failed - " . $e->getMessage());
        }
    }
    
    /**
     * Log comprehensive API request with all required fields
     */
    public function logComprehensiveApiRequest(array $requestData, array $responseData, array $metadata = []): void
    {
        $context = [
            'request' => [
                'ip' => $requestData['ip'] ?? null,
                'visitor_hash' => $requestData['visitor_hash'] ?? null,
                'plugin_id' => $requestData['plugin_id'] ?? null,
                'install_id' => $requestData['install_id'] ?? null,
                'url' => $requestData['url'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'referer' => $_SERVER['HTTP_REFERER'] ?? null,
                'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'POST',
                'content_type' => $_SERVER['CONTENT_TYPE'] ?? null,
                'content_length' => $_SERVER['CONTENT_LENGTH'] ?? null
            ],
            'response' => [
                'status' => $responseData['status'] ?? null,
                'success' => $responseData['success'] ?? false,
                'freemius_valid' => $responseData['freemius_valid'] ?? null,
                'error_code' => $responseData['error_code'] ?? null,
                'error_message' => $responseData['error_message'] ?? null,
                'data_size' => isset($responseData['data']) ? strlen(json_encode($responseData['data'])) : null
            ],
            'performance' => [
                'response_time_ms' => $metadata['response_time_ms'] ?? null,
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true),
                'database_queries' => $metadata['database_queries'] ?? null,
                'cache_hits' => $metadata['cache_hits'] ?? null,
                'cache_misses' => $metadata['cache_misses'] ?? null
            ],
            'validation' => [
                'freemius_check_time' => $metadata['freemius_check_time'] ?? null,
                'ip_lookup_time' => $metadata['ip_lookup_time'] ?? null,
                'validation_steps' => $metadata['validation_steps'] ?? []
            ],
            'security' => [
                'rate_limit_remaining' => $metadata['rate_limit_remaining'] ?? null,
                'security_flags' => $metadata['security_flags'] ?? [],
                'threat_level' => $metadata['threat_level'] ?? null
            ]
        ];
        
        $message = sprintf(
            'API Request: %s from %s (Plugin: %s, Install: %s) -> %d in %dms',
            $_SERVER['REQUEST_METHOD'] ?? 'POST',
            $requestData['ip'] ?? 'unknown',
            $requestData['plugin_id'] ?? 'unknown',
            $requestData['install_id'] ?? 'unknown',
            $responseData['status'] ?? 0,
            $metadata['response_time_ms'] ?? 0
        );
        
        $level = ($responseData['status'] ?? 0) >= 400 ? self::LEVEL_WARNING : self::LEVEL_INFO;
        $this->log(self::TYPE_API, $level, $message, $context);
        
        // Also log to specialized API request table
        $this->logApiRequestToDatabase($requestData, $responseData, $metadata['response_time_ms'] ?? null);
    }
    
    /**
     * Check if this is a database operation to avoid recursion
     */
    private function isDatabaseOperation(array $context): bool
    {
        $contextStr = json_encode($context);
        return str_contains($contextStr, 'database') ||
               str_contains($contextStr, 'sql') ||
               str_contains($contextStr, 'query') ||
               isset($context['sql']) ||
               isset($context['database']);
    }
    
    /**
     * Generate or get request ID for tracing
     */
    private function getRequestId(): string
    {
        static $requestId = null;
        
        if ($requestId === null) {
            $requestId = uniqid('req_', true);
        }
        
        return $requestId;
    }
    
    /**
     * Validate log type
     */
    private function isValidType(string $type): bool
    {
        return in_array($type, [self::TYPE_API, self::TYPE_ADMIN, self::TYPE_ERROR, self::TYPE_SYSTEM]);
    }
    
    /**
     * Validate log level
     */
    private function isValidLevel(string $level): bool
    {
        return isset(self::LOG_LEVELS[$level]);
    }
    
    /**
     * Get log entries from database with filtering
     */
    public function getLogEntries(array $filters = [], int $limit = 100, int $offset = 0): array
    {
        try {
            $sql = "SELECT * FROM system_logs WHERE 1=1";
            $params = [];
            $paramIndex = 1;
            
            // Apply filters
            if (!empty($filters['type'])) {
                $sql .= " AND type = ?";
                $params[] = $filters['type'];
            }
            
            if (!empty($filters['level'])) {
                $sql .= " AND level = ?";
                $params[] = $filters['level'];
            }
            
            if (!empty($filters['user_id'])) {
                $sql .= " AND user_id = ?";
                $params[] = $filters['user_id'];
            }
            
            if (!empty($filters['from_date'])) {
                $sql .= " AND created_at >= ?";
                $params[] = $filters['from_date'];
            }
            
            if (!empty($filters['to_date'])) {
                $sql .= " AND created_at <= ?";
                $params[] = $filters['to_date'];
            }
            
            if (!empty($filters['search'])) {
                $sql .= " AND (message ILIKE ? OR context::text ILIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $statement = DatabaseConnection::execute($sql, $params);
            return $statement->fetchAll();
            
        } catch (DatabaseException $e) {
            error_log("LoggingService: Failed to retrieve log entries - " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get API request logs with filtering
     */
    public function getApiRequestLogs(array $filters = [], int $limit = 100, int $offset = 0): array
    {
        try {
            $sql = "SELECT * FROM api_requests WHERE 1=1";
            $params = [];
            
            // Apply filters
            if (!empty($filters['ip'])) {
                $sql .= " AND ip = ?";
                $params[] = $filters['ip'];
            }
            
            if (!empty($filters['plugin_id'])) {
                $sql .= " AND plugin_id = ?";
                $params[] = $filters['plugin_id'];
            }
            
            if (!empty($filters['response_status'])) {
                $sql .= " AND response_status = ?";
                $params[] = $filters['response_status'];
            }
            
            if (!empty($filters['from_date'])) {
                $sql .= " AND created_at >= ?";
                $params[] = $filters['from_date'];
            }
            
            if (!empty($filters['to_date'])) {
                $sql .= " AND created_at <= ?";
                $params[] = $filters['to_date'];
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $statement = DatabaseConnection::execute($sql, $params);
            return $statement->fetchAll();
            
        } catch (DatabaseException $e) {
            error_log("LoggingService: Failed to retrieve API request logs - " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get logging statistics
     */
    public function getLoggingStats(): array
    {
        try {
            // Get system logs stats
            $systemLogsSql = "SELECT type, level, COUNT(*) as count 
                             FROM system_logs 
                             WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
                             GROUP BY type, level";
            $systemLogsStmt = DatabaseConnection::execute($systemLogsSql);
            $systemLogsStats = $systemLogsStmt->fetchAll();
            
            // Get API request stats
            $apiStatsSql = "SELECT response_status, COUNT(*) as count,
                           AVG(response_time_ms) as avg_response_time
                           FROM api_requests 
                           WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
                           GROUP BY response_status";
            $apiStatsStmt = DatabaseConnection::execute($apiStatsSql);
            $apiStats = $apiStatsStmt->fetchAll();
            
            // Get file logging stats
            $fileStats = $this->fileLogger->getLogStats();
            
            return [
                'system_logs' => $systemLogsStats,
                'api_requests' => $apiStats,
                'file_logs' => $fileStats,
                'database_enabled' => $this->databaseLoggingEnabled
            ];
            
        } catch (DatabaseException $e) {
            error_log("LoggingService: Failed to retrieve logging stats - " . $e->getMessage());
            return [
                'error' => 'Failed to retrieve statistics',
                'file_logs' => $this->fileLogger->getLogStats(),
                'database_enabled' => $this->databaseLoggingEnabled
            ];
        }
    }
    
    /**
     * Clear old log entries
     */
    public function clearOldLogs(int $daysToKeep = 30): int
    {
        try {
            $cutoffDate = (new DateTime())->modify("-{$daysToKeep} days")->format('Y-m-d H:i:s');
            
            // Clear system logs
            $systemLogsSql = "DELETE FROM system_logs WHERE created_at < ?";
            $systemLogsStmt = DatabaseConnection::execute($systemLogsSql, [$cutoffDate]);
            $systemLogsDeleted = $systemLogsStmt->rowCount();
            
            // Clear API request logs
            $apiLogsSql = "DELETE FROM api_requests WHERE created_at < ?";
            $apiLogsStmt = DatabaseConnection::execute($apiLogsSql, [$cutoffDate]);
            $apiLogsDeleted = $apiLogsStmt->rowCount();
            
            $totalDeleted = $systemLogsDeleted + $apiLogsDeleted;
            
            $this->logSystemEvent('log_cleanup', [
                'days_kept' => $daysToKeep,
                'system_logs_deleted' => $systemLogsDeleted,
                'api_logs_deleted' => $apiLogsDeleted,
                'total_deleted' => $totalDeleted
            ]);
            
            return $totalDeleted;
            
        } catch (DatabaseException $e) {
            error_log("LoggingService: Failed to clear old logs - " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Enable or disable database logging
     */
    public function setDatabaseLogging(bool $enabled): void
    {
        $this->databaseLoggingEnabled = $enabled;
        $this->logSystemEvent('database_logging_changed', ['enabled' => $enabled]);
    }
    
    /**
     * Categorize error based on exception type and context
     */
    private function categorizeError(\Throwable $exception): string
    {
        $exceptionClass = get_class($exception);
        
        // Database errors
        if (str_contains($exceptionClass, 'Database') || 
            str_contains($exceptionClass, 'PDO') ||
            str_contains($exception->getMessage(), 'database') ||
            str_contains($exception->getMessage(), 'SQL')) {
            return 'database';
        }
        
        // Network/API errors
        if (str_contains($exceptionClass, 'Guzzle') ||
            str_contains($exceptionClass, 'Http') ||
            str_contains($exceptionClass, 'Curl') ||
            str_contains($exception->getMessage(), 'curl') ||
            str_contains($exception->getMessage(), 'timeout')) {
            return 'network';
        }
        
        // Authentication/Authorization errors
        if (str_contains($exceptionClass, 'Auth') ||
            str_contains($exception->getMessage(), 'authentication') ||
            str_contains($exception->getMessage(), 'authorization') ||
            str_contains($exception->getMessage(), 'permission')) {
            return 'security';
        }
        
        // Validation errors
        if (str_contains($exceptionClass, 'Validation') ||
            str_contains($exception->getMessage(), 'validation') ||
            str_contains($exception->getMessage(), 'invalid')) {
            return 'validation';
        }
        
        // File system errors
        if (str_contains($exception->getMessage(), 'file') ||
            str_contains($exception->getMessage(), 'directory') ||
            str_contains($exception->getMessage(), 'permission denied')) {
            return 'filesystem';
        }
        
        // Configuration errors
        if (str_contains($exception->getMessage(), 'config') ||
            str_contains($exception->getMessage(), 'environment') ||
            str_contains($exception->getMessage(), 'setting')) {
            return 'configuration';
        }
        
        // Memory/Resource errors
        if (str_contains($exception->getMessage(), 'memory') ||
            str_contains($exception->getMessage(), 'resource') ||
            str_contains($exception->getMessage(), 'limit')) {
            return 'resource';
        }
        
        return 'application';
    }
    
    /**
     * Determine error severity based on exception and context
     */
    private function determineSeverity(\Throwable $exception, array $context): string
    {
        $exceptionClass = get_class($exception);
        $message = strtolower($exception->getMessage());
        
        // Critical severity indicators
        if (str_contains($message, 'fatal') ||
            str_contains($message, 'critical') ||
            str_contains($message, 'out of memory') ||
            str_contains($message, 'segmentation fault') ||
            $exceptionClass === 'Error' ||
            $exceptionClass === 'ParseError') {
            return 'critical';
        }
        
        // High severity indicators
        if (str_contains($message, 'database') ||
            str_contains($message, 'connection') ||
            str_contains($message, 'authentication') ||
            str_contains($message, 'security') ||
            isset($context['security_incident'])) {
            return 'high';
        }
        
        // Medium severity indicators
        if (str_contains($message, 'validation') ||
            str_contains($message, 'timeout') ||
            str_contains($message, 'not found') ||
            str_contains($message, 'permission')) {
            return 'medium';
        }
        
        // Low severity (default)
        return 'low';
    }
    
    /**
     * Map severity to log level
     */
    private function mapSeverityToLevel(string $severity): string
    {
        return match($severity) {
            'critical' => self::LEVEL_CRITICAL,
            'high' => self::LEVEL_ERROR,
            'medium' => self::LEVEL_WARNING,
            'low' => self::LEVEL_INFO,
            default => self::LEVEL_ERROR
        };
    }
    
    /**
     * Format stack trace for logging
     */
    private function formatStackTrace(array $trace): array
    {
        $formatted = [];
        
        foreach (array_slice($trace, 0, 10) as $index => $frame) {
            $formatted[] = [
                'index' => $index,
                'file' => isset($frame['file']) ? basename($frame['file']) : 'unknown',
                'line' => $frame['line'] ?? 0,
                'function' => $frame['function'] ?? 'unknown',
                'class' => $frame['class'] ?? null,
                'type' => $frame['type'] ?? null
            ];
        }
        
        return $formatted;
    }
    
    /**
     * Log structured error with additional metadata
     */
    public function logStructuredError(string $category, string $severity, string $message, array $details = []): void
    {
        $context = [
            'category' => $category,
            'severity' => $severity,
            'details' => $details,
            'timestamp' => (new \DateTime())->format('c'),
            'request_id' => $this->getRequestId(),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        $formattedMessage = sprintf(
            '[%s] %s: %s',
            strtoupper($category),
            strtoupper($severity),
            $message
        );
        
        $level = $this->mapSeverityToLevel($severity);
        $this->log(self::TYPE_ERROR, $level, $formattedMessage, $context);
    }
    
    /**
     * Log security incident with enhanced tracking
     */
    public function logSecurityIncident(string $incident, string $severity, array $details = []): void
    {
        $context = array_merge($details, [
            'incident_type' => $incident,
            'severity' => $severity,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? null,
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? null,
            'timestamp' => (new \DateTime())->format('c'),
            'session_id' => session_id() ?: null
        ]);
        
        $message = sprintf(
            'SECURITY INCIDENT: %s (severity: %s)',
            $incident,
            strtoupper($severity)
        );
        
        $level = match($severity) {
            'critical' => self::LEVEL_CRITICAL,
            'high' => self::LEVEL_ERROR,
            'medium' => self::LEVEL_WARNING,
            default => self::LEVEL_INFO
        };
        
        $this->log(self::TYPE_ERROR, $level, $message, $context);
        
        // Also create a separate security log entry
        $this->logToSecurityLog($incident, $severity, $context);
    }
    
    /**
     * Log to separate security log (if configured)
     */
    private function logToSecurityLog(string $incident, string $severity, array $context): void
    {
        try {
            // Create security-specific log entry
            $sql = "INSERT INTO system_logs (type, level, message, context, ip, user_id, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
            
            $message = sprintf('SECURITY: %s', $incident);
            $level = $this->mapSeverityToLevel($severity);
            
            $params = [
                'security', // Special type for security incidents
                $level,
                $message,
                json_encode($context, JSON_UNESCAPED_SLASHES),
                $context['ip'] ?? null,
                $context['user_id'] ?? null
            ];
            
            DatabaseConnection::execute($sql, $params);
            
        } catch (\Exception $e) {
            // Silently fail - main logging already occurred
            error_log("LoggingService: Security log failed - " . $e->getMessage());
        }
    }