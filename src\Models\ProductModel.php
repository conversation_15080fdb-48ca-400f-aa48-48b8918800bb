<?php

namespace Skpassegna\GuardgeoApi\Models;

use DateTime;

/**
 * Freemius Product Model
 * 
 * Represents a Freemius product with complete field mapping
 * based on the Freemius OpenAPI specification.
 */
class ProductModel extends BaseModel
{
    // Core identification fields
    public ?int $id = null;
    public ?string $secret_key = null;
    public ?string $public_key = null;
    public ?DateTime $created = null;
    public ?DateTime $updated = null;
    
    // Product hierarchy and ownership
    public ?int $parent_plugin_id = null;
    public ?int $developer_id = null;
    public ?int $store_id = null;
    
    // Product information
    public ?string $slug = null;
    public ?string $title = null;
    public ?int $environment = null;
    public ?string $icon = null;
    
    // Plan and feature configuration
    public ?string $default_plan_id = null;
    public ?string $plans = null; // Comma separated plan IDs
    public ?string $features = null; // Comma separated feature IDs
    
    // Refund and discount policies
    public ?int $money_back_period = null;
    public ?string $refund_policy = null; // flexible, moderate, strict
    public ?int $annual_renewals_discount = null;
    public ?string $renewals_discount_type = null; // percentage, dollar
    
    // Product settings and flags
    public bool $is_released = false;
    public bool $is_sdk_required = false;
    public bool $is_pricing_visible = false;
    public bool $is_wp_org_compliant = false;
    
    // Statistics and metrics
    public int $installs_count = 0;
    public int $active_installs_count = 0;
    public int $free_releases_count = 0;
    public int $premium_releases_count = 0;
    public int $total_purchases = 0;
    public int $total_subscriptions = 0;
    public int $total_renewals = 0;
    public string $total_failed_purchases = '0';
    public string $earnings = '0';
    
    // Product type and classification
    public ?string $type = null; // plugin, theme, widget, template
    public bool $is_static = false;
    
    // Cache management
    public ?DateTime $cached_at = null;
    public ?array $raw_data = null; // Complete Freemius response

    /**
     * Constructor
     */
    public function __construct(array $data = [])
    {
        // Core identification
        $this->id = $this->toNullableInt($data['id'] ?? null);
        $this->secret_key = $this->toNullableString($data['secret_key'] ?? null);
        $this->public_key = $this->toNullableString($data['public_key'] ?? null);
        $this->created = isset($data['created']) ? $this->dbToDateTime($data['created']) : null;
        $this->updated = isset($data['updated']) ? $this->dbToDateTime($data['updated']) : null;
        
        // Product hierarchy and ownership
        $this->parent_plugin_id = $this->toNullableInt($data['parent_plugin_id'] ?? null);
        $this->developer_id = $this->toNullableInt($data['developer_id'] ?? null);
        $this->store_id = $this->toNullableInt($data['store_id'] ?? null);
        
        // Product information
        $this->slug = $this->toNullableString($data['slug'] ?? null);
        $this->title = $this->toNullableString($data['title'] ?? null);
        $this->environment = $this->toNullableInt($data['environment'] ?? null);
        $this->icon = $this->toNullableString($data['icon'] ?? null);
        
        // Plan and feature configuration
        $this->default_plan_id = $this->toNullableString($data['default_plan_id'] ?? null);
        $this->plans = $this->toNullableString($data['plans'] ?? null);
        $this->features = $this->toNullableString($data['features'] ?? null);
        
        // Refund and discount policies
        $this->money_back_period = $this->toNullableInt($data['money_back_period'] ?? null);
        $this->refund_policy = $this->toNullableString($data['refund_policy'] ?? null);
        $this->annual_renewals_discount = $this->toNullableInt($data['annual_renewals_discount'] ?? null);
        $this->renewals_discount_type = $this->toNullableString($data['renewals_discount_type'] ?? null);
        
        // Product settings and flags
        $this->is_released = $this->toBool($data['is_released'] ?? false);
        $this->is_sdk_required = $this->toBool($data['is_sdk_required'] ?? false);
        $this->is_pricing_visible = $this->toBool($data['is_pricing_visible'] ?? false);
        $this->is_wp_org_compliant = $this->toBool($data['is_wp_org_compliant'] ?? false);
        
        // Statistics and metrics
        $this->installs_count = $this->toInt($data['installs_count'] ?? 0);
        $this->active_installs_count = $this->toInt($data['active_installs_count'] ?? 0);
        $this->free_releases_count = $this->toInt($data['free_releases_count'] ?? 0);
        $this->premium_releases_count = $this->toInt($data['premium_releases_count'] ?? 0);
        $this->total_purchases = $this->toInt($data['total_purchases'] ?? 0);
        $this->total_subscriptions = $this->toInt($data['total_subscriptions'] ?? 0);
        $this->total_renewals = $this->toInt($data['total_renewals'] ?? 0);
        $this->total_failed_purchases = $this->toString($data['total_failed_purchases'] ?? '0');
        $this->earnings = $this->toString($data['earnings'] ?? '0');
        
        // Product type and classification
        $this->type = $this->toNullableString($data['type'] ?? null);
        $this->is_static = $this->toBool($data['is_static'] ?? false);
        
        // Cache management
        $this->cached_at = isset($data['cached_at']) ? $this->dbToDateTime($data['cached_at']) : null;
        $this->raw_data = $data['raw_data'] ?? null;
        
        // If raw_data is a JSON string, decode it
        if (is_string($this->raw_data)) {
            $this->raw_data = json_decode($this->raw_data, true);
        }
    }
    
    /**
     * Create ProductModel from Freemius API response
     */
    public static function fromFreemiusResponse(array $data): self
    {
        $processedData = $data;
        $processedData['cached_at'] = (new DateTime())->format('Y-m-d H:i:s');
        $processedData['raw_data'] = $data;
        
        return new self($processedData);
    }
    
    /**
     * Create ProductModel from database row
     */
    public static function fromDatabaseRow(array $row): self
    {
        return new self($row);
    }
    
    /**
     * Convert to database array for insertion/update
     */
    public function toDatabaseArray(): array
    {
        return [
            'id' => $this->id,
            'secret_key' => $this->secret_key,
            'public_key' => $this->public_key,
            'created' => $this->dateTimeToDb($this->created),
            'updated' => $this->dateTimeToDb($this->updated),
            'parent_plugin_id' => $this->parent_plugin_id,
            'developer_id' => $this->developer_id,
            'store_id' => $this->store_id,
            'slug' => $this->slug,
            'title' => $this->title,
            'environment' => $this->environment,
            'icon' => $this->icon,
            'default_plan_id' => $this->default_plan_id,
            'plans' => $this->plans,
            'features' => $this->features,
            'money_back_period' => $this->money_back_period,
            'refund_policy' => $this->refund_policy,
            'annual_renewals_discount' => $this->annual_renewals_discount,
            'renewals_discount_type' => $this->renewals_discount_type,
            'is_released' => $this->is_released,
            'is_sdk_required' => $this->is_sdk_required,
            'is_pricing_visible' => $this->is_pricing_visible,
            'is_wp_org_compliant' => $this->is_wp_org_compliant,
            'installs_count' => $this->installs_count,
            'active_installs_count' => $this->active_installs_count,
            'free_releases_count' => $this->free_releases_count,
            'premium_releases_count' => $this->premium_releases_count,
            'total_purchases' => $this->total_purchases,
            'total_subscriptions' => $this->total_subscriptions,
            'total_renewals' => $this->total_renewals,
            'total_failed_purchases' => $this->total_failed_purchases,
            'earnings' => $this->earnings,
            'type' => $this->type,
            'is_static' => $this->is_static,
            'cached_at' => $this->dateTimeToDb($this->cached_at),
            'raw_data' => $this->raw_data ? json_encode($this->raw_data) : null
        ];
    }
    
    /**
     * Comprehensive validation
     */
    public function validate(): array
    {
        $errors = [];
        
        // Required fields validation
        if ($error = $this->validateRequired($this->id, 'id')) {
            $errors[] = $error;
        } elseif ($error = $this->validatePositiveInt($this->id, 'id')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->secret_key, 'secret_key')) {
            $errors[] = $error;
        } elseif ($error = $this->validateStringLength($this->secret_key, 255, 'secret_key')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->public_key, 'public_key')) {
            $errors[] = $error;
        } elseif ($error = $this->validateStringLength($this->public_key, 255, 'public_key')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->slug, 'slug')) {
            $errors[] = $error;
        } elseif ($error = $this->validateStringLength($this->slug, 255, 'slug')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->title, 'title')) {
            $errors[] = $error;
        } elseif ($error = $this->validateStringLength($this->title, 255, 'title')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->developer_id, 'developer_id')) {
            $errors[] = $error;
        } elseif ($error = $this->validatePositiveInt($this->developer_id, 'developer_id')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->store_id, 'store_id')) {
            $errors[] = $error;
        } elseif ($error = $this->validatePositiveInt($this->store_id, 'store_id')) {
            $errors[] = $error;
        }
        
        // Enum validation
        if ($this->refund_policy && ($error = $this->validateEnum($this->refund_policy, ModelValidator::REFUND_POLICIES, 'refund_policy'))) {
            $errors[] = $error;
        }
        
        if ($this->renewals_discount_type && ($error = $this->validateEnum($this->renewals_discount_type, ModelValidator::RENEWALS_DISCOUNT_TYPES, 'renewals_discount_type'))) {
            $errors[] = $error;
        }
        
        if ($this->type && ($error = $this->validateEnum($this->type, ModelValidator::PRODUCT_TYPES, 'type'))) {
            $errors[] = $error;
        }
        
        // Numeric validation
        if ($this->environment !== null && ($error = $this->validateNonNegativeInt($this->environment, 'environment'))) {
            $errors[] = $error;
        }
        
        if ($this->money_back_period !== null && ($error = $this->validateNonNegativeInt($this->money_back_period, 'money_back_period'))) {
            $errors[] = $error;
        }
        
        if ($this->annual_renewals_discount !== null) {
            if ($this->annual_renewals_discount < 0 || $this->annual_renewals_discount > 100) {
                $errors[] = 'annual_renewals_discount must be between 0 and 100';
            }
        }
        
        // Parent plugin ID validation
        if ($this->parent_plugin_id !== null && ($error = $this->validatePositiveInt($this->parent_plugin_id, 'parent_plugin_id'))) {
            $errors[] = $error;
        }
        
        // Icon URL validation
        if ($this->icon && ($error = $this->validateStringLength($this->icon, 500, 'icon'))) {
            $errors[] = $error;
        }
        
        // Statistics validation
        if ($error = $this->validateNonNegativeInt($this->installs_count, 'installs_count')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateNonNegativeInt($this->active_installs_count, 'active_installs_count')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateNonNegativeInt($this->free_releases_count, 'free_releases_count')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateNonNegativeInt($this->premium_releases_count, 'premium_releases_count')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateNonNegativeInt($this->total_purchases, 'total_purchases')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateNonNegativeInt($this->total_subscriptions, 'total_subscriptions')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateNonNegativeInt($this->total_renewals, 'total_renewals')) {
            $errors[] = $error;
        }
        
        // Earnings validation
        if (!is_numeric($this->earnings) || (float) $this->earnings < 0) {
            $errors[] = 'earnings must be a non-negative number';
        }
        
        if (!is_numeric($this->total_failed_purchases) || (int) $this->total_failed_purchases < 0) {
            $errors[] = 'total_failed_purchases must be a non-negative number';
        }
        
        return $errors;
    }
    
    /**
     * Check if product data is expired and needs refresh
     */
    public function isExpired(int $maxAgeHours = 24): bool
    {
        $now = new DateTime();
        $ageInHours = ($now->getTimestamp() - $this->cached_at->getTimestamp()) / 3600;
        
        return $ageInHours > $maxAgeHours;
    }
    
    /**
     * Get plan IDs as array
     */
    public function getPlanIds(): array
    {
        return array_filter(array_map('trim', explode(',', $this->plans)));
    }
    
    /**
     * Get feature IDs as array
     */
    public function getFeatureIds(): array
    {
        return array_filter(array_map('trim', explode(',', $this->features)));
    }
    
    /**
     * Get earnings as float
     */
    public function getEarningsFloat(): float
    {
        return (float) $this->earnings;
    }
    
    /**
     * Get total failed purchases as integer
     */
    public function getTotalFailedPurchasesInt(): int
    {
        return (int) $this->total_failed_purchases;
    }
    
    /**
     * JSON serialization
     */
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'secret_key' => $this->secret_key,
            'public_key' => $this->public_key,
            'created' => $this->dateTimeToJson($this->created),
            'updated' => $this->dateTimeToJson($this->updated),
            'parent_plugin_id' => $this->parent_plugin_id,
            'developer_id' => $this->developer_id,
            'store_id' => $this->store_id,
            'slug' => $this->slug,
            'title' => $this->title,
            'environment' => $this->environment,
            'icon' => $this->icon,
            'default_plan_id' => $this->default_plan_id,
            'plans' => $this->getPlanIds(),
            'features' => $this->getFeatureIds(),
            'money_back_period' => $this->money_back_period,
            'refund_policy' => $this->refund_policy,
            'annual_renewals_discount' => $this->annual_renewals_discount,
            'renewals_discount_type' => $this->renewals_discount_type,
            'is_released' => $this->is_released,
            'is_sdk_required' => $this->is_sdk_required,
            'is_pricing_visible' => $this->is_pricing_visible,
            'is_wp_org_compliant' => $this->is_wp_org_compliant,
            'installs_count' => $this->installs_count,
            'active_installs_count' => $this->active_installs_count,
            'free_releases_count' => $this->free_releases_count,
            'premium_releases_count' => $this->premium_releases_count,
            'total_purchases' => $this->total_purchases,
            'total_subscriptions' => $this->total_subscriptions,
            'total_renewals' => $this->total_renewals,
            'total_failed_purchases' => $this->getTotalFailedPurchasesInt(),
            'earnings' => $this->getEarningsFloat(),
            'type' => $this->type,
            'is_static' => $this->is_static,
            'cached_at' => $this->dateTimeToJson($this->cached_at),
            'is_expired' => $this->isExpired()
        ];
    }

    /**
     * Transform for external API integration (Freemius format)
     */
    public function toFreemiusApiFormat(): array
    {
        return [
            'id' => $this->id,
            'secret_key' => $this->secret_key,
            'public_key' => $this->public_key,
            'slug' => $this->slug,
            'title' => $this->title,
            'type' => $this->type,
            'is_released' => $this->is_released,
            'developer_id' => $this->developer_id,
            'store_id' => $this->store_id,
            'environment' => $this->environment
        ];
    }

    /**
     * Get safe array for API responses (excludes sensitive data)
     */
    public function toSafeArray(): array
    {
        return [
            'id' => $this->id,
            'slug' => $this->slug,
            'title' => $this->title,
            'type' => $this->type,
            'is_released' => $this->is_released,
            'environment' => $this->environment,
            'icon' => $this->icon,
            'installs_count' => $this->installs_count,
            'active_installs_count' => $this->active_installs_count,
            'cached_at' => $this->dateTimeToJson($this->cached_at),
            'is_expired' => $this->isExpired()
        ];
    }

    /**
     * Handle foreign key relationships
     */
    public function getRelatedInstallations(): array
    {
        // This would be implemented by the repository layer
        return [];
    }

    /**
     * Validate foreign key constraints
     */
    public function validateForeignKeys(): array
    {
        $errors = [];
        
        // Parent plugin ID should reference another product if set
        if ($this->parent_plugin_id !== null && $this->parent_plugin_id === $this->id) {
            $errors[] = 'Product cannot be its own parent';
        }
        
        return $errors;
    }
}