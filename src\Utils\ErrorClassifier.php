<?php

namespace Skpassegna\GuardgeoApi\Utils;

/**
 * Error Classifier
 * 
 * Comprehensive error classification system for categorizing and handling
 * different types of errors with appropriate response codes and security levels.
 */
class ErrorClassifier
{
    // Error categories
    public const CATEGORY_CLIENT = 'client_error';
    public const CATEGORY_SERVER = 'server_error';
    public const CATEGORY_SECURITY = 'security_error';
    public const CATEGORY_EXTERNAL = 'external_error';
    public const CATEGORY_VALIDATION = 'validation_error';
    public const CATEGORY_AUTHENTICATION = 'authentication_error';
    public const CATEGORY_AUTHORIZATION = 'authorization_error';
    public const CATEGORY_RATE_LIMIT = 'rate_limit_error';
    public const CATEGORY_MAINTENANCE = 'maintenance_error';

    // Security levels
    public const SECURITY_LOW = 'low';
    public const SECURITY_MEDIUM = 'medium';
    public const SECURITY_HIGH = 'high';
    public const SECURITY_CRITICAL = 'critical';

    // Error code mappings
    private const ERROR_CLASSIFICATIONS = [
        // Client errors (4xx)
        'VALIDATION_ERROR' => [
            'category' => self::CATEGORY_VALIDATION,
            'http_code' => 400,
            'security_level' => self::SECURITY_LOW,
            'log_level' => 'warning',
            'user_message' => 'Request validation failed',
            'recovery_action' => 'validate_input'
        ],
        'INVALID_REQUEST' => [
            'category' => self::CATEGORY_CLIENT,
            'http_code' => 400,
            'security_level' => self::SECURITY_LOW,
            'log_level' => 'warning',
            'user_message' => 'Invalid request format',
            'recovery_action' => 'check_request_format'
        ],
        'INVALID_JSON' => [
            'category' => self::CATEGORY_CLIENT,
            'http_code' => 400,
            'security_level' => self::SECURITY_MEDIUM,
            'log_level' => 'warning',
            'user_message' => 'Invalid JSON format',
            'recovery_action' => 'validate_json'
        ],
        'AUTHENTICATION_ERROR' => [
            'category' => self::CATEGORY_AUTHENTICATION,
            'http_code' => 401,
            'security_level' => self::SECURITY_HIGH,
            'log_level' => 'warning',
            'user_message' => 'Authentication failed',
            'recovery_action' => 'check_credentials'
        ],
        'INVALID_INSTALLATION' => [
            'category' => self::CATEGORY_AUTHENTICATION,
            'http_code' => 401,
            'security_level' => self::SECURITY_HIGH,
            'log_level' => 'warning',
            'user_message' => 'Installation not found or inactive',
            'recovery_action' => 'verify_freemius_installation'
        ],
        'AUTHORIZATION_ERROR' => [
            'category' => self::CATEGORY_AUTHORIZATION,
            'http_code' => 403,
            'security_level' => self::SECURITY_HIGH,
            'log_level' => 'warning',
            'user_message' => 'Access denied',
            'recovery_action' => 'check_permissions'
        ],
        'INSUFFICIENT_PERMISSIONS' => [
            'category' => self::CATEGORY_AUTHORIZATION,
            'http_code' => 403,
            'security_level' => self::SECURITY_HIGH,
            'log_level' => 'warning',
            'user_message' => 'Insufficient permissions',
            'recovery_action' => 'request_elevated_access'
        ],
        'NOT_FOUND' => [
            'category' => self::CATEGORY_CLIENT,
            'http_code' => 404,
            'security_level' => self::SECURITY_LOW,
            'log_level' => 'info',
            'user_message' => 'Resource not found',
            'recovery_action' => 'check_resource_path'
        ],
        'METHOD_NOT_ALLOWED' => [
            'category' => self::CATEGORY_CLIENT,
            'http_code' => 405,
            'security_level' => self::SECURITY_LOW,
            'log_level' => 'warning',
            'user_message' => 'HTTP method not allowed',
            'recovery_action' => 'use_correct_method'
        ],
        'RATE_LIMIT_EXCEEDED' => [
            'category' => self::CATEGORY_RATE_LIMIT,
            'http_code' => 429,
            'security_level' => self::SECURITY_MEDIUM,
            'log_level' => 'warning',
            'user_message' => 'Rate limit exceeded',
            'recovery_action' => 'wait_and_retry'
        ],

        // Server errors (5xx)
        'INTERNAL_ERROR' => [
            'category' => self::CATEGORY_SERVER,
            'http_code' => 500,
            'security_level' => self::SECURITY_MEDIUM,
            'log_level' => 'error',
            'user_message' => 'An internal server error occurred',
            'recovery_action' => 'retry_later'
        ],
        'DATABASE_ERROR' => [
            'category' => self::CATEGORY_SERVER,
            'http_code' => 500,
            'security_level' => self::SECURITY_HIGH,
            'log_level' => 'error',
            'user_message' => 'Database operation failed',
            'recovery_action' => 'retry_later'
        ],
        'EXTERNAL_API_ERROR' => [
            'category' => self::CATEGORY_EXTERNAL,
            'http_code' => 502,
            'security_level' => self::SECURITY_MEDIUM,
            'log_level' => 'error',
            'user_message' => 'External service unavailable',
            'recovery_action' => 'retry_later'
        ],
        'IP_INTELLIGENCE_ERROR' => [
            'category' => self::CATEGORY_EXTERNAL,
            'http_code' => 502,
            'security_level' => self::SECURITY_MEDIUM,
            'log_level' => 'error',
            'user_message' => 'IP intelligence service unavailable',
            'recovery_action' => 'retry_later'
        ],
        'FREEMIUS_API_ERROR' => [
            'category' => self::CATEGORY_EXTERNAL,
            'http_code' => 502,
            'security_level' => self::SECURITY_MEDIUM,
            'log_level' => 'error',
            'user_message' => 'Freemius service unavailable',
            'recovery_action' => 'retry_later'
        ],
        'MAINTENANCE_MODE' => [
            'category' => self::CATEGORY_MAINTENANCE,
            'http_code' => 503,
            'security_level' => self::SECURITY_LOW,
            'log_level' => 'info',
            'user_message' => 'Service temporarily unavailable for maintenance',
            'recovery_action' => 'retry_later'
        ],

        // Security errors
        'SECURITY_VIOLATION' => [
            'category' => self::CATEGORY_SECURITY,
            'http_code' => 403,
            'security_level' => self::SECURITY_CRITICAL,
            'log_level' => 'critical',
            'user_message' => 'Security policy violation',
            'recovery_action' => 'contact_support'
        ],
        'SQL_INJECTION_ATTEMPT' => [
            'category' => self::CATEGORY_SECURITY,
            'http_code' => 403,
            'security_level' => self::SECURITY_CRITICAL,
            'log_level' => 'critical',
            'user_message' => 'Request blocked for security reasons',
            'recovery_action' => 'contact_support'
        ],
        'XSS_ATTEMPT' => [
            'category' => self::CATEGORY_SECURITY,
            'http_code' => 403,
            'security_level' => self::SECURITY_CRITICAL,
            'log_level' => 'critical',
            'user_message' => 'Request blocked for security reasons',
            'recovery_action' => 'contact_support'
        ],
        'CSRF_VIOLATION' => [
            'category' => self::CATEGORY_SECURITY,
            'http_code' => 403,
            'security_level' => self::SECURITY_HIGH,
            'log_level' => 'error',
            'user_message' => 'CSRF token validation failed',
            'recovery_action' => 'refresh_page'
        ],
        'SUSPICIOUS_ACTIVITY' => [
            'category' => self::CATEGORY_SECURITY,
            'http_code' => 403,
            'security_level' => self::SECURITY_HIGH,
            'log_level' => 'error',
            'user_message' => 'Suspicious activity detected',
            'recovery_action' => 'contact_support'
        ]
    ];

    /**
     * Classify an error by its code
     */
    public function classifyError(string $errorCode): array
    {
        $classification = self::ERROR_CLASSIFICATIONS[$errorCode] ?? null;
        
        if (!$classification) {
            // Default classification for unknown errors
            return [
                'category' => self::CATEGORY_SERVER,
                'http_code' => 500,
                'security_level' => self::SECURITY_MEDIUM,
                'log_level' => 'error',
                'user_message' => 'An unexpected error occurred',
                'recovery_action' => 'retry_later'
            ];
        }

        return $classification;
    }

    /**
     * Classify an exception
     */
    public function classifyException(\Throwable $exception): array
    {
        $exceptionClass = get_class($exception);
        $message = $exception->getMessage();
        
        // Database exceptions
        if (strpos($exceptionClass, 'Database') !== false || 
            strpos($exceptionClass, 'PDO') !== false) {
            return $this->classifyError('DATABASE_ERROR');
        }
        
        // API exceptions
        if (strpos($exceptionClass, 'FreemiusApi') !== false) {
            return $this->classifyError('FREEMIUS_API_ERROR');
        }
        
        if (strpos($exceptionClass, 'IpRegistryApi') !== false) {
            return $this->classifyError('IP_INTELLIGENCE_ERROR');
        }
        
        // Security exceptions
        if (strpos($message, 'SQL injection') !== false) {
            return $this->classifyError('SQL_INJECTION_ATTEMPT');
        }
        
        if (strpos($message, 'XSS') !== false) {
            return $this->classifyError('XSS_ATTEMPT');
        }
        
        // Validation exceptions
        if (strpos($exceptionClass, 'InvalidArgument') !== false ||
            strpos($exceptionClass, 'Validation') !== false) {
            return $this->classifyError('VALIDATION_ERROR');
        }
        
        // Default to internal error
        return $this->classifyError('INTERNAL_ERROR');
    }

    /**
     * Get all error codes for a category
     */
    public function getErrorCodesByCategory(string $category): array
    {
        $codes = [];
        
        foreach (self::ERROR_CLASSIFICATIONS as $code => $classification) {
            if ($classification['category'] === $category) {
                $codes[] = $code;
            }
        }
        
        return $codes;
    }

    /**
     * Get security level for error code
     */
    public function getSecurityLevel(string $errorCode): string
    {
        $classification = $this->classifyError($errorCode);
        return $classification['security_level'];
    }

    /**
     * Check if error requires immediate security response
     */
    public function requiresSecurityResponse(string $errorCode): bool
    {
        $securityLevel = $this->getSecurityLevel($errorCode);
        return in_array($securityLevel, [self::SECURITY_HIGH, self::SECURITY_CRITICAL]);
    }

    /**
     * Get recovery suggestions for error code
     */
    public function getRecoveryAction(string $errorCode): string
    {
        $classification = $this->classifyError($errorCode);
        return $classification['recovery_action'];
    }

    /**
     * Get user-safe error message
     */
    public function getUserMessage(string $errorCode): string
    {
        $classification = $this->classifyError($errorCode);
        return $classification['user_message'];
    }

    /**
     * Get appropriate log level for error
     */
    public function getLogLevel(string $errorCode): string
    {
        $classification = $this->classifyError($errorCode);
        return $classification['log_level'];
    }

    /**
     * Get HTTP status code for error
     */
    public function getHttpCode(string $errorCode): int
    {
        $classification = $this->classifyError($errorCode);
        return $classification['http_code'];
    }

    /**
     * Check if error should be exposed to client
     */
    public function shouldExposeToClient(string $errorCode): bool
    {
        $classification = $this->classifyError($errorCode);
        
        // Don't expose internal server errors or high security issues
        return !in_array($classification['category'], [
            self::CATEGORY_SERVER,
            self::CATEGORY_SECURITY
        ]) || $classification['security_level'] === self::SECURITY_LOW;
    }

    /**
     * Get sanitized error details for client
     */
    public function getSanitizedDetails(string $errorCode, array $originalDetails): array
    {
        if (!$this->shouldExposeToClient($errorCode)) {
            // Only return safe, generic details
            return [
                'error_id' => $originalDetails['error_id'] ?? uniqid('err_'),
                'timestamp' => date('c'),
                'recovery_action' => $this->getRecoveryAction($errorCode)
            ];
        }
        
        // For client errors, return more detailed information
        $safeDetails = [];
        $allowedFields = [
            'field_errors', 'validation_errors', 'allowed_methods',
            'expected', 'received', 'retry_after', 'error_id',
            'timestamp', 'recovery_action'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($originalDetails[$field])) {
                $safeDetails[$field] = $originalDetails[$field];
            }
        }
        
        return $safeDetails;
    }
}