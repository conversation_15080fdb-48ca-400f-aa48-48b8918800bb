<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Services\CacheManager;
use Skpassegna\GuardgeoApi\Services\CacheWarmingService;
use Skpassegna\GuardgeoApi\Services\CacheStatisticsService;
use Skpassegna\GuardgeoApi\Services\CacheInvalidationService;
use Skpassegna\GuardgeoApi\Services\DatabaseOptimizationService;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;
use Skpassegna\GuardgeoApi\Utils\Logger;

/**
 * Cache Management Controller
 * 
 * Handles admin interface requests for cache management, statistics,
 * warming, invalidation, and performance optimization.
 */
class CacheManagementController
{
    private CacheManager $cacheManager;
    private CacheWarmingService $warmingService;
    private CacheStatisticsService $statisticsService;
    private CacheInvalidationService $invalidationService;
    private DatabaseOptimizationService $optimizationService;
    private ResponseFormatter $responseFormatter;
    private Logger $logger;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->cacheManager = new CacheManager();
        $this->warmingService = new CacheWarmingService();
        $this->statisticsService = new CacheStatisticsService();
        $this->invalidationService = new CacheInvalidationService();
        $this->optimizationService = new DatabaseOptimizationService();
        $this->responseFormatter = new ResponseFormatter();
        $this->logger = new Logger();
    }
    
    /**
     * Get cache overview statistics
     */
    public function getOverview(): array
    {
        try {
            $this->logger->info("Getting cache overview");
            
            $overview = $this->statisticsService->getOverviewStatistics();
            $health = $this->cacheManager->getCacheHealth();
            $recommendations = $this->cacheManager->getMaintenanceScheduleRecommendations();
            
            return $this->responseFormatter->success([
                'overview' => $overview,
                'health' => $health,
                'recommendations' => $recommendations,
                'last_updated' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get cache overview", [
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to get cache overview',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Get comprehensive cache statistics
     */
    public function getStatistics(): array
    {
        try {
            $this->logger->info("Getting comprehensive cache statistics");
            
            $statistics = $this->statisticsService->getComprehensiveStatistics();
            
            return $this->responseFormatter->success($statistics);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get cache statistics", [
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to get cache statistics',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Get cache performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        try {
            $this->logger->info("Getting cache performance metrics");
            
            $performance = $this->statisticsService->getPerformanceStatistics();
            $dbMetrics = $this->optimizationService->getPerformanceMetrics();
            
            return $this->responseFormatter->success([
                'cache_performance' => $performance,
                'database_performance' => $dbMetrics,
                'generated_at' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get performance metrics", [
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to get performance metrics',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Perform cache warming
     */
    public function performWarmup(array $params = []): array
    {
        try {
            $this->logger->info("Performing cache warmup", $params);
            
            $strategy = $params['strategy'] ?? 'intelligent';
            
            switch ($strategy) {
                case 'popular':
                    $results = $this->warmingService->warmPopularIps();
                    break;
                    
                case 'expiring':
                    $results = $this->warmingService->warmExpiringIps();
                    break;
                    
                case 'geographic':
                    $results = $this->warmingService->warmGeographicRegions();
                    break;
                    
                case 'security':
                    $results = $this->warmingService->warmSecurityPatterns();
                    break;
                    
                case 'manual':
                    $ips = $params['ips'] ?? [];
                    if (empty($ips)) {
                        return $this->responseFormatter->error('No IPs provided for manual warmup', 400);
                    }
                    $results = $this->warmingService->preloadIpList($ips, 'manual');
                    break;
                    
                case 'intelligent':
                default:
                    $results = $this->warmingService->performIntelligentWarmup();
                    break;
            }
            
            return $this->responseFormatter->success([
                'strategy' => $strategy,
                'results' => $results,
                'completed_at' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to perform cache warmup", [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to perform cache warmup',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Perform cache invalidation
     */
    public function performInvalidation(array $params): array
    {
        try {
            $this->logger->info("Performing cache invalidation", $params);
            
            $ips = $params['ips'] ?? [];
            $reason = $params['reason'] ?? 'manual';
            $dataTypes = $params['data_types'] ?? [];
            $strategy = $params['strategy'] ?? 'single';
            
            if (empty($ips)) {
                return $this->responseFormatter->error('No IPs provided for invalidation', 400);
            }
            
            switch ($strategy) {
                case 'age_based':
                    $ageRules = $params['age_rules'] ?? [];
                    $results = $this->invalidationService->invalidateByAge($ageRules);
                    break;
                    
                case 'usage_pattern':
                    $results = $this->invalidationService->invalidateByUsagePattern();
                    break;
                    
                case 'batch':
                    $results = $this->invalidationService->invalidateMultipleIps($ips, $reason, $dataTypes);
                    break;
                    
                case 'single':
                default:
                    if (count($ips) === 1) {
                        $results = $this->invalidationService->invalidateIp($ips[0], $reason, $dataTypes);
                    } else {
                        $results = $this->invalidationService->invalidateMultipleIps($ips, $reason, $dataTypes);
                    }
                    break;
            }
            
            return $this->responseFormatter->success([
                'strategy' => $strategy,
                'results' => $results,
                'completed_at' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to perform cache invalidation", [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to perform cache invalidation',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Perform cache cleanup
     */
    public function performCleanup(array $params = []): array
    {
        try {
            $this->logger->info("Performing cache cleanup", $params);
            
            $results = $this->cacheManager->performCleanup();
            
            return $this->responseFormatter->success([
                'results' => $results,
                'completed_at' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to perform cache cleanup", [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to perform cache cleanup',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Perform database optimization
     */
    public function performDatabaseOptimization(array $params = []): array
    {
        try {
            $this->logger->info("Performing database optimization", $params);
            
            $results = $this->optimizationService->performOptimization();
            
            return $this->responseFormatter->success([
                'results' => $results,
                'completed_at' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to perform database optimization", [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to perform database optimization',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Get cache warming recommendations
     */
    public function getWarmingRecommendations(): array
    {
        try {
            $this->logger->info("Getting cache warming recommendations");
            
            $recommendations = $this->warmingService->getWarmingRecommendations();
            $statistics = $this->warmingService->getWarmingStatistics();
            
            return $this->responseFormatter->success([
                'recommendations' => $recommendations,
                'statistics' => $statistics,
                'generated_at' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get warming recommendations", [
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to get warming recommendations',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Get invalidation statistics
     */
    public function getInvalidationStatistics(): array
    {
        try {
            $this->logger->info("Getting invalidation statistics");
            
            $statistics = $this->invalidationService->getInvalidationStatistics();
            
            return $this->responseFormatter->success($statistics);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get invalidation statistics", [
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to get invalidation statistics',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Perform scheduled maintenance
     */
    public function performScheduledMaintenance(): array
    {
        try {
            $this->logger->info("Performing scheduled cache maintenance");
            
            $results = $this->cacheManager->performScheduledMaintenance();
            
            return $this->responseFormatter->success([
                'results' => $results,
                'completed_at' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to perform scheduled maintenance", [
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to perform scheduled maintenance',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Get cache health status
     */
    public function getHealthStatus(): array
    {
        try {
            $this->logger->info("Getting cache health status");
            
            $health = $this->cacheManager->getCacheHealth();
            $dbHealth = $this->optimizationService->getPerformanceMetrics();
            
            return $this->responseFormatter->success([
                'cache_health' => $health,
                'database_health' => $dbHealth['connection_stats'] ?? [],
                'overall_status' => $health['status'],
                'checked_at' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get health status", [
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to get health status',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Get optimization recommendations
     */
    public function getOptimizationRecommendations(): array
    {
        try {
            $this->logger->info("Getting optimization recommendations");
            
            $cacheRecommendations = $this->statisticsService->getOptimizationRecommendations();
            $dbRecommendations = $this->optimizationService->getPerformanceMetrics()['recommendations'] ?? [];
            $warmingRecommendations = $this->warmingService->getWarmingRecommendations();
            
            return $this->responseFormatter->success([
                'cache_recommendations' => $cacheRecommendations,
                'database_recommendations' => $dbRecommendations,
                'warming_recommendations' => $warmingRecommendations,
                'generated_at' => date('c')
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get optimization recommendations", [
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to get optimization recommendations',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
    
    /**
     * Export cache statistics
     */
    public function exportStatistics(array $params = []): array
    {
        try {
            $this->logger->info("Exporting cache statistics", $params);
            
            $format = $params['format'] ?? 'json';
            $includeRawData = $params['include_raw_data'] ?? false;
            
            $statistics = $this->statisticsService->getComprehensiveStatistics();
            
            if (!$includeRawData) {
                // Remove raw data to reduce export size
                unset($statistics['raw_data']);
            }
            
            $export = [
                'export_info' => [
                    'generated_at' => date('c'),
                    'format' => $format,
                    'include_raw_data' => $includeRawData,
                    'version' => '1.0'
                ],
                'statistics' => $statistics
            ];
            
            return $this->responseFormatter->success($export);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to export statistics", [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            
            return $this->responseFormatter->error(
                'Failed to export statistics',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
}