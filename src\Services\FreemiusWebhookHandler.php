<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Models\ProductModel;
use Skpassegna\GuardgeoApi\Models\InstallationModel;
use Skpassegna\GuardgeoApi\Database\ProductRepository;
use Skpassegna\GuardgeoApi\Database\InstallationRepository;
use Skpassegna\GuardgeoApi\Utils\Logger;
use Skpassegna\GuardgeoApi\Config\Environment;

/**
 * Freemius Webhook Handler
 * 
 * Handles incoming webhooks from Freemius for real-time data synchronization.
 * Processes installation events, subscription changes, and product updates.
 */
class FreemiusWebhookHandler
{
    private ProductRepository $productRepository;
    private InstallationRepository $installationRepository;
    private Logger $logger;
    private string $webhookSecret;
    
    // Supported webhook event types based on official Freemius documentation
    private const SUPPORTED_EVENTS = [
        // Install events
        'install.activated',
        'install.deactivated', 
        'install.uninstalled',
        'install.upgraded',
        
        // Subscription events
        'subscription.created',
        'subscription.updated',
        'subscription.cancelled',
        'subscription.expired',
        'subscription.renewed',
        
        // License events
        'license.activated',
        'license.deactivated',
        'license.created',
        'license.updated',
        'license.cancelled',
        'license.expired',
        
        // Payment events
        'payment.completed',
        'payment.failed',
        'payment.refunded',
        
        // User events
        'user.created',
        'user.updated',
        
        // Product events (less common for our use case)
        'product.updated'
    ];
    
    /**
     * Constructor
     */
    public function __construct(
        ?ProductRepository $productRepository = null,
        ?InstallationRepository $installationRepository = null,
        ?string $webhookSecret = null
    ) {
        $this->productRepository = $productRepository ?? new ProductRepository();
        $this->installationRepository = $installationRepository ?? new InstallationRepository();
        $this->logger = new Logger();
        $this->webhookSecret = $webhookSecret ?? Environment::get('FREEMIUS_WEBHOOK_SECRET', '');
    }
    
    /**
     * Process incoming webhook
     */
    public function processWebhook(array $headers, string $payload): array
    {
        $this->logger->info("Processing Freemius webhook", [
            'payload_size' => strlen($payload),
            'headers' => array_keys($headers)
        ]);
        
        try {
            // Verify webhook signature if secret is configured
            if (!empty($this->webhookSecret)) {
                if (!$this->verifySignature($headers, $payload)) {
                    $this->logger->warning("Webhook signature verification failed");
                    return [
                        'success' => false,
                        'error' => 'Invalid webhook signature',
                        'code' => 'INVALID_SIGNATURE'
                    ];
                }
            }
            
            // Parse webhook payload
            $data = json_decode($payload, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logger->error("Invalid JSON in webhook payload", [
                    'json_error' => json_last_error_msg()
                ]);
                return [
                    'success' => false,
                    'error' => 'Invalid JSON payload',
                    'code' => 'INVALID_JSON'
                ];
            }
            
            // Validate webhook structure - Freemius webhooks have 'type' and 'objects' fields
            if (!isset($data['type'])) {
                $this->logger->error("Invalid webhook structure - missing event type", [
                    'data_keys' => array_keys($data)
                ]);
                return [
                    'success' => false,
                    'error' => 'Invalid webhook structure - missing event type',
                    'code' => 'INVALID_STRUCTURE'
                ];
            }
            
            $eventType = $data['type'];
            
            // Objects field contains the actual data - it may be missing for some event types
            $objects = $data['objects'] ?? [];
            
            // Some events might have the data directly in the root
            if (empty($objects) && isset($data['id'])) {
                // For events where the main object is in the root
                $objects = [$this->getObjectTypeFromEvent($eventType) => $data];
            }
            
            // Check if event type is supported
            if (!in_array($eventType, self::SUPPORTED_EVENTS)) {
                $this->logger->info("Unsupported webhook event type", [
                    'event_type' => $eventType
                ]);
                return [
                    'success' => true,
                    'message' => 'Event type not processed',
                    'code' => 'UNSUPPORTED_EVENT'
                ];
            }
            
            // Process the webhook based on event type
            $result = $this->processWebhookEvent($eventType, $objects);
            
            $this->logger->info("Webhook processed successfully", [
                'event_type' => $eventType,
                'result' => $result
            ]);
            
            return [
                'success' => true,
                'event_type' => $eventType,
                'result' => $result
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Webhook processing failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'error' => 'Internal processing error',
                'code' => 'PROCESSING_ERROR'
            ];
        }
    }
    
    /**
     * Verify webhook signature using Freemius standard
     */
    private function verifySignature(array $headers, string $payload): bool
    {
        // Look for signature in various header formats (Freemius uses X-Freemius-Signature)
        $signature = null;
        $headerVariations = [
            'X-Freemius-Signature',
            'HTTP_X_FREEMIUS_SIGNATURE', 
            'x-freemius-signature',
            'http_x_freemius_signature'
        ];
        
        foreach ($headerVariations as $headerName) {
            if (isset($headers[$headerName])) {
                $signature = $headers[$headerName];
                break;
            }
        }
        
        if (empty($signature)) {
            $this->logger->warning("No Freemius signature found in webhook headers", [
                'available_headers' => array_keys($headers)
            ]);
            return false;
        }
        
        // Freemius uses HMAC-SHA256 with the webhook secret
        // The signature format is typically: sha256=<hash>
        if (strpos($signature, 'sha256=') === 0) {
            $signature = substr($signature, 7); // Remove 'sha256=' prefix
        }
        
        // Calculate expected signature
        $expectedSignature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $this->logger->debug("Signature verification", [
            'received_signature_length' => strlen($signature),
            'expected_signature_length' => strlen($expectedSignature),
            'signatures_match' => hash_equals($expectedSignature, $signature)
        ]);
        
        // Compare signatures using timing-safe comparison
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Process webhook event based on type
     */
    private function processWebhookEvent(string $eventType, array $objects): array
    {
        switch ($eventType) {
            case 'install.activated':
            case 'install.deactivated':
            case 'install.uninstalled':
            case 'install.upgraded':
                return $this->processInstallationEvent($eventType, $objects);
                
            case 'subscription.created':
            case 'subscription.updated':
            case 'subscription.cancelled':
            case 'subscription.expired':
            case 'subscription.renewed':
                return $this->processSubscriptionEvent($eventType, $objects);
                
            case 'license.activated':
            case 'license.deactivated':
            case 'license.created':
            case 'license.updated':
            case 'license.cancelled':
            case 'license.expired':
                return $this->processLicenseEvent($eventType, $objects);
                
            case 'payment.completed':
            case 'payment.failed':
            case 'payment.refunded':
                return $this->processPaymentEvent($eventType, $objects);
                
            case 'user.created':
            case 'user.updated':
                return $this->processUserEvent($eventType, $objects);
                
            case 'product.updated':
                return $this->processProductEvent($eventType, $objects);
                
            default:
                return [
                    'processed' => false,
                    'reason' => 'Unsupported event type'
                ];
        }
    }
    
    /**
     * Process installation-related events
     */
    private function processInstallationEvent(string $eventType, array $objects): array
    {
        $results = [];
        
        foreach ($objects as $objectType => $objectData) {
            if ($objectType === 'install') {
                try {
                    // Create or update installation model
                    $installation = InstallationModel::fromFreemiusResponse($objectData);
                    
                    // Apply event-specific updates
                    switch ($eventType) {
                        case 'install.activated':
                            $installation->is_active = true;
                            $installation->is_uninstalled = false;
                            break;
                            
                        case 'install.deactivated':
                            $installation->is_active = false;
                            break;
                            
                        case 'install.uninstalled':
                            $installation->is_uninstalled = true;
                            $installation->is_active = false;
                            break;
                            
                        case 'install.upgraded':
                            $installation->upgraded = new \DateTime();
                            break;
                    }
                    
                    // Save to database
                    $savedInstallation = $this->installationRepository->save($installation);
                    
                    $results[] = [
                        'type' => 'installation',
                        'id' => $installation->id,
                        'action' => $eventType,
                        'success' => true
                    ];
                    
                    $this->logger->info("Installation updated via webhook", [
                        'event_type' => $eventType,
                        'installation_id' => $installation->id,
                        'plugin_id' => $installation->plugin_id
                    ]);
                    
                } catch (\Exception $e) {
                    $results[] = [
                        'type' => 'installation',
                        'id' => $objectData['id'] ?? 'unknown',
                        'action' => $eventType,
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    
                    $this->logger->error("Failed to process installation webhook", [
                        'event_type' => $eventType,
                        'error' => $e->getMessage(),
                        'object_data' => $objectData
                    ]);
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Process subscription-related events
     */
    private function processSubscriptionEvent(string $eventType, array $objects): array
    {
        $results = [];
        
        // Update related installations based on subscription changes
        foreach ($objects as $objectType => $objectData) {
            if ($objectType === 'subscription') {
                try {
                    $subscriptionId = $objectData['id'];
                    $installId = $objectData['install_id'] ?? null;
                    
                    if ($installId) {
                        // Find and update the installation
                        $installation = $this->installationRepository->findByInstallationId($installId);
                        
                        if ($installation) {
                            // Update subscription-related fields
                            $installation->subscription_id = $subscriptionId;
                            
                            switch ($eventType) {
                                case 'subscription.created':
                                case 'subscription.renewed':
                                    $installation->is_premium = true;
                                    break;
                                    
                                case 'subscription.cancelled':
                                case 'subscription.expired':
                                    $installation->is_premium = false;
                                    break;
                                    
                                case 'subscription.updated':
                                    // For updates, maintain current premium status
                                    // but refresh the cached data
                                    break;
                            }
                            
                            // Update cached timestamp
                            $installation->cached_at = new \DateTime();
                            
                            // Save changes
                            $this->installationRepository->save($installation);
                            
                            $results[] = [
                                'type' => 'subscription',
                                'subscription_id' => $subscriptionId,
                                'installation_id' => $installId,
                                'action' => $eventType,
                                'success' => true
                            ];
                            
                            $this->logger->info("Installation updated via subscription webhook", [
                                'event_type' => $eventType,
                                'subscription_id' => $subscriptionId,
                                'installation_id' => $installId
                            ]);
                        }
                    }
                    
                } catch (\Exception $e) {
                    $results[] = [
                        'type' => 'subscription',
                        'id' => $objectData['id'] ?? 'unknown',
                        'action' => $eventType,
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    
                    $this->logger->error("Failed to process subscription webhook", [
                        'event_type' => $eventType,
                        'error' => $e->getMessage(),
                        'object_data' => $objectData
                    ]);
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Process license-related events
     */
    private function processLicenseEvent(string $eventType, array $objects): array
    {
        $results = [];
        
        // Update related installations based on license changes
        foreach ($objects as $objectType => $objectData) {
            if ($objectType === 'license') {
                try {
                    $licenseId = $objectData['id'];
                    $installId = $objectData['install_id'] ?? null;
                    
                    if ($installId) {
                        // Find and update the installation
                        $installation = $this->installationRepository->findByInstallationId($installId);
                        
                        if ($installation) {
                            // Update license-related fields
                            $installation->license_id = $licenseId;
                            
                            switch ($eventType) {
                                case 'license.activated':
                                case 'license.created':
                                    $installation->is_premium = true;
                                    break;
                                    
                                case 'license.deactivated':
                                case 'license.cancelled':
                                case 'license.expired':
                                    $installation->is_premium = false;
                                    break;
                                    
                                case 'license.updated':
                                    // For updates, maintain current premium status
                                    // but refresh the cached data
                                    break;
                            }
                            
                            // Update cached timestamp
                            $installation->cached_at = new \DateTime();
                            
                            // Save changes
                            $this->installationRepository->save($installation);
                            
                            $results[] = [
                                'type' => 'license',
                                'license_id' => $licenseId,
                                'installation_id' => $installId,
                                'action' => $eventType,
                                'success' => true
                            ];
                            
                            $this->logger->info("Installation updated via license webhook", [
                                'event_type' => $eventType,
                                'license_id' => $licenseId,
                                'installation_id' => $installId
                            ]);
                        }
                    }
                    
                } catch (\Exception $e) {
                    $results[] = [
                        'type' => 'license',
                        'id' => $objectData['id'] ?? 'unknown',
                        'action' => $eventType,
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    
                    $this->logger->error("Failed to process license webhook", [
                        'event_type' => $eventType,
                        'error' => $e->getMessage(),
                        'object_data' => $objectData
                    ]);
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Process payment-related events
     */
    private function processPaymentEvent(string $eventType, array $objects): array
    {
        $results = [];
        
        foreach ($objects as $objectType => $objectData) {
            if ($objectType === 'payment') {
                try {
                    // For payment events, we mainly want to update related installations
                    $installId = $objectData['install_id'] ?? null;
                    
                    if ($installId) {
                        $installation = $this->installationRepository->findByInstallationId($installId);
                        
                        if ($installation) {
                            // Update installation based on payment status
                            switch ($eventType) {
                                case 'payment.completed':
                                    $installation->is_premium = true;
                                    break;
                                    
                                case 'payment.failed':
                                    // Don't immediately revoke premium status on failed payment
                                    // This should be handled by subscription expiration
                                    break;
                                    
                                case 'payment.refunded':
                                    $installation->is_premium = false;
                                    break;
                            }
                            
                            // Update cached timestamp
                            $installation->cached_at = new \DateTime();
                            
                            // Save changes
                            $this->installationRepository->save($installation);
                            
                            $results[] = [
                                'type' => 'payment',
                                'payment_id' => $objectData['id'] ?? 'unknown',
                                'installation_id' => $installId,
                                'action' => $eventType,
                                'success' => true
                            ];
                            
                            $this->logger->info("Installation updated via payment webhook", [
                                'event_type' => $eventType,
                                'payment_id' => $objectData['id'] ?? 'unknown',
                                'installation_id' => $installId
                            ]);
                        }
                    }
                    
                } catch (\Exception $e) {
                    $results[] = [
                        'type' => 'payment',
                        'id' => $objectData['id'] ?? 'unknown',
                        'action' => $eventType,
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    
                    $this->logger->error("Failed to process payment webhook", [
                        'event_type' => $eventType,
                        'error' => $e->getMessage(),
                        'object_data' => $objectData
                    ]);
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Process user-related events
     */
    private function processUserEvent(string $eventType, array $objects): array
    {
        $results = [];
        
        foreach ($objects as $objectType => $objectData) {
            if ($objectType === 'user') {
                try {
                    // For user events, we might want to update related installations
                    $userId = $objectData['id'] ?? null;
                    
                    if ($userId) {
                        // Find installations for this user
                        $installations = $this->installationRepository->findByUserId($userId);
                        
                        foreach ($installations as $installation) {
                            // Update cached timestamp to reflect user data changes
                            $installation->cached_at = new \DateTime();
                            $this->installationRepository->save($installation);
                        }
                        
                        $results[] = [
                            'type' => 'user',
                            'user_id' => $userId,
                            'action' => $eventType,
                            'installations_updated' => count($installations),
                            'success' => true
                        ];
                        
                        $this->logger->info("User installations updated via webhook", [
                            'event_type' => $eventType,
                            'user_id' => $userId,
                            'installations_count' => count($installations)
                        ]);
                    }
                    
                } catch (\Exception $e) {
                    $results[] = [
                        'type' => 'user',
                        'id' => $objectData['id'] ?? 'unknown',
                        'action' => $eventType,
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    
                    $this->logger->error("Failed to process user webhook", [
                        'event_type' => $eventType,
                        'error' => $e->getMessage(),
                        'object_data' => $objectData
                    ]);
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Process product-related events
     */
    private function processProductEvent(string $eventType, array $objects): array
    {
        $results = [];
        
        foreach ($objects as $objectType => $objectData) {
            if ($objectType === 'product') {
                try {
                    // Create or update product model
                    $product = ProductModel::fromFreemiusResponse($objectData);
                    
                    // Save to database
                    $savedProduct = $this->productRepository->save($product);
                    
                    $results[] = [
                        'type' => 'product',
                        'id' => $product->id,
                        'action' => $eventType,
                        'success' => true
                    ];
                    
                    $this->logger->info("Product updated via webhook", [
                        'event_type' => $eventType,
                        'product_id' => $product->id,
                        'title' => $product->title
                    ]);
                    
                } catch (\Exception $e) {
                    $results[] = [
                        'type' => 'product',
                        'id' => $objectData['id'] ?? 'unknown',
                        'action' => $eventType,
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    
                    $this->logger->error("Failed to process product webhook", [
                        'event_type' => $eventType,
                        'error' => $e->getMessage(),
                        'object_data' => $objectData
                    ]);
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Get object type from event type
     */
    private function getObjectTypeFromEvent(string $eventType): string
    {
        $parts = explode('.', $eventType);
        return $parts[0] ?? 'unknown';
    }
    
    /**
     * Get webhook statistics
     */
    public function getWebhookStatistics(): array
    {
        // This would typically be stored in a separate webhook_logs table
        // For now, return basic information
        return [
            'supported_events' => self::SUPPORTED_EVENTS,
            'webhook_secret_configured' => !empty($this->webhookSecret),
            'last_processed' => null // Would come from webhook logs
        ];
    }
    
    /**
     * Validate webhook configuration
     */
    public function validateConfiguration(): array
    {
        $issues = [];
        
        if (empty($this->webhookSecret)) {
            $issues[] = 'Webhook secret not configured - signatures cannot be verified';
        }
        
        // Test database connections
        try {
            $this->productRepository->countBy([]);
        } catch (\Exception $e) {
            $issues[] = 'Product repository connection failed: ' . $e->getMessage();
        }
        
        try {
            $this->installationRepository->countBy([]);
        } catch (\Exception $e) {
            $issues[] = 'Installation repository connection failed: ' . $e->getMessage();
        }
        
        return [
            'valid' => empty($issues),
            'issues' => $issues,
            'supported_events' => self::SUPPORTED_EVENTS
        ];
    }
}