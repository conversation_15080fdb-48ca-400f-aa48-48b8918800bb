<?php

namespace Skpassegna\GuardgeoApi\Models;

use DateTime;

/**
 * API Request Model
 * 
 * Represents an API request log entry for tracking and performance monitoring.
 */
class ApiRequestModel extends BaseModel
{
    // Properties
    public ?int $id = null;
    public ?string $ip = null;
    public ?string $visitor_hash = null;
    public ?int $plugin_id = null;
    public ?int $install_id = null;
    public ?string $url = null;
    public ?int $response_status = null;
    public ?int $response_time_ms = null;
    public ?bool $freemius_valid = null;
    public ?string $error_message = null;
    public ?DateTime $created_at = null;

    /**
     * Constructor
     */
    public function __construct(array $data = [])
    {
        $this->id = $this->toNullableInt($data['id'] ?? null);
        $this->ip = $this->toNullableString($data['ip'] ?? null);
        $this->visitor_hash = $this->toNullableString($data['visitor_hash'] ?? null);
        $this->plugin_id = $this->toNullableInt($data['plugin_id'] ?? null);
        $this->install_id = $this->toNullableInt($data['install_id'] ?? null);
        $this->url = ModelValidator::sanitizeUrl($data['url'] ?? null);
        $this->response_status = $this->toNullableInt($data['response_status'] ?? null);
        $this->response_time_ms = $this->toNullableInt($data['response_time_ms'] ?? null);
        $this->freemius_valid = isset($data['freemius_valid']) ? $this->toBool($data['freemius_valid']) : null;
        $this->error_message = $this->toNullableString($data['error_message'] ?? null);
        
        $this->created_at = isset($data['created_at']) 
            ? $this->dbToDateTime($data['created_at']) 
            : null;
    }

    /**
     * Create from database row
     */
    public static function fromDatabaseRow(array $row): self
    {
        return new self($row);
    }

    /**
     * Create from request data
     */
    public static function fromRequestData(
        string $ip,
        ?string $visitorHash = null,
        ?int $pluginId = null,
        ?int $installId = null,
        ?string $url = null,
        ?int $responseStatus = null,
        ?int $responseTimeMs = null,
        ?bool $freemiusValid = null,
        ?string $errorMessage = null
    ): self {
        return new self([
            'ip' => $ip,
            'visitor_hash' => $visitorHash,
            'plugin_id' => $pluginId,
            'install_id' => $installId,
            'url' => $url,
            'response_status' => $responseStatus,
            'response_time_ms' => $responseTimeMs,
            'freemius_valid' => $freemiusValid,
            'error_message' => $errorMessage,
            'created_at' => (new DateTime())->format('Y-m-d H:i:s')
        ]);
    }

    /**
     * Comprehensive validation
     */
    public function validate(): array
    {
        $errors = [];

        // Required fields validation
        if ($error = $this->validateRequired($this->ip, 'ip')) {
            $errors[] = $error;
        } elseif (!ModelValidator::validateIpAddress($this->ip)) {
            $errors[] = 'ip must be a valid IP address';
        }

        // Response status validation
        if ($this->response_status !== null && !ModelValidator::validateHttpStatusCode($this->response_status)) {
            $errors[] = 'response_status must be a valid HTTP status code (100-599)';
        }

        // Response time validation
        if ($this->response_time_ms !== null && ($error = $this->validateNonNegativeInt($this->response_time_ms, 'response_time_ms'))) {
            $errors[] = $error;
        }

        // Plugin ID validation
        if ($this->plugin_id !== null && ($error = $this->validatePositiveInt($this->plugin_id, 'plugin_id'))) {
            $errors[] = $error;
        }

        // Install ID validation
        if ($this->install_id !== null && ($error = $this->validatePositiveInt($this->install_id, 'install_id'))) {
            $errors[] = $error;
        }

        // URL validation
        if ($this->url && ($error = $this->validateUrl($this->url, 'url'))) {
            $errors[] = $error;
        }

        // String length validations
        if ($this->url && ($error = $this->validateStringLength($this->url, 500, 'url'))) {
            $errors[] = $error;
        }

        if ($this->visitor_hash && ($error = $this->validateStringLength($this->visitor_hash, 255, 'visitor_hash'))) {
            $errors[] = $error;
        }

        return $errors;
    }

    /**
     * Convert to database array
     */
    public function toDatabaseArray(): array
    {
        return [
            'id' => $this->id,
            'ip' => $this->ip,
            'visitor_hash' => $this->visitor_hash,
            'plugin_id' => $this->plugin_id,
            'install_id' => $this->install_id,
            'url' => $this->url,
            'response_status' => $this->response_status,
            'response_time_ms' => $this->response_time_ms,
            'freemius_valid' => $this->freemius_valid,
            'error_message' => $this->error_message,
            'created_at' => $this->dateTimeToDb($this->created_at)
        ];
    }

    /**
     * JSON serialization
     */
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'ip' => $this->ip,
            'visitor_hash' => $this->visitor_hash,
            'plugin_id' => $this->plugin_id,
            'install_id' => $this->install_id,
            'url' => $this->url,
            'response_status' => $this->response_status,
            'response_time_ms' => $this->response_time_ms,
            'freemius_valid' => $this->freemius_valid,
            'error_message' => $this->error_message,
            'created_at' => $this->dateTimeToJson($this->created_at),
            'is_success' => $this->isSuccess(),
            'is_error' => $this->isError(),
            'performance_category' => $this->getPerformanceCategory()
        ];
    }

    /**
     * Transform for external API integration
     */
    public function toExternalApiFormat(): array
    {
        return [
            'request_id' => $this->id,
            'client_ip' => $this->ip,
            'plugin_id' => $this->plugin_id,
            'install_id' => $this->install_id,
            'response_status' => $this->response_status,
            'response_time' => $this->response_time_ms,
            'timestamp' => $this->dateTimeToJson($this->created_at),
            'success' => $this->isSuccess()
        ];
    }

    /**
     * Get safe array for API responses (excludes sensitive data)
     */
    public function toSafeArray(): array
    {
        return [
            'id' => $this->id,
            'plugin_id' => $this->plugin_id,
            'install_id' => $this->install_id,
            'response_status' => $this->response_status,
            'response_time_ms' => $this->response_time_ms,
            'freemius_valid' => $this->freemius_valid,
            'created_at' => $this->dateTimeToJson($this->created_at),
            'is_success' => $this->isSuccess(),
            'performance_category' => $this->getPerformanceCategory()
        ];
    }

    /**
     * Handle foreign key relationships
     */
    public function getRelatedProduct(): ?ProductModel
    {
        // This would be implemented by the repository layer
        return null;
    }

    /**
     * Get related installation
     */
    public function getRelatedInstallation(): ?InstallationModel
    {
        // This would be implemented by the repository layer
        return null;
    }

    /**
     * Validate foreign key constraints
     */
    public function validateForeignKeys(): array
    {
        $errors = [];
        
        // plugin_id and install_id should reference valid records if set
        // This validation would be handled by the repository layer
        
        return $errors;
    }

    /**
     * Check if request was successful
     */
    public function isSuccess(): bool
    {
        return $this->response_status !== null && $this->response_status >= 200 && $this->response_status < 300;
    }

    /**
     * Check if request was an error
     */
    public function isError(): bool
    {
        return $this->response_status !== null && $this->response_status >= 400;
    }

    /**
     * Check if request was a client error (4xx)
     */
    public function isClientError(): bool
    {
        return $this->response_status !== null && $this->response_status >= 400 && $this->response_status < 500;
    }

    /**
     * Check if request was a server error (5xx)
     */
    public function isServerError(): bool
    {
        return $this->response_status !== null && $this->response_status >= 500;
    }

    /**
     * Get performance category based on response time
     */
    public function getPerformanceCategory(): string
    {
        if ($this->response_time_ms === null) {
            return 'unknown';
        }

        return match (true) {
            $this->response_time_ms < 100 => 'excellent',
            $this->response_time_ms < 300 => 'good',
            $this->response_time_ms < 1000 => 'fair',
            $this->response_time_ms < 3000 => 'poor',
            default => 'very_poor'
        };
    }

    /**
     * Check if Freemius validation was successful
     */
    public function isFreemiusValid(): bool
    {
        return $this->freemius_valid === true;
    }

    /**
     * Get request summary
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'plugin_id' => $this->plugin_id,
            'install_id' => $this->install_id,
            'status' => $this->response_status,
            'success' => $this->isSuccess(),
            'freemius_valid' => $this->isFreemiusValid(),
            'response_time' => $this->response_time_ms,
            'performance' => $this->getPerformanceCategory(),
            'timestamp' => $this->dateTimeToJson($this->created_at)
        ];
    }
}