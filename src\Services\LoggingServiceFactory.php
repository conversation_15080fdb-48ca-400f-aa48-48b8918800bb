<?php

namespace Skpassegna\GuardgeoApi\Services;

/**
 * Logging Service Factory
 * 
 * Factory class for creating and managing logging service instances.
 * Provides singleton pattern to ensure consistent logging across the application.
 */
class LoggingServiceFactory
{
    private static ?LoggingService $loggingService = null;
    private static ?MonitoringService $monitoringService = null;
    private static ?AuditTrailService $auditTrailService = null;
    
    /**
     * Get logging service instance
     */
    public static function getLoggingService(bool $enableDatabaseLogging = true): LoggingService
    {
        if (self::$loggingService === null) {
            self::$loggingService = new LoggingService($enableDatabaseLogging);
        }
        
        return self::$loggingService;
    }
    
    /**
     * Get monitoring service instance
     */
    public static function getMonitoringService(): MonitoringService
    {
        if (self::$monitoringService === null) {
            $loggingService = self::getLoggingService();
            self::$monitoringService = new MonitoringService($loggingService);
        }
        
        return self::$monitoringService;
    }
    
    /**
     * Get audit trail service instance
     */
    public static function getAuditTrailService(): AuditTrailService
    {
        if (self::$auditTrailService === null) {
            $loggingService = self::getLoggingService();
            self::$auditTrailService = new AuditTrailService($loggingService);
        }
        
        return self::$auditTrailService;
    }
    
    /**
     * Get API logger instance
     */
    public static function getApiLogger(): ApiLogger
    {
        $loggingService = self::getLoggingService();
        return $loggingService->getLogger(LoggingService::TYPE_API);
    }
    
    /**
     * Get admin logger instance
     */
    public static function getAdminLogger(): AdminLogger
    {
        $loggingService = self::getLoggingService();
        return $loggingService->getLogger(LoggingService::TYPE_ADMIN);
    }
    
    /**
     * Get error logger instance
     */
    public static function getErrorLogger(): ErrorLogger
    {
        $loggingService = self::getLoggingService();
        return $loggingService->getLogger(LoggingService::TYPE_ERROR);
    }
    
    /**
     * Get system logger instance
     */
    public static function getSystemLogger(): SystemLogger
    {
        $loggingService = self::getLoggingService();
        return $loggingService->getLogger(LoggingService::TYPE_SYSTEM);
    }
    
    /**
     * Reset all service instances (useful for testing)
     */
    public static function reset(): void
    {
        self::$loggingService = null;
        self::$monitoringService = null;
        self::$auditTrailService = null;
    }
    
    /**
     * Configure logging service settings
     */
    public static function configure(array $config): void
    {
        // Reset existing instances to apply new configuration
        self::reset();
        
        // Create new logging service with configuration
        $enableDatabaseLogging = $config['enable_database_logging'] ?? true;
        self::$loggingService = new LoggingService($enableDatabaseLogging);
    }
}