<?php

/**
 * Development Environment Configuration Overrides
 * 
 * These settings override the base configuration when running in development mode.
 * This file should be included in version control but should not contain sensitive data.
 */

return [
    'app' => [
        'debug' => true,
        'url' => 'http://localhost:8000',
    ],
    
    'logging' => [
        'level' => 'debug',
        'max_files' => 10,
    ],
    
    'api' => [
        'rate_limit_enabled' => false,
        'request_timeout' => 60, // Longer timeout for debugging
    ],
    
    'security' => [
        'session_secure' => false, // Allow non-HTTPS in development
        'csrf_protection' => true, // Keep CSRF protection even in dev
    ],
    
    'auth' => [
        'session_lifetime' => 86400 * 7, // 7 days in development
        'max_login_attempts' => 10, // More lenient in development
        'lockout_duration' => 300, // 5 minutes
    ],
    
    'monitoring' => [
        'performance_tracking' => true,
        'error_reporting' => true,
        'health_check_interval' => 60, // More frequent checks in dev
    ],
    
    'cache' => [
        'default_ttl' => 300, // Shorter cache in development
    ],
    
    // Development-specific features
    'development' => [
        'enable_debug_toolbar' => true,
        'enable_query_logging' => true,
        'enable_profiling' => true,
        'mock_external_apis' => false, // Set to true to mock Freemius/ipRegistry
    ]
];