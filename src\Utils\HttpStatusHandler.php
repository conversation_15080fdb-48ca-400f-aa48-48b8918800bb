<?php

namespace Skpassegna\GuardgeoApi\Utils;

/**
 * HTTP Status Code Handler
 * 
 * Provides comprehensive HTTP status code handling with proper headers,
 * status messages, and response formatting for world-class REST API standards.
 */
class HttpStatusHandler
{
    // HTTP Status Code Constants
    public const STATUS_OK = 200;
    public const STATUS_CREATED = 201;
    public const STATUS_ACCEPTED = 202;
    public const STATUS_NO_CONTENT = 204;
    
    public const STATUS_BAD_REQUEST = 400;
    public const STATUS_UNAUTHORIZED = 401;
    public const STATUS_FORBIDDEN = 403;
    public const STATUS_NOT_FOUND = 404;
    public const STATUS_METHOD_NOT_ALLOWED = 405;
    public const STATUS_NOT_ACCEPTABLE = 406;
    public const STATUS_CONFLICT = 409;
    public const STATUS_UNPROCESSABLE_ENTITY = 422;
    public const STATUS_TOO_MANY_REQUESTS = 429;
    
    public const STATUS_INTERNAL_SERVER_ERROR = 500;
    public const STATUS_NOT_IMPLEMENTED = 501;
    public const STATUS_BAD_GATEWAY = 502;
    public const STATUS_SERVICE_UNAVAILABLE = 503;
    public const STATUS_GATEWAY_TIMEOUT = 504;

    /**
     * HTTP status code messages
     */
    private const STATUS_MESSAGES = [
        200 => 'OK',
        201 => 'Created',
        202 => 'Accepted',
        204 => 'No Content',
        
        400 => 'Bad Request',
        401 => 'Unauthorized',
        403 => 'Forbidden',
        404 => 'Not Found',
        405 => 'Method Not Allowed',
        406 => 'Not Acceptable',
        409 => 'Conflict',
        422 => 'Unprocessable Entity',
        429 => 'Too Many Requests',
        
        500 => 'Internal Server Error',
        501 => 'Not Implemented',
        502 => 'Bad Gateway',
        503 => 'Service Unavailable',
        504 => 'Gateway Timeout'
    ];

    /**
     * Error code to HTTP status code mapping
     */
    private const ERROR_CODE_MAPPING = [
        'VALIDATION_ERROR' => 400,
        'INVALID_REQUEST' => 400,
        'BAD_REQUEST' => 400,
        'INVALID_JSON' => 400,
        'MISSING_REQUIRED_FIELD' => 400,
        'INVALID_FIELD_FORMAT' => 400,
        
        'AUTHENTICATION_ERROR' => 401,
        'INVALID_INSTALLATION' => 401,
        'UNAUTHORIZED' => 401,
        'INVALID_CREDENTIALS' => 401,
        
        'AUTHORIZATION_ERROR' => 403,
        'FORBIDDEN' => 403,
        'ACCESS_DENIED' => 403,
        'INSUFFICIENT_PERMISSIONS' => 403,
        
        'NOT_FOUND' => 404,
        'ENDPOINT_NOT_FOUND' => 404,
        'RESOURCE_NOT_FOUND' => 404,
        
        'METHOD_NOT_ALLOWED' => 405,
        'INVALID_HTTP_METHOD' => 405,
        
        'NOT_ACCEPTABLE' => 406,
        'INVALID_ACCEPT_HEADER' => 406,
        
        'CONFLICT' => 409,
        'RESOURCE_CONFLICT' => 409,
        'DUPLICATE_RESOURCE' => 409,
        
        'UNPROCESSABLE_ENTITY' => 422,
        'VALIDATION_FAILED' => 422,
        'BUSINESS_RULE_VIOLATION' => 422,
        
        'RATE_LIMIT_EXCEEDED' => 429,
        'TOO_MANY_REQUESTS' => 429,
        'QUOTA_EXCEEDED' => 429,
        
        'INTERNAL_ERROR' => 500,
        'INTERNAL_SERVER_ERROR' => 500,
        'UNEXPECTED_ERROR' => 500,
        'DATABASE_ERROR' => 500,
        
        'NOT_IMPLEMENTED' => 501,
        'FEATURE_NOT_IMPLEMENTED' => 501,
        
        'BAD_GATEWAY' => 502,
        'EXTERNAL_API_ERROR' => 502,
        'IP_INTELLIGENCE_ERROR' => 502,
        'FREEMIUS_API_ERROR' => 502,
        
        'SERVICE_UNAVAILABLE' => 503,
        'MAINTENANCE_MODE' => 503,
        'TEMPORARY_UNAVAILABLE' => 503,
        
        'GATEWAY_TIMEOUT' => 504,
        'EXTERNAL_API_TIMEOUT' => 504,
        'REQUEST_TIMEOUT' => 504
    ];

    /**
     * Set HTTP status code with proper headers
     */
    public static function setStatusCode(int $statusCode): void
    {
        if (!isset(self::STATUS_MESSAGES[$statusCode])) {
            throw new \InvalidArgumentException("Invalid HTTP status code: $statusCode");
        }

        $message = self::STATUS_MESSAGES[$statusCode];
        
        // Set the status code
        http_response_code($statusCode);
        
        // Set additional headers based on status code
        self::setStatusSpecificHeaders($statusCode);
    }

    /**
     * Get HTTP status code from error code
     */
    public static function getStatusCodeFromErrorCode(string $errorCode): int
    {
        return self::ERROR_CODE_MAPPING[$errorCode] ?? 500;
    }

    /**
     * Get status message for status code
     */
    public static function getStatusMessage(int $statusCode): string
    {
        return self::STATUS_MESSAGES[$statusCode] ?? 'Unknown Status';
    }

    /**
     * Check if status code is successful (2xx)
     */
    public static function isSuccessful(int $statusCode): bool
    {
        return $statusCode >= 200 && $statusCode < 300;
    }

    /**
     * Check if status code is client error (4xx)
     */
    public static function isClientError(int $statusCode): bool
    {
        return $statusCode >= 400 && $statusCode < 500;
    }

    /**
     * Check if status code is server error (5xx)
     */
    public static function isServerError(int $statusCode): bool
    {
        return $statusCode >= 500 && $statusCode < 600;
    }

    /**
     * Get error category from status code
     */
    public static function getErrorCategory(int $statusCode): string
    {
        if (self::isSuccessful($statusCode)) {
            return 'success';
        } elseif (self::isClientError($statusCode)) {
            return 'client_error';
        } elseif (self::isServerError($statusCode)) {
            return 'server_error';
        } else {
            return 'unknown';
        }
    }

    /**
     * Set status-specific headers
     */
    private static function setStatusSpecificHeaders(int $statusCode): void
    {
        switch ($statusCode) {
            case 401:
                header('WWW-Authenticate: Bearer realm="GuardGeo API"');
                break;
                
            case 405:
                // Method Not Allowed - should include Allow header
                // This will be set by the specific handler
                break;
                
            case 429:
                // Rate limiting headers
                header('Retry-After: 3600');
                header('X-RateLimit-Limit: 100');
                header('X-RateLimit-Remaining: 0');
                header('X-RateLimit-Reset: ' . (time() + 3600));
                break;
                
            case 503:
                // Service Unavailable
                header('Retry-After: 300');
                break;
        }
    }

    /**
     * Send HTTP status with JSON response
     */
    public static function sendJsonResponse(int $statusCode, array $data): void
    {
        self::setStatusCode($statusCode);
        
        header('Content-Type: application/json; charset=utf-8');
        header('Cache-Control: no-cache, must-revalidate');
        
        echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Send error response with proper status code
     */
    public static function sendErrorResponse(string $errorCode, string $message, array $details = []): void
    {
        $statusCode = self::getStatusCodeFromErrorCode($errorCode);
        
        $response = [
            'success' => false,
            'error' => [
                'code' => $errorCode,
                'message' => $message,
                'status' => $statusCode,
                'status_text' => self::getStatusMessage($statusCode),
                'type' => self::getErrorCategory($statusCode)
            ],
            'meta' => [
                'timestamp' => date('c'),
                'request_id' => uniqid('req_', true)
            ]
        ];

        if (!empty($details)) {
            $response['error']['details'] = $details;
        }

        self::sendJsonResponse($statusCode, $response);
    }

    /**
     * Send success response with proper status code
     */
    public static function sendSuccessResponse(array $data, int $statusCode = 200, array $metadata = []): void
    {
        $response = [
            'success' => true,
            'data' => $data,
            'meta' => array_merge([
                'timestamp' => date('c'),
                'status' => $statusCode,
                'status_text' => self::getStatusMessage($statusCode),
                'request_id' => uniqid('req_', true)
            ], $metadata)
        ];

        self::sendJsonResponse($statusCode, $response);
    }

    /**
     * Get comprehensive status information
     */
    public static function getStatusInfo(int $statusCode): array
    {
        return [
            'code' => $statusCode,
            'message' => self::getStatusMessage($statusCode),
            'category' => self::getErrorCategory($statusCode),
            'is_successful' => self::isSuccessful($statusCode),
            'is_client_error' => self::isClientError($statusCode),
            'is_server_error' => self::isServerError($statusCode)
        ];
    }

    /**
     * Validate status code
     */
    public static function isValidStatusCode(int $statusCode): bool
    {
        return isset(self::STATUS_MESSAGES[$statusCode]);
    }

    /**
     * Get all available status codes
     */
    public static function getAvailableStatusCodes(): array
    {
        return array_keys(self::STATUS_MESSAGES);
    }

    /**
     * Get status codes by category
     */
    public static function getStatusCodesByCategory(string $category): array
    {
        $codes = [];
        
        foreach (self::STATUS_MESSAGES as $code => $message) {
            if (self::getErrorCategory($code) === $category) {
                $codes[] = $code;
            }
        }
        
        return $codes;
    }
}