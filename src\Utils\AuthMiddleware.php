<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\AuthService;
use Skpassegna\GuardgeoApi\Utils\RoleManager;
use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Authentication Middleware
 * 
 * Handles authentication and authorization checks for admin routes
 * with role-based permission validation.
 */
class AuthMiddleware
{
    private AuthService $authService;
    private RoleManager $roleManager;
    private LoggingService $logger;

    public function __construct(
        AuthService $authService,
        RoleManager $roleManager,
        LoggingService $logger
    ) {
        $this->authService = $authService;
        $this->roleManager = $roleManager;
        $this->logger = $logger;
    }

    /**
     * Check if user is authenticated
     *
     * @return array|null User data if authenticated, null otherwise
     */
    public function checkAuthentication(): ?array
    {
        return $this->authService->validateSession();
    }

    /**
     * Require authentication for route
     *
     * @param string $redirectUrl URL to redirect to if not authenticated
     * @return array|null User data if authenticated, redirects if not
     */
    public function requireAuthentication(string $redirectUrl = '/admin/login'): ?array
    {
        $user = $this->checkAuthentication();

        if (!$user) {
            $this->logger->logAdminAction('access_denied', [
                'reason' => 'not_authenticated',
                'requested_url' => $_SERVER['REQUEST_URI'] ?? '',
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            $this->redirectToLogin($redirectUrl);
            return null;
        }

        return $user;
    }

    /**
     * Check if user has specific permission
     *
     * @param string $category Permission category
     * @param string $action Permission action
     * @param array|null $user User data (if null, will check current session)
     * @return bool
     */
    public function hasPermission(string $category, string $action, ?array $user = null): bool
    {
        if (!$user) {
            $user = $this->checkAuthentication();
        }

        if (!$user) {
            return false;
        }

        return $this->roleManager->hasPermission($user['role'], $category, $action);
    }

    /**
     * Require specific permission for route
     *
     * @param string $category Permission category
     * @param string $action Permission action
     * @param string $errorMessage Custom error message
     * @return array User data if authorized
     */
    public function requirePermission(
        string $category,
        string $action,
        string $errorMessage = 'Access denied: insufficient permissions'
    ): array {
        $user = $this->requireAuthentication();

        if (!$this->hasPermission($category, $action, $user)) {
            $this->logger->logAdminAction('access_denied', [
                'reason' => 'insufficient_permissions',
                'required_permission' => "{$category}.{$action}",
                'user_id' => $user['id'],
                'user_role' => $user['role'],
                'requested_url' => $_SERVER['REQUEST_URI'] ?? '',
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            $this->sendAccessDeniedResponse($errorMessage);
        }

        return $user;
    }

    /**
     * Check if user can access specific feature
     *
     * @param string $feature Feature name
     * @param array|null $user User data (if null, will check current session)
     * @return bool
     */
    public function canAccessFeature(string $feature, ?array $user = null): bool
    {
        if (!$user) {
            $user = $this->checkAuthentication();
        }

        if (!$user) {
            return false;
        }

        return $this->roleManager->canAccessFeature($user['role'], $feature);
    }

    /**
     * Require feature access for route
     *
     * @param string $feature Feature name
     * @param string $errorMessage Custom error message
     * @return array User data if authorized
     */
    public function requireFeatureAccess(
        string $feature,
        string $errorMessage = 'Access denied: feature not available for your role'
    ): array {
        $user = $this->requireAuthentication();

        if (!$this->canAccessFeature($feature, $user)) {
            $this->logger->logAdminAction('access_denied', [
                'reason' => 'feature_not_accessible',
                'required_feature' => $feature,
                'user_id' => $user['id'],
                'user_role' => $user['role'],
                'requested_url' => $_SERVER['REQUEST_URI'] ?? '',
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            $this->sendAccessDeniedResponse($errorMessage);
        }

        return $user;
    }

    /**
     * Require Super Admin role
     *
     * @param string $errorMessage Custom error message
     * @return array User data if authorized
     */
    public function requireSuperAdmin(string $errorMessage = 'Access denied: Super Administrator role required'): array
    {
        $user = $this->requireAuthentication();

        if ($user['role'] !== 'super_admin') {
            $this->logger->logAdminAction('access_denied', [
                'reason' => 'super_admin_required',
                'user_id' => $user['id'],
                'user_role' => $user['role'],
                'requested_url' => $_SERVER['REQUEST_URI'] ?? '',
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            $this->sendAccessDeniedResponse($errorMessage);
        }

        return $user;
    }

    /**
     * Check if user can manage another user
     *
     * @param string $targetRole Target user's role
     * @param array|null $user Manager user data
     * @return bool
     */
    public function canManageUser(string $targetRole, ?array $user = null): bool
    {
        if (!$user) {
            $user = $this->checkAuthentication();
        }

        if (!$user) {
            return false;
        }

        return $this->roleManager->canManageUser($user['role'], $targetRole);
    }

    /**
     * Require user management permission
     *
     * @param string $targetRole Target user's role
     * @param string $errorMessage Custom error message
     * @return array User data if authorized
     */
    public function requireUserManagement(
        string $targetRole,
        string $errorMessage = 'Access denied: cannot manage users with this role'
    ): array {
        $user = $this->requireAuthentication();

        if (!$this->canManageUser($targetRole, $user)) {
            $this->logger->logAdminAction('access_denied', [
                'reason' => 'cannot_manage_user',
                'target_role' => $targetRole,
                'user_id' => $user['id'],
                'user_role' => $user['role'],
                'requested_url' => $_SERVER['REQUEST_URI'] ?? '',
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            $this->sendAccessDeniedResponse($errorMessage);
        }

        return $user;
    }

    /**
     * Validate CSRF token
     *
     * @param string|null $token Token to validate (if null, will check POST/header)
     * @return bool
     */
    public function validateCsrfToken(?string $token = null): bool
    {
        if (!$token) {
            // Check POST data first
            $token = $_POST['csrf_token'] ?? null;
            
            // Check headers
            if (!$token) {
                $token = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? null;
            }
        }

        if (!$token) {
            return false;
        }

        $sessionManager = new SessionManager();
        return $sessionManager->validateCsrfToken($token);
    }

    /**
     * Require valid CSRF token
     *
     * @param string|null $token Token to validate
     * @param string $errorMessage Custom error message
     * @return void
     */
    public function requireCsrfToken(?string $token = null, string $errorMessage = 'Invalid CSRF token'): void
    {
        if (!$this->validateCsrfToken($token)) {
            $this->logger->logAdminAction('csrf_token_invalid', [
                'requested_url' => $_SERVER['REQUEST_URI'] ?? '',
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);

            $this->sendAccessDeniedResponse($errorMessage, 403);
        }
    }

    /**
     * Get current user data
     *
     * @return array|null
     */
    public function getCurrentUser(): ?array
    {
        return $this->checkAuthentication();
    }

    /**
     * Check if current request is from authenticated user
     *
     * @return bool
     */
    public function isAuthenticated(): bool
    {
        return $this->checkAuthentication() !== null;
    }

    /**
     * Get user's navigation menu
     *
     * @param array|null $user User data
     * @return array
     */
    public function getNavigationMenu(?array $user = null): array
    {
        if (!$user) {
            $user = $this->checkAuthentication();
        }

        if (!$user) {
            return [];
        }

        return $this->roleManager->getNavigationMenu($user['role']);
    }

    /**
     * Get user's dashboard widgets
     *
     * @param array|null $user User data
     * @return array
     */
    public function getDashboardWidgets(?array $user = null): array
    {
        if (!$user) {
            $user = $this->checkAuthentication();
        }

        if (!$user) {
            return [];
        }

        return $this->roleManager->getDashboardWidgets($user['role']);
    }

    /**
     * Redirect to login page
     *
     * @param string $loginUrl Login URL
     * @return void
     */
    private function redirectToLogin(string $loginUrl): void
    {
        $currentUrl = $_SERVER['REQUEST_URI'] ?? '';
        
        if ($currentUrl && $currentUrl !== $loginUrl) {
            $loginUrl .= '?redirect=' . urlencode($currentUrl);
        }

        header("Location: {$loginUrl}");
        exit;
    }

    /**
     * Send access denied response
     *
     * @param string $message Error message
     * @param int $statusCode HTTP status code
     * @return void
     */
    private function sendAccessDeniedResponse(string $message, int $statusCode = 403): void
    {
        http_response_code($statusCode);

        // Check if this is an AJAX request
        $isAjax = (
            !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest'
        ) || (
            !empty($_SERVER['HTTP_ACCEPT']) &&
            strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false
        );

        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $message,
                'code' => 'ACCESS_DENIED'
            ]);
        } else {
            // Redirect to error page or show error
            header('Content-Type: text/html');
            echo "<!DOCTYPE html>
<html>
<head>
    <title>Access Denied</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
        .error { color: #d32f2f; }
    </style>
</head>
<body>
    <h1 class=\"error\">Access Denied</h1>
    <p>{$message}</p>
    <a href=\"/admin/dashboard\">Return to Dashboard</a>
</body>
</html>";
        }

        exit;
    }
}