<?php

namespace Skpassegna\GuardgeoApi\Views\Pages;

use Skpassegna\GuardgeoApi\Views\Templates\BaseTemplate;
use Skpassegna\GuardgeoApi\Views\Components\Card;
use Skpassegna\GuardgeoApi\Views\Components\Button;
use Skpassegna\GuardgeoApi\Views\Components\Modal;

/**
 * User Management Page Template
 * 
 * Admin user management page with CRUD operations and role management
 * using the component-based design system.
 */
class UserManagementPage extends BaseTemplate
{
    public function render(): string
    {
        return $this->renderStatisticsCards() . 
               $this->renderUsersTable() . 
               $this->renderUserModal() . 
               $this->renderPageScript();
    }

    private function renderStatisticsCards(): string
    {
        $totalUsersCard = Card::stat([
            'icon' => 'fas fa-users',
            'iconColor' => 'bg-blue-500',
            'title' => 'Total Users',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('total-users-card');

        $activeUsersCard = Card::stat([
            'icon' => 'fas fa-user-check',
            'iconColor' => 'bg-green-500',
            'title' => 'Active Users',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('active-users-card');

        $superAdminsCard = Card::stat([
            'icon' => 'fas fa-user-shield',
            'iconColor' => 'bg-purple-500',
            'title' => 'Super Admins',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('super-admins-card');

        $recentLoginsCard = Card::stat([
            'icon' => 'fas fa-sign-in-alt',
            'iconColor' => 'bg-yellow-500',
            'title' => 'Recent Logins',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('recent-logins-card');

        return <<<HTML
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {$totalUsersCard->render()}
            {$activeUsersCard->render()}
            {$superAdminsCard->render()}
            {$recentLoginsCard->render()}
        </div>
HTML;
    }

    private function renderUsersTable(): string
    {
        $usersCard = new Card([
            'title' => 'Admin Users',
            'content' => $this->getUsersTableContent()
        ]);

        return $usersCard->render();
    }

    private function getUsersTableContent(): string
    {
        return <<<HTML
        <div class="flex justify-between items-center mb-4">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <input 
                        type="text" 
                        id="usersSearch" 
                        placeholder="Search users..."
                        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div class="flex items-center space-x-4">
                <select id="roleFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="all">All Roles</option>
                    <option value="super_admin">Super Admin</option>
                    <option value="dev">Developer</option>
                    <option value="marketing">Marketing</option>
                    <option value="sales">Sales</option>
                </select>
                
                {$this->component('button', [
                    'text' => 'Add User',
                    'icon' => 'fas fa-plus',
                    'variant' => 'primary',
                    'onclick' => 'showCreateUserModal()',
                    'id' => 'addUserBtn'
                ])}
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody id="usersTable" class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                            <p class="text-gray-500">Loading users...</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div id="usersPagination" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing <span id="usersFrom">0</span> to <span id="usersTo">0</span> of <span id="usersTotal">0</span> results
            </div>
            <div id="usersPaginationButtons" class="flex space-x-2">
                <!-- Pagination buttons will be inserted here -->
            </div>
        </div>
HTML;
    }

    private function renderUserModal(): string
    {
        // Create user form using the new Form component
        $userForm = \Skpassegna\GuardgeoApi\Views\Components\Form::userForm([
            'id' => 'userForm',
            'action' => '/admin/api/users/create',
            'ajax' => true,
            'csrf_token' => $this->getCsrfToken()
        ]);

        $modal = new Modal([
            'id' => 'userModal',
            'title' => 'Add New User',
            'size' => 'lg',
            'content' => $userForm->render()
        ]);

        return $modal->render() . <<<HTML
        <!-- User Modal -->
        <div id="userModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 id="userModalTitle" class="text-lg font-medium text-gray-900">Add User</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeUserModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="userForm" class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <input type="email" id="userEmail" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                            <select id="userRole" required class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Select Role</option>
                                <option value="super_admin">Super Admin</option>
                                <option value="dev">Developer</option>
                                <option value="marketing">Marketing</option>
                                <option value="sales">Sales</option>
                            </select>
                        </div>
                        <div id="passwordField">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <input type="password" id="userPassword" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Leave blank to keep current password (edit mode)</p>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="userActive" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Active</span>
                            </label>
                        </div>
                    </div>
                </form>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    {$this->component('button', [
                        'text' => 'Cancel',
                        'variant' => 'outline',
                        'onclick' => 'closeUserModal()'
                    ])}
                    {$this->component('button', [
                        'text' => 'Save',
                        'variant' => 'primary',
                        'onclick' => 'saveUser()',
                        'id' => 'saveUserBtn'
                    ])}
                </div>
            </div>
        </div>
HTML;
    }

    private function renderPageScript(): string
    {
        return <<<HTML
        <script>
            class UserManagementManager {
                constructor() {
                    this.currentPage = 1;
                    this.currentFilters = {
                        search: '',
                        role: 'all'
                    };
                    this.editingUserId = null;
                    this.init();
                }

                init() {
                    this.loadUserStatistics();
                    this.loadUsers();
                    this.bindEvents();
                }

                bindEvents() {
                    // Search functionality
                    document.getElementById('usersSearch').addEventListener('input', this.debounce(() => {
                        this.currentFilters.search = document.getElementById('usersSearch').value;
                        this.currentPage = 1;
                        this.loadUsers();
                    }, 500));

                    // Role filter
                    document.getElementById('roleFilter').addEventListener('change', (e) => {
                        this.currentFilters.role = e.target.value;
                        this.currentPage = 1;
                        this.loadUsers();
                    });

                    // Form submission
                    document.getElementById('userForm').addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.saveUser();
                    });
                }

                async loadUserStatistics() {
                    try {
                        const response = await fetch('/admin/api/users/statistics');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateStatistics(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading user statistics:', error);
                    }
                }

                async loadUsers() {
                    try {
                        const params = new URLSearchParams({
                            page: this.currentPage,
                            limit: 25,
                            ...this.currentFilters
                        });

                        const response = await fetch('/admin/api/users?' + params);
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateUsersTable(data.data, data.pagination);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading users:', error);
                    }
                }

                updateStatistics(stats) {
                    this.updateStatCard('.total-users-card', stats.total_users || 0);
                    this.updateStatCard('.active-users-card', stats.active_users || 0);
                    this.updateStatCard('.super-admins-card', stats.super_admins || 0);
                    this.updateStatCard('.recent-logins-card', stats.recent_logins || 0, 'Last 24h');
                }

                updateStatCard(selector, value, subtitle = '') {
                    const card = document.querySelector(selector);
                    if (card) {
                        const valueElement = card.querySelector('.text-2xl');
                        const subtitleElement = card.querySelector('.text-sm.text-gray-600');
                        if (valueElement) {
                            valueElement.textContent = value;
                        }
                        if (subtitleElement && subtitle) {
                            subtitleElement.textContent = subtitle;
                        }
                    }
                }

                updateUsersTable(users, pagination) {
                    const tbody = document.getElementById('usersTable');

                    if (users.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-12 text-center text-gray-500">No users found</td></tr>';
                        return;
                    }

                    let html = '';
                    users.forEach(user => {
                        const roleDisplay = this.getRoleDisplayName(user.role);
                        const statusClass = user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                        const statusText = user.is_active ? 'Active' : 'Inactive';

                        html += `
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                                                <span class="text-sm font-medium text-white">\${this.getUserInitials(user.email)}</span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">\${user.email}</div>
                                            <div class="text-sm text-gray-500">ID: \${user.id}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        \${roleDisplay}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \${statusClass}">
                                        \${statusText}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    \${user.last_login || 'Never'}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    \${user.created_at}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="userManager.editUser(\${user.id})" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                    <button onclick="userManager.deleteUser(\${user.id}, '\${user.email}')" class="text-red-600 hover:text-red-900">Delete</button>
                                </td>
                            </tr>
                        `;
                    });

                    tbody.innerHTML = html;
                    this.updatePagination(pagination);
                }

                updatePagination(pagination) {
                    document.getElementById('usersFrom').textContent = ((pagination.current_page - 1) * pagination.per_page) + 1;
                    document.getElementById('usersTo').textContent = Math.min(pagination.current_page * pagination.per_page, pagination.total);
                    document.getElementById('usersTotal').textContent = pagination.total;

                    const container = document.getElementById('usersPaginationButtons');
                    let html = '';

                    if (pagination.current_page > 1) {
                        html += `<button onclick="userManager.changePage(\${pagination.current_page - 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</button>`;
                    }

                    if (pagination.current_page < pagination.total_pages) {
                        html += `<button onclick="userManager.changePage(\${pagination.current_page + 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>`;
                    }

                    container.innerHTML = html;
                }

                changePage(page) {
                    this.currentPage = page;
                    this.loadUsers();
                }

                showCreateUserModal() {
                    this.editingUserId = null;
                    document.getElementById('userModalTitle').textContent = 'Add User';
                    document.getElementById('userForm').reset();
                    document.getElementById('userActive').checked = true;
                    document.getElementById('passwordField').style.display = 'block';
                    document.getElementById('userPassword').required = true;
                    document.getElementById('userModal').classList.remove('hidden');
                }

                async editUser(userId) {
                    try {
                        const response = await fetch(`/admin/api/users/details?id=\${userId}`);
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.editingUserId = userId;
                                document.getElementById('userModalTitle').textContent = 'Edit User';
                                document.getElementById('userEmail').value = data.data.email;
                                document.getElementById('userRole').value = data.data.role;
                                document.getElementById('userActive').checked = data.data.is_active;
                                document.getElementById('userPassword').value = '';
                                document.getElementById('userPassword').required = false;
                                document.getElementById('userModal').classList.remove('hidden');
                            }
                        }
                    } catch (error) {
                        console.error('Error loading user details:', error);
                        alert('Error loading user details');
                    }
                }

                async saveUser() {
                    const email = document.getElementById('userEmail').value;
                    const role = document.getElementById('userRole').value;
                    const password = document.getElementById('userPassword').value;
                    const isActive = document.getElementById('userActive').checked;

                    if (!email || !role) {
                        alert('Please fill in all required fields');
                        return;
                    }

                    if (!this.editingUserId && !password) {
                        alert('Password is required for new users');
                        return;
                    }

                    try {
                        const url = this.editingUserId ? '/admin/api/users/update' : '/admin/api/users/create';
                        const payload = {
                            email: email,
                            role: role,
                            is_active: isActive
                        };

                        if (this.editingUserId) {
                            payload.id = this.editingUserId;
                        }

                        if (password) {
                            payload.password = password;
                        }

                        const response = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(payload)
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                alert(this.editingUserId ? 'User updated successfully' : 'User created successfully');
                                this.closeUserModal();
                                this.loadUsers();
                                this.loadUserStatistics();
                            } else {
                                alert('Error: ' + data.error);
                            }
                        }
                    } catch (error) {
                        console.error('Error saving user:', error);
                        alert('Error saving user');
                    }
                }

                async deleteUser(userId, email) {
                    if (!confirm(`Are you sure you want to delete user "\${email}"?`)) return;

                    try {
                        const response = await fetch('/admin/api/users/delete', {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ id: userId })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                alert('User deleted successfully');
                                this.loadUsers();
                                this.loadUserStatistics();
                            } else {
                                alert('Error deleting user: ' + data.error);
                            }
                        }
                    } catch (error) {
                        console.error('Error deleting user:', error);
                        alert('Error deleting user');
                    }
                }

                closeUserModal() {
                    document.getElementById('userModal').classList.add('hidden');
                    this.editingUserId = null;
                }

                getRoleDisplayName(role) {
                    const roleNames = {
                        'super_admin': 'Super Admin',
                        'dev': 'Developer',
                        'marketing': 'Marketing',
                        'sales': 'Sales'
                    };
                    return roleNames[role] || role;
                }

                getUserInitials(email) {
                    const parts = email.split('@');
                    const username = parts[0] || '';
                    
                    if (username.length >= 2) {
                        return username.substring(0, 2).toUpperCase();
                    }
                    
                    return email.substring(0, 1).toUpperCase();
                }

                debounce(func, wait) {
                    let timeout;
                    return function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeout);
                            func(...args);
                        };
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                    };
                }
            }

            // Global functions
            function showCreateUserModal() {
                userManager.showCreateUserModal();
            }

            function closeUserModal() {
                userManager.closeUserModal();
            }

            function saveUser() {
                userManager.saveUser();
            }

            // Initialize user management manager when DOM is loaded
            let userManager;
            document.addEventListener('DOMContentLoaded', function() {
                userManager = new UserManagementManager();
            });
        </script>
HTML;
    }

    /**
     * Get CSRF token for forms
     */
    private function getCsrfToken(): string
    {
        $sessionManager = new \Skpassegna\GuardgeoApi\Utils\SessionManager();
        $sessionManager->startSession();
        return $sessionManager->getCsrfToken();
    }
}