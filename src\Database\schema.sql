-- GuardGeo Admin Platform Database Schema
-- PostgreSQL Database Schema for IP Intelligence and Admin Management

-- Enable UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Admin Users Table
CREATE TABLE admin_users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('super_admin', 'dev', 'marketing', 'sales')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    
    -- Constraints
    CONSTRAINT admin_users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Freemius Products Table
CREATE TABLE freemius_products (
    id BIGINT PRIMARY KEY,
    secret_key VARCHAR(255) NOT NULL,
    public_key VARCHAR(255) NOT NULL,
    created TIMESTAMP NOT NULL,
    updated TIMESTAMP NOT NULL,
    parent_plugin_id BIGINT,
    developer_id BIGINT NOT NULL,
    store_id BIGINT NOT NULL,
    slug VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    environment INTEGER NOT NULL,
    icon VARCHAR(500),
    default_plan_id VARCHAR(255) NOT NULL,
    plans TEXT NOT NULL, -- Comma separated plan IDs
    features TEXT NOT NULL, -- Comma separated feature IDs
    money_back_period INTEGER NOT NULL,
    refund_policy VARCHAR(50) NOT NULL CHECK (refund_policy IN ('flexible', 'moderate', 'strict')),
    annual_renewals_discount INTEGER,
    renewals_discount_type VARCHAR(50) NOT NULL CHECK (renewals_discount_type IN ('percentage', 'dollar')),
    is_released BOOLEAN NOT NULL,
    is_sdk_required BOOLEAN NOT NULL,
    is_pricing_visible BOOLEAN NOT NULL,
    is_wp_org_compliant BOOLEAN NOT NULL,
    installs_count INTEGER NOT NULL DEFAULT 0,
    active_installs_count INTEGER NOT NULL DEFAULT 0,
    free_releases_count INTEGER NOT NULL DEFAULT 0,
    premium_releases_count INTEGER NOT NULL DEFAULT 0,
    total_purchases INTEGER NOT NULL DEFAULT 0,
    total_subscriptions INTEGER NOT NULL DEFAULT 0,
    total_renewals INTEGER NOT NULL DEFAULT 0,
    total_failed_purchases VARCHAR(255) NOT NULL DEFAULT '0',
    earnings VARCHAR(255) NOT NULL DEFAULT '0',
    type VARCHAR(50) NOT NULL CHECK (type IN ('plugin', 'theme', 'widget', 'template')),
    is_static BOOLEAN NOT NULL DEFAULT false,
    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    raw_data JSONB, -- Complete Freemius response
    
    -- Constraints
    CONSTRAINT freemius_products_environment_check CHECK (environment >= 0),
    CONSTRAINT freemius_products_money_back_period_check CHECK (money_back_period >= 0)
);

-- Freemius Installations Table
CREATE TABLE freemius_installations (
    id BIGINT PRIMARY KEY,
    secret_key VARCHAR(255) NOT NULL,
    public_key VARCHAR(255) NOT NULL,
    created TIMESTAMP NOT NULL,
    updated TIMESTAMP NOT NULL,
    site_id BIGINT NOT NULL,
    plugin_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    url VARCHAR(500),
    title VARCHAR(255),
    version VARCHAR(50) NOT NULL,
    plan_id BIGINT,
    license_id BIGINT,
    trial_plan_id BIGINT,
    trial_ends TIMESTAMP,
    subscription_id BIGINT,
    gross DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    country_code VARCHAR(2),
    language VARCHAR(10),
    platform_version VARCHAR(50),
    sdk_version VARCHAR(50),
    programming_language_version VARCHAR(50),
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_disconnected BOOLEAN NOT NULL DEFAULT false,
    is_premium BOOLEAN NOT NULL DEFAULT false,
    is_uninstalled BOOLEAN NOT NULL DEFAULT false,
    is_locked BOOLEAN NOT NULL DEFAULT false,
    source INTEGER NOT NULL DEFAULT 0,
    upgraded TIMESTAMP,
    last_seen_at TIMESTAMP,
    last_served_update_version VARCHAR(50),
    is_beta BOOLEAN NOT NULL DEFAULT false,
    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    raw_data JSONB, -- Complete Freemius response
    
    -- Foreign Key Constraints
    CONSTRAINT fk_freemius_installations_plugin_id 
        FOREIGN KEY (plugin_id) REFERENCES freemius_products(id) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- Constraints
    CONSTRAINT freemius_installations_gross_check CHECK (gross >= 0),
    CONSTRAINT freemius_installations_source_check CHECK (source >= 0),
    CONSTRAINT freemius_installations_country_code_check CHECK (country_code IS NULL OR LENGTH(country_code) = 2)
);

-- IP Intelligence Data Table
CREATE TABLE ip_intelligence (
    id SERIAL PRIMARY KEY,
    ip INET UNIQUE NOT NULL,
    type VARCHAR(10) NOT NULL CHECK (type IN ('IPv4', 'IPv6')),
    hostname VARCHAR(255),
    carrier_data JSONB, -- name, mcc, mnc
    company_data JSONB, -- domain, name, type
    connection_data JSONB, -- asn, domain, organization, route, type
    currency_data JSONB, -- code, name, symbol, format, etc.
    location_data JSONB, -- continent, country, region, city, coordinates, etc.
    security_data JSONB, -- is_abuser, is_attacker, is_bogon, is_proxy, is_vpn, etc.
    time_zone_data JSONB, -- id, abbreviation, current_time, name, offset, etc.
    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    location_expires_at TIMESTAMP,
    security_expires_at TIMESTAMP,
    connection_expires_at TIMESTAMP,
    company_expires_at TIMESTAMP,
    raw_data JSONB -- Complete ipRegistry response
);

-- System Logs Table
CREATE TABLE system_logs (
    id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL CHECK (type IN ('api', 'admin', 'error', 'system')),
    level VARCHAR(20) NOT NULL CHECK (level IN ('debug', 'info', 'warning', 'error', 'critical')),
    message TEXT NOT NULL,
    context JSONB,
    ip INET,
    user_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    CONSTRAINT fk_system_logs_user_id 
        FOREIGN KEY (user_id) REFERENCES admin_users(id) 
        ON DELETE SET NULL ON UPDATE CASCADE
);

-- API Request Logs Table
CREATE TABLE api_requests (
    id SERIAL PRIMARY KEY,
    ip INET NOT NULL,
    visitor_hash VARCHAR(255),
    plugin_id BIGINT,
    install_id BIGINT,
    url VARCHAR(500),
    response_status INTEGER NOT NULL,
    response_time_ms INTEGER,
    freemius_valid BOOLEAN,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT api_requests_response_status_check CHECK (response_status >= 100 AND response_status < 600),
    CONSTRAINT api_requests_response_time_check CHECK (response_time_ms IS NULL OR response_time_ms >= 0)
);

-- Performance Indexes
CREATE INDEX idx_admin_users_email ON admin_users(email);
CREATE INDEX idx_admin_users_role ON admin_users(role);
CREATE INDEX idx_admin_users_active ON admin_users(is_active);

CREATE INDEX idx_freemius_products_slug ON freemius_products(slug);
CREATE INDEX idx_freemius_products_developer ON freemius_products(developer_id);
CREATE INDEX idx_freemius_products_type ON freemius_products(type);
CREATE INDEX idx_freemius_products_cached ON freemius_products(cached_at);

CREATE INDEX idx_freemius_installations_plugin ON freemius_installations(plugin_id);
CREATE INDEX idx_freemius_installations_site ON freemius_installations(site_id);
CREATE INDEX idx_freemius_installations_user ON freemius_installations(user_id);
CREATE INDEX idx_freemius_installations_active ON freemius_installations(is_active);
CREATE INDEX idx_freemius_installations_premium ON freemius_installations(is_premium);
CREATE INDEX idx_freemius_installations_cached ON freemius_installations(cached_at);

CREATE INDEX idx_ip_intelligence_ip ON ip_intelligence(ip);
CREATE INDEX idx_ip_intelligence_type ON ip_intelligence(type);
CREATE INDEX idx_ip_intelligence_expires ON ip_intelligence(security_expires_at, location_expires_at);
CREATE INDEX idx_ip_intelligence_cached ON ip_intelligence(cached_at);

CREATE INDEX idx_system_logs_type ON system_logs(type);
CREATE INDEX idx_system_logs_level ON system_logs(level);
CREATE INDEX idx_system_logs_type_created ON system_logs(type, created_at);
CREATE INDEX idx_system_logs_user ON system_logs(user_id);
CREATE INDEX idx_system_logs_created ON system_logs(created_at);

CREATE INDEX idx_api_requests_ip ON api_requests(ip);
CREATE INDEX idx_api_requests_plugin_install ON api_requests(plugin_id, install_id);
CREATE INDEX idx_api_requests_status ON api_requests(response_status);
CREATE INDEX idx_api_requests_created ON api_requests(created_at);
CREATE INDEX idx_api_requests_freemius_valid ON api_requests(freemius_valid);

-- Triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_admin_users_updated_at 
    BEFORE UPDATE ON admin_users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE admin_users IS 'Administrative users with role-based access control';
COMMENT ON TABLE freemius_products IS 'Freemius product data cached from API';
COMMENT ON TABLE freemius_installations IS 'Freemius installation data for subscription validation';
COMMENT ON TABLE ip_intelligence IS 'Cached IP intelligence data with deprecation rules';
COMMENT ON TABLE system_logs IS 'Comprehensive system activity logging';
COMMENT ON TABLE api_requests IS 'API request tracking and performance monitoring';

COMMENT ON COLUMN admin_users.role IS 'User role: super_admin, dev, marketing, sales';
COMMENT ON COLUMN freemius_products.raw_data IS 'Complete JSON response from Freemius API';
COMMENT ON COLUMN freemius_installations.raw_data IS 'Complete JSON response from Freemius API';
COMMENT ON COLUMN ip_intelligence.raw_data IS 'Complete JSON response from ipRegistry API';
COMMENT ON COLUMN ip_intelligence.location_expires_at IS 'Location data expires after 10 days';
COMMENT ON COLUMN ip_intelligence.security_expires_at IS 'Security data expires after 3 days';
COMMENT ON COLUMN ip_intelligence.connection_expires_at IS 'Connection data expires after 7 days';
COMMENT ON COLUMN ip_intelligence.company_expires_at IS 'Company data expires after 30 days';