<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Input Sanitizer
 * 
 * Comprehensive input sanitization across all entry points with
 * multi-layer protection against various attack vectors.
 */
class InputSanitizer
{
    private LoggingService $logger;
    private SqlInjectionPrevention $sqlInjectionPrevention;
    private array $config;

    // Sanitization configuration
    private const DEFAULT_CONFIG = [
        'max_string_length' => 10000,
        'max_array_depth' => 10,
        'max_array_elements' => 1000,
        'strip_tags' => true,
        'encode_html' => true,
        'normalize_unicode' => true,
        'remove_null_bytes' => true,
        'trim_whitespace' => true,
        'log_sanitization' => true
    ];

    // Dangerous patterns to remove/escape
    private const DANGEROUS_PATTERNS = [
        'script_tags' => '/<script[^>]*>.*?<\/script>/is',
        'iframe_tags' => '/<iframe[^>]*>.*?<\/iframe>/is',
        'object_tags' => '/<object[^>]*>.*?<\/object>/is',
        'embed_tags' => '/<embed[^>]*>/i',
        'form_tags' => '/<form[^>]*>.*?<\/form>/is',
        'javascript_protocol' => '/javascript:/i',
        'vbscript_protocol' => '/vbscript:/i',
        'data_protocol' => '/data:/i',
        'event_handlers' => '/on\w+\s*=/i',
        'style_expressions' => '/expression\s*\(/i',
        'css_imports' => '/@import/i',
        'null_bytes' => '/\x00/',
        'control_chars' => '/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/'
    ];

    public function __construct(LoggingService $logger = null, array $config = [])
    {
        $this->logger = $logger ?? new LoggingService();
        $this->sqlInjectionPrevention = new SqlInjectionPrevention();
        $this->config = array_merge(self::DEFAULT_CONFIG, $config);
    }

    /**
     * Sanitize all input data comprehensively
     *
     * @param mixed $input Input data to sanitize
     * @param string $context Context for sanitization (api, admin, form, etc.)
     * @param array $rules Specific sanitization rules
     * @return mixed Sanitized input
     */
    public function sanitizeInput($input, string $context = 'general', array $rules = [])
    {
        $sanitizationRules = array_merge($this->getContextRules($context), $rules);
        
        try {
            $sanitized = $this->applySanitization($input, $sanitizationRules, 0);
            
            if ($this->config['log_sanitization'] && $this->inputWasModified($input, $sanitized)) {
                $this->logSanitization($input, $sanitized, $context);
            }
            
            return $sanitized;
        } catch (\Exception $e) {
            $this->logger->logError('Input sanitization failed', [
                'context' => $context,
                'error' => $e->getMessage(),
                'input_type' => gettype($input)
            ]);
            
            // Return safe fallback
            return $this->getSafeFallback($input);
        }
    }

    /**
     * Apply sanitization based on rules
     *
     * @param mixed $input
     * @param array $rules
     * @param int $depth Current recursion depth
     * @return mixed
     */
    private function applySanitization($input, array $rules, int $depth = 0)
    {
        // Prevent infinite recursion
        if ($depth > $this->config['max_array_depth']) {
            throw new \InvalidArgumentException('Maximum array depth exceeded');
        }

        if (is_null($input)) {
            return null;
        }

        if (is_bool($input)) {
            return $input;
        }

        if (is_numeric($input)) {
            return $this->sanitizeNumeric($input, $rules);
        }

        if (is_string($input)) {
            return $this->sanitizeString($input, $rules);
        }

        if (is_array($input)) {
            return $this->sanitizeArray($input, $rules, $depth);
        }

        // Unknown type - convert to string and sanitize
        return $this->sanitizeString((string)$input, $rules);
    }

    /**
     * Sanitize string input with comprehensive protection
     *
     * @param string $input
     * @param array $rules
     * @return string
     */
    private function sanitizeString(string $input, array $rules): string
    {
        // Check length limits
        if (strlen($input) > $this->config['max_string_length']) {
            $input = substr($input, 0, $this->config['max_string_length']);
        }

        // Remove null bytes and control characters
        if ($this->config['remove_null_bytes']) {
            $input = $this->removeNullBytes($input);
        }

        // Normalize Unicode
        if ($this->config['normalize_unicode']) {
            $input = $this->normalizeUnicode($input);
        }

        // SQL injection prevention
        if (!$this->sqlInjectionPrevention->validateParameter($input)) {
            throw new \InvalidArgumentException('Input contains potentially dangerous SQL patterns');
        }

        // Remove dangerous patterns
        $input = $this->removeDangerousPatterns($input, $rules);

        // HTML encoding
        if ($this->config['encode_html'] && ($rules['encode_html'] ?? true)) {
            $input = $this->encodeHtml($input, $rules);
        }

        // Strip HTML tags
        if ($this->config['strip_tags'] && ($rules['strip_tags'] ?? true)) {
            $input = $this->stripTags($input, $rules);
        }

        // Trim whitespace
        if ($this->config['trim_whitespace']) {
            $input = trim($input);
        }

        // Apply custom filters
        if (isset($rules['custom_filters'])) {
            foreach ($rules['custom_filters'] as $filter) {
                $input = $this->applyCustomFilter($input, $filter);
            }
        }

        return $input;
    }

    /**
     * Sanitize numeric input
     *
     * @param mixed $input
     * @param array $rules
     * @return int|float
     */
    private function sanitizeNumeric($input, array $rules)
    {
        if (is_int($input)) {
            return $this->validateIntegerRange($input, $rules);
        }

        if (is_float($input)) {
            return $this->validateFloatRange($input, $rules);
        }

        // Convert string to number
        if (is_string($input)) {
            $cleaned = preg_replace('/[^0-9.\-+]/', '', $input);
            
            if (strpos($cleaned, '.') !== false) {
                $value = (float)$cleaned;
                return $this->validateFloatRange($value, $rules);
            } else {
                $value = (int)$cleaned;
                return $this->validateIntegerRange($value, $rules);
            }
        }

        return 0; // Safe fallback
    }

    /**
     * Sanitize array input recursively
     *
     * @param array $input
     * @param array $rules
     * @param int $depth
     * @return array
     */
    private function sanitizeArray(array $input, array $rules, int $depth): array
    {
        // Check array size limits
        if (count($input) > $this->config['max_array_elements']) {
            throw new \InvalidArgumentException('Array exceeds maximum element count');
        }

        $sanitized = [];
        foreach ($input as $key => $value) {
            // Sanitize key
            $sanitizedKey = $this->sanitizeString((string)$key, $rules);
            
            // Sanitize value
            $sanitizedValue = $this->applySanitization($value, $rules, $depth + 1);
            
            $sanitized[$sanitizedKey] = $sanitizedValue;
        }

        return $sanitized;
    }

    /**
     * Remove null bytes and control characters
     *
     * @param string $input
     * @return string
     */
    private function removeNullBytes(string $input): string
    {
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Remove other control characters except common whitespace
        $input = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $input);
        
        return $input;
    }

    /**
     * Normalize Unicode characters
     *
     * @param string $input
     * @return string
     */
    private function normalizeUnicode(string $input): string
    {
        if (class_exists('Normalizer')) {
            return \Normalizer::normalize($input, \Normalizer::FORM_C);
        }
        
        return $input;
    }

    /**
     * Remove dangerous patterns from input
     *
     * @param string $input
     * @param array $rules
     * @return string
     */
    private function removeDangerousPatterns(string $input, array $rules): string
    {
        $patternsToCheck = $rules['dangerous_patterns'] ?? array_keys(self::DANGEROUS_PATTERNS);
        
        foreach ($patternsToCheck as $patternName) {
            if (isset(self::DANGEROUS_PATTERNS[$patternName])) {
                $pattern = self::DANGEROUS_PATTERNS[$patternName];
                $input = preg_replace($pattern, '', $input);
            }
        }
        
        return $input;
    }

    /**
     * Encode HTML entities
     *
     * @param string $input
     * @param array $rules
     * @return string
     */
    private function encodeHtml(string $input, array $rules): string
    {
        $flags = ENT_QUOTES | ENT_HTML5;
        
        if ($rules['double_encode'] ?? false) {
            $flags |= ENT_SUBSTITUTE;
        } else {
            $flags |= ENT_NOQUOTES;
        }
        
        return htmlspecialchars($input, $flags, 'UTF-8');
    }

    /**
     * Strip HTML tags
     *
     * @param string $input
     * @param array $rules
     * @return string
     */
    private function stripTags(string $input, array $rules): string
    {
        $allowedTags = $rules['allowed_tags'] ?? '';
        return strip_tags($input, $allowedTags);
    }

    /**
     * Apply custom filter
     *
     * @param string $input
     * @param array $filter
     * @return string
     */
    private function applyCustomFilter(string $input, array $filter): string
    {
        switch ($filter['type']) {
            case 'regex_replace':
                return preg_replace($filter['pattern'], $filter['replacement'] ?? '', $input);
            
            case 'callback':
                if (is_callable($filter['callback'])) {
                    return call_user_func($filter['callback'], $input);
                }
                break;
            
            case 'filter_var':
                return filter_var($input, $filter['filter'], $filter['options'] ?? 0);
        }
        
        return $input;
    }

    /**
     * Validate integer range
     *
     * @param int $value
     * @param array $rules
     * @return int
     */
    private function validateIntegerRange(int $value, array $rules): int
    {
        if (isset($rules['min_value']) && $value < $rules['min_value']) {
            return $rules['min_value'];
        }
        
        if (isset($rules['max_value']) && $value > $rules['max_value']) {
            return $rules['max_value'];
        }
        
        return $value;
    }

    /**
     * Validate float range
     *
     * @param float $value
     * @param array $rules
     * @return float
     */
    private function validateFloatRange(float $value, array $rules): float
    {
        if (isset($rules['min_value']) && $value < $rules['min_value']) {
            return (float)$rules['min_value'];
        }
        
        if (isset($rules['max_value']) && $value > $rules['max_value']) {
            return (float)$rules['max_value'];
        }
        
        return $value;
    }

    /**
     * Get context-specific sanitization rules
     *
     * @param string $context
     * @return array
     */
    private function getContextRules(string $context): array
    {
        $rules = [
            'api' => [
                'strip_tags' => true,
                'encode_html' => true,
                'dangerous_patterns' => array_keys(self::DANGEROUS_PATTERNS),
                'max_value' => 999999999,
                'min_value' => -999999999
            ],
            'admin' => [
                'strip_tags' => false,
                'encode_html' => true,
                'allowed_tags' => '<p><br><strong><em><u><a><ul><ol><li>',
                'dangerous_patterns' => ['script_tags', 'iframe_tags', 'object_tags', 'embed_tags']
            ],
            'form' => [
                'strip_tags' => true,
                'encode_html' => true,
                'dangerous_patterns' => array_keys(self::DANGEROUS_PATTERNS)
            ],
            'search' => [
                'strip_tags' => true,
                'encode_html' => true,
                'max_string_length' => 500,
                'dangerous_patterns' => array_keys(self::DANGEROUS_PATTERNS)
            ],
            'general' => [
                'strip_tags' => true,
                'encode_html' => true,
                'dangerous_patterns' => array_keys(self::DANGEROUS_PATTERNS)
            ]
        ];

        return $rules[$context] ?? $rules['general'];
    }

    /**
     * Check if input was modified during sanitization
     *
     * @param mixed $original
     * @param mixed $sanitized
     * @return bool
     */
    private function inputWasModified($original, $sanitized): bool
    {
        if (is_string($original) && is_string($sanitized)) {
            return $original !== $sanitized;
        }
        
        if (is_array($original) && is_array($sanitized)) {
            return serialize($original) !== serialize($sanitized);
        }
        
        return $original !== $sanitized;
    }

    /**
     * Log sanitization activity
     *
     * @param mixed $original
     * @param mixed $sanitized
     * @param string $context
     * @return void
     */
    private function logSanitization($original, $sanitized, string $context): void
    {
        $this->logger->logInfo('Input sanitized', [
            'context' => $context,
            'original_type' => gettype($original),
            'sanitized_type' => gettype($sanitized),
            'original_length' => is_string($original) ? strlen($original) : null,
            'sanitized_length' => is_string($sanitized) ? strlen($sanitized) : null,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    }

    /**
     * Get safe fallback value
     *
     * @param mixed $input
     * @return mixed
     */
    private function getSafeFallback($input)
    {
        if (is_string($input)) {
            return '';
        }
        
        if (is_numeric($input)) {
            return 0;
        }
        
        if (is_array($input)) {
            return [];
        }
        
        if (is_bool($input)) {
            return false;
        }
        
        return null;
    }

    /**
     * Sanitize all request data
     *
     * @param string $context
     * @return array Sanitized request data
     */
    public function sanitizeAllRequestData(string $context = 'general'): array
    {
        $sanitized = [];

        // Sanitize GET data
        if (!empty($_GET)) {
            $sanitized['GET'] = $this->sanitizeInput($_GET, $context);
        }

        // Sanitize POST data
        if (!empty($_POST)) {
            $sanitized['POST'] = $this->sanitizeInput($_POST, $context);
        }

        // Sanitize JSON input
        $jsonInput = $this->getJsonInput();
        if (!empty($jsonInput)) {
            $sanitized['JSON'] = $this->sanitizeInput($jsonInput, $context);
        }

        // Sanitize cookies (selective)
        if (!empty($_COOKIE)) {
            $sanitized['COOKIE'] = $this->sanitizeCookies($_COOKIE, $context);
        }

        return $sanitized;
    }

    /**
     * Get JSON input from request body
     *
     * @return array
     */
    private function getJsonInput(): array
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (strpos($contentType, 'application/json') === false) {
            return [];
        }

        $input = file_get_contents('php://input');
        if (empty($input)) {
            return [];
        }

        $decoded = json_decode($input, true);
        return is_array($decoded) ? $decoded : [];
    }

    /**
     * Sanitize cookies selectively
     *
     * @param array $cookies
     * @param string $context
     * @return array
     */
    private function sanitizeCookies(array $cookies, string $context): array
    {
        $sanitized = [];
        $safeCookies = ['GUARDGEO_SESSION', 'GUARDGEO_ADMIN_SESSION', 'csrf_token'];

        foreach ($cookies as $name => $value) {
            if (in_array($name, $safeCookies)) {
                // Only basic sanitization for session cookies
                $sanitized[$name] = $this->sanitizeString($value, ['strip_tags' => false, 'encode_html' => false]);
            } else {
                // Full sanitization for other cookies
                $sanitized[$name] = $this->sanitizeInput($value, $context);
            }
        }

        return $sanitized;
    }

    /**
     * Update sanitization configuration
     *
     * @param array $newConfig
     * @return void
     */
    public function updateConfiguration(array $newConfig): void
    {
        $this->config = array_merge($this->config, $newConfig);
    }

    /**
     * Get current configuration
     *
     * @return array
     */
    public function getConfiguration(): array
    {
        return $this->config;
    }
}