<?php

namespace Skpassegna\GuardgeoApi\Utils;

/**
 * Performance Tracker Utility
 * 
 * Provides simple performance tracking capabilities for measuring
 * execution time and memory usage of operations.
 */
class PerformanceTracker
{
    private static array $timers = [];
    private static array $memoryMarkers = [];
    
    /**
     * Start timing an operation
     */
    public static function start(string $operation): void
    {
        self::$timers[$operation] = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true)
        ];
    }
    
    /**
     * End timing an operation and return metrics
     */
    public static function end(string $operation): array
    {
        if (!isset(self::$timers[$operation])) {
            return [
                'error' => 'Timer not started for operation: ' . $operation
            ];
        }
        
        $timer = self::$timers[$operation];
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $metrics = [
            'operation' => $operation,
            'duration' => $endTime - $timer['start_time'],
            'duration_ms' => round(($endTime - $timer['start_time']) * 1000, 2),
            'memory_start' => $timer['start_memory'],
            'memory_end' => $endMemory,
            'memory_used' => $endMemory - $timer['start_memory'],
            'memory_peak' => memory_get_peak_usage(true)
        ];
        
        // Clean up timer
        unset(self::$timers[$operation]);
        
        return $metrics;
    }
    
    /**
     * Measure execution time and memory usage of a callable
     */
    public static function measure(string $operation, callable $callback): array
    {
        self::start($operation);
        
        try {
            $result = $callback();
            $success = true;
            $error = null;
        } catch (\Exception $e) {
            $result = null;
            $success = false;
            $error = $e->getMessage();
        }
        
        $metrics = self::end($operation);
        $metrics['success'] = $success;
        $metrics['error'] = $error;
        $metrics['result'] = $result;
        
        return $metrics;
    }
    
    /**
     * Set a memory marker
     */
    public static function markMemory(string $marker): void
    {
        self::$memoryMarkers[$marker] = [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'timestamp' => microtime(true)
        ];
    }
    
    /**
     * Get memory usage between two markers
     */
    public static function getMemoryDiff(string $startMarker, string $endMarker): array
    {
        if (!isset(self::$memoryMarkers[$startMarker]) || !isset(self::$memoryMarkers[$endMarker])) {
            return [
                'error' => 'One or both memory markers not found'
            ];
        }
        
        $start = self::$memoryMarkers[$startMarker];
        $end = self::$memoryMarkers[$endMarker];
        
        return [
            'start_marker' => $startMarker,
            'end_marker' => $endMarker,
            'memory_diff' => $end['memory_usage'] - $start['memory_usage'],
            'time_diff' => $end['timestamp'] - $start['timestamp'],
            'start_memory' => $start['memory_usage'],
            'end_memory' => $end['memory_usage'],
            'peak_memory' => max($start['memory_peak'], $end['memory_peak'])
        ];
    }
    
    /**
     * Get current memory usage
     */
    public static function getCurrentMemoryUsage(): array
    {
        return [
            'current_usage' => memory_get_usage(true),
            'current_usage_formatted' => self::formatBytes(memory_get_usage(true)),
            'peak_usage' => memory_get_peak_usage(true),
            'peak_usage_formatted' => self::formatBytes(memory_get_peak_usage(true)),
            'limit' => ini_get('memory_limit'),
            'timestamp' => microtime(true)
        ];
    }
    
    /**
     * Get all active timers
     */
    public static function getActiveTimers(): array
    {
        $active = [];
        $currentTime = microtime(true);
        
        foreach (self::$timers as $operation => $timer) {
            $active[$operation] = [
                'elapsed' => $currentTime - $timer['start_time'],
                'elapsed_ms' => round(($currentTime - $timer['start_time']) * 1000, 2),
                'memory_start' => $timer['start_memory'],
                'memory_current' => memory_get_usage(true)
            ];
        }
        
        return $active;
    }
    
    /**
     * Clear all timers and markers
     */
    public static function clear(): void
    {
        self::$timers = [];
        self::$memoryMarkers = [];
    }
    
    /**
     * Format bytes to human readable format
     */
    private static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }
    
    /**
     * Profile a function call with detailed metrics
     */
    public static function profile(string $operation, callable $callback, array $context = []): array
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        $startPeakMemory = memory_get_peak_usage(true);
        
        // Capture system state before execution
        $beforeState = [
            'memory_usage' => $startMemory,
            'memory_peak' => $startPeakMemory,
            'time' => $startTime
        ];
        
        try {
            $result = $callback();
            $success = true;
            $error = null;
        } catch (\Exception $e) {
            $result = null;
            $success = false;
            $error = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'code' => $e->getCode()
            ];
        }
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $endPeakMemory = memory_get_peak_usage(true);
        
        // Capture system state after execution
        $afterState = [
            'memory_usage' => $endMemory,
            'memory_peak' => $endPeakMemory,
            'time' => $endTime
        ];
        
        return [
            'operation' => $operation,
            'success' => $success,
            'error' => $error,
            'result' => $result,
            'context' => $context,
            'timing' => [
                'start_time' => $startTime,
                'end_time' => $endTime,
                'duration' => $endTime - $startTime,
                'duration_ms' => round(($endTime - $startTime) * 1000, 2)
            ],
            'memory' => [
                'start_usage' => $startMemory,
                'end_usage' => $endMemory,
                'memory_used' => $endMemory - $startMemory,
                'start_peak' => $startPeakMemory,
                'end_peak' => $endPeakMemory,
                'peak_increase' => $endPeakMemory - $startPeakMemory
            ],
            'formatted' => [
                'duration' => round(($endTime - $startTime) * 1000, 2) . 'ms',
                'memory_used' => self::formatBytes($endMemory - $startMemory),
                'start_memory' => self::formatBytes($startMemory),
                'end_memory' => self::formatBytes($endMemory),
                'peak_memory' => self::formatBytes($endPeakMemory)
            ]
        ];
    }
    
    /**
     * Create a performance checkpoint
     */
    public static function checkpoint(string $name, array $metadata = []): array
    {
        $checkpoint = [
            'name' => $name,
            'timestamp' => microtime(true),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'metadata' => $metadata
        ];
        
        self::$memoryMarkers[$name] = $checkpoint;
        
        return $checkpoint;
    }
    
    /**
     * Get performance summary between checkpoints
     */
    public static function getSummary(array $checkpointNames): array
    {
        if (empty($checkpointNames)) {
            return ['error' => 'No checkpoints provided'];
        }
        
        $checkpoints = [];
        $missingCheckpoints = [];
        
        foreach ($checkpointNames as $name) {
            if (isset(self::$memoryMarkers[$name])) {
                $checkpoints[$name] = self::$memoryMarkers[$name];
            } else {
                $missingCheckpoints[] = $name;
            }
        }
        
        if (!empty($missingCheckpoints)) {
            return [
                'error' => 'Missing checkpoints: ' . implode(', ', $missingCheckpoints)
            ];
        }
        
        if (count($checkpoints) < 2) {
            return ['error' => 'At least 2 checkpoints required for summary'];
        }
        
        $sortedCheckpoints = $checkpoints;
        uasort($sortedCheckpoints, fn($a, $b) => $a['timestamp'] <=> $b['timestamp']);
        
        $first = reset($sortedCheckpoints);
        $last = end($sortedCheckpoints);
        
        $summary = [
            'total_duration' => $last['timestamp'] - $first['timestamp'],
            'total_duration_ms' => round(($last['timestamp'] - $first['timestamp']) * 1000, 2),
            'memory_start' => $first['memory_usage'],
            'memory_end' => $last['memory_usage'],
            'memory_diff' => $last['memory_usage'] - $first['memory_usage'],
            'peak_memory' => max(array_column($sortedCheckpoints, 'memory_peak')),
            'checkpoints' => count($sortedCheckpoints),
            'checkpoint_details' => []
        ];
        
        // Calculate intervals between checkpoints
        $previousCheckpoint = null;
        foreach ($sortedCheckpoints as $name => $checkpoint) {
            if ($previousCheckpoint !== null) {
                $interval = [
                    'from' => $previousCheckpoint['name'],
                    'to' => $name,
                    'duration_ms' => round(($checkpoint['timestamp'] - $previousCheckpoint['timestamp']) * 1000, 2),
                    'memory_diff' => $checkpoint['memory_usage'] - $previousCheckpoint['memory_usage']
                ];
                $summary['checkpoint_details'][] = $interval;
            }
            $previousCheckpoint = $checkpoint;
            $previousCheckpoint['name'] = $name;
        }
        
        return $summary;
    }
}