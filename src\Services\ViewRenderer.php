<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Views\Templates\AdminLayout;

/**
 * View Renderer Service
 * 
 * Service for rendering views using the template system with proper
 * data binding and layout composition.
 */
class ViewRenderer
{
    private array $globalData;

    public function __construct()
    {
        $this->globalData = [];
    }

    /**
     * Set global data available to all views
     *
     * @param array $data
     * @return self
     */
    public function setGlobalData(array $data): self
    {
        $this->globalData = array_merge($this->globalData, $data);
        return $this;
    }

    /**
     * Render a page with the admin layout
     *
     * @param string $pageClass
     * @param array $data
     * @return string
     */
    public function renderAdminPage(string $pageClass, array $data = []): string
    {
        // Merge global data with page-specific data
        $mergedData = array_merge($this->globalData, $data);

        // Create page instance
        $pageInstance = new $pageClass($mergedData);
        $pageContent = $pageInstance->render();

        // Create layout with page content
        $layout = new AdminLayout(array_merge($mergedData, [
            'content' => $pageContent
        ]));

        return $layout->render();
    }

    /**
     * Render a component
     *
     * @param string $componentClass
     * @param array $props
     * @return string
     */
    public function renderComponent(string $componentClass, array $props = []): string
    {
        $component = new $componentClass($props);
        return $component->render();
    }

    /**
     * Render a template
     *
     * @param string $templateClass
     * @param array $data
     * @return string
     */
    public function renderTemplate(string $templateClass, array $data = []): string
    {
        $mergedData = array_merge($this->globalData, $data);
        $template = new $templateClass($mergedData);
        return $template->render();
    }

    /**
     * Render JSON response
     *
     * @param array $data
     * @param int $statusCode
     * @return void
     */
    public function renderJson(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Render error page
     *
     * @param int $statusCode
     * @param string $message
     * @param string $title
     * @return string
     */
    public function renderError(int $statusCode, string $message, string $title = 'Error'): string
    {
        http_response_code($statusCode);
        
        return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title} - GuardGeo Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full text-center">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">{$title}</h1>
            <p class="text-gray-600 mb-6">{$message}</p>
            <div class="space-y-2">
                <button onclick="window.history.back()" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Go Back
                </button>
                <br>
                <a href="/admin/dashboard" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-home mr-2"></i>
                    Dashboard
                </a>
            </div>
        </div>
    </div>
</body>
</html>
HTML;
    }

    /**
     * Redirect to a URL
     *
     * @param string $url
     * @param int $statusCode
     * @return void
     */
    public function redirect(string $url, int $statusCode = 302): void
    {
        http_response_code($statusCode);
        header("Location: {$url}");
        exit;
    }
}