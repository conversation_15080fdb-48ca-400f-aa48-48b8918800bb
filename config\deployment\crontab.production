# GuardGeo Admin Platform - Production Cron Jobs
# 
# Install this crontab with: crontab -u www-data config/deployment/crontab.production
# Or copy individual entries to your system crontab

# Set environment variables
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=<EMAIL>

# Health checks every 5 minutes
*/5 * * * * /usr/bin/php /var/www/guardgeo-admin/scripts/monitoring/health-check.php | /usr/bin/php /var/www/guardgeo-admin/scripts/monitoring/alert-manager.php

# Performance monitoring every 15 minutes
*/15 * * * * /usr/bin/php /var/www/guardgeo-admin/scripts/monitoring/performance-monitor.php >> /var/log/guardgeo/performance.log 2>&1

# Full system backup daily at 2:00 AM
0 2 * * * /usr/bin/php /var/www/guardgeo-admin/scripts/backup/backup-manager.php backup >> /var/log/guardgeo/backup.log 2>&1

# Database backup every 6 hours
0 */6 * * * /usr/bin/php /var/www/guardgeo-admin/scripts/backup/backup-manager.php backup --database-only >> /var/log/guardgeo/backup.log 2>&1

# Log rotation and cleanup daily at 3:00 AM
0 3 * * * /usr/sbin/logrotate /etc/logrotate.d/guardgeo

# Clean up old temporary files daily at 4:00 AM
0 4 * * * find /tmp -name "guardgeo_*" -type f -mtime +1 -delete

# Clean up old session files daily at 4:30 AM
30 4 * * * find /var/www/guardgeo-admin/storage/sessions -name "sess_*" -type f -mtime +1 -delete

# Update IP intelligence cache for frequently accessed IPs every hour
0 * * * * /usr/bin/php /var/www/guardgeo-admin/scripts/maintenance/update-ip-cache.php >> /var/log/guardgeo/maintenance.log 2>&1

# Sync Freemius data every 30 minutes
*/30 * * * * /usr/bin/php /var/www/guardgeo-admin/scripts/maintenance/sync-freemius-data.php >> /var/log/guardgeo/maintenance.log 2>&1

# Generate daily performance report at 6:00 AM
0 6 * * * /usr/bin/php /var/www/guardgeo-admin/scripts/monitoring/performance-monitor.php report 24 > /var/www/guardgeo-admin/storage/reports/performance-$(date +\%Y-\%m-\%d).json

# Generate weekly performance report on Sundays at 7:00 AM
0 7 * * 0 /usr/bin/php /var/www/guardgeo-admin/scripts/monitoring/performance-monitor.php report 168 > /var/www/guardgeo-admin/storage/reports/performance-weekly-$(date +\%Y-\%m-\%d).json

# Clean up old performance reports monthly on the 1st at 8:00 AM
0 8 1 * * find /var/www/guardgeo-admin/storage/reports -name "performance-*.json" -type f -mtime +90 -delete

# SSL certificate expiry check daily at 9:00 AM
0 9 * * * /usr/bin/openssl x509 -in /etc/ssl/certs/guardgeo.crt -noout -checkend 2592000 || echo "SSL certificate expires within 30 days" | mail -s "SSL Certificate Warning" <EMAIL>

# Disk space monitoring every hour
0 * * * * df -h | awk '$5 > 80 {print "Disk usage warning: " $0}' | mail -s "Disk Space Alert" <EMAIL>

# Database maintenance weekly on Sundays at 1:00 AM
0 1 * * 0 /usr/bin/php /var/www/guardgeo-admin/scripts/maintenance/database-maintenance.php >> /var/log/guardgeo/maintenance.log 2>&1

# Security log analysis daily at 10:00 AM
0 10 * * * /usr/bin/php /var/www/guardgeo-admin/scripts/security/analyze-logs.php >> /var/log/guardgeo/security.log 2>&1

# Update GeoIP database monthly on the 1st at 5:00 AM
0 5 1 * * /usr/bin/php /var/www/guardgeo-admin/scripts/maintenance/update-geoip-database.php >> /var/log/guardgeo/maintenance.log 2>&1

# Test alert system monthly on the 15th at 11:00 AM
0 11 15 * * /usr/bin/php /var/www/guardgeo-admin/scripts/monitoring/alert-manager.php test warning

# Backup verification weekly on Saturdays at 11:00 PM
0 23 * * 6 /usr/bin/php /var/www/guardgeo-admin/scripts/backup/verify-backups.php >> /var/log/guardgeo/backup.log 2>&1

# Clean up old API request logs weekly on Sundays at 2:00 AM
0 2 * * 0 /usr/bin/php /var/www/guardgeo-admin/scripts/maintenance/cleanup-old-logs.php --days=30 >> /var/log/guardgeo/maintenance.log 2>&1

# Monitor external API health every 30 minutes
*/30 * * * * /usr/bin/php /var/www/guardgeo-admin/scripts/monitoring/external-api-monitor.php >> /var/log/guardgeo/api-monitoring.log 2>&1

# Generate security report weekly on Mondays at 8:00 AM
0 8 * * 1 /usr/bin/php /var/www/guardgeo-admin/scripts/security/generate-security-report.php > /var/www/guardgeo-admin/storage/reports/security-$(date +\%Y-\%m-\%d).json

# Update system packages check weekly on Wednesdays at 6:00 AM (notification only)
0 6 * * 3 /usr/bin/apt list --upgradable 2>/dev/null | grep -v "WARNING" | wc -l | awk '{if($1>0) print $1 " packages available for update"}' | mail -s "System Updates Available" <EMAIL>

# Check for failed login attempts every 15 minutes
*/15 * * * * /usr/bin/php /var/www/guardgeo-admin/scripts/security/check-failed-logins.php >> /var/log/guardgeo/security.log 2>&1

# Monitor rate limiting violations every 10 minutes
*/10 * * * * /usr/bin/php /var/www/guardgeo-admin/scripts/security/monitor-rate-limits.php >> /var/log/guardgeo/security.log 2>&1

# Optimize database tables weekly on Sundays at 3:00 AM
0 3 * * 0 /usr/bin/php /var/www/guardgeo-admin/scripts/maintenance/optimize-database.php >> /var/log/guardgeo/maintenance.log 2>&1

# Clean up expired sessions every 6 hours
0 */6 * * * /usr/bin/php /var/www/guardgeo-admin/scripts/maintenance/cleanup-sessions.php >> /var/log/guardgeo/maintenance.log 2>&1

# Monitor memory usage every 5 minutes
*/5 * * * * free | awk 'NR==2{printf "Memory Usage: %s/%sMB (%.2f%%)\n", $3,$2,$3*100/$2 }' | awk -F'[(%]' '$2>90{print}' | mail -s "High Memory Usage Alert" <EMAIL>

# Check for zombie processes every hour
0 * * * * ps aux | awk '$8 ~ /^Z/ { print "Zombie process found: " $0 }' | mail -s "Zombie Process Alert" <EMAIL>

# Restart PHP-FPM if memory usage is too high (weekly check)
0 4 * * 1 /usr/bin/php /var/www/guardgeo-admin/scripts/maintenance/check-php-fpm-memory.php >> /var/log/guardgeo/maintenance.log 2>&1