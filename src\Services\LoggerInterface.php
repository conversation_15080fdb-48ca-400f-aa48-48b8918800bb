<?php

namespace Skpassegna\GuardgeoApi\Services;

/**
 * Logger Interface
 * 
 * Defines the contract for specialized logger implementations.
 */
interface LoggerInterface
{
    /**
     * Log debug message
     */
    public function debug(string $message, array $context = []): void;
    
    /**
     * Log info message
     */
    public function info(string $message, array $context = []): void;
    
    /**
     * Log warning message
     */
    public function warning(string $message, array $context = []): void;
    
    /**
     * Log error message
     */
    public function error(string $message, array $context = []): void;
    
    /**
     * Log critical message
     */
    public function critical(string $message, array $context = []): void;
}