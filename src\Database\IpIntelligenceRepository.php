<?php

namespace Skpassegna\GuardgeoApi\Database;

use Skpassegna\GuardgeoApi\Models\IpDataModel;
use DateTime;

/**
 * IP Intelligence Repository
 * 
 * Handles database operations for IP intelligence data with caching
 * and deprecation rule management.
 */
class IpIntelligenceRepository extends BaseRepository
{
    protected string $table = 'ip_intelligence';
    
    /**
     * Find IP data by IP address
     */
    public function findByIp(string $ip): ?IpDataModel
    {
        try {
            $row = $this->findOneBy(['ip' => $ip]);
            
            if (!$row) {
                return null;
            }
            
            return $this->mapRowToModel($row);
            
        } catch (DatabaseException $e) {
            $this->logger->error('Failed to find IP data', [
                'ip' => $ip,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Create new IP intelligence record
     */
    public function create(IpDataModel $model): bool
    {
        try {
            $data = $model->toDatabaseArray();
            $this->insert($data);
            return true;
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to create IP intelligence record", [
                'ip' => $model->ip,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Update existing IP intelligence record
     */
    public function update(IpDataModel $model): bool
    {
        return $this->updateByIp($model);
    }
    
    /**
     * Update existing IP intelligence record by IP
     */
    public function updateByIp(IpDataModel $model): bool
    {
        try {
            $data = $model->toDatabaseArray();
            
            // Remove IP from update data since it's used in WHERE clause
            unset($data['ip']);
            
            $sql = "UPDATE {$this->table} SET " . 
                   implode(', ', array_map(fn($col) => "$col = :$col", array_keys($data))) . 
                   " WHERE ip = :ip";
            
            $data['ip'] = $model->ip; // Add IP back for WHERE clause
            
            $statement = $this->executeQuery($sql, $data);
            return $statement->rowCount() > 0;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to update IP intelligence record", [
                'ip' => $model->ip,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Delete IP intelligence record by IP
     */
    public function deleteByIp(string $ip): bool
    {
        try {
            $sql = "DELETE FROM {$this->table} WHERE ip = :ip";
            $statement = $this->executeQuery($sql, ['ip' => $ip]);
            return $statement->rowCount() > 0;
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to delete IP intelligence record", [
                'ip' => $ip,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Find IPs that need refresh based on deprecation rules
     */
    public function findExpiredIps(int $limit = 100): array
    {
        $now = (new DateTime())->format('Y-m-d H:i:s');
        
        $query = $this->queryBuilder
            ->select('ip, cached_at, location_expires_at, security_expires_at, connection_expires_at, company_expires_at')
            ->from($this->table)
            ->where('location_expires_at < ? OR security_expires_at < ? OR connection_expires_at < ? OR company_expires_at < ?')
            ->orderBy('cached_at ASC')
            ->limit($limit)
            ->build();
        
        $params = [$now, $now, $now, $now];
        
        try {
            $result = $this->connection->executeQuery($query, $params);
            $rows = $result->fetchAll();
            
            $expiredIps = [];
            foreach ($rows as $row) {
                $expiredIps[] = [
                    'ip' => $row['ip'],
                    'cached_at' => $row['cached_at'],
                    'expired_types' => $this->getExpiredTypes($row, $now)
                ];
            }
            
            return $expiredIps;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to find expired IPs", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get count of total cached IPs
     */
    public function getTotalCount(): int
    {
        $query = $this->queryBuilder
            ->select('COUNT(*) as count')
            ->from($this->table)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query);
            $row = $result->fetch();
            return (int) ($row['count'] ?? 0);
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get total IP count", [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * Get count of fresh (non-expired) IPs
     */
    public function getFreshCount(): int
    {
        $now = (new DateTime())->format('Y-m-d H:i:s');
        
        $query = $this->queryBuilder
            ->select('COUNT(*) as count')
            ->from($this->table)
            ->where('location_expires_at >= ? AND security_expires_at >= ? AND connection_expires_at >= ? AND company_expires_at >= ?')
            ->build();
        
        $params = [$now, $now, $now, $now];
        
        try {
            $result = $this->connection->executeQuery($query, $params);
            $row = $result->fetch();
            return (int) ($row['count'] ?? 0);
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get fresh IP count", [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * Get count of expired IPs
     */
    public function getExpiredCount(): int
    {
        $now = (new DateTime())->format('Y-m-d H:i:s');
        
        $query = $this->queryBuilder
            ->select('COUNT(*) as count')
            ->from($this->table)
            ->where('location_expires_at < ? OR security_expires_at < ? OR connection_expires_at < ? OR company_expires_at < ?')
            ->build();
        
        $params = [$now, $now, $now, $now];
        
        try {
            $result = $this->connection->executeQuery($query, $params);
            $row = $result->fetch();
            return (int) ($row['count'] ?? 0);
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get expired IP count", [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * Delete old data beyond retention period
     */
    public function deleteOldData(int $retentionDays): int
    {
        $cutoffDate = (new DateTime())->modify("-{$retentionDays} days")->format('Y-m-d H:i:s');
        
        $query = $this->queryBuilder
            ->delete($this->table)
            ->where('cached_at < ?')
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, [$cutoffDate]);
            return $result->rowCount();
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to delete old IP data", [
                'retention_days' => $retentionDays,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * Delete oldest entries to reduce cache size
     */
    public function deleteOldestEntries(int $count): int
    {
        try {
            // First, get the IDs of the oldest entries
            $selectQuery = $this->queryBuilder
                ->select('ip')
                ->from($this->table)
                ->orderBy('cached_at ASC')
                ->limit($count)
                ->build();
            
            $result = $this->connection->executeQuery($selectQuery);
            $oldestIps = $result->fetchAll();
            
            if (empty($oldestIps)) {
                return 0;
            }
            
            // Create placeholders for IN clause
            $placeholders = str_repeat('?,', count($oldestIps) - 1) . '?';
            $ips = array_column($oldestIps, 'ip');
            
            // Delete the oldest entries
            $deleteQuery = "DELETE FROM {$this->table} WHERE ip IN ({$placeholders})";
            $deleteResult = $this->connection->executeQuery($deleteQuery, $ips);
            
            $deletedCount = $deleteResult->rowCount();
            
            $this->logger->info("Deleted oldest cache entries", [
                'requested_count' => $count,
                'deleted_count' => $deletedCount
            ]);
            
            return $deletedCount;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to delete oldest entries", [
                'count' => $count,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * Find IPs by location country
     */
    public function findByCountry(string $countryCode, int $limit = 100): array
    {
        $query = $this->queryBuilder
            ->select('*')
            ->from($this->table)
            ->where("location_data->>'country'->>'code' = ?")
            ->limit($limit)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, [$countryCode]);
            $rows = $result->fetchAll();
            
            $models = [];
            foreach ($rows as $row) {
                $models[] = $this->mapRowToModel($row);
            }
            
            return $models;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to find IPs by country", [
                'country_code' => $countryCode,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Find high-risk IPs
     */
    public function findHighRiskIps(int $limit = 100): array
    {
        $query = $this->queryBuilder
            ->select('*')
            ->from($this->table)
            ->where("security_data->>'is_threat' = 'true' OR security_data->>'is_abuser' = 'true' OR security_data->>'is_attacker' = 'true'")
            ->orderBy('cached_at DESC')
            ->limit($limit)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query);
            $rows = $result->fetchAll();
            
            $models = [];
            foreach ($rows as $row) {
                $models[] = $this->mapRowToModel($row);
            }
            
            return $models;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to find high-risk IPs", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Find IPs using anonymization services
     */
    public function findAnonymizedIps(int $limit = 100): array
    {
        $query = $this->queryBuilder
            ->select('*')
            ->from($this->table)
            ->where("security_data->>'is_proxy' = 'true' OR security_data->>'is_vpn' = 'true' OR security_data->>'is_tor' = 'true' OR security_data->>'is_anonymous' = 'true'")
            ->orderBy('cached_at DESC')
            ->limit($limit)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query);
            $rows = $result->fetchAll();
            
            $models = [];
            foreach ($rows as $row) {
                $models[] = $this->mapRowToModel($row);
            }
            
            return $models;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to find anonymized IPs", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Search IPs with pagination
     */
    public function search(array $filters = [], int $page = 1, int $perPage = 50): array
    {
        $offset = ($page - 1) * $perPage;
        
        $queryBuilder = $this->queryBuilder
            ->select('*')
            ->from($this->table);
        
        $params = [];
        
        // Apply filters
        if (!empty($filters['ip'])) {
            $queryBuilder->where('ip LIKE ?');
            $params[] = '%' . $filters['ip'] . '%';
        }
        
        if (!empty($filters['country'])) {
            $queryBuilder->where("location_data->>'country'->>'code' = ?");
            $params[] = $filters['country'];
        }
        
        if (!empty($filters['is_threat'])) {
            $queryBuilder->where("security_data->>'is_threat' = ?");
            $params[] = $filters['is_threat'] ? 'true' : 'false';
        }
        
        if (!empty($filters['is_vpn'])) {
            $queryBuilder->where("security_data->>'is_vpn' = ?");
            $params[] = $filters['is_vpn'] ? 'true' : 'false';
        }
        
        $query = $queryBuilder
            ->orderBy('cached_at DESC')
            ->limit($perPage)
            ->offset($offset)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, $params);
            $rows = $result->fetchAll();
            
            $models = [];
            foreach ($rows as $row) {
                $models[] = $this->mapRowToModel($row);
            }
            
            return [
                'data' => $models,
                'page' => $page,
                'per_page' => $perPage,
                'total' => $this->getSearchCount($filters)
            ];
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to search IPs", [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [
                'data' => [],
                'page' => $page,
                'per_page' => $perPage,
                'total' => 0
            ];
        }
    }
    
    /**
     * Get search result count
     */
    private function getSearchCount(array $filters): int
    {
        $queryBuilder = $this->queryBuilder
            ->select('COUNT(*) as count')
            ->from($this->table);
        
        $params = [];
        
        // Apply same filters as search
        if (!empty($filters['ip'])) {
            $queryBuilder->where('ip LIKE ?');
            $params[] = '%' . $filters['ip'] . '%';
        }
        
        if (!empty($filters['country'])) {
            $queryBuilder->where("location_data->>'country'->>'code' = ?");
            $params[] = $filters['country'];
        }
        
        if (!empty($filters['is_threat'])) {
            $queryBuilder->where("security_data->>'is_threat' = ?");
            $params[] = $filters['is_threat'] ? 'true' : 'false';
        }
        
        if (!empty($filters['is_vpn'])) {
            $queryBuilder->where("security_data->>'is_vpn' = ?");
            $params[] = $filters['is_vpn'] ? 'true' : 'false';
        }
        
        $query = $queryBuilder->build();
        
        try {
            $result = $this->connection->executeQuery($query, $params);
            $row = $result->fetch();
            return (int) ($row['count'] ?? 0);
        } catch (DatabaseException $e) {
            return 0;
        }
    }
    
    /**
     * Map database row to IpDataModel
     */
    private function mapRowToModel(array $row): IpDataModel
    {
        // Decode JSON fields
        $row['carrier'] = json_decode($row['carrier_data'] ?? '[]', true);
        $row['company'] = json_decode($row['company_data'] ?? '[]', true);
        $row['connection'] = json_decode($row['connection_data'] ?? '[]', true);
        $row['currency'] = json_decode($row['currency_data'] ?? '[]', true);
        $row['location'] = json_decode($row['location_data'] ?? '[]', true);
        $row['security'] = json_decode($row['security_data'] ?? '[]', true);
        $row['time_zone'] = json_decode($row['time_zone_data'] ?? '[]', true);
        $row['raw_data'] = json_decode($row['raw_data'] ?? 'null', true);
        
        return new IpDataModel($row);
    }
    
    /**
     * Get expired data types for a row
     */
    private function getExpiredTypes(array $row, string $now): array
    {
        $expired = [];
        
        if ($row['location_expires_at'] < $now) {
            $expired[] = 'location';
        }
        
        if ($row['security_expires_at'] < $now) {
            $expired[] = 'security';
        }
        
        if ($row['connection_expires_at'] < $now) {
            $expired[] = 'connection';
        }
        
        if ($row['company_expires_at'] < $now) {
            $expired[] = 'company';
        }
        
        return $expired;
    }
    
    /**
     * Get IP records with pagination and filtering for admin interface
     */
    public function getIpRecordsPaginated(int $page, int $limit, array $filters = [], string $sortBy = 'cached_at', string $sortOrder = 'desc'): array
    {
        $offset = ($page - 1) * $limit;
        
        $queryBuilder = $this->queryBuilder
            ->select('*')
            ->from($this->table);
        
        $params = [];
        
        // Apply search filter
        if (!empty($filters['search'])) {
            $queryBuilder->where('ip::text LIKE ? OR hostname LIKE ?');
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        // Apply status filter
        if (!empty($filters['status'])) {
            $now = (new DateTime())->format('Y-m-d H:i:s');
            if ($filters['status'] === 'fresh') {
                $queryBuilder->where('security_expires_at >= ?');
                $params[] = $now;
            } elseif ($filters['status'] === 'stale') {
                $queryBuilder->where('security_expires_at < ?');
                $params[] = $now;
            }
        }
        
        // Apply sorting
        $validSortColumns = ['ip', 'cached_at', 'security_expires_at', 'location_expires_at'];
        if (!in_array($sortBy, $validSortColumns)) {
            $sortBy = 'cached_at';
        }
        
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
        
        $query = $queryBuilder
            ->orderBy("{$sortBy} {$sortOrder}")
            ->limit($limit)
            ->offset($offset)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, $params);
            $rows = $result->fetchAll();
            
            $records = [];
            foreach ($rows as $row) {
                $records[] = $this->mapRowToAdminArray($row);
            }
            
            return [
                'records' => $records,
                'total' => $this->getFilteredCount($filters)
            ];
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get paginated IP records", [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [
                'records' => [],
                'total' => 0
            ];
        }
    }
    
    /**
     * Get IP data by IP address for admin interface
     */
    public function getByIp(string $ip): ?array
    {
        $query = $this->queryBuilder
            ->select('*')
            ->from($this->table)
            ->where('ip = ?')
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, [$ip]);
            $row = $result->fetch();
            
            if (!$row) {
                return null;
            }
            
            return $this->mapRowToAdminArray($row);
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get IP data", [
                'ip' => $ip,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Get stale IP addresses for bulk refresh
     */
    public function getStaleIpAddresses(int $limit): array
    {
        $now = (new DateTime())->format('Y-m-d H:i:s');
        
        $query = $this->queryBuilder
            ->select('ip')
            ->from($this->table)
            ->where('security_expires_at < ?')
            ->orderBy('security_expires_at ASC')
            ->limit($limit)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, [$now]);
            $rows = $result->fetchAll();
            
            return array_column($rows, 'ip');
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get stale IP addresses", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get statistics for admin dashboard
     */
    public function getStatistics(): array
    {
        try {
            $now = (new DateTime())->format('Y-m-d H:i:s');
            
            // Total records
            $totalQuery = $this->queryBuilder
                ->select('COUNT(*) as count')
                ->from($this->table)
                ->build();
            $totalResult = $this->connection->executeQuery($totalQuery);
            $total = (int)$totalResult->fetch()['count'];
            
            // Fresh records
            $freshQuery = $this->queryBuilder
                ->select('COUNT(*) as count')
                ->from($this->table)
                ->where('security_expires_at >= ?')
                ->build();
            $freshResult = $this->connection->executeQuery($freshQuery, [$now]);
            $fresh = (int)$freshResult->fetch()['count'];
            
            // Recent additions (last 24 hours)
            $recentQuery = $this->queryBuilder
                ->select('COUNT(*) as count')
                ->from($this->table)
                ->where('cached_at >= ?')
                ->build();
            $yesterday = (new DateTime())->modify('-24 hours')->format('Y-m-d H:i:s');
            $recentResult = $this->connection->executeQuery($recentQuery, [$yesterday]);
            $recent = (int)$recentResult->fetch()['count'];
            
            // Threat IPs
            $threatQuery = $this->queryBuilder
                ->select('COUNT(*) as count')
                ->from($this->table)
                ->where("security_data->>'is_threat' = 'true'")
                ->build();
            $threatResult = $this->connection->executeQuery($threatQuery);
            $threats = (int)$threatResult->fetch()['count'];
            
            return [
                'total_records' => $total,
                'fresh_records' => $fresh,
                'stale_records' => $total - $fresh,
                'recent_additions' => $recent,
                'threat_ips' => $threats,
                'cache_efficiency' => $total > 0 ? round(($fresh / $total) * 100, 1) : 0
            ];
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get IP statistics", [
                'error' => $e->getMessage()
            ]);
            return [
                'total_records' => 0,
                'fresh_records' => 0,
                'stale_records' => 0,
                'recent_additions' => 0,
                'threat_ips' => 0,
                'cache_efficiency' => 0
            ];
        }
    }
    
    /**
     * Get filtered count for pagination
     */
    private function getFilteredCount(array $filters): int
    {
        $queryBuilder = $this->queryBuilder
            ->select('COUNT(*) as count')
            ->from($this->table);
        
        $params = [];
        
        // Apply same filters as main query
        if (!empty($filters['search'])) {
            $queryBuilder->where('ip::text LIKE ? OR hostname LIKE ?');
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if (!empty($filters['status'])) {
            $now = (new DateTime())->format('Y-m-d H:i:s');
            if ($filters['status'] === 'fresh') {
                $queryBuilder->where('security_expires_at >= ?');
                $params[] = $now;
            } elseif ($filters['status'] === 'stale') {
                $queryBuilder->where('security_expires_at < ?');
                $params[] = $now;
            }
        }
        
        $query = $queryBuilder->build();
        
        try {
            $result = $this->connection->executeQuery($query, $params);
            $row = $result->fetch();
            return (int)($row['count'] ?? 0);
        } catch (DatabaseException $e) {
            return 0;
        }
    }
    
    /**
     * Map database row to admin-friendly array
     */
    private function mapRowToAdminArray(array $row): array
    {
        $now = new DateTime();
        
        // Decode JSON fields
        $location = json_decode($row['location_data'] ?? '{}', true);
        $security = json_decode($row['security_data'] ?? '{}', true);
        $connection = json_decode($row['connection_data'] ?? '{}', true);
        $company = json_decode($row['company_data'] ?? '{}', true);
        
        // Determine status
        $securityExpires = new DateTime($row['security_expires_at'] ?? 'now');
        $isStale = $now > $securityExpires;
        
        return [
            'ip' => $row['ip'],
            'type' => $row['type'] ?? 'IPv4',
            'hostname' => $row['hostname'],
            'location' => [
                'country' => $location['country']['name'] ?? 'Unknown',
                'country_code' => $location['country']['code'] ?? '',
                'region' => $location['region']['name'] ?? '',
                'city' => $location['city'] ?? '',
                'coordinates' => [
                    'latitude' => $location['latitude'] ?? null,
                    'longitude' => $location['longitude'] ?? null
                ]
            ],
            'security' => [
                'is_threat' => $security['is_threat'] ?? false,
                'is_abuser' => $security['is_abuser'] ?? false,
                'is_attacker' => $security['is_attacker'] ?? false,
                'is_proxy' => $security['is_proxy'] ?? false,
                'is_vpn' => $security['is_vpn'] ?? false,
                'is_tor' => $security['is_tor'] ?? false,
                'is_anonymous' => $security['is_anonymous'] ?? false
            ],
            'connection' => [
                'asn' => $connection['asn'] ?? null,
                'organization' => $connection['organization'] ?? '',
                'type' => $connection['type'] ?? ''
            ],
            'company' => [
                'name' => $company['name'] ?? '',
                'domain' => $company['domain'] ?? '',
                'type' => $company['type'] ?? ''
            ],
            'cached_at' => $row['cached_at'],
            'expires_at' => [
                'location' => $row['location_expires_at'],
                'security' => $row['security_expires_at'],
                'connection' => $row['connection_expires_at'],
                'company' => $row['company_expires_at']
            ],
            'is_stale' => $isStale,
            'status' => $isStale ? 'stale' : 'fresh',
            'raw_data' => json_decode($row['raw_data'] ?? 'null', true)
        ];
    }

    /**
     * Health check for repository
     */
    public function healthCheck(): bool
    {
        try {
            $query = $this->queryBuilder
                ->select('1')
                ->from($this->table)
                ->limit(1)
                ->build();
            
            $this->connection->executeQuery($query);
            return true;
        } catch (DatabaseException $e) {
            return false;
        }
    }
    
    /**
     * Find IPs that are expiring within specified time
     */
    public function findExpiringIps(DateTime $expiryThreshold, int $limit): array
    {
        $thresholdStr = $expiryThreshold->format('Y-m-d H:i:s');
        
        $query = $this->queryBuilder
            ->select('ip')
            ->from($this->table)
            ->where('location_expires_at <= ? OR security_expires_at <= ? OR connection_expires_at <= ? OR company_expires_at <= ?')
            ->orderBy('cached_at ASC')
            ->limit($limit)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, [$thresholdStr, $thresholdStr, $thresholdStr, $thresholdStr]);
            $rows = $result->fetchAll();
            
            return array_column($rows, 'ip');
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to find expiring IPs", [
                'threshold' => $thresholdStr,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Find IPs by geographic regions
     */
    public function findIpsByRegions(array $regions, int $limit): array
    {
        if (empty($regions)) {
            return [];
        }
        
        $conditions = [];
        $params = [];
        
        foreach ($regions as $region) {
            $conditions[] = "location_data->>'country'->>'code' = ?";
            $params[] = $region;
        }
        
        $whereClause = implode(' OR ', $conditions);
        
        $query = $this->queryBuilder
            ->select('ip')
            ->from($this->table)
            ->where($whereClause)
            ->limit($limit)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, $params);
            $rows = $result->fetchAll();
            
            return array_column($rows, 'ip');
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to find IPs by regions", [
                'regions' => $regions,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Find IPs with security threats that have stale security data
     */
    public function findSecurityThreatIps(int $limit): array
    {
        $now = (new DateTime())->format('Y-m-d H:i:s');
        
        $query = $this->queryBuilder
            ->select('ip')
            ->from($this->table)
            ->where("(security_data->>'is_threat' = 'true' OR security_data->>'is_abuser' = 'true' OR security_data->>'is_attacker' = 'true') AND security_expires_at < ?")
            ->orderBy('security_expires_at ASC')
            ->limit($limit)
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, [$now]);
            $rows = $result->fetchAll();
            
            return array_column($rows, 'ip');
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to find security threat IPs", [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Find expired IPs by specific data type
     */
    public function findExpiredByDataType(string $dataType, DateTime $cutoffTime): array
    {
        $cutoffStr = $cutoffTime->format('Y-m-d H:i:s');
        
        $columnMap = [
            'security' => 'security_expires_at',
            'location' => 'location_expires_at',
            'connection' => 'connection_expires_at',
            'company' => 'company_expires_at'
        ];
        
        if (!isset($columnMap[$dataType])) {
            return [];
        }
        
        $column = $columnMap[$dataType];
        
        $query = $this->queryBuilder
            ->select('ip')
            ->from($this->table)
            ->where("{$column} < ?")
            ->build();
        
        try {
            $result = $this->connection->executeQuery($query, [$cutoffStr]);
            $rows = $result->fetchAll();
            
            return array_column($rows, 'ip');
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to find expired IPs by data type", [
                'data_type' => $dataType,
                'cutoff' => $cutoffStr,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * Get data type breakdown statistics
     */
    public function getDataTypeBreakdown(): array
    {
        try {
            $query = $this->queryBuilder
                ->select("type, COUNT(*) as count")
                ->from($this->table)
                ->groupBy('type')
                ->build();
            
            $result = $this->connection->executeQuery($query);
            $rows = $result->fetchAll();
            
            $breakdown = ['IPv4' => 0, 'IPv6' => 0];
            
            foreach ($rows as $row) {
                $breakdown[$row['type']] = (int) $row['count'];
            }
            
            return $breakdown;
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get data type breakdown", [
                'error' => $e->getMessage()
            ]);
            return ['IPv4' => 0, 'IPv6' => 0];
        }
    }
    
    /**
     * Get cache statistics for CacheManager
     */
    public function getCacheStatistics(): array
    {
        try {
            $totalCount = $this->getTotalCount();
            $freshCount = $this->getFreshCount();
            $expiredCount = $this->getExpiredCount();
            
            return [
                'total_cached_ips' => $totalCount,
                'fresh_ips' => $freshCount,
                'expired_ips' => $expiredCount,
                'fresh_percentage' => $totalCount > 0 ? round(($freshCount / $totalCount) * 100, 2) : 0,
                'expired_percentage' => $totalCount > 0 ? round(($expiredCount / $totalCount) * 100, 2) : 0
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get cache statistics", [
                'error' => $e->getMessage()
            ]);
            return [
                'total_cached_ips' => 0,
                'fresh_ips' => 0,
                'expired_ips' => 0,
                'fresh_percentage' => 0,
                'expired_percentage' => 0
            ];
        }
    }
}