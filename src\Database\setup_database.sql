-- GuardGeo Admin Platform Database Setup Script
-- This script sets up the complete database schema and creates the initial Super Admin account
-- 
-- Usage:
-- 1. Create a PostgreSQL database named 'guardgeo_admin'
-- 2. Run this script: psql -d guardgeo_admin -f setup_database.sql
-- 3. Change the default Super Admin credentials after first login

-- Display setup start message
DO $$
BEGIN
    RAISE NOTICE '=== GuardGeo Admin Platform Database Setup ===';
    RAISE NOTICE 'Starting database schema creation...';
END $$;

-- Include the main schema
\i schema.sql

-- Display schema completion message
DO $$
BEGIN
    RAISE NOTICE 'Database schema created successfully!';
    RAISE NOTICE 'Creating Super Admin account...';
END $$;

-- Include the Super Admin creation script
\i create_super_admin.sql

-- Display completion message
DO $$
BEGIN
    RAISE NOTICE '=== Database Setup Complete ===';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Configure database connection in your application';
    RAISE NOTICE '2. <NAME_EMAIL> / SuperAdmin123!';
    RAISE NOTICE '3. IMMEDIATELY change the default credentials';
    RAISE NOTICE '4. Create additional admin accounts as needed';
END $$;