# GuardGeo Admin Platform - Production Nginx Configuration

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
limit_req_zone $binary_remote_addr zone=admin:10m rate=200r/m;
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

# Connection limiting
limit_conn_zone $binary_remote_addr zone=perip:10m;

# Upstream PHP-FPM
upstream php-fpm {
    server guardgeo-app:9000;
    keepalive 32;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name _;
    return 301 https://$host$request_uri;
}

# Main HTTPS server
server {
    listen 443 ssl http2;
    server_name your-production-domain.com;
    root /var/www/html;
    index index.php;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/guardgeo.crt;
    ssl_certificate_key /etc/ssl/private/guardgeo.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Permitted-Cross-Domain-Policies "none" always;
    add_header Cross-Origin-Embedder-Policy "require-corp" always;
    add_header Cross-Origin-Opener-Policy "same-origin" always;
    add_header Cross-Origin-Resource-Policy "same-origin" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(self), sync-xhr=()" always;

    # Content Security Policy
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests" always;

    # Connection limits
    limit_conn perip 10;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # API Routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        try_files $uri $uri/ /api.php?$query_string;
        
        # CORS headers for API (if needed)
        add_header Access-Control-Allow-Origin "https://your-wordpress-sites.com" always;
        add_header Access-Control-Allow-Methods "POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
        add_header Access-Control-Max-Age "86400" always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # Admin Routes
    location /admin/ {
        limit_req zone=admin burst=50 nodelay;
        
        try_files $uri $uri/ /admin.php?$query_string;
    }

    # Admin Login (stricter rate limiting)
    location /admin/login {
        limit_req zone=login burst=3 nodelay;
        
        try_files $uri $uri/ /admin.php?$query_string;
    }

    # PHP Processing
    location ~ \.php$ {
        fastcgi_pass php-fpm;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param HTTPS on;
        fastcgi_param HTTP_SCHEME https;
        
        # Security parameters
        fastcgi_param PHP_VALUE "expose_php=0";
        fastcgi_param PHP_VALUE "display_errors=0";
        fastcgi_param PHP_VALUE "log_errors=1";
        fastcgi_param PHP_VALUE "max_execution_time=60";
        fastcgi_param PHP_VALUE "memory_limit=256M";
        fastcgi_param PHP_VALUE "post_max_size=10M";
        fastcgi_param PHP_VALUE "upload_max_filesize=5M";
        
        include fastcgi_params;
        
        # Timeout settings
        fastcgi_connect_timeout 60s;
        fastcgi_send_timeout 60s;
        fastcgi_read_timeout 60s;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }

    # Health check endpoint (no rate limiting)
    location /health {
        access_log off;
        return 200 "OK\n";
        add_header Content-Type text/plain;
    }

    # PHP-FPM status (restricted access)
    location ~ ^/(php-fpm-status|php-fpm-ping)$ {
        allow 127.0.0.1;
        allow **********/16;  # Docker network
        deny all;
        
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Static assets caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Security headers for static assets
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-Frame-Options "DENY" always;
    }

    # Deny access to sensitive files and directories
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /(vendor|src|config|logs|scripts|storage|\.env|composer\.(json|lock)|\.git)/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Deny access to PHP files in uploads directory
    location ~* /uploads/.*\.php$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Block common exploit attempts
    location ~* /(wp-admin|wp-login|phpmyadmin|adminer|shell|cmd|exec)/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Block suspicious user agents
    if ($http_user_agent ~* (bot|crawler|spider|scraper|curl|wget|python|perl|ruby|scanner|exploit|hack|attack|sqlmap|nikto|nmap|masscan)) {
        return 403;
    }

    # Block suspicious request methods
    if ($request_method !~ ^(GET|POST|HEAD|OPTIONS)$) {
        return 405;
    }

    # Logging
    access_log /var/log/nginx/guardgeo_access.log combined buffer=16k flush=5m;
    error_log /var/log/nginx/guardgeo_error.log warn;

    # Custom error pages
    error_page 403 /errors/403.html;
    error_page 404 /errors/404.html;
    error_page 500 502 503 504 /errors/50x.html;

    location ^~ /errors/ {
        internal;
        root /var/www/html/resources/views;
    }
}

# Monitoring server (internal access only)
server {
    listen 8080;
    server_name localhost;
    
    allow 127.0.0.1;
    allow **********/16;  # Docker network
    deny all;

    location /nginx-status {
        stub_status on;
        access_log off;
    }

    location /php-fpm-status {
        fastcgi_pass php-fpm;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        access_log off;
    }
}