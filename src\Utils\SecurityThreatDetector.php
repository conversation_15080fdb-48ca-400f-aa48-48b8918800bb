<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Security Threat Detector
 * 
 * Advanced security threat detection with pattern matching,
 * behavioral analysis, and threat intelligence integration.
 */
class SecurityThreatDetector
{
    private LoggingService $logger;
    private array $threatPatterns;
    private array $suspiciousUserAgents;
    private array $knownAttackSignatures;

    public function __construct(LoggingService $logger)
    {
        $this->logger = $logger;
        $this->initializeThreatPatterns();
        $this->initializeSuspiciousUserAgents();
        $this->initializeAttackSignatures();
    }

    /**
     * Analyze request for security threats
     *
     * @param array $requestData All request data (GET, POST, headers, etc.)
     * @return array Threat analysis results
     */
    public function analyzeRequest(array $requestData): array
    {
        $threats = [];
        $riskScore = 0;

        // Analyze input data for injection attacks
        $injectionThreats = $this->detectInjectionAttacks($requestData);
        if (!empty($injectionThreats)) {
            $threats = array_merge($threats, $injectionThreats);
            $riskScore += count($injectionThreats) * 25;
        }

        // Analyze for XSS attempts
        $xssThreats = $this->detectXssAttacks($requestData);
        if (!empty($xssThreats)) {
            $threats = array_merge($threats, $xssThreats);
            $riskScore += count($xssThreats) * 20;
        }

        // Analyze user agent
        $userAgentThreats = $this->analyzeUserAgent();
        if (!empty($userAgentThreats)) {
            $threats = array_merge($threats, $userAgentThreats);
            $riskScore += count($userAgentThreats) * 15;
        }

        // Analyze request patterns
        $patternThreats = $this->analyzeRequestPatterns();
        if (!empty($patternThreats)) {
            $threats = array_merge($threats, $patternThreats);
            $riskScore += count($patternThreats) * 10;
        }

        // Analyze for path traversal
        $traversalThreats = $this->detectPathTraversal($requestData);
        if (!empty($traversalThreats)) {
            $threats = array_merge($threats, $traversalThreats);
            $riskScore += count($traversalThreats) * 30;
        }

        // Analyze for command injection
        $commandThreats = $this->detectCommandInjection($requestData);
        if (!empty($commandThreats)) {
            $threats = array_merge($threats, $commandThreats);
            $riskScore += count($commandThreats) * 35;
        }

        return [
            'threats' => $threats,
            'risk_score' => min(100, $riskScore),
            'threat_level' => $this->calculateThreatLevel($riskScore),
            'recommended_action' => $this->getRecommendedAction($riskScore)
        ];
    }

    /**
     * Detect SQL injection and NoSQL injection attempts
     *
     * @param array $requestData
     * @return array Detected injection threats
     */
    private function detectInjectionAttacks(array $requestData): array
    {
        $threats = [];

        foreach ($requestData as $key => $value) {
            if (!is_string($value)) {
                continue;
            }

            // SQL injection patterns
            foreach ($this->threatPatterns['sql_injection'] as $pattern) {
                if (preg_match($pattern, $value)) {
                    $threats[] = [
                        'type' => 'sql_injection',
                        'field' => $key,
                        'pattern' => $pattern,
                        'value' => substr($value, 0, 100),
                        'severity' => 'high'
                    ];
                }
            }

            // NoSQL injection patterns
            foreach ($this->threatPatterns['nosql_injection'] as $pattern) {
                if (preg_match($pattern, $value)) {
                    $threats[] = [
                        'type' => 'nosql_injection',
                        'field' => $key,
                        'pattern' => $pattern,
                        'value' => substr($value, 0, 100),
                        'severity' => 'high'
                    ];
                }
            }
        }

        return $threats;
    }  
  /**
     * Detect XSS (Cross-Site Scripting) attempts
     *
     * @param array $requestData
     * @return array Detected XSS threats
     */
    private function detectXssAttacks(array $requestData): array
    {
        $threats = [];

        foreach ($requestData as $key => $value) {
            if (!is_string($value)) {
                continue;
            }

            foreach ($this->threatPatterns['xss'] as $pattern) {
                if (preg_match($pattern, $value)) {
                    $threats[] = [
                        'type' => 'xss',
                        'field' => $key,
                        'pattern' => $pattern,
                        'value' => substr($value, 0, 100),
                        'severity' => 'medium'
                    ];
                }
            }
        }

        return $threats;
    }

    /**
     * Detect path traversal attempts
     *
     * @param array $requestData
     * @return array Detected path traversal threats
     */
    private function detectPathTraversal(array $requestData): array
    {
        $threats = [];

        foreach ($requestData as $key => $value) {
            if (!is_string($value)) {
                continue;
            }

            foreach ($this->threatPatterns['path_traversal'] as $pattern) {
                if (preg_match($pattern, $value)) {
                    $threats[] = [
                        'type' => 'path_traversal',
                        'field' => $key,
                        'pattern' => $pattern,
                        'value' => substr($value, 0, 100),
                        'severity' => 'high'
                    ];
                }
            }
        }

        return $threats;
    }

    /**
     * Detect command injection attempts
     *
     * @param array $requestData
     * @return array Detected command injection threats
     */
    private function detectCommandInjection(array $requestData): array
    {
        $threats = [];

        foreach ($requestData as $key => $value) {
            if (!is_string($value)) {
                continue;
            }

            foreach ($this->threatPatterns['command_injection'] as $pattern) {
                if (preg_match($pattern, $value)) {
                    $threats[] = [
                        'type' => 'command_injection',
                        'field' => $key,
                        'pattern' => $pattern,
                        'value' => substr($value, 0, 100),
                        'severity' => 'critical'
                    ];
                }
            }
        }

        return $threats;
    }

    /**
     * Analyze user agent for suspicious patterns
     *
     * @return array Detected user agent threats
     */
    private function analyzeUserAgent(): array
    {
        $threats = [];
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        if (empty($userAgent)) {
            $threats[] = [
                'type' => 'missing_user_agent',
                'field' => 'user_agent',
                'value' => '',
                'severity' => 'low'
            ];
            return $threats;
        }

        foreach ($this->suspiciousUserAgents as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                $threats[] = [
                    'type' => 'suspicious_user_agent',
                    'field' => 'user_agent',
                    'pattern' => $pattern,
                    'value' => substr($userAgent, 0, 100),
                    'severity' => 'medium'
                ];
            }
        }

        return $threats;
    }

    /**
     * Analyze request patterns for suspicious behavior
     *
     * @return array Detected pattern threats
     */
    private function analyzeRequestPatterns(): array
    {
        $threats = [];
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        $method = $_SERVER['REQUEST_METHOD'] ?? '';

        // Check for suspicious paths
        foreach ($this->threatPatterns['suspicious_paths'] as $pattern) {
            if (preg_match($pattern, $uri)) {
                $threats[] = [
                    'type' => 'suspicious_path',
                    'field' => 'request_uri',
                    'pattern' => $pattern,
                    'value' => $uri,
                    'severity' => 'medium'
                ];
            }
        }

        // Check for unusual request methods
        $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
        if (!in_array($method, $allowedMethods)) {
            $threats[] = [
                'type' => 'unusual_method',
                'field' => 'request_method',
                'value' => $method,
                'severity' => 'low'
            ];
        }

        return $threats;
    }

    /**
     * Calculate threat level based on risk score
     *
     * @param int $riskScore
     * @return string Threat level
     */
    private function calculateThreatLevel(int $riskScore): string
    {
        if ($riskScore >= 75) {
            return 'critical';
        } elseif ($riskScore >= 50) {
            return 'high';
        } elseif ($riskScore >= 25) {
            return 'medium';
        } elseif ($riskScore > 0) {
            return 'low';
        } else {
            return 'none';
        }
    }

    /**
     * Get recommended action based on risk score
     *
     * @param int $riskScore
     * @return string Recommended action
     */
    private function getRecommendedAction(int $riskScore): string
    {
        if ($riskScore >= 75) {
            return 'block_immediately';
        } elseif ($riskScore >= 50) {
            return 'block_and_investigate';
        } elseif ($riskScore >= 25) {
            return 'monitor_closely';
        } elseif ($riskScore > 0) {
            return 'log_and_monitor';
        } else {
            return 'allow';
        }
    }

    /**
     * Initialize threat detection patterns
     *
     * @return void
     */
    private function initializeThreatPatterns(): void
    {
        $this->threatPatterns = [
            'sql_injection' => [
                '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute)\s+/i',
                '/(\s|^)(or|and)\s+[\w\'"]+\s*=\s*[\w\'"]+/i',
                '/(\s|^)(or|and)\s+\d+\s*=\s*\d+/i',
                '/(\s|^)(having|group\s+by|order\s+by)\s+/i',
                '/(\s|^)(information_schema|sys\.|mysql\.|pg_)/i',
                '/(\s|^)(load_file|into\s+outfile|into\s+dumpfile)/i',
                '/(\s|^)(benchmark|sleep|waitfor\s+delay)/i',
                '/(\s|^)(@@|@)/i',
                '/(\s|^)(--|#|\/\*|\*\/)/i',
                '/(\s|^)(;|\||&)/i',
                '/(\s|^)(0x[0-9a-f]+)/i'
            ],
            'nosql_injection' => [
                '/\$where/i',
                '/\$ne/i',
                '/\$in/i',
                '/\$nin/i',
                '/\$gt/i',
                '/\$lt/i',
                '/\$regex/i',
                '/\$exists/i'
            ],
            'xss' => [
                '/<script[^>]*>.*?<\/script>/is',
                '/<iframe[^>]*>.*?<\/iframe>/is',
                '/<object[^>]*>.*?<\/object>/is',
                '/<embed[^>]*>/i',
                '/javascript:/i',
                '/vbscript:/i',
                '/on\w+\s*=/i',
                '/<img[^>]+src[^>]*>/i',
                '/expression\s*\(/i',
                '/url\s*\(/i'
            ],
            'path_traversal' => [
                '/\.\.[\/\\\\]/i',
                '/\.\.%2f/i',
                '/\.\.%5c/i',
                '/%2e%2e%2f/i',
                '/%2e%2e%5c/i',
                '/\.\.\/\.\.\/\.\.\//i'
            ],
            'command_injection' => [
                '/[;&|`$(){}[\]]/i',
                '/\|\s*(cat|ls|pwd|whoami|id|uname)/i',
                '/;\s*(cat|ls|pwd|whoami|id|uname)/i',
                '/&&\s*(cat|ls|pwd|whoami|id|uname)/i',
                '/\$\(.*\)/i',
                '/`.*`/i'
            ],
            'suspicious_paths' => [
                '/\/\.(env|git|svn|htaccess|htpasswd)/i',
                '/\/(admin|phpmyadmin|wp-admin|wp-login)/i',
                '/\/(config|backup|test|debug)/i',
                '/\/(shell|cmd|exec|eval)/i',
                '/\/\w+\.(php|asp|jsp|cgi)$/i'
            ]
        ];
    }

    /**
     * Initialize suspicious user agent patterns
     *
     * @return void
     */
    private function initializeSuspiciousUserAgents(): void
    {
        $this->suspiciousUserAgents = [
            '/bot|crawler|spider|scraper/i',
            '/curl|wget|python|perl|ruby/i',
            '/scanner|exploit|hack|attack/i',
            '/sqlmap|nikto|nmap|masscan|burp/i',
            '/acunetix|nessus|openvas|w3af/i',
            '/havij|pangolin|sqlninja/i'
        ];
    }

    /**
     * Initialize known attack signatures
     *
     * @return void
     */
    private function initializeAttackSignatures(): void
    {
        $this->knownAttackSignatures = [
            'log4j' => '/\$\{jndi:(ldap|rmi|dns):/i',
            'spring4shell' => '/class\.module\.classLoader/i',
            'struts2' => '/%\{.*\}/i',
            'shellshock' => '/\(\)\s*\{.*;\s*\}/i'
        ];
    }

    /**
     * Log detected threat
     *
     * @param array $threat
     * @return void
     */
    public function logThreat(array $threat): void
    {
        $this->logger->logError('Security threat detected', array_merge($threat, [
            'timestamp' => date('c'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
        ]));
    }

    /**
     * Get threat statistics
     *
     * @return array Threat detection statistics
     */
    public function getThreatStatistics(): array
    {
        return [
            'total_patterns' => array_sum(array_map('count', $this->threatPatterns)),
            'pattern_categories' => array_keys($this->threatPatterns),
            'suspicious_user_agents' => count($this->suspiciousUserAgents),
            'attack_signatures' => count($this->knownAttackSignatures)
        ];
    }
}