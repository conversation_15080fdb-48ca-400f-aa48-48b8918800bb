-- System Configuration Table
-- Stores runtime configuration that can be modified through the admin interface

CREATE TABLE IF NOT EXISTS system_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(50) NOT NULL DEFAULT 'string' CHECK (config_type IN ('string', 'integer', 'float', 'boolean', 'json', 'array')),
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_sensitive BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER REFERENCES admin_users(id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(config_key);
CREATE INDEX IF NOT EXISTS idx_system_config_active ON system_config(is_active);
CREATE INDEX IF NOT EXISTS idx_system_config_type ON system_config(config_type);

-- Insert default configuration values
INSERT INTO system_config (config_key, config_value, config_type, description, is_sensitive) VALUES
-- IP Cache Configuration
('ip_cache.location_days', '10', 'integer', 'Days to cache IP location data', false),
('ip_cache.security_days', '3', 'integer', 'Days to cache IP security data', false),
('ip_cache.connection_days', '7', 'integer', 'Days to cache IP connection data', false),
('ip_cache.company_days', '30', 'integer', 'Days to cache IP company data', false),
('ip_cache.max_size', '100000', 'integer', 'Maximum number of IP records to cache', false),
('ip_cache.retention_days', '90', 'integer', 'Days to retain IP cache records', false),
('ip_cache.batch_size', '100', 'integer', 'Batch size for IP processing', false),
('ip_cache.enable_auto_cleanup', 'true', 'boolean', 'Enable automatic cache cleanup', false),

-- API Configuration
('api.rate_limit_requests', '1000', 'integer', 'API requests per rate limit window', false),
('api.rate_limit_window', '3600', 'integer', 'Rate limit window in seconds', false),
('api.request_timeout', '30', 'integer', 'API request timeout in seconds', false),
('api.max_request_size', '1048576', 'integer', 'Maximum API request size in bytes', false),

-- Security Configuration
('security.max_login_attempts', '5', 'integer', 'Maximum login attempts before lockout', false),
('security.lockout_duration', '900', 'integer', 'Account lockout duration in seconds', false),
('security.session_lifetime', '86400', 'integer', 'Admin session lifetime in seconds', false),
('security.password_min_length', '12', 'integer', 'Minimum password length', false),

-- Monitoring Configuration
('monitoring.health_check_interval', '300', 'integer', 'Health check interval in seconds', false),
('monitoring.performance_tracking', 'true', 'boolean', 'Enable performance tracking', false),
('monitoring.error_reporting', 'true', 'boolean', 'Enable error reporting', false),

-- Email Configuration
('email.allowed_domains', 'skpassegna.me,guardgeo.com', 'array', 'Allowed email domains for admin accounts', false),
('email.blocked_domains', '', 'array', 'Blocked email domains', false),
('email.strict_mode', 'true', 'boolean', 'Strict email domain validation', false),

-- Logging Configuration
('logging.level', 'info', 'string', 'Default logging level', false),
('logging.max_file_size', '********', 'integer', 'Maximum log file size in bytes', false),
('logging.max_files', '5', 'integer', 'Maximum number of log files to keep', false),

-- External API Configuration
('freemius.timeout', '30', 'integer', 'Freemius API timeout in seconds', false),
('freemius.retry_attempts', '3', 'integer', 'Freemius API retry attempts', false),
('freemius.retry_delay', '1', 'integer', 'Freemius API retry delay in seconds', false),
('ipregistry.timeout', '30', 'integer', 'ipRegistry API timeout in seconds', false),
('ipregistry.retry_attempts', '3', 'integer', 'ipRegistry API retry attempts', false),
('ipregistry.retry_delay', '1', 'integer', 'ipRegistry API retry delay in seconds', false)

ON CONFLICT (config_key) DO NOTHING;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_system_config_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_system_config_timestamp
    BEFORE UPDATE ON system_config
    FOR EACH ROW
    EXECUTE FUNCTION update_system_config_timestamp();