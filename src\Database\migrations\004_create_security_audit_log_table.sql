-- Security Audit Log Table
-- Stores comprehensive security events and audit trail

CREATE TABLE IF NOT EXISTS security_audit_log (
    id SERIAL PRIMARY KEY,
    event_id VARCHAR(50) UNIQUE NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    user_id INTEGER,
    session_id VARCHAR(128),
    ip_address INET,
    user_agent TEXT,
    request_uri TEXT,
    request_method VARCHAR(10),
    event_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_security_audit_event_type ON security_audit_log(event_type);
CREATE INDEX IF NOT EXISTS idx_security_audit_severity ON security_audit_log(severity);
CREATE INDEX IF NOT EXISTS idx_security_audit_user_id ON security_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_security_audit_ip_address ON security_audit_log(ip_address);
CREATE INDEX IF NOT EXISTS idx_security_audit_created_at ON security_audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_security_audit_event_type_created_at ON security_audit_log(event_type, created_at);

-- Composite index for common queries
CREATE INDEX IF NOT EXISTS idx_security_audit_severity_created_at ON security_audit_log(severity, created_at);

-- Foreign key constraint (if admin_users table exists)
-- ALTER TABLE security_audit_log ADD CONSTRAINT fk_security_audit_user 
-- FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE SET NULL;

-- Comments
COMMENT ON TABLE security_audit_log IS 'Comprehensive security audit log for all security events';
COMMENT ON COLUMN security_audit_log.event_id IS 'Unique identifier for the security event';
COMMENT ON COLUMN security_audit_log.event_type IS 'Type of security event (auth_failure, threat_detected, etc.)';
COMMENT ON COLUMN security_audit_log.severity IS 'Severity level: low, medium, high, critical';
COMMENT ON COLUMN security_audit_log.event_data IS 'JSON data containing event-specific information';