<?php

namespace Skpassegna\GuardgeoApi\Utils;

/**
 * Security Manager
 * 
 * Comprehensive security utilities for input sanitization, XSS protection,
 * CSRF validation, and other security measures.
 */
class SecurityManager
{
    private const CSRF_TOKEN_LENGTH = 32;
    private const CSRF_TOKEN_LIFETIME = 3600; // 1 hour
    
    /**
     * Sanitize input to prevent XSS attacks
     */
    public function sanitizeInput($input, string $type = 'string')
    {
        if (is_null($input)) {
            return null;
        }

        switch ($type) {
            case 'string':
                return $this->sanitizeString($input);
            case 'html':
                return $this->sanitizeHtml($input);
            case 'email':
                return $this->sanitizeEmail($input);
            case 'url':
                return $this->sanitizeUrl($input);
            case 'int':
                return $this->sanitizeInteger($input);
            case 'float':
                return $this->sanitizeFloat($input);
            case 'boolean':
                return $this->sanitizeBoolean($input);
            case 'array':
                return $this->sanitizeArray($input);
            default:
                return $this->sanitizeString($input);
        }
    }

    /**
     * Sanitize string input
     */
    public function sanitizeString($input): ?string
    {
        if (!is_string($input) && !is_numeric($input)) {
            return null;
        }

        $input = trim((string)$input);
        
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Convert special characters to HTML entities
        $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        
        return $input;
    }

    /**
     * Sanitize HTML input (allows basic HTML tags)
     */
    public function sanitizeHtml(string $input): string
    {
        // Define allowed tags and attributes
        $allowedTags = '<p><br><strong><em><u><a><ul><ol><li><h1><h2><h3><h4><h5><h6>';
        
        // Strip tags except allowed ones
        $input = strip_tags($input, $allowedTags);
        
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        return trim($input);
    }

    /**
     * Sanitize email input
     */
    public function sanitizeEmail($input): ?string
    {
        if (!is_string($input)) {
            return null;
        }

        $email = filter_var(trim($input), FILTER_SANITIZE_EMAIL);
        
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $email;
        }
        
        return null;
    }

    /**
     * Sanitize URL input
     */
    public function sanitizeUrl($input): ?string
    {
        if (!is_string($input)) {
            return null;
        }

        $url = filter_var(trim($input), FILTER_SANITIZE_URL);
        
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            return $url;
        }
        
        return null;
    }

    /**
     * Sanitize integer input
     */
    public function sanitizeInteger($input): ?int
    {
        if (is_int($input)) {
            return $input;
        }

        if (!is_numeric($input)) {
            return null;
        }

        return (int)filter_var($input, FILTER_SANITIZE_NUMBER_INT);
    }

    /**
     * Sanitize float input
     */
    public function sanitizeFloat($input): ?float
    {
        if (is_float($input)) {
            return $input;
        }

        if (!is_numeric($input)) {
            return null;
        }

        return (float)filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
    }

    /**
     * Sanitize boolean input
     */
    public function sanitizeBoolean($input): ?bool
    {
        if (is_bool($input)) {
            return $input;
        }

        if (is_string($input)) {
            $input = strtolower(trim($input));
            if (in_array($input, ['true', '1', 'yes', 'on'])) {
                return true;
            }
            if (in_array($input, ['false', '0', 'no', 'off', ''])) {
                return false;
            }
        }

        if (is_numeric($input)) {
            return (bool)(int)$input;
        }

        return null;
    }

    /**
     * Sanitize array input recursively
     */
    public function sanitizeArray($input): ?array
    {
        if (!is_array($input)) {
            return null;
        }

        $sanitized = [];
        foreach ($input as $key => $value) {
            $sanitizedKey = $this->sanitizeString($key);
            
            if (is_array($value)) {
                $sanitized[$sanitizedKey] = $this->sanitizeArray($value);
            } else {
                $sanitized[$sanitizedKey] = $this->sanitizeString($value);
            }
        }

        return $sanitized;
    }

    /**
     * Generate CSRF token
     */
    public function generateCsrfToken(): string
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $token = bin2hex(random_bytes(self::CSRF_TOKEN_LENGTH));
        $timestamp = time();

        $_SESSION['csrf_token'] = $token;
        $_SESSION['csrf_token_time'] = $timestamp;

        return $token;
    }

    /**
     * Validate CSRF token
     */
    public function validateCsrfToken(string $token): bool
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }

        // Check token expiration
        if (time() - $_SESSION['csrf_token_time'] > self::CSRF_TOKEN_LIFETIME) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
            return false;
        }

        // Use hash_equals to prevent timing attacks
        $isValid = hash_equals($_SESSION['csrf_token'], $token);

        if ($isValid) {
            // Regenerate token after successful validation
            $this->generateCsrfToken();
        }

        return $isValid;
    }

    /**
     * Get CSRF token for forms
     */
    public function getCsrfToken(): string
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return $this->generateCsrfToken();
        }

        // Check if token is expired
        if (time() - $_SESSION['csrf_token_time'] > self::CSRF_TOKEN_LIFETIME) {
            return $this->generateCsrfToken();
        }

        return $_SESSION['csrf_token'];
    }

    /**
     * Prevent SQL injection by validating and escaping parameters
     */
    public function escapeSqlParameter($value): string
    {
        if (is_null($value)) {
            return 'NULL';
        }

        if (is_bool($value)) {
            return $value ? 'TRUE' : 'FALSE';
        }

        if (is_numeric($value)) {
            return (string)$value;
        }

        // For strings, we rely on prepared statements, but provide basic escaping as fallback
        return "'" . str_replace("'", "''", (string)$value) . "'";
    }

    /**
     * Validate and sanitize file upload
     */
    public function validateFileUpload(array $file, array $allowedTypes = [], int $maxSize = 5242880): array
    {
        $result = [
            'valid' => false,
            'error' => null,
            'sanitized_name' => null
        ];

        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $result['error'] = $this->getUploadErrorMessage($file['error']);
            return $result;
        }

        // Check file size
        if ($file['size'] > $maxSize) {
            $result['error'] = "File size exceeds maximum allowed size of " . ($maxSize / 1024 / 1024) . "MB";
            return $result;
        }

        // Validate file type
        if (!empty($allowedTypes)) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $file['tmp_name']);
            finfo_close($finfo);

            if (!in_array($mimeType, $allowedTypes)) {
                $result['error'] = "File type not allowed. Allowed types: " . implode(', ', $allowedTypes);
                return $result;
            }
        }

        // Sanitize filename
        $filename = $file['name'];
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        $filename = trim($filename, '.');

        if (empty($filename)) {
            $filename = 'upload_' . uniqid();
        }

        $result['valid'] = true;
        $result['sanitized_name'] = $filename;

        return $result;
    }

    /**
     * Get upload error message
     */
    private function getUploadErrorMessage(int $errorCode): string
    {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }

    /**
     * Generate secure random password
     */
    public function generateSecurePassword(int $length = 16): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
        $password = '';
        $charactersLength = strlen($characters);

        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, $charactersLength - 1)];
        }

        return $password;
    }

    /**
     * Hash password securely
     */
    public function hashPassword(string $password): string
    {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3          // 3 threads
        ]);
    }

    /**
     * Verify password hash
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * Rate limiting check
     */
    public function checkRateLimit(string $identifier, int $maxAttempts = 5, int $timeWindow = 300): bool
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $key = 'rate_limit_' . md5($identifier);
        $now = time();

        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['count' => 1, 'first_attempt' => $now];
            return true;
        }

        $data = $_SESSION[$key];

        // Reset if time window has passed
        if ($now - $data['first_attempt'] > $timeWindow) {
            $_SESSION[$key] = ['count' => 1, 'first_attempt' => $now];
            return true;
        }

        // Check if limit exceeded
        if ($data['count'] >= $maxAttempts) {
            return false;
        }

        // Increment counter
        $_SESSION[$key]['count']++;
        return true;
    }

    /**
     * Clear rate limit for identifier
     */
    public function clearRateLimit(string $identifier): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $key = 'rate_limit_' . md5($identifier);
        unset($_SESSION[$key]);
    }

    /**
     * Validate IP address and check if it's from allowed ranges
     */
    public function validateIpAddress(string $ip, array $allowedRanges = []): bool
    {
        if (!filter_var($ip, FILTER_VALIDATE_IP)) {
            return false;
        }

        if (empty($allowedRanges)) {
            return true;
        }

        foreach ($allowedRanges as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if IP is in range
     */
    private function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }

        list($subnet, $bits) = explode('/', $range);
        
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            $ip = ip2long($ip);
            $subnet = ip2long($subnet);
            $mask = -1 << (32 - $bits);
            $subnet &= $mask;
            return ($ip & $mask) === $subnet;
        }

        // IPv6 support would require additional implementation
        return false;
    }

    /**
     * Set secure HTTP headers
     */
    public function setSecurityHeaders(): void
    {
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // Prevent clickjacking
        header('X-Frame-Options: DENY');
        
        // XSS protection
        header('X-XSS-Protection: 1; mode=block');
        
        // Referrer policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Content Security Policy - Enhanced for production security
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; " .
               "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; " .
               "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; " .
               "img-src 'self' data: https:; " .
               "connect-src 'self'; " .
               "object-src 'none'; " .
               "base-uri 'self'; " .
               "form-action 'self'; " .
               "frame-ancestors 'none'; " .
               "upgrade-insecure-requests";
        header("Content-Security-Policy: $csp");
        
        // Strict Transport Security (HTTPS only)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }
        
        // Permissions Policy - Enhanced restrictions
        header('Permissions-Policy: geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(self), sync-xhr=()');
        
        // Additional security headers
        header('X-Permitted-Cross-Domain-Policies: none');
        header('Cross-Origin-Embedder-Policy: require-corp');
        header('Cross-Origin-Opener-Policy: same-origin');
        header('Cross-Origin-Resource-Policy: same-origin');
        
        // Cache control for sensitive pages
        if ($this->isSensitivePage()) {
            header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
            header('Pragma: no-cache');
            header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
        }
    }

    /**
     * Check if current page is sensitive (admin pages)
     */
    private function isSensitivePage(): bool
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($uri, '/admin') !== false || strpos($uri, '/api') !== false;
    }

    /**
     * Log security event
     */
    public function logSecurityEvent(string $event, array $context = []): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'context' => $context
        ];

        error_log('SECURITY_EVENT: ' . json_encode($logData));
    }

    /**
     * Enhanced input validation with multiple layers
     */
    public function validateAndSanitizeInput(array $input, array $rules = []): array
    {
        $result = [
            'valid' => true,
            'errors' => [],
            'sanitized' => []
        ];

        foreach ($input as $key => $value) {
            $rule = $rules[$key] ?? 'string';
            
            // First layer: SQL injection prevention
            if (!$this->sqlInjectionPrevention->validateParameter($value)) {
                $result['valid'] = false;
                $result['errors'][$key] = 'Input contains potentially dangerous content';
                $this->logSecurityEvent('input_validation_failed', [
                    'field' => $key,
                    'value' => substr((string)$value, 0, 100),
                    'rule' => $rule
                ]);
                continue;
            }

            // Second layer: Type-specific sanitization
            $sanitized = $this->sanitizeInput($value, $rule);
            
            // Third layer: Additional validation based on rules
            if (!$this->validateByRule($sanitized, $rule)) {
                $result['valid'] = false;
                $result['errors'][$key] = 'Input does not meet validation requirements';
                continue;
            }

            $result['sanitized'][$key] = $sanitized;
        }

        return $result;
    }

    /**
     * Validate input based on specific rules
     */
    private function validateByRule($value, string $rule): bool
    {
        switch ($rule) {
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            case 'url':
                return filter_var($value, FILTER_VALIDATE_URL) !== false;
            case 'ip':
                return filter_var($value, FILTER_VALIDATE_IP) !== false;
            case 'int':
                return is_int($value) || (is_string($value) && ctype_digit($value));
            case 'float':
                return is_float($value) || is_numeric($value);
            case 'boolean':
                return is_bool($value) || in_array(strtolower((string)$value), ['true', 'false', '1', '0', 'yes', 'no']);
            case 'uuid':
                return preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', (string)$value);
            case 'alphanumeric':
                return ctype_alnum((string)$value);
            case 'slug':
                return preg_match('/^[a-z0-9\-_]+$/i', (string)$value);
            default:
                return true; // Default string validation
        }
    }

    /**
     * Enhanced CSRF protection with token rotation
     */
    public function validateCsrfTokenWithRotation(string $token): bool
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Check current token
        if ($this->validateCsrfToken($token)) {
            return true;
        }

        // Check previous token (for race conditions)
        if (isset($_SESSION['csrf_token_previous']) && hash_equals($_SESSION['csrf_token_previous'], $token)) {
            // Allow previous token but generate new one
            $this->generateCsrfToken();
            return true;
        }

        $this->logSecurityEvent('csrf_token_validation_failed', [
            'provided_token' => substr($token, 0, 8) . '...',
            'session_has_token' => isset($_SESSION['csrf_token'])
        ]);

        return false;
    }

    /**
     * Generate CSRF token with previous token backup
     */
    public function generateCsrfTokenWithBackup(): string
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Backup current token
        if (isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token_previous'] = $_SESSION['csrf_token'];
        }

        return $this->generateCsrfToken();
    }

    /**
     * Advanced rate limiting with sliding window
     */
    public function checkAdvancedRateLimit(string $identifier, int $maxAttempts = 5, int $timeWindow = 300, string $action = 'general'): bool
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $key = 'rate_limit_' . $action . '_' . md5($identifier);
        $now = time();

        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['attempts' => [time()], 'blocked_until' => null];
            return true;
        }

        $data = $_SESSION[$key];

        // Check if currently blocked
        if ($data['blocked_until'] && $now < $data['blocked_until']) {
            $this->logSecurityEvent('rate_limit_blocked', [
                'identifier' => $identifier,
                'action' => $action,
                'blocked_until' => date('Y-m-d H:i:s', $data['blocked_until'])
            ]);
            return false;
        }

        // Clean old attempts (sliding window)
        $data['attempts'] = array_filter($data['attempts'], function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) <= $timeWindow;
        });

        // Check if limit exceeded
        if (count($data['attempts']) >= $maxAttempts) {
            // Block for increasing duration based on violations
            $blockDuration = min(3600, $timeWindow * (count($data['attempts']) - $maxAttempts + 1));
            $data['blocked_until'] = $now + $blockDuration;
            
            $this->logSecurityEvent('rate_limit_exceeded', [
                'identifier' => $identifier,
                'action' => $action,
                'attempts' => count($data['attempts']),
                'blocked_for_seconds' => $blockDuration
            ]);
            
            $_SESSION[$key] = $data;
            return false;
        }

        // Add current attempt
        $data['attempts'][] = $now;
        $data['blocked_until'] = null;
        $_SESSION[$key] = $data;

        return true;
    }

    /**
     * Detect and prevent common attack patterns
     */
    public function detectAttackPatterns(array $requestData): array
    {
        $threats = [];

        foreach ($requestData as $key => $value) {
            if (!is_string($value)) {
                continue;
            }

            // Check for common attack patterns
            $patterns = [
                'xss' => '/<script|javascript:|on\w+\s*=|<iframe|<object|<embed/i',
                'sql_injection' => '/(\bunion\b|\bselect\b|\binsert\b|\bupdate\b|\bdelete\b|\bdrop\b).*(\bfrom\b|\bwhere\b|\binto\b)/i',
                'path_traversal' => '/\.\.[\/\\\\]|\.\.%2f|\.\.%5c/i',
                'command_injection' => '/[;&|`$(){}[\]]/i',
                'ldap_injection' => '/[()=*!&|]/i'
            ];

            foreach ($patterns as $type => $pattern) {
                if (preg_match($pattern, $value)) {
                    $threats[] = [
                        'type' => $type,
                        'field' => $key,
                        'value' => substr($value, 0, 100)
                    ];
                }
            }
        }

        if (!empty($threats)) {
            $this->logSecurityEvent('attack_pattern_detected', [
                'threats' => $threats,
                'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
            ]);
        }

        return $threats;
    }

    /**
     * Secure session configuration
     */
    public function configureSecureSession(): void
    {
        if (session_status() === PHP_SESSION_ACTIVE) {
            return;
        }

        // Secure session configuration
        ini_set('session.cookie_httponly', '1');
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? '1' : '0');
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.use_strict_mode', '1');
        ini_set('session.use_only_cookies', '1');
        ini_set('session.cookie_lifetime', '0'); // Session cookies only
        ini_set('session.gc_maxlifetime', '3600'); // 1 hour
        ini_set('session.name', 'GUARDGEO_SESSION');

        // Regenerate session ID periodically
        if (!isset($_SESSION['last_regeneration'])) {
            session_start();
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        } else {
            session_start();
        }
    }

    /**
     * Initialize SQL injection prevention
     */
    private function initializeSqlInjectionPrevention(): void
    {
        if (!isset($this->sqlInjectionPrevention)) {
            $this->sqlInjectionPrevention = new SqlInjectionPrevention();
        }
    }

    /**
     * Constructor to initialize dependencies
     */
    public function __construct()
    {
        $this->initializeSqlInjectionPrevention();
    }

    private SqlInjectionPrevention $sqlInjectionPrevention;
}