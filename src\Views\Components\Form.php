<?php

namespace Skpassegna\GuardgeoApi\Views\Components;

/**
 * Form Component
 * 
 * Comprehensive form component with built-in validation display,
 * CSRF protection, and AJAX submission support.
 */
class Form extends BaseComponent
{
    protected function getDefaultClasses(): string
    {
        return 'space-y-6';
    }

    public function render(): string
    {
        $id = $this->prop('id', 'form-' . uniqid());
        $action = $this->prop('action', '');
        $method = $this->prop('method', 'POST');
        $enctype = $this->prop('enctype');
        $ajax = $this->prop('ajax', false);
        $csrfToken = $this->prop('csrf_token');
        $fields = $this->prop('fields', []);
        $submitButton = $this->prop('submit_button');
        $cancelButton = $this->prop('cancel_button');
        $errors = $this->prop('errors', []);
        $values = $this->prop('values', []);

        $html = '<form id="' . $this->escape($id) . '"';
        
        if (!$ajax) {
            $html .= ' action="' . $this->escape($action) . '" method="' . $this->escape($method) . '"';
        }
        
        if ($enctype) {
            $html .= ' enctype="' . $this->escape($enctype) . '"';
        }
        
        $html .= ' class="' . $this->getClasses() . '"';
        
        if ($this->attributes) {
            $html .= ' ' . $this->renderAttributes();
        }
        
        $html .= '>';

        // CSRF token
        if ($csrfToken) {
            $html .= '<input type="hidden" name="csrf_token" value="' . $this->escape($csrfToken) . '">';
        }

        // Error summary
        if (!empty($errors) && !is_array(reset($errors))) {
            $html .= $this->renderErrorSummary($errors);
        }

        // Form fields
        foreach ($fields as $field) {
            $html .= $this->renderField($field, $errors, $values);
        }

        // Form buttons
        if ($submitButton || $cancelButton) {
            $html .= '<div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">';
            
            if ($cancelButton) {
                $html .= $this->renderButton($cancelButton, 'outline');
            }
            
            if ($submitButton) {
                $html .= $this->renderButton($submitButton, 'primary', 'submit');
            }
            
            $html .= '</div>';
        }

        $html .= '</form>';

        // Add AJAX handling script if enabled
        if ($ajax) {
            $html .= $this->renderAjaxScript($id, $action);
        }

        return $html;
    }

    /**
     * Render error summary
     */
    private function renderErrorSummary(array $errors): string
    {
        if (empty($errors)) {
            return '';
        }

        $html = '<div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">';
        $html .= '<div class="flex">';
        $html .= '<div class="flex-shrink-0">';
        $html .= '<i class="fas fa-exclamation-circle text-red-400"></i>';
        $html .= '</div>';
        $html .= '<div class="ml-3">';
        $html .= '<h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>';
        $html .= '<div class="mt-2 text-sm text-red-700">';
        $html .= '<ul class="list-disc pl-5 space-y-1">';
        
        foreach ($errors as $error) {
            $html .= '<li>' . $this->escape($error) . '</li>';
        }
        
        $html .= '</ul>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Render form field
     */
    private function renderField(array $field, array $errors, array $values): string
    {
        $type = $field['type'] ?? 'text';
        $name = $field['name'] ?? '';
        $label = $field['label'] ?? '';
        $required = $field['required'] ?? false;
        $placeholder = $field['placeholder'] ?? '';
        $help = $field['help'] ?? '';
        $options = $field['options'] ?? [];
        $value = $values[$name] ?? $field['value'] ?? '';
        $fieldErrors = $errors[$name] ?? [];

        $html = '<div class="form-field">';

        // Label
        if ($label) {
            $html .= '<label for="' . $this->escape($name) . '" class="block text-sm font-medium text-gray-700 mb-1">';
            $html .= $this->escape($label);
            if ($required) {
                $html .= ' <span class="text-red-500">*</span>';
            }
            $html .= '</label>';
        }

        // Field input
        $html .= $this->renderFieldInput($type, $name, $value, $placeholder, $options, $required, $fieldErrors);

        // Help text
        if ($help) {
            $html .= '<p class="mt-1 text-sm text-gray-500">' . $this->escape($help) . '</p>';
        }

        // Field errors
        if (!empty($fieldErrors)) {
            $html .= '<div class="mt-1">';
            foreach ($fieldErrors as $error) {
                $html .= '<p class="text-sm text-red-600">' . $this->escape($error) . '</p>';
            }
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Render field input based on type
     */
    private function renderFieldInput(
        string $type,
        string $name,
        $value,
        string $placeholder,
        array $options,
        bool $required,
        array $errors
    ): string {
        $hasError = !empty($errors);
        $baseClasses = 'block w-full rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500';
        $errorClasses = $hasError ? 'border-red-300 text-red-900 placeholder-red-300' : 'border-gray-300';
        $classes = $baseClasses . ' ' . $errorClasses;

        switch ($type) {
            case 'text':
            case 'email':
            case 'password':
            case 'url':
            case 'number':
                return $this->renderTextInput($type, $name, $value, $placeholder, $classes, $required);

            case 'textarea':
                return $this->renderTextarea($name, $value, $placeholder, $classes, $required);

            case 'select':
                return $this->renderSelect($name, $value, $options, $classes, $required);

            case 'checkbox':
                return $this->renderCheckbox($name, $value, $options, $required);

            case 'radio':
                return $this->renderRadio($name, $value, $options, $required);

            case 'file':
                return $this->renderFileInput($name, $classes, $required, $options);

            case 'hidden':
                return '<input type="hidden" name="' . $this->escape($name) . '" value="' . $this->escape($value) . '">';

            default:
                return $this->renderTextInput('text', $name, $value, $placeholder, $classes, $required);
        }
    }

    /**
     * Render text input
     */
    private function renderTextInput(string $type, string $name, $value, string $placeholder, string $classes, bool $required): string
    {
        $html = '<input type="' . $this->escape($type) . '"';
        $html .= ' name="' . $this->escape($name) . '"';
        $html .= ' id="' . $this->escape($name) . '"';
        $html .= ' value="' . $this->escape($value) . '"';
        $html .= ' class="' . $classes . '"';
        
        if ($placeholder) {
            $html .= ' placeholder="' . $this->escape($placeholder) . '"';
        }
        
        if ($required) {
            $html .= ' required';
        }
        
        $html .= '>';

        return $html;
    }

    /**
     * Render textarea
     */
    private function renderTextarea(string $name, $value, string $placeholder, string $classes, bool $required): string
    {
        $html = '<textarea';
        $html .= ' name="' . $this->escape($name) . '"';
        $html .= ' id="' . $this->escape($name) . '"';
        $html .= ' class="' . $classes . '"';
        $html .= ' rows="4"';
        
        if ($placeholder) {
            $html .= ' placeholder="' . $this->escape($placeholder) . '"';
        }
        
        if ($required) {
            $html .= ' required';
        }
        
        $html .= '>';
        $html .= $this->escape($value);
        $html .= '</textarea>';

        return $html;
    }

    /**
     * Render select dropdown
     */
    private function renderSelect(string $name, $value, array $options, string $classes, bool $required): string
    {
        $html = '<select';
        $html .= ' name="' . $this->escape($name) . '"';
        $html .= ' id="' . $this->escape($name) . '"';
        $html .= ' class="' . $classes . '"';
        
        if ($required) {
            $html .= ' required';
        }
        
        $html .= '>';

        foreach ($options as $optionValue => $optionLabel) {
            $selected = ($optionValue == $value) ? ' selected' : '';
            $html .= '<option value="' . $this->escape($optionValue) . '"' . $selected . '>';
            $html .= $this->escape($optionLabel);
            $html .= '</option>';
        }

        $html .= '</select>';

        return $html;
    }

    /**
     * Render checkbox
     */
    private function renderCheckbox(string $name, $value, array $options, bool $required): string
    {
        if (empty($options)) {
            // Single checkbox
            $checked = $value ? ' checked' : '';
            return '<input type="checkbox" name="' . $this->escape($name) . '" id="' . $this->escape($name) . '" value="1"' . $checked . ' class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">';
        }

        // Multiple checkboxes
        $html = '<div class="space-y-2">';
        foreach ($options as $optionValue => $optionLabel) {
            $checked = (is_array($value) && in_array($optionValue, $value)) ? ' checked' : '';
            $html .= '<label class="flex items-center">';
            $html .= '<input type="checkbox" name="' . $this->escape($name) . '[]" value="' . $this->escape($optionValue) . '"' . $checked . ' class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">';
            $html .= '<span class="ml-2 text-sm text-gray-700">' . $this->escape($optionLabel) . '</span>';
            $html .= '</label>';
        }
        $html .= '</div>';

        return $html;
    }

    /**
     * Render radio buttons
     */
    private function renderRadio(string $name, $value, array $options, bool $required): string
    {
        $html = '<div class="space-y-2">';
        foreach ($options as $optionValue => $optionLabel) {
            $checked = ($optionValue == $value) ? ' checked' : '';
            $html .= '<label class="flex items-center">';
            $html .= '<input type="radio" name="' . $this->escape($name) . '" value="' . $this->escape($optionValue) . '"' . $checked . ' class="border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">';
            $html .= '<span class="ml-2 text-sm text-gray-700">' . $this->escape($optionLabel) . '</span>';
            $html .= '</label>';
        }
        $html .= '</div>';

        return $html;
    }

    /**
     * Render file input
     */
    private function renderFileInput(string $name, string $classes, bool $required, array $options): string
    {
        $accept = isset($options['accept']) ? ' accept="' . $this->escape($options['accept']) . '"' : '';
        $multiple = isset($options['multiple']) && $options['multiple'] ? ' multiple' : '';

        $html = '<input type="file"';
        $html .= ' name="' . $this->escape($name) . '"';
        $html .= ' id="' . $this->escape($name) . '"';
        $html .= ' class="' . $classes . '"';
        $html .= $accept;
        $html .= $multiple;
        
        if ($required) {
            $html .= ' required';
        }
        
        $html .= '>';

        return $html;
    }

    /**
     * Render button
     */
    private function renderButton(array $button, string $variant = 'primary', string $type = 'button'): string
    {
        $text = $button['text'] ?? 'Button';
        $icon = $button['icon'] ?? '';
        $onclick = $button['onclick'] ?? '';
        $disabled = $button['disabled'] ?? false;

        $buttonComponent = new Button([
            'text' => $text,
            'icon' => $icon,
            'variant' => $variant,
            'type' => $type,
            'onclick' => $onclick,
            'disabled' => $disabled
        ]);

        return $buttonComponent->render();
    }

    /**
     * Render AJAX handling script
     */
    private function renderAjaxScript(string $formId, string $action): string
    {
        return <<<HTML
<script>
document.getElementById('{$formId}').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const form = this;
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
    
    // Clear previous errors
    form.querySelectorAll('.text-red-600').forEach(el => el.remove());
    form.querySelectorAll('.border-red-300').forEach(el => {
        el.classList.remove('border-red-300', 'text-red-900', 'placeholder-red-300');
        el.classList.add('border-gray-300');
    });
    
    try {
        const formData = new FormData(form);
        const response = await fetch('{$action}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Handle success
            if (result.redirect) {
                window.location.href = result.redirect;
            } else if (result.message) {
                showNotification(result.message, 'success');
            }
            
            // Trigger custom success event
            form.dispatchEvent(new CustomEvent('formSuccess', { detail: result }));
        } else {
            // Handle validation errors
            if (result.errors) {
                displayFormErrors(form, result.errors);
            } else if (result.error) {
                showNotification(result.error, 'error');
            }
            
            // Trigger custom error event
            form.dispatchEvent(new CustomEvent('formError', { detail: result }));
        }
    } catch (error) {
        console.error('Form submission error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    } finally {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
});

function displayFormErrors(form, errors) {
    Object.keys(errors).forEach(fieldName => {
        const field = form.querySelector('[name="' + fieldName + '"]');
        if (field) {
            // Add error styling
            field.classList.add('border-red-300', 'text-red-900', 'placeholder-red-300');
            field.classList.remove('border-gray-300');
            
            // Add error messages
            const fieldContainer = field.closest('.form-field');
            if (fieldContainer) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'mt-1';
                
                errors[fieldName].forEach(error => {
                    const errorP = document.createElement('p');
                    errorP.className = 'text-sm text-red-600';
                    errorP.textContent = error;
                    errorDiv.appendChild(errorP);
                });
                
                fieldContainer.appendChild(errorDiv);
            }
        }
    });
}

function showNotification(message, type) {
    // This function should be implemented globally for notifications
    console.log(type + ': ' + message);
}
</script>
HTML;
    }

    /**
     * Create a user form
     */
    public static function userForm(array $props = []): self
    {
        $user = $props['user'] ?? [];
        $errors = $props['errors'] ?? [];
        $isEdit = !empty($user['id']);

        $fields = [
            [
                'type' => 'email',
                'name' => 'email',
                'label' => 'Email Address',
                'required' => true,
                'placeholder' => '<EMAIL>',
                'value' => $user['email'] ?? ''
            ],
            [
                'type' => 'select',
                'name' => 'role',
                'label' => 'Role',
                'required' => true,
                'options' => [
                    'super_admin' => 'Super Admin',
                    'dev' => 'Developer',
                    'marketing' => 'Marketing',
                    'sales' => 'Sales'
                ],
                'value' => $user['role'] ?? ''
            ]
        ];

        if (!$isEdit) {
            $fields[] = [
                'type' => 'password',
                'name' => 'password',
                'label' => 'Password',
                'required' => true,
                'placeholder' => 'Enter secure password',
                'help' => 'Password must be at least 12 characters long'
            ];
            
            $fields[] = [
                'type' => 'password',
                'name' => 'password_confirmation',
                'label' => 'Confirm Password',
                'required' => true,
                'placeholder' => 'Confirm password'
            ];
        }

        $fields[] = [
            'type' => 'checkbox',
            'name' => 'is_active',
            'label' => 'Active',
            'value' => $user['is_active'] ?? true
        ];

        return new self(array_merge($props, [
            'fields' => $fields,
            'errors' => $errors,
            'submit_button' => [
                'text' => $isEdit ? 'Update User' : 'Create User',
                'icon' => 'fas fa-save'
            ],
            'cancel_button' => [
                'text' => 'Cancel',
                'onclick' => 'closeModal()'
            ]
        ]));
    }
}