<?php

/**
 * Cache Maintenance Script
 * 
 * Automated script for performing IP cache maintenance tasks including
 * cleanup, refresh, and health monitoring.
 * 
 * Usage:
 * php scripts/cache-maintenance.php [--task=cleanup|refresh|health|all] [--batch-size=N] [--verbose]
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Skpassegna\GuardgeoApi\Services\IpRegistryService;
use Skpassegna\GuardgeoApi\Services\CacheManager;
use Skpassegna\GuardgeoApi\Utils\Logger;
use Skpassegna\GuardgeoApi\Config\Environment;

// Parse command line arguments
$options = getopt('', ['task:', 'batch-size:', 'verbose', 'help']);

if (isset($options['help'])) {
    showHelp();
    exit(0);
}

$task = $options['task'] ?? 'all';
$batchSize = isset($options['batch-size']) ? (int)$options['batch-size'] : 50;
$verbose = isset($options['verbose']);

// Initialize services
$logger = new Logger();
$ipRegistryService = new IpRegistryService();
$cacheManager = new CacheManager();

$logger->info("Starting cache maintenance script", [
    'task' => $task,
    'batch_size' => $batchSize,
    'verbose' => $verbose
]);

try {
    switch ($task) {
        case 'cleanup':
            performCleanup($cacheManager, $logger, $verbose);
            break;
            
        case 'refresh':
            performRefresh($ipRegistryService, $logger, $batchSize, $verbose);
            break;
            
        case 'health':
            performHealthCheck($ipRegistryService, $cacheManager, $logger, $verbose);
            break;
            
        case 'all':
            performCleanup($cacheManager, $logger, $verbose);
            performRefresh($ipRegistryService, $logger, $batchSize, $verbose);
            performHealthCheck($ipRegistryService, $cacheManager, $logger, $verbose);
            break;
            
        default:
            echo "Error: Unknown task '{$task}'. Use --help for usage information.\n";
            exit(1);
    }
    
    $logger->info("Cache maintenance script completed successfully");
    echo "Cache maintenance completed successfully.\n";
    
} catch (Exception $e) {
    $logger->error("Cache maintenance script failed", [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    echo "Error: Cache maintenance failed - " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Perform cache cleanup
 */
function performCleanup(CacheManager $cacheManager, Logger $logger, bool $verbose): void
{
    if ($verbose) {
        echo "Starting cache cleanup...\n";
    }
    
    $results = $cacheManager->performCleanup();
    
    if ($verbose) {
        echo "Cleanup Results:\n";
        echo "- Old data deleted: {$results['old_data_deleted']} records\n";
        echo "- Cache size reduced: " . ($results['cache_size_reduced'] ? 'Yes' : 'No') . "\n";
        
        if (!empty($results['errors'])) {
            echo "- Errors: " . implode(', ', $results['errors']) . "\n";
        }
    }
    
    $logger->info("Cache cleanup completed", $results);
}

/**
 * Perform cache refresh
 */
function performRefresh(IpRegistryService $ipRegistryService, Logger $logger, int $batchSize, bool $verbose): void
{
    if ($verbose) {
        echo "Starting cache refresh (batch size: {$batchSize})...\n";
    }
    
    $results = $ipRegistryService->refreshExpiredIps($batchSize);
    
    if ($verbose) {
        echo "Refresh Results:\n";
        echo "- Processed: {$results['processed']} IPs\n";
        echo "- Refreshed: {$results['refreshed']} IPs\n";
        echo "- Failed: {$results['failed']} IPs\n";
        
        if (!empty($results['errors'])) {
            echo "- Errors: " . implode(', ', $results['errors']) . "\n";
        }
    }
    
    $logger->info("Cache refresh completed", $results);
}

/**
 * Perform health check
 */
function performHealthCheck(IpRegistryService $ipRegistryService, CacheManager $cacheManager, Logger $logger, bool $verbose): void
{
    if ($verbose) {
        echo "Performing health check...\n";
    }
    
    // Service health check
    $serviceHealth = $ipRegistryService->healthCheck();
    
    // Cache health check
    $cacheHealth = $cacheManager->getCacheHealth();
    
    // Cache statistics
    $stats = $cacheManager->getCacheStatistics();
    
    if ($verbose) {
        echo "Health Check Results:\n";
        echo "Service Health:\n";
        echo "- API Client: " . ($serviceHealth['api_client'] ? 'OK' : 'FAILED') . "\n";
        echo "- Database: " . ($serviceHealth['database'] ? 'OK' : 'FAILED') . "\n";
        echo "- Cache Health: {$serviceHealth['cache_health']}\n";
        echo "- Overall: " . ($serviceHealth['overall'] ? 'HEALTHY' : 'UNHEALTHY') . "\n";
        
        echo "\nCache Statistics:\n";
        echo "- Total IPs: {$stats['total_cached_ips']}\n";
        echo "- Fresh IPs: {$stats['fresh_ips']}\n";
        echo "- Expired IPs: {$stats['expired_ips']}\n";
        echo "- Cache Utilization: " . round($stats['cache_utilization'], 1) . "%\n";
        
        echo "\nCache Health Status: {$cacheHealth['status']}\n";
        
        if (!empty($cacheHealth['issues'])) {
            echo "Issues:\n";
            foreach ($cacheHealth['issues'] as $issue) {
                echo "- {$issue}\n";
            }
        }
        
        if (!empty($cacheHealth['recommendations'])) {
            echo "Recommendations:\n";
            foreach ($cacheHealth['recommendations'] as $recommendation) {
                echo "- {$recommendation}\n";
            }
        }
    }
    
    $logger->info("Health check completed", [
        'service_health' => $serviceHealth,
        'cache_health' => $cacheHealth['status'],
        'statistics' => $stats
    ]);
}

/**
 * Show help information
 */
function showHelp(): void
{
    echo "Cache Maintenance Script\n";
    echo "========================\n\n";
    echo "Usage: php scripts/cache-maintenance.php [OPTIONS]\n\n";
    echo "Options:\n";
    echo "  --task=TASK        Task to perform (cleanup|refresh|health|all) [default: all]\n";
    echo "  --batch-size=N     Batch size for refresh operations [default: 50]\n";
    echo "  --verbose          Show detailed output\n";
    echo "  --help             Show this help message\n\n";
    echo "Tasks:\n";
    echo "  cleanup            Remove old cached data and reduce cache size if needed\n";
    echo "  refresh            Refresh expired IP data from API\n";
    echo "  health             Check system and cache health status\n";
    echo "  all                Perform all maintenance tasks\n\n";
    echo "Examples:\n";
    echo "  php scripts/cache-maintenance.php --task=cleanup --verbose\n";
    echo "  php scripts/cache-maintenance.php --task=refresh --batch-size=100\n";
    echo "  php scripts/cache-maintenance.php --verbose\n";
}