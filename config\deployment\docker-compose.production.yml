version: '3.8'

services:
  # GuardGeo Application
  guardgeo-app:
    build:
      context: ../..
      dockerfile: config/deployment/Dockerfile.production
    container_name: guardgeo-app
    restart: unless-stopped
    environment:
      - APP_ENV=production
    volumes:
      - ../../:/var/www/html
      - guardgeo-logs:/var/www/html/logs
      - guardgeo-backups:/var/backups/guardgeo
    networks:
      - guardgeo-network
    depends_on:
      - guardgeo-db
      - guardgeo-redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/admin/api/maintenance/health-check"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  guardgeo-db:
    image: postgres:15-alpine
    container_name: guardgeo-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: guardgeo_production
      POSTGRES_USER: guardgeo_prod
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - guardgeo-db-data:/var/lib/postgresql/data
      - ../../src/Database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ../../src/Database/create_super_admin.sql:/docker-entrypoint-initdb.d/02-super-admin.sql
    networks:
      - guardgeo-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U guardgeo_prod -d guardgeo_production"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  guardgeo-redis:
    image: redis:7-alpine
    container_name: guardgeo-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - guardgeo-redis-data:/data
    networks:
      - guardgeo-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx Reverse Proxy
  guardgeo-nginx:
    image: nginx:alpine
    container_name: guardgeo-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../../:/var/www/html:ro
      - ./nginx.production.conf:/etc/nginx/conf.d/default.conf:ro
      - /etc/ssl/certs:/etc/ssl/certs:ro
      - /etc/ssl/private:/etc/ssl/private:ro
      - guardgeo-logs:/var/log/nginx
    networks:
      - guardgeo-network
    depends_on:
      - guardgeo-app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Agent
  guardgeo-monitor:
    build:
      context: ../..
      dockerfile: config/deployment/Dockerfile.monitor
    container_name: guardgeo-monitor
    restart: unless-stopped
    volumes:
      - ../../:/var/www/html:ro
      - guardgeo-logs:/var/www/html/logs:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - guardgeo-network
    depends_on:
      - guardgeo-app
      - guardgeo-db
      - guardgeo-redis
    environment:
      - MONITOR_INTERVAL=300
      - ALERT_EMAIL=${ALERT_EMAIL}
      - SLACK_WEBHOOK=${SLACK_WEBHOOK}

  # Backup Service
  guardgeo-backup:
    build:
      context: ../..
      dockerfile: config/deployment/Dockerfile.backup
    container_name: guardgeo-backup
    restart: unless-stopped
    volumes:
      - ../../:/var/www/html:ro
      - guardgeo-backups:/var/backups/guardgeo
      - guardgeo-db-data:/var/lib/postgresql/data:ro
    networks:
      - guardgeo-network
    depends_on:
      - guardgeo-db
    environment:
      - BACKUP_SCHEDULE=0 2 * * *
      - BACKUP_RETENTION_DAYS=30
      - S3_BUCKET=${BACKUP_S3_BUCKET}
      - AWS_ACCESS_KEY_ID=${BACKUP_S3_KEY}
      - AWS_SECRET_ACCESS_KEY=${BACKUP_S3_SECRET}
      - AWS_DEFAULT_REGION=${BACKUP_S3_REGION}

volumes:
  guardgeo-db-data:
    driver: local
  guardgeo-redis-data:
    driver: local
  guardgeo-logs:
    driver: local
  guardgeo-backups:
    driver: local

networks:
  guardgeo-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16