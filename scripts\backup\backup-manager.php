<?php
/**
 * GuardGeo Admin Platform Backup Manager
 * 
 * This script handles automated backups and recovery procedures.
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Skpassegna\GuardgeoApi\Config\ConfigManager;
use Skpassegna\GuardgeoApi\Database\DatabaseManager;
use Skpassegna\GuardgeoApi\Services\LoggingService;

class BackupManager
{
    private $config;
    private $logger;
    private $backupDir;
    private $encryptionKey;

    public function __construct()
    {
        $this->config = ConfigManager::getInstance();
        $this->logger = new LoggingService();
        $this->backupDir = $this->config->get('backup.destinations.local.path', '/var/backups/guardgeo');
        $this->encryptionKey = $this->config->get('backup.encryption.key', '');
        
        // Ensure backup directory exists
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }

    public function createFullBackup(): array
    {
        $backupId = date('Y-m-d_H-i-s') . '_' . uniqid();
        $backupPath = $this->backupDir . '/' . $backupId;
        
        try {
            mkdir($backupPath, 0755, true);
            
            $results = [
                'backup_id' => $backupId,
                'timestamp' => date('c'),
                'type' => 'full',
                'status' => 'in_progress',
                'components' => []
            ];

            // Backup database
            $dbResult = $this->backupDatabase($backupPath);
            $results['components']['database'] = $dbResult;

            // Backup configuration files
            $configResult = $this->backupConfiguration($backupPath);
            $results['components']['configuration'] = $configResult;

            // Backup application files
            $appResult = $this->backupApplication($backupPath);
            $results['components']['application'] = $appResult;

            // Backup logs
            $logsResult = $this->backupLogs($backupPath);
            $results['components']['logs'] = $logsResult;

            // Create backup manifest
            $manifestResult = $this->createManifest($backupPath, $results);
            $results['components']['manifest'] = $manifestResult;

            // Compress backup
            $compressionResult = $this->compressBackup($backupPath, $backupId);
            $results['components']['compression'] = $compressionResult;

            // Encrypt backup if enabled
            if ($this->config->get('backup.encryption.enabled', false)) {
                $encryptionResult = $this->encryptBackup($backupPath . '.tar.gz', $backupId);
                $results['components']['encryption'] = $encryptionResult;
            }

            // Upload to remote destinations
            $uploadResults = $this->uploadToRemoteDestinations($backupId);
            $results['components']['remote_upload'] = $uploadResults;

            // Verify backup integrity
            $verificationResult = $this->verifyBackupIntegrity($backupId);
            $results['components']['verification'] = $verificationResult;

            // Clean up temporary files
            $this->cleanupTemporaryFiles($backupPath);

            // Update backup status
            $results['status'] = $this->determineOverallStatus($results['components']);
            $results['size_bytes'] = $this->getBackupSize($backupId);
            $results['duration_seconds'] = time() - strtotime($results['timestamp']);

            // Log backup completion
            $this->logger->logInfo('Backup completed', $results);

            // Clean up old backups
            $this->cleanupOldBackups();

            return $results;

        } catch (Exception $e) {
            $this->logger->logError('Backup failed', [
                'backup_id' => $backupId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'backup_id' => $backupId,
                'timestamp' => date('c'),
                'status' => 'failed',
                'error' => $e->getMessage()
            ];
        }
    }

    private function backupDatabase(string $backupPath): array
    {
        try {
            $dbConfig = $this->config->get('database');
            $dumpFile = $backupPath . '/database.sql';
            
            $command = sprintf(
                'pg_dump -h %s -p %s -U %s -d %s --no-password > %s',
                escapeshellarg($dbConfig['host']),
                escapeshellarg($dbConfig['port']),
                escapeshellarg($dbConfig['username']),
                escapeshellarg($dbConfig['database']),
                escapeshellarg($dumpFile)
            );

            // Set PGPASSWORD environment variable
            putenv('PGPASSWORD=' . $dbConfig['password']);
            
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);
            
            // Clear password from environment
            putenv('PGPASSWORD');

            if ($returnCode !== 0) {
                throw new Exception('pg_dump failed: ' . implode("\n", $output));
            }

            $fileSize = file_exists($dumpFile) ? filesize($dumpFile) : 0;
            
            return [
                'status' => 'success',
                'file' => 'database.sql',
                'size_bytes' => $fileSize,
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    private function backupConfiguration(string $backupPath): array
    {
        try {
            $configDir = $backupPath . '/config';
            mkdir($configDir, 0755, true);

            $sourceFiles = [
                '.env' => __DIR__ . '/../../.env',
                'config/' => __DIR__ . '/../../config/',
                'composer.json' => __DIR__ . '/../../composer.json',
                'composer.lock' => __DIR__ . '/../../composer.lock'
            ];

            $backedUpFiles = [];
            $totalSize = 0;

            foreach ($sourceFiles as $dest => $source) {
                if (is_file($source)) {
                    $destPath = $configDir . '/' . basename($dest);
                    if (copy($source, $destPath)) {
                        $size = filesize($destPath);
                        $backedUpFiles[] = ['file' => $dest, 'size' => $size];
                        $totalSize += $size;
                    }
                } elseif (is_dir($source)) {
                    $destPath = $configDir . '/' . rtrim($dest, '/');
                    $size = $this->copyDirectory($source, $destPath);
                    $backedUpFiles[] = ['directory' => $dest, 'size' => $size];
                    $totalSize += $size;
                }
            }

            return [
                'status' => 'success',
                'files' => $backedUpFiles,
                'total_size_bytes' => $totalSize,
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    private function backupApplication(string $backupPath): array
    {
        try {
            $appDir = $backupPath . '/application';
            mkdir($appDir, 0755, true);

            $sourceDir = __DIR__ . '/../../src';
            $size = $this->copyDirectory($sourceDir, $appDir . '/src');

            // Also backup public files
            $publicFiles = [
                'api.php' => __DIR__ . '/../../api.php',
                'admin.php' => __DIR__ . '/../../admin.php',
                '.htaccess' => __DIR__ . '/../../.htaccess'
            ];

            foreach ($publicFiles as $dest => $source) {
                if (file_exists($source)) {
                    copy($source, $appDir . '/' . $dest);
                    $size += filesize($appDir . '/' . $dest);
                }
            }

            return [
                'status' => 'success',
                'size_bytes' => $size,
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    private function backupLogs(string $backupPath): array
    {
        try {
            $logsDir = $backupPath . '/logs';
            mkdir($logsDir, 0755, true);

            $sourceDir = __DIR__ . '/../../logs';
            $size = $this->copyDirectory($sourceDir, $logsDir);

            return [
                'status' => 'success',
                'size_bytes' => $size,
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    private function createManifest(string $backupPath, array $backupInfo): array
    {
        try {
            $manifest = [
                'backup_info' => $backupInfo,
                'system_info' => [
                    'hostname' => gethostname(),
                    'php_version' => PHP_VERSION,
                    'os' => PHP_OS,
                    'timestamp' => date('c')
                ],
                'file_checksums' => $this->calculateChecksums($backupPath)
            ];

            $manifestFile = $backupPath . '/manifest.json';
            file_put_contents($manifestFile, json_encode($manifest, JSON_PRETTY_PRINT));

            return [
                'status' => 'success',
                'file' => 'manifest.json',
                'size_bytes' => filesize($manifestFile),
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    private function compressBackup(string $backupPath, string $backupId): array
    {
        try {
            $archivePath = $this->backupDir . '/' . $backupId . '.tar.gz';
            
            $command = sprintf(
                'tar -czf %s -C %s .',
                escapeshellarg($archivePath),
                escapeshellarg($backupPath)
            );

            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                throw new Exception('Compression failed: ' . implode("\n", $output));
            }

            $compressedSize = filesize($archivePath);
            $originalSize = $this->getDirectorySize($backupPath);
            $compressionRatio = $originalSize > 0 ? ($compressedSize / $originalSize) * 100 : 0;

            return [
                'status' => 'success',
                'archive_file' => $backupId . '.tar.gz',
                'original_size_bytes' => $originalSize,
                'compressed_size_bytes' => $compressedSize,
                'compression_ratio_percent' => round($compressionRatio, 2),
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    private function encryptBackup(string $archivePath, string $backupId): array
    {
        try {
            if (empty($this->encryptionKey)) {
                throw new Exception('Encryption key not configured');
            }

            $encryptedPath = $archivePath . '.enc';
            
            // Use OpenSSL for encryption
            $command = sprintf(
                'openssl enc -aes-256-cbc -salt -in %s -out %s -k %s',
                escapeshellarg($archivePath),
                escapeshellarg($encryptedPath),
                escapeshellarg($this->encryptionKey)
            );

            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                throw new Exception('Encryption failed: ' . implode("\n", $output));
            }

            // Remove unencrypted archive
            unlink($archivePath);

            return [
                'status' => 'success',
                'encrypted_file' => $backupId . '.tar.gz.enc',
                'size_bytes' => filesize($encryptedPath),
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    private function uploadToRemoteDestinations(string $backupId): array
    {
        $results = [];
        $destinations = $this->config->get('backup.destinations', []);

        foreach ($destinations as $name => $config) {
            if ($name === 'local' || !isset($config['driver'])) {
                continue;
            }

            try {
                switch ($config['driver']) {
                    case 's3':
                        $result = $this->uploadToS3($backupId, $config);
                        break;
                    case 'ftp':
                        $result = $this->uploadToFtp($backupId, $config);
                        break;
                    case 'sftp':
                        $result = $this->uploadToSftp($backupId, $config);
                        break;
                    default:
                        $result = [
                            'status' => 'failed',
                            'error' => 'Unsupported destination driver: ' . $config['driver']
                        ];
                }

                $results[$name] = $result;

            } catch (Exception $e) {
                $results[$name] = [
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'timestamp' => date('c')
                ];
            }
        }

        return $results;
    }

    private function uploadToS3(string $backupId, array $config): array
    {
        // This would require AWS SDK - simplified implementation
        $backupFile = $this->getBackupFilePath($backupId);
        
        if (!file_exists($backupFile)) {
            throw new Exception('Backup file not found: ' . $backupFile);
        }

        // Simulate S3 upload using AWS CLI (if available)
        $command = sprintf(
            'aws s3 cp %s s3://%s/%s --region %s',
            escapeshellarg($backupFile),
            escapeshellarg($config['bucket']),
            escapeshellarg('backups/' . basename($backupFile)),
            escapeshellarg($config['region'])
        );

        // Set AWS credentials as environment variables
        putenv('AWS_ACCESS_KEY_ID=' . $config['key']);
        putenv('AWS_SECRET_ACCESS_KEY=' . $config['secret']);

        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);

        // Clear credentials from environment
        putenv('AWS_ACCESS_KEY_ID');
        putenv('AWS_SECRET_ACCESS_KEY');

        if ($returnCode !== 0) {
            throw new Exception('S3 upload failed: ' . implode("\n", $output));
        }

        return [
            'status' => 'success',
            'destination' => 's3://' . $config['bucket'] . '/backups/' . basename($backupFile),
            'size_bytes' => filesize($backupFile),
            'timestamp' => date('c')
        ];
    }

    private function uploadToFtp(string $backupId, array $config): array
    {
        $backupFile = $this->getBackupFilePath($backupId);
        
        if (!file_exists($backupFile)) {
            throw new Exception('Backup file not found: ' . $backupFile);
        }

        $ftpConnection = ftp_connect($config['host'], $config['port'] ?? 21);
        if (!$ftpConnection) {
            throw new Exception('Failed to connect to FTP server');
        }

        if (!ftp_login($ftpConnection, $config['username'], $config['password'])) {
            ftp_close($ftpConnection);
            throw new Exception('FTP login failed');
        }

        // Enable passive mode
        ftp_pasv($ftpConnection, true);

        $remoteFile = ($config['path'] ?? '/') . '/' . basename($backupFile);
        
        if (!ftp_put($ftpConnection, $remoteFile, $backupFile, FTP_BINARY)) {
            ftp_close($ftpConnection);
            throw new Exception('FTP upload failed');
        }

        ftp_close($ftpConnection);

        return [
            'status' => 'success',
            'destination' => $config['host'] . ':' . $remoteFile,
            'size_bytes' => filesize($backupFile),
            'timestamp' => date('c')
        ];
    }

    private function uploadToSftp(string $backupId, array $config): array
    {
        $backupFile = $this->getBackupFilePath($backupId);
        
        if (!file_exists($backupFile)) {
            throw new Exception('Backup file not found: ' . $backupFile);
        }

        // Use SCP command for SFTP upload
        $command = sprintf(
            'scp -P %s %s %s@%s:%s',
            escapeshellarg($config['port'] ?? 22),
            escapeshellarg($backupFile),
            escapeshellarg($config['username']),
            escapeshellarg($config['host']),
            escapeshellarg(($config['path'] ?? '/') . '/' . basename($backupFile))
        );

        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);

        if ($returnCode !== 0) {
            throw new Exception('SFTP upload failed: ' . implode("\n", $output));
        }

        return [
            'status' => 'success',
            'destination' => $config['host'] . ':' . ($config['path'] ?? '/') . '/' . basename($backupFile),
            'size_bytes' => filesize($backupFile),
            'timestamp' => date('c')
        ];
    }

    private function verifyBackupIntegrity(string $backupId): array
    {
        try {
            $backupFile = $this->getBackupFilePath($backupId);
            
            if (!file_exists($backupFile)) {
                throw new Exception('Backup file not found for verification');
            }

            // Calculate checksum
            $checksum = hash_file('sha256', $backupFile);
            
            // Test archive integrity
            $command = sprintf('tar -tzf %s > /dev/null', escapeshellarg($backupFile));
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                throw new Exception('Archive integrity check failed: ' . implode("\n", $output));
            }

            return [
                'status' => 'success',
                'checksum' => $checksum,
                'archive_valid' => true,
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    public function restoreFromBackup(string $backupId, array $options = []): array
    {
        try {
            $backupFile = $this->getBackupFilePath($backupId);
            
            if (!file_exists($backupFile)) {
                throw new Exception('Backup file not found: ' . $backupFile);
            }

            $restoreDir = $this->backupDir . '/restore_' . $backupId . '_' . time();
            mkdir($restoreDir, 0755, true);

            $results = [
                'backup_id' => $backupId,
                'timestamp' => date('c'),
                'status' => 'in_progress',
                'components' => []
            ];

            // Extract backup
            $extractResult = $this->extractBackup($backupFile, $restoreDir);
            $results['components']['extraction'] = $extractResult;

            if ($extractResult['status'] !== 'success') {
                throw new Exception('Failed to extract backup');
            }

            // Restore database if requested
            if ($options['restore_database'] ?? true) {
                $dbResult = $this->restoreDatabase($restoreDir);
                $results['components']['database'] = $dbResult;
            }

            // Restore configuration if requested
            if ($options['restore_configuration'] ?? true) {
                $configResult = $this->restoreConfiguration($restoreDir);
                $results['components']['configuration'] = $configResult;
            }

            // Restore application if requested
            if ($options['restore_application'] ?? true) {
                $appResult = $this->restoreApplication($restoreDir);
                $results['components']['application'] = $appResult;
            }

            // Clean up restore directory
            $this->removeDirectory($restoreDir);

            $results['status'] = $this->determineOverallStatus($results['components']);
            $results['duration_seconds'] = time() - strtotime($results['timestamp']);

            $this->logger->logInfo('Restore completed', $results);

            return $results;

        } catch (Exception $e) {
            $this->logger->logError('Restore failed', [
                'backup_id' => $backupId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'backup_id' => $backupId,
                'timestamp' => date('c'),
                'status' => 'failed',
                'error' => $e->getMessage()
            ];
        }
    }

    private function extractBackup(string $backupFile, string $restoreDir): array
    {
        try {
            // Handle encrypted backups
            if (str_ends_with($backupFile, '.enc')) {
                $decryptedFile = $restoreDir . '/backup.tar.gz';
                $command = sprintf(
                    'openssl enc -aes-256-cbc -d -in %s -out %s -k %s',
                    escapeshellarg($backupFile),
                    escapeshellarg($decryptedFile),
                    escapeshellarg($this->encryptionKey)
                );

                $output = [];
                $returnCode = 0;
                exec($command . ' 2>&1', $output, $returnCode);

                if ($returnCode !== 0) {
                    throw new Exception('Decryption failed: ' . implode("\n", $output));
                }

                $backupFile = $decryptedFile;
            }

            // Extract archive
            $command = sprintf(
                'tar -xzf %s -C %s',
                escapeshellarg($backupFile),
                escapeshellarg($restoreDir)
            );

            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                throw new Exception('Extraction failed: ' . implode("\n", $output));
            }

            return [
                'status' => 'success',
                'extracted_to' => $restoreDir,
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    private function restoreDatabase(string $restoreDir): array
    {
        try {
            $sqlFile = $restoreDir . '/database.sql';
            
            if (!file_exists($sqlFile)) {
                throw new Exception('Database backup file not found');
            }

            $dbConfig = $this->config->get('database');
            
            $command = sprintf(
                'psql -h %s -p %s -U %s -d %s --no-password < %s',
                escapeshellarg($dbConfig['host']),
                escapeshellarg($dbConfig['port']),
                escapeshellarg($dbConfig['username']),
                escapeshellarg($dbConfig['database']),
                escapeshellarg($sqlFile)
            );

            // Set PGPASSWORD environment variable
            putenv('PGPASSWORD=' . $dbConfig['password']);
            
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);
            
            // Clear password from environment
            putenv('PGPASSWORD');

            if ($returnCode !== 0) {
                throw new Exception('Database restore failed: ' . implode("\n", $output));
            }

            return [
                'status' => 'success',
                'restored_from' => 'database.sql',
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    private function restoreConfiguration(string $restoreDir): array
    {
        try {
            $configBackupDir = $restoreDir . '/config';
            
            if (!is_dir($configBackupDir)) {
                throw new Exception('Configuration backup directory not found');
            }

            $restoredFiles = [];
            
            // Restore .env file
            if (file_exists($configBackupDir . '/.env')) {
                copy($configBackupDir . '/.env', __DIR__ . '/../../.env');
                $restoredFiles[] = '.env';
            }

            // Restore config directory
            if (is_dir($configBackupDir . '/config')) {
                $this->copyDirectory($configBackupDir . '/config', __DIR__ . '/../../config');
                $restoredFiles[] = 'config/';
            }

            return [
                'status' => 'success',
                'restored_files' => $restoredFiles,
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    private function restoreApplication(string $restoreDir): array
    {
        try {
            $appBackupDir = $restoreDir . '/application';
            
            if (!is_dir($appBackupDir)) {
                throw new Exception('Application backup directory not found');
            }

            $restoredComponents = [];
            
            // Restore src directory
            if (is_dir($appBackupDir . '/src')) {
                $this->copyDirectory($appBackupDir . '/src', __DIR__ . '/../../src');
                $restoredComponents[] = 'src/';
            }

            // Restore public files
            $publicFiles = ['api.php', 'admin.php', '.htaccess'];
            foreach ($publicFiles as $file) {
                if (file_exists($appBackupDir . '/' . $file)) {
                    copy($appBackupDir . '/' . $file, __DIR__ . '/../../' . $file);
                    $restoredComponents[] = $file;
                }
            }

            return [
                'status' => 'success',
                'restored_components' => $restoredComponents,
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }

    public function listBackups(): array
    {
        $backups = [];
        $files = glob($this->backupDir . '/*.tar.gz*');
        
        foreach ($files as $file) {
            $filename = basename($file);
            $backupId = str_replace(['.tar.gz', '.enc'], '', $filename);
            
            $backups[] = [
                'backup_id' => $backupId,
                'filename' => $filename,
                'size_bytes' => filesize($file),
                'created_at' => date('c', filemtime($file)),
                'encrypted' => str_ends_with($filename, '.enc')
            ];
        }

        // Sort by creation date (newest first)
        usort($backups, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return $backups;
    }

    private function cleanupOldBackups(): void
    {
        $retention = $this->config->get('backup.retention', [
            'daily' => 7,
            'weekly' => 4,
            'monthly' => 12
        ]);

        $backups = $this->listBackups();
        $now = time();
        
        foreach ($backups as $backup) {
            $age = $now - strtotime($backup['created_at']);
            $ageDays = $age / (24 * 3600);
            
            $shouldDelete = false;
            
            if ($ageDays > 365 && count($backups) > $retention['monthly']) {
                $shouldDelete = true;
            } elseif ($ageDays > 30 && count($backups) > $retention['weekly']) {
                $shouldDelete = true;
            } elseif ($ageDays > 7 && count($backups) > $retention['daily']) {
                $shouldDelete = true;
            }
            
            if ($shouldDelete) {
                $backupFile = $this->backupDir . '/' . $backup['filename'];
                if (file_exists($backupFile)) {
                    unlink($backupFile);
                    $this->logger->logInfo('Old backup deleted', [
                        'backup_id' => $backup['backup_id'],
                        'age_days' => round($ageDays, 1)
                    ]);
                }
            }
        }
    }

    // Helper methods
    private function copyDirectory(string $source, string $destination): int
    {
        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }

        $totalSize = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $item) {
            $destPath = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                if (!is_dir($destPath)) {
                    mkdir($destPath, 0755, true);
                }
            } else {
                copy($item, $destPath);
                $totalSize += $item->getSize();
            }
        }

        return $totalSize;
    }

    private function removeDirectory(string $directory): void
    {
        if (!is_dir($directory)) {
            return;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($iterator as $item) {
            if ($item->isDir()) {
                rmdir($item);
            } else {
                unlink($item);
            }
        }

        rmdir($directory);
    }

    private function getDirectorySize(string $directory): int
    {
        $size = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            $size += $file->getSize();
        }

        return $size;
    }

    private function calculateChecksums(string $directory): array
    {
        $checksums = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $relativePath = str_replace($directory . DIRECTORY_SEPARATOR, '', $file->getPathname());
                $checksums[$relativePath] = hash_file('sha256', $file->getPathname());
            }
        }

        return $checksums;
    }

    private function getBackupFilePath(string $backupId): string
    {
        $possibleExtensions = ['.tar.gz.enc', '.tar.gz'];
        
        foreach ($possibleExtensions as $ext) {
            $path = $this->backupDir . '/' . $backupId . $ext;
            if (file_exists($path)) {
                return $path;
            }
        }

        return $this->backupDir . '/' . $backupId . '.tar.gz';
    }

    private function getBackupSize(string $backupId): int
    {
        $backupFile = $this->getBackupFilePath($backupId);
        return file_exists($backupFile) ? filesize($backupFile) : 0;
    }

    private function determineOverallStatus(array $components): string
    {
        $hasFailure = false;
        $hasWarning = false;

        foreach ($components as $component) {
            if (is_array($component)) {
                if (isset($component['status'])) {
                    if ($component['status'] === 'failed') {
                        $hasFailure = true;
                    } elseif ($component['status'] === 'warning') {
                        $hasWarning = true;
                    }
                } else {
                    // Check nested components
                    foreach ($component as $subComponent) {
                        if (is_array($subComponent) && isset($subComponent['status'])) {
                            if ($subComponent['status'] === 'failed') {
                                $hasFailure = true;
                            } elseif ($subComponent['status'] === 'warning') {
                                $hasWarning = true;
                            }
                        }
                    }
                }
            }
        }

        if ($hasFailure) {
            return 'failed';
        } elseif ($hasWarning) {
            return 'warning';
        } else {
            return 'success';
        }
    }

    private function cleanupTemporaryFiles(string $backupPath): void
    {
        if (is_dir($backupPath)) {
            $this->removeDirectory($backupPath);
        }
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    $backupManager = new BackupManager();
    
    $command = $argv[1] ?? 'backup';
    
    switch ($command) {
        case 'backup':
            echo "Starting full backup...\n";
            $result = $backupManager->createFullBackup();
            echo json_encode($result, JSON_PRETTY_PRINT) . PHP_EOL;
            exit($result['status'] === 'success' ? 0 : 1);
            
        case 'restore':
            if (!isset($argv[2])) {
                echo "Usage: php backup-manager.php restore <backup_id>\n";
                exit(1);
            }
            
            $backupId = $argv[2];
            $options = [];
            
            // Parse additional options
            for ($i = 3; $i < count($argv); $i++) {
                switch ($argv[$i]) {
                    case '--no-database':
                        $options['restore_database'] = false;
                        break;
                    case '--no-config':
                        $options['restore_configuration'] = false;
                        break;
                    case '--no-app':
                        $options['restore_application'] = false;
                        break;
                }
            }
            
            echo "Starting restore from backup: $backupId\n";
            $result = $backupManager->restoreFromBackup($backupId, $options);
            echo json_encode($result, JSON_PRETTY_PRINT) . PHP_EOL;
            exit($result['status'] === 'success' ? 0 : 1);
            
        case 'list':
            $backups = $backupManager->listBackups();
            echo json_encode($backups, JSON_PRETTY_PRINT) . PHP_EOL;
            break;
            
        default:
            echo "Usage: php backup-manager.php [backup|restore|list]\n";
            echo "  backup                    - Create a full backup\n";
            echo "  restore <backup_id>       - Restore from backup\n";
            echo "  list                      - List available backups\n";
            echo "\nRestore options:\n";
            echo "  --no-database            - Skip database restore\n";
            echo "  --no-config              - Skip configuration restore\n";
            echo "  --no-app                 - Skip application restore\n";
            exit(1);
    }
}