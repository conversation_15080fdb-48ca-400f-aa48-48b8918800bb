-- Migration: Add missing performance indexes
-- Created: 2025-01-30 13:00:00
-- Version: 2025_01_30_130000

-- Additional performance indexes for better query performance

-- Admin Users indexes
CREATE INDEX IF NOT EXISTS idx_admin_users_last_login ON admin_users(last_login);
CREATE INDEX IF NOT EXISTS idx_admin_users_created_at ON admin_users(created_at);

-- Freemius Products indexes
CREATE INDEX IF NOT EXISTS idx_freemius_products_is_released ON freemius_products(is_released);
CREATE INDEX IF NOT EXISTS idx_freemius_products_is_premium ON freemius_products(is_premium);
CREATE INDEX IF NOT EXISTS idx_freemius_products_environment ON freemius_products(environment);

-- Freemius Installations indexes
CREATE INDEX IF NOT EXISTS idx_freemius_installations_url ON freemius_installations(url);
CREATE INDEX IF NOT EXISTS idx_freemius_installations_version ON freemius_installations(version);
CREATE INDEX IF NOT EXISTS idx_freemius_installations_last_seen ON freemius_installations(last_seen_at);
CREATE INDEX IF NOT EXISTS idx_freemius_installations_trial_ends ON freemius_installations(trial_ends);

-- IP Intelligence indexes
CREATE INDEX IF NOT EXISTS idx_ip_intelligence_hostname ON ip_intelligence(hostname);
CREATE INDEX IF NOT EXISTS idx_ip_intelligence_location_expires ON ip_intelligence(location_expires_at);
CREATE INDEX IF NOT EXISTS idx_ip_intelligence_connection_expires ON ip_intelligence(connection_expires_at);
CREATE INDEX IF NOT EXISTS idx_ip_intelligence_company_expires ON ip_intelligence(company_expires_at);

-- System Logs indexes
CREATE INDEX IF NOT EXISTS idx_system_logs_ip ON system_logs(ip);
CREATE INDEX IF NOT EXISTS idx_system_logs_level_created ON system_logs(level, created_at);

-- API Requests indexes
CREATE INDEX IF NOT EXISTS idx_api_requests_visitor_hash ON api_requests(visitor_hash);
CREATE INDEX IF NOT EXISTS idx_api_requests_response_time ON api_requests(response_time_ms);
CREATE INDEX IF NOT EXISTS idx_api_requests_url ON api_requests(url);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_freemius_installations_active_premium ON freemius_installations(is_active, is_premium, plugin_id);
CREATE INDEX IF NOT EXISTS idx_ip_intelligence_fresh_data ON ip_intelligence(security_expires_at, location_expires_at) WHERE security_expires_at > NOW();
CREATE INDEX IF NOT EXISTS idx_system_logs_recent_errors ON system_logs(created_at, level) WHERE level IN ('error', 'critical');
CREATE INDEX IF NOT EXISTS idx_api_requests_recent_status ON api_requests(created_at, response_status) WHERE created_at > NOW() - INTERVAL '7 days';

-- ROLLBACK:
-- DROP INDEX IF EXISTS idx_admin_users_last_login;
-- DROP INDEX IF EXISTS idx_admin_users_created_at;
-- DROP INDEX IF EXISTS idx_freemius_products_is_released;
-- DROP INDEX IF EXISTS idx_freemius_products_is_premium;
-- DROP INDEX IF EXISTS idx_freemius_products_environment;
-- DROP INDEX IF EXISTS idx_freemius_installations_url;
-- DROP INDEX IF EXISTS idx_freemius_installations_version;
-- DROP INDEX IF EXISTS idx_freemius_installations_last_seen;
-- DROP INDEX IF EXISTS idx_freemius_installations_trial_ends;
-- DROP INDEX IF EXISTS idx_ip_intelligence_hostname;
-- DROP INDEX IF EXISTS idx_ip_intelligence_location_expires;
-- DROP INDEX IF EXISTS idx_ip_intelligence_connection_expires;
-- DROP INDEX IF EXISTS idx_ip_intelligence_company_expires;
-- DROP INDEX IF EXISTS idx_system_logs_ip;
-- DROP INDEX IF EXISTS idx_system_logs_level_created;
-- DROP INDEX IF EXISTS idx_api_requests_visitor_hash;
-- DROP INDEX IF EXISTS idx_api_requests_response_time;
-- DROP INDEX IF EXISTS idx_api_requests_url;
-- DROP INDEX IF EXISTS idx_freemius_installations_active_premium;
-- DROP INDEX IF EXISTS idx_ip_intelligence_fresh_data;
-- DROP INDEX IF EXISTS idx_system_logs_recent_errors;
-- DROP INDEX IF EXISTS idx_api_requests_recent_status;