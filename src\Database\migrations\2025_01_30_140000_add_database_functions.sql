-- Migration: Add database functions and procedures
-- Created: 2025-01-30 14:00:00
-- Version: 2025_01_30_140000

-- Database functions for common operations and maintenance

-- Function to clean up expired IP intelligence data
CREATE OR REPLACE FUNCTION cleanup_expired_ip_data()
RETURNS INTEGER AS $
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete records where all data types have expired
    DELETE FROM ip_intelligence 
    WHERE security_expires_at < NOW() 
    AND location_expires_at < NOW() 
    AND connection_expires_at < NOW() 
    AND company_expires_at < NOW()
    AND cached_at < NOW() - INTERVAL '90 days'; -- Keep for 90 days minimum
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO system_logs (type, level, message, context, created_at)
    VALUES ('system', 'info', 'IP intelligence cleanup completed', 
            json_build_object('deleted_records', deleted_count), NOW());
    
    RETURN deleted_count;
END;
$ LANGUAGE plpgsql;

-- Function to update IP data expiration dates based on deprecation rules
CREATE OR REPLACE FUNCTION update_ip_expiration_dates()
R<PERSON>URNS VOID AS $
BEGIN
    UPDATE ip_intelligence SET
        location_expires_at = cached_at + INTERVAL '10 days',
        security_expires_at = cached_at + INTERVAL '3 days',
        connection_expires_at = cached_at + INTERVAL '7 days',
        company_expires_at = cached_at + INTERVAL '30 days'
    WHERE location_expires_at IS NULL 
       OR security_expires_at IS NULL 
       OR connection_expires_at IS NULL 
       OR company_expires_at IS NULL;
END;
$ LANGUAGE plpgsql;

-- Function to get system health statistics
CREATE OR REPLACE FUNCTION get_system_health_stats()
RETURNS JSON AS $
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'database', json_build_object(
            'total_size', pg_size_pretty(pg_database_size(current_database())),
            'connection_count', (SELECT count(*) FROM pg_stat_activity WHERE datname = current_database())
        ),
        'tables', json_build_object(
            'admin_users', (SELECT count(*) FROM admin_users),
            'freemius_products', (SELECT count(*) FROM freemius_products),
            'freemius_installations', (SELECT count(*) FROM freemius_installations),
            'ip_intelligence', (SELECT count(*) FROM ip_intelligence),
            'system_logs', (SELECT count(*) FROM system_logs),
            'api_requests', (SELECT count(*) FROM api_requests)
        ),
        'cache_stats', json_build_object(
            'fresh_ip_records', (SELECT count(*) FROM ip_intelligence WHERE security_expires_at > NOW()),
            'stale_ip_records', (SELECT count(*) FROM ip_intelligence WHERE security_expires_at <= NOW()),
            'recent_api_requests', (SELECT count(*) FROM api_requests WHERE created_at > NOW() - INTERVAL '24 hours')
        ),
        'timestamp', NOW()
    ) INTO result;
    
    RETURN result;
END;
$ LANGUAGE plpgsql;

-- Function to archive old logs
CREATE OR REPLACE FUNCTION archive_old_logs(retention_days INTEGER DEFAULT 30)
RETURNS INTEGER AS $
DECLARE
    archived_count INTEGER;
    cutoff_date TIMESTAMP;
BEGIN
    cutoff_date := NOW() - (retention_days || ' days')::INTERVAL;
    
    -- Archive system logs older than retention period
    DELETE FROM system_logs WHERE created_at < cutoff_date;
    GET DIAGNOSTICS archived_count = ROW_COUNT;
    
    -- Log the archival operation
    INSERT INTO system_logs (type, level, message, context, created_at)
    VALUES ('system', 'info', 'Log archival completed', 
            json_build_object('archived_records', archived_count, 'retention_days', retention_days), NOW());
    
    RETURN archived_count;
END;
$ LANGUAGE plpgsql;

-- Function to get API performance metrics
CREATE OR REPLACE FUNCTION get_api_performance_metrics(hours_back INTEGER DEFAULT 24)
RETURNS JSON AS $
DECLARE
    result JSON;
    start_time TIMESTAMP;
BEGIN
    start_time := NOW() - (hours_back || ' hours')::INTERVAL;
    
    SELECT json_build_object(
        'period_hours', hours_back,
        'total_requests', count(*),
        'successful_requests', count(*) FILTER (WHERE response_status >= 200 AND response_status < 300),
        'failed_requests', count(*) FILTER (WHERE response_status >= 400),
        'avg_response_time_ms', round(avg(response_time_ms)::numeric, 2),
        'max_response_time_ms', max(response_time_ms),
        'min_response_time_ms', min(response_time_ms),
        'unique_ips', count(DISTINCT ip),
        'freemius_valid_rate', round(
            (count(*) FILTER (WHERE freemius_valid = true)::numeric / 
             NULLIF(count(*), 0) * 100), 2
        ),
        'requests_per_hour', round(count(*)::numeric / hours_back, 2)
    ) INTO result
    FROM api_requests 
    WHERE created_at >= start_time;
    
    RETURN result;
END;
$ LANGUAGE plpgsql;

-- Function to validate database integrity
CREATE OR REPLACE FUNCTION validate_database_integrity()
RETURNS JSON AS $
DECLARE
    result JSON;
    issues JSON[];
BEGIN
    issues := ARRAY[]::JSON[];
    
    -- Check for orphaned installations (plugin_id not in products)
    IF EXISTS (
        SELECT 1 FROM freemius_installations i 
        LEFT JOIN freemius_products p ON i.plugin_id = p.id 
        WHERE p.id IS NULL
    ) THEN
        issues := issues || json_build_object(
            'type', 'orphaned_installations',
            'description', 'Installations exist without corresponding products',
            'count', (SELECT count(*) FROM freemius_installations i 
                     LEFT JOIN freemius_products p ON i.plugin_id = p.id 
                     WHERE p.id IS NULL)
        );
    END IF;
    
    -- Check for logs with invalid user references
    IF EXISTS (
        SELECT 1 FROM system_logs l 
        LEFT JOIN admin_users u ON l.user_id = u.id 
        WHERE l.user_id IS NOT NULL AND u.id IS NULL
    ) THEN
        issues := issues || json_build_object(
            'type', 'invalid_log_users',
            'description', 'System logs reference non-existent users',
            'count', (SELECT count(*) FROM system_logs l 
                     LEFT JOIN admin_users u ON l.user_id = u.id 
                     WHERE l.user_id IS NOT NULL AND u.id IS NULL)
        );
    END IF;
    
    -- Check for IP records with invalid expiration dates
    IF EXISTS (
        SELECT 1 FROM ip_intelligence 
        WHERE security_expires_at < cached_at 
           OR location_expires_at < cached_at
           OR connection_expires_at < cached_at
           OR company_expires_at < cached_at
    ) THEN
        issues := issues || json_build_object(
            'type', 'invalid_expiration_dates',
            'description', 'IP records have expiration dates before cache date',
            'count', (SELECT count(*) FROM ip_intelligence 
                     WHERE security_expires_at < cached_at 
                        OR location_expires_at < cached_at
                        OR connection_expires_at < cached_at
                        OR company_expires_at < cached_at)
        );
    END IF;
    
    SELECT json_build_object(
        'timestamp', NOW(),
        'issues_found', array_length(issues, 1),
        'issues', issues,
        'status', CASE WHEN array_length(issues, 1) = 0 THEN 'healthy' ELSE 'issues_detected' END
    ) INTO result;
    
    RETURN result;
END;
$ LANGUAGE plpgsql;

-- Create a maintenance procedure that can be called regularly
CREATE OR REPLACE FUNCTION run_maintenance()
RETURNS JSON AS $
DECLARE
    result JSON;
    cleaned_ip INTEGER;
    archived_logs INTEGER;
BEGIN
    -- Run cleanup operations
    SELECT cleanup_expired_ip_data() INTO cleaned_ip;
    SELECT archive_old_logs(30) INTO archived_logs;
    
    -- Update IP expiration dates
    PERFORM update_ip_expiration_dates();
    
    -- Analyze tables for better query performance
    ANALYZE admin_users;
    ANALYZE freemius_products;
    ANALYZE freemius_installations;
    ANALYZE ip_intelligence;
    ANALYZE system_logs;
    ANALYZE api_requests;
    
    SELECT json_build_object(
        'timestamp', NOW(),
        'operations', json_build_object(
            'cleaned_ip_records', cleaned_ip,
            'archived_log_records', archived_logs,
            'updated_expiration_dates', true,
            'analyzed_tables', true
        ),
        'status', 'completed'
    ) INTO result;
    
    -- Log the maintenance operation
    INSERT INTO system_logs (type, level, message, context, created_at)
    VALUES ('system', 'info', 'Scheduled maintenance completed', result, NOW());
    
    RETURN result;
END;
$ LANGUAGE plpgsql;

-- ROLLBACK:
-- DROP FUNCTION IF EXISTS cleanup_expired_ip_data();
-- DROP FUNCTION IF EXISTS update_ip_expiration_dates();
-- DROP FUNCTION IF EXISTS get_system_health_stats();
-- DROP FUNCTION IF EXISTS archive_old_logs(INTEGER);
-- DROP FUNCTION IF EXISTS get_api_performance_metrics(INTEGER);
-- DROP FUNCTION IF EXISTS validate_database_integrity();
-- DROP FUNCTION IF EXISTS run_maintenance();