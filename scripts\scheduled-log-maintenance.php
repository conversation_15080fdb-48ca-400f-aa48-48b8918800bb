<?php

/**
 * Scheduled Log Maintenance Script
 * 
 * Automated log maintenance script designed to run via cron job.
 * Performs log rotation, cleanup, and health monitoring on a schedule.
 * 
 * Recommended cron schedule:
 * # Daily log maintenance at 2 AM
 * 0 2 * * * /usr/bin/php /path/to/guardgeo_api/scripts/scheduled-log-maintenance.php
 * 
 * # Weekly comprehensive maintenance on Sundays at 3 AM
 * 0 3 * * 0 /usr/bin/php /path/to/guardgeo_api/scripts/scheduled-log-maintenance.php --comprehensive
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Skpassegna\GuardgeoApi\Services\LogRotationService;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Utils\Logger;

// Parse command line arguments
$options = getopt('', ['comprehensive', 'force', 'quiet', 'config:']);

$comprehensive = isset($options['comprehensive']);
$force = isset($options['force']);
$quiet = isset($options['quiet']);
$configFile = $options['config'] ?? null;

// Initialize services
$logger = new Logger();
$loggingService = new LoggingService();

// Load configuration
$config = loadConfiguration($configFile);
$logRotationService = new LogRotationService($config);

// Start maintenance
$startTime = microtime(true);
$results = [
    'start_time' => date('Y-m-d H:i:s'),
    'comprehensive' => $comprehensive,
    'tasks_completed' => [],
    'errors' => [],
    'warnings' => [],
    'statistics' => []
];

try {
    if (!$quiet) {
        echo "Starting scheduled log maintenance...\n";
    }
    
    // Always perform basic maintenance
    performBasicMaintenance($logRotationService, $loggingService, $results, $quiet);
    
    // Perform comprehensive maintenance if requested or if it's been too long
    if ($comprehensive || shouldPerformComprehensiveMaintenance($config)) {
        performComprehensiveMaintenance($logRotationService, $loggingService, $results, $quiet);
    }
    
    // Health check and monitoring
    performHealthCheck($logRotationService, $loggingService, $results, $quiet);
    
    // Generate summary report
    $results['execution_time'] = microtime(true) - $startTime;
    $results['end_time'] = date('Y-m-d H:i:s');
    $results['success'] = empty($results['errors']);
    
    // Log completion
    $logger->info('Scheduled log maintenance completed', $results);
    
    if (!$quiet) {
        echo "Log maintenance completed successfully.\n";
        echo "Execution time: " . round($results['execution_time'], 2) . "s\n";
        echo "Tasks completed: " . implode(', ', $results['tasks_completed']) . "\n";
        
        if (!empty($results['warnings'])) {
            echo "Warnings: " . count($results['warnings']) . "\n";
        }
    }
    
    // Update last maintenance timestamp
    updateLastMaintenanceTime($config);
    
} catch (Exception $e) {
    $results['errors'][] = $e->getMessage();
    $results['success'] = false;
    
    $logger->error('Scheduled log maintenance failed', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'results' => $results
    ]);
    
    if (!$quiet) {
        echo "Error: Log maintenance failed - " . $e->getMessage() . "\n";
    }
    
    exit(1);
}

/**
 * Perform basic daily maintenance
 */
function performBasicMaintenance(LogRotationService $logRotationService, LoggingService $loggingService, array &$results, bool $quiet): void
{
    if (!$quiet) {
        echo "Performing basic maintenance...\n";
    }
    
    try {
        // Clean old database logs (keep last 30 days)
        $deletedLogs = $loggingService->clearOldLogs(30);
        $results['tasks_completed'][] = 'database_cleanup';
        $results['statistics']['database_logs_deleted'] = $deletedLogs;
        
        if (!$quiet && $deletedLogs > 0) {
            echo "- Cleaned {$deletedLogs} old database log entries\n";
        }
        
        // Check if file rotation is needed
        $rotationStats = $logRotationService->getLogStatistics();
        $needsRotation = false;
        
        foreach ($rotationStats['file_logs'] as $type => $fileStats) {
            if ($fileStats['needs_rotation'] ?? false) {
                $needsRotation = true;
                break;
            }
        }
        
        if ($needsRotation) {
            $rotationResults = $logRotationService->rotateFileLogs();
            $results['tasks_completed'][] = 'file_rotation';
            $results['statistics']['files_rotated'] = count($rotationResults['files_rotated']);
            $results['statistics']['space_freed'] = $rotationResults['space_freed'];
            
            if (!$quiet) {
                echo "- Rotated " . count($rotationResults['files_rotated']) . " log files\n";
                echo "- Freed " . formatBytes($rotationResults['space_freed']) . " of space\n";
            }
            
            if (!empty($rotationResults['errors'])) {
                $results['errors'] = array_merge($results['errors'], $rotationResults['errors']);
            }
        }
        
    } catch (Exception $e) {
        $results['errors'][] = "Basic maintenance failed: " . $e->getMessage();
        throw $e;
    }
}

/**
 * Perform comprehensive weekly maintenance
 */
function performComprehensiveMaintenance(LogRotationService $logRotationService, LoggingService $loggingService, array &$results, bool $quiet): void
{
    if (!$quiet) {
        echo "Performing comprehensive maintenance...\n";
    }
    
    try {
        // Full log rotation including archives cleanup
        $fullRotationResults = $logRotationService->performLogRotation();
        $results['tasks_completed'][] = 'comprehensive_rotation';
        $results['statistics']['comprehensive_results'] = $fullRotationResults;
        
        if (!$quiet) {
            echo "- Comprehensive rotation completed\n";
            echo "- Total space freed: " . formatBytes($fullRotationResults['total_space_freed']) . "\n";
        }
        
        // Clean up old archives
        $archiveResults = $logRotationService->cleanupOldArchives();
        $results['tasks_completed'][] = 'archive_cleanup';
        $results['statistics']['archives_deleted'] = count($archiveResults['archives_deleted']);
        
        if (!$quiet && !empty($archiveResults['archives_deleted'])) {
            echo "- Cleaned " . count($archiveResults['archives_deleted']) . " old archives\n";
        }
        
        // Optimize database (if supported)
        optimizeDatabase($results, $quiet);
        
    } catch (Exception $e) {
        $results['errors'][] = "Comprehensive maintenance failed: " . $e->getMessage();
        throw $e;
    }
}

/**
 * Perform health check and monitoring
 */
function performHealthCheck(LogRotationService $logRotationService, LoggingService $loggingService, array &$results, bool $quiet): void
{
    if (!$quiet) {
        echo "Performing health check...\n";
    }
    
    try {
        // Get comprehensive statistics
        $stats = $logRotationService->getLogStatistics();
        $loggingStats = $loggingService->getLoggingStats();
        
        $results['tasks_completed'][] = 'health_check';
        $results['statistics']['health_check'] = [
            'database_logs' => $stats['database_logs'],
            'file_logs' => $stats['file_logs'],
            'archives' => $stats['archives'],
            'logging_activity' => $loggingStats
        ];
        
        // Check for potential issues
        checkForIssues($stats, $results, $quiet);
        
    } catch (Exception $e) {
        $results['warnings'][] = "Health check failed: " . $e->getMessage();
    }
}

/**
 * Check for potential issues and warnings
 */
function checkForIssues(array $stats, array &$results, bool $quiet): void
{
    // Check database log growth
    if (isset($stats['database_logs']['system_logs'])) {
        $systemLogs = $stats['database_logs']['system_logs'];
        $recentGrowth = $systemLogs['last_7_days'] ?? 0;
        
        if ($recentGrowth > 10000) { // More than 10k logs in 7 days
            $results['warnings'][] = "High database log growth: {$recentGrowth} entries in last 7 days";
        }
    }
    
    // Check file log sizes
    foreach ($stats['file_logs'] as $type => $fileStats) {
        if (isset($fileStats['size']) && $fileStats['size'] > 100 * 1024 * 1024) { // 100MB
            $results['warnings'][] = "Large {$type} log file: " . formatBytes($fileStats['size']);
        }
    }
    
    // Check archive count
    if (isset($stats['archives']['total_archives'])) {
        $archiveCount = $stats['archives']['total_archives'];
        $maxArchives = $stats['archives']['max_archives_allowed'];
        
        if ($archiveCount >= $maxArchives * 0.9) { // 90% of max
            $results['warnings'][] = "Archive storage nearly full: {$archiveCount}/{$maxArchives}";
        }
    }
    
    if (!$quiet && !empty($results['warnings'])) {
        echo "Warnings detected:\n";
        foreach ($results['warnings'] as $warning) {
            echo "- {$warning}\n";
        }
    }
}

/**
 * Optimize database tables (if supported)
 */
function optimizeDatabase(array &$results, bool $quiet): void
{
    try {
        // PostgreSQL doesn't have OPTIMIZE TABLE, but we can run VACUUM ANALYZE
        $sql = "VACUUM ANALYZE system_logs, api_requests";
        \Skpassegna\GuardgeoApi\Database\DatabaseConnection::execute($sql);
        
        $results['tasks_completed'][] = 'database_optimization';
        
        if (!$quiet) {
            echo "- Database optimization completed\n";
        }
        
    } catch (Exception $e) {
        $results['warnings'][] = "Database optimization failed: " . $e->getMessage();
    }
}

/**
 * Check if comprehensive maintenance should be performed
 */
function shouldPerformComprehensiveMaintenance(array $config): bool
{
    $lastMaintenanceFile = $config['last_maintenance_file'] ?? '/tmp/guardgeo_last_maintenance';
    
    if (!file_exists($lastMaintenanceFile)) {
        return true; // Never run before
    }
    
    $lastMaintenance = (int)file_get_contents($lastMaintenanceFile);
    $daysSinceLastMaintenance = (time() - $lastMaintenance) / (24 * 60 * 60);
    
    return $daysSinceLastMaintenance >= 7; // Weekly comprehensive maintenance
}

/**
 * Update last maintenance timestamp
 */
function updateLastMaintenanceTime(array $config): void
{
    $lastMaintenanceFile = $config['last_maintenance_file'] ?? '/tmp/guardgeo_last_maintenance';
    file_put_contents($lastMaintenanceFile, time());
}

/**
 * Load configuration from file or use defaults
 */
function loadConfiguration(?string $configFile): array
{
    $defaultConfig = [
        'retention_days' => [
            'system_logs' => 30,
            'api_requests' => 90,
            'file_logs' => 7,
            'error_logs' => 60
        ],
        'size_limits' => [
            'combined_log' => 50 * 1024 * 1024, // 50MB
            'error_log' => 20 * 1024 * 1024,    // 20MB
        ],
        'max_archives' => 12,
        'compression' => true,
        'last_maintenance_file' => '/tmp/guardgeo_last_maintenance'
    ];
    
    if ($configFile && file_exists($configFile)) {
        try {
            $fileConfig = json_decode(file_get_contents($configFile), true);
            return array_merge($defaultConfig, $fileConfig);
        } catch (Exception $e) {
            error_log("Failed to load config file {$configFile}: " . $e->getMessage());
        }
    }
    
    return $defaultConfig;
}

/**
 * Format bytes to human readable format
 */
function formatBytes(int $bytes): string
{
    $units = ['B', 'KB', 'MB', 'GB'];
    $unitIndex = 0;
    
    while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
        $bytes /= 1024;
        $unitIndex++;
    }
    
    return round($bytes, 2) . ' ' . $units[$unitIndex];
}