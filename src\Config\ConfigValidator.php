<?php

namespace Skpassegna\GuardgeoApi\Config;

use Skpassegna\GuardgeoApi\Utils\Logger;

/**
 * Configuration Validator
 * 
 * Validates configuration settings for completeness, correctness,
 * and security compliance across different environments.
 */
class ConfigValidator
{
    private Logger $logger;
    private array $validationRules;
    private array $errors = [];
    private array $warnings = [];
    
    public function __construct()
    {
        $this->logger = new Logger();
        $this->initializeValidationRules();
    }
    
    /**
     * Initialize validation rules
     */
    private function initializeValidationRules(): void
    {
        $this->validationRules = [
            'database' => [
                'required' => ['host', 'database', 'username'],
                'optional' => ['password', 'port', 'charset', 'options'],
                'validators' => [
                    'host' => 'validateDatabaseHost',
                    'port' => 'validateDatabasePort',
                    'database' => 'validateDatabaseName',
                    'username' => 'validateDatabaseUsername',
                ]
            ],
            
            'security' => [
                'required' => ['hash_algorithm'],
                'optional' => ['encryption_key', 'session_secure', 'csrf_protection', 'force_https'],
                'validators' => [
                    'encryption_key' => 'validateEncryptionKey',
                    'hash_algorithm' => 'validateHashAlgorithm',
                ]
            ],
            
            'logging' => [
                'required' => ['level'],
                'optional' => ['file_path', 'max_file_size', 'max_files'],
                'validators' => [
                    'level' => 'validateLogLevel',
                    'file_path' => 'validateLogPath',
                    'max_file_size' => 'validatePositiveInteger',
                    'max_files' => 'validatePositiveInteger',
                ]
            ],
            
            'api' => [
                'required' => [],
                'optional' => ['rate_limit_enabled', 'rate_limit_requests', 'rate_limit_window', 'request_timeout'],
                'validators' => [
                    'rate_limit_requests' => 'validatePositiveInteger',
                    'rate_limit_window' => 'validatePositiveInteger',
                    'request_timeout' => 'validatePositiveInteger',
                ]
            ],
            
            'freemius' => [
                'required' => ['api_token'],
                'optional' => ['base_url', 'timeout', 'retry_attempts'],
                'validators' => [
                    'api_token' => 'validateApiToken',
                    'base_url' => 'validateUrl',
                    'timeout' => 'validatePositiveInteger',
                    'retry_attempts' => 'validatePositiveInteger',
                ]
            ],
            
            'ipregistry' => [
                'required' => ['api_key'],
                'optional' => ['base_url', 'timeout', 'retry_attempts'],
                'validators' => [
                    'api_key' => 'validateApiKey',
                    'base_url' => 'validateUrl',
                    'timeout' => 'validatePositiveInteger',
                    'retry_attempts' => 'validatePositiveInteger',
                ]
            ],
        ];
    }
    
    /**
     * Validate complete configuration
     */
    public function validate(array $config, string $environment = 'development'): array
    {
        $this->errors = [];
        $this->warnings = [];
        
        // Validate each configuration section
        foreach ($this->validationRules as $section => $rules) {
            if (isset($config[$section])) {
                $this->validateSection($section, $config[$section], $rules, $environment);
            } else {
                $this->addError($section, "Configuration section '{$section}' is missing");
            }
        }
        
        // Environment-specific validations
        $this->validateEnvironmentSpecific($config, $environment);
        
        // Security validations
        $this->validateSecurity($config, $environment);
        
        $result = [
            'valid' => empty($this->errors),
            'errors' => $this->errors,
            'warnings' => $this->warnings,
            'environment' => $environment,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        if (!empty($this->errors)) {
            $this->logger->error('Configuration validation failed', [
                'environment' => $environment,
                'errors' => $this->errors,
                'warnings' => $this->warnings
            ]);
        } elseif (!empty($this->warnings)) {
            $this->logger->warning('Configuration validation completed with warnings', [
                'environment' => $environment,
                'warnings' => $this->warnings
            ]);
        } else {
            $this->logger->info('Configuration validation passed', [
                'environment' => $environment
            ]);
        }
        
        return $result;
    }
    
    /**
     * Validate a configuration section
     */
    private function validateSection(string $section, array $config, array $rules, string $environment): void
    {
        // Check required fields
        foreach ($rules['required'] as $field) {
            if (!isset($config[$field]) || empty($config[$field])) {
                $this->addError($section, "Required field '{$field}' is missing or empty");
            }
        }
        
        // Validate individual fields
        if (isset($rules['validators'])) {
            foreach ($rules['validators'] as $field => $validator) {
                if (isset($config[$field])) {
                    $this->$validator($section, $field, $config[$field], $environment);
                }
            }
        }
    }
    
    /**
     * Environment-specific validations
     */
    private function validateEnvironmentSpecific(array $config, string $environment): void
    {
        switch ($environment) {
            case 'production':
                $this->validateProduction($config);
                break;
            case 'staging':
                $this->validateStaging($config);
                break;
            case 'development':
                $this->validateDevelopment($config);
                break;
        }
    }
    
    /**
     * Production environment validations
     */
    private function validateProduction(array $config): void
    {
        // Debug must be disabled
        if (isset($config['app']['debug']) && $config['app']['debug']) {
            $this->addError('app', 'Debug mode must be disabled in production');
        }
        
        // HTTPS must be enforced
        if (!isset($config['security']['force_https']) || !$config['security']['force_https']) {
            $this->addWarning('security', 'HTTPS should be enforced in production');
        }
        
        // Secure sessions
        if (!isset($config['security']['session_secure']) || !$config['security']['session_secure']) {
            $this->addWarning('security', 'Secure sessions should be enabled in production');
        }
        
        // Encryption key must be set
        if (!isset($config['security']['encryption_key']) || empty($config['security']['encryption_key'])) {
            $this->addError('security', 'Encryption key is required in production');
        }
        
        // Log level should not be debug
        if (isset($config['logging']['level']) && $config['logging']['level'] === 'debug') {
            $this->addWarning('logging', 'Debug log level is not recommended in production');
        }
        
        // Rate limiting should be enabled
        if (!isset($config['api']['rate_limit_enabled']) || !$config['api']['rate_limit_enabled']) {
            $this->addWarning('api', 'Rate limiting should be enabled in production');
        }
    }
    
    /**
     * Staging environment validations
     */
    private function validateStaging(array $config): void
    {
        // Similar to production but more lenient
        if (isset($config['app']['debug']) && $config['app']['debug']) {
            $this->addWarning('app', 'Debug mode should typically be disabled in staging');
        }
        
        if (!isset($config['security']['encryption_key']) || empty($config['security']['encryption_key'])) {
            $this->addError('security', 'Encryption key is required in staging');
        }
    }
    
    /**
     * Development environment validations
     */
    private function validateDevelopment(array $config): void
    {
        // More lenient validations for development
        if (!isset($config['app']['debug'])) {
            $this->addWarning('app', 'Debug mode is typically enabled in development');
        }
    }
    
    /**
     * Security-specific validations
     */
    private function validateSecurity(array $config, string $environment): void
    {
        // Check for weak passwords in database config
        if (isset($config['database']['password'])) {
            $password = $config['database']['password'];
            if ($environment === 'production' && (empty($password) || strlen($password) < 12)) {
                $this->addError('database', 'Database password is too weak for production');
            }
        }
        
        // Check for default/weak API keys
        if (isset($config['freemius']['api_token'])) {
            $token = $config['freemius']['api_token'];
            if (in_array($token, ['test', 'demo', 'example', 'changeme'])) {
                $this->addError('freemius', 'Freemius API token appears to be a default/test value');
            }
        }
        
        if (isset($config['ipregistry']['api_key'])) {
            $key = $config['ipregistry']['api_key'];
            if (in_array($key, ['test', 'demo', 'example', 'changeme'])) {
                $this->addError('ipregistry', 'ipRegistry API key appears to be a default/test value');
            }
        }
    }
    
    // Individual field validators
    
    private function validateDatabaseHost(string $section, string $field, $value, string $environment): void
    {
        if (!is_string($value) || empty($value)) {
            $this->addError($section, "Database host must be a non-empty string");
            return;
        }
        
        // Check for localhost in production
        if ($environment === 'production' && in_array($value, ['localhost', '127.0.0.1', '::1'])) {
            $this->addWarning($section, "Using localhost in production may not be appropriate");
        }
    }
    
    private function validateDatabasePort(string $section, string $field, $value, string $environment): void
    {
        if (!is_int($value) || $value < 1 || $value > 65535) {
            $this->addError($section, "Database port must be an integer between 1 and 65535");
        }
    }
    
    private function validateDatabaseName(string $section, string $field, $value, string $environment): void
    {
        if (!is_string($value) || empty($value)) {
            $this->addError($section, "Database name must be a non-empty string");
            return;
        }
        
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $value)) {
            $this->addError($section, "Database name contains invalid characters");
        }
    }
    
    private function validateDatabaseUsername(string $section, string $field, $value, string $environment): void
    {
        if (!is_string($value) || empty($value)) {
            $this->addError($section, "Database username must be a non-empty string");
            return;
        }
        
        if ($environment === 'production' && in_array($value, ['root', 'admin', 'postgres', 'mysql'])) {
            $this->addWarning($section, "Using default database username in production is not recommended");
        }
    }
    
    private function validateEncryptionKey(string $section, string $field, $value, string $environment): void
    {
        if (!is_string($value)) {
            $this->addError($section, "Encryption key must be a string");
            return;
        }
        
        if (strlen($value) < 32) {
            $this->addError($section, "Encryption key must be at least 32 characters long");
        }
        
        if ($environment === 'production' && in_array($value, ['changeme', 'default', 'test'])) {
            $this->addError($section, "Encryption key appears to be a default value");
        }
    }
    
    private function validateHashAlgorithm(string $section, string $field, $value, string $environment): void
    {
        $validAlgorithms = ['sha256', 'sha512', 'bcrypt', 'argon2i', 'argon2id'];
        
        if (!in_array($value, $validAlgorithms)) {
            $this->addError($section, "Invalid hash algorithm. Must be one of: " . implode(', ', $validAlgorithms));
        }
        
        if ($value === 'sha256' && $environment === 'production') {
            $this->addWarning($section, "Consider using bcrypt or argon2 for better security in production");
        }
    }
    
    private function validateLogLevel(string $section, string $field, $value, string $environment): void
    {
        $validLevels = ['debug', 'info', 'warning', 'error', 'critical'];
        
        if (!in_array($value, $validLevels)) {
            $this->addError($section, "Invalid log level. Must be one of: " . implode(', ', $validLevels));
        }
    }
    
    private function validateLogPath(string $section, string $field, $value, string $environment): void
    {
        if (!is_string($value) || empty($value)) {
            $this->addError($section, "Log path must be a non-empty string");
            return;
        }
        
        $dir = dirname($value);
        if (!is_dir($dir)) {
            $this->addWarning($section, "Log directory does not exist: {$dir}");
        } elseif (!is_writable($dir)) {
            $this->addError($section, "Log directory is not writable: {$dir}");
        }
    }
    
    private function validatePositiveInteger(string $section, string $field, $value, string $environment): void
    {
        if (!is_int($value) || $value <= 0) {
            $this->addError($section, "Field '{$field}' must be a positive integer");
        }
    }
    
    private function validateApiToken(string $section, string $field, $value, string $environment): void
    {
        if (!is_string($value) || empty($value)) {
            $this->addError($section, "API token must be a non-empty string");
            return;
        }
        
        if (strlen($value) < 20) {
            $this->addWarning($section, "API token seems too short, verify it's correct");
        }
    }
    
    private function validateApiKey(string $section, string $field, $value, string $environment): void
    {
        if (!is_string($value) || empty($value)) {
            $this->addError($section, "API key must be a non-empty string");
            return;
        }
        
        if (strlen($value) < 16) {
            $this->addWarning($section, "API key seems too short, verify it's correct");
        }
    }
    
    private function validateUrl(string $section, string $field, $value, string $environment): void
    {
        if (!is_string($value) || !filter_var($value, FILTER_VALIDATE_URL)) {
            $this->addError($section, "Field '{$field}' must be a valid URL");
            return;
        }
        
        if ($environment === 'production' && !str_starts_with($value, 'https://')) {
            $this->addWarning($section, "HTTPS URLs are recommended in production");
        }
    }
    
    /**
     * Add validation error
     */
    private function addError(string $section, string $message): void
    {
        $this->errors[] = [
            'section' => $section,
            'message' => $message,
            'severity' => 'error'
        ];
    }
    
    /**
     * Add validation warning
     */
    private function addWarning(string $section, string $message): void
    {
        $this->warnings[] = [
            'section' => $section,
            'message' => $message,
            'severity' => 'warning'
        ];
    }
    
    /**
     * Get validation errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }
    
    /**
     * Get validation warnings
     */
    public function getWarnings(): array
    {
        return $this->warnings;
    }
    
    /**
     * Check if configuration is valid
     */
    public function isValid(): bool
    {
        return empty($this->errors);
    }
    
    /**
     * Get validation summary
     */
    public function getSummary(): array
    {
        return [
            'valid' => $this->isValid(),
            'error_count' => count($this->errors),
            'warning_count' => count($this->warnings),
            'total_issues' => count($this->errors) + count($this->warnings)
        ];
    }
}