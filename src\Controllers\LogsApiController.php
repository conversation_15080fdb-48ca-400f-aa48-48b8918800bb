<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Utils\AuthMiddleware;
use Skpassegna\GuardgeoApi\Utils\RoleManager;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;

/**
 * Logs API Controller
 * 
 * Provides JSON API endpoints for system logs and monitoring
 * with advanced filtering, search, and export capabilities.
 */
class LogsApiController
{
    private AuthMiddleware $authMiddleware;
    private RoleManager $roleManager;
    private DatabaseConnection $db;
    private LoggingService $logger;

    public function __construct(
        AuthMiddleware $authMiddleware,
        RoleManager $roleManager,
        DatabaseConnection $db,
        LoggingService $logger
    ) {
        $this->authMiddleware = $authMiddleware;
        $this->roleManager = $roleManager;
        $this->db = $db;
        $this->logger = $logger;
    }

    /**
     * Get system logs with pagination and filtering
     *
     * @return void
     */
    public function getSystemLogs(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'log_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            // Get query parameters
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = min(max(1, (int)($_GET['limit'] ?? 50)), 200);
            $search = $_GET['search'] ?? '';
            $type = $_GET['type'] ?? 'all'; // all, api, admin, error
            $level = $_GET['level'] ?? 'all'; // all, info, warning, error
            $dateFrom = $_GET['date_from'] ?? '';
            $dateTo = $_GET['date_to'] ?? '';
            $sortBy = $_GET['sort'] ?? 'created_at';
            $sortOrder = $_GET['order'] ?? 'desc';

            // Build filters
            $filters = [];
            
            if (!empty($search)) {
                $filters['search'] = $search;
            }
            
            if ($type !== 'all') {
                $filters['type'] = $type;
            }
            
            if ($level !== 'all') {
                $filters['level'] = $level;
            }
            
            if (!empty($dateFrom)) {
                $filters['date_from'] = $dateFrom;
            }
            
            if (!empty($dateTo)) {
                $filters['date_to'] = $dateTo;
            }

            // Get logs
            $result = $this->getLogsPaginated(
                $page,
                $limit,
                $filters,
                $sortBy,
                $sortOrder
            );

            $this->sendJsonResponse([
                'success' => true,
                'data' => $result['logs'],
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $result['total'],
                    'total_pages' => ceil($result['total'] / $limit)
                ],
                'filters' => [
                    'search' => $search,
                    'type' => $type,
                    'level' => $level,
                    'date_from' => $dateFrom,
                    'date_to' => $dateTo,
                    'sort' => $sortBy,
                    'order' => $sortOrder
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('System logs API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load system logs'
            ], 500);
        }
    }

    /**
     * Get API request logs with pagination and filtering
     *
     * @return void
     */
    public function getApiLogs(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'log_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            // Get query parameters
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = min(max(1, (int)($_GET['limit'] ?? 50)), 200);
            $search = $_GET['search'] ?? '';
            $status = $_GET['status'] ?? 'all'; // all, success, error
            $dateFrom = $_GET['date_from'] ?? '';
            $dateTo = $_GET['date_to'] ?? '';
            $sortBy = $_GET['sort'] ?? 'created_at';
            $sortOrder = $_GET['order'] ?? 'desc';

            // Build filters
            $filters = [];
            
            if (!empty($search)) {
                $filters['search'] = $search;
            }
            
            if ($status !== 'all') {
                $filters['status'] = $status;
            }
            
            if (!empty($dateFrom)) {
                $filters['date_from'] = $dateFrom;
            }
            
            if (!empty($dateTo)) {
                $filters['date_to'] = $dateTo;
            }

            // Get API logs
            $result = $this->getApiLogsPaginated(
                $page,
                $limit,
                $filters,
                $sortBy,
                $sortOrder
            );

            $this->sendJsonResponse([
                'success' => true,
                'data' => $result['logs'],
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $result['total'],
                    'total_pages' => ceil($result['total'] / $limit)
                ],
                'filters' => [
                    'search' => $search,
                    'status' => $status,
                    'date_from' => $dateFrom,
                    'date_to' => $dateTo,
                    'sort' => $sortBy,
                    'order' => $sortOrder
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('API logs API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load API logs'
            ], 500);
        }
    }

    /**
     * Get log statistics
     *
     * @return void
     */
    public function getLogStatistics(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'log_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            $stats = $this->getLogsStatistics();
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Log statistics API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load log statistics'
            ], 500);
        }
    }

    /**
     * Export logs (if user has export permission)
     *
     * @return void
     */
    public function exportLogs(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->hasPermission($user['role'], RoleManager::CATEGORY_LOGS, RoleManager::ACTION_EXPORT)) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Export permission required'
            ], 403);
            return;
        }

        try {
            $format = $_GET['format'] ?? 'csv'; // csv, json
            $type = $_GET['type'] ?? 'system'; // system, api
            $dateFrom = $_GET['date_from'] ?? '';
            $dateTo = $_GET['date_to'] ?? '';
            $limit = min(max(1, (int)($_GET['limit'] ?? 1000)), 10000);

            // Build filters for export
            $filters = [];
            if (!empty($dateFrom)) {
                $filters['date_from'] = $dateFrom;
            }
            if (!empty($dateTo)) {
                $filters['date_to'] = $dateTo;
            }

            if ($type === 'api') {
                $logs = $this->getApiLogsForExport($filters, $limit);
                $filename = 'api_logs_' . date('Y-m-d_H-i-s');
            } else {
                $logs = $this->getSystemLogsForExport($filters, $limit);
                $filename = 'system_logs_' . date('Y-m-d_H-i-s');
            }

            // Log the export action
            $this->logger->logAdminAction('Logs exported', [
                'type' => $type,
                'format' => $format,
                'count' => count($logs),
                'user' => $user['email']
            ]);

            if ($format === 'json') {
                $this->exportAsJson($logs, $filename);
            } else {
                $this->exportAsCsv($logs, $filename, $type);
            }

        } catch (\Exception $e) {
            $this->logger->logError('Log export API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to export logs'
            ], 500);
        }
    }

    /**
     * Get system logs with pagination
     */
    private function getLogsPaginated(int $page, int $limit, array $filters, string $sortBy, string $sortOrder): array
    {
        $offset = ($page - 1) * $limit;
        
        $whereConditions = [];
        $params = [];
        
        // Apply search filter
        if (!empty($filters['search'])) {
            $whereConditions[] = '(message ILIKE :search OR context::text ILIKE :search OR ip::text ILIKE :search)';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        // Apply type filter
        if (!empty($filters['type'])) {
            $whereConditions[] = 'type = :type';
            $params['type'] = $filters['type'];
        }
        
        // Apply level filter
        if (!empty($filters['level'])) {
            $whereConditions[] = 'level = :level';
            $params['level'] = $filters['level'];
        }
        
        // Apply date filters
        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= :date_from';
            $params['date_from'] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= :date_to';
            $params['date_to'] = $filters['date_to'] . ' 23:59:59';
        }
        
        // Build WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        // Apply sorting
        $validSortColumns = ['id', 'type', 'level', 'message', 'created_at'];
        if (!in_array($sortBy, $validSortColumns)) {
            $sortBy = 'created_at';
        }
        
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql = "
            SELECT 
                id, type, level, message, context, ip, user_id, created_at,
                (SELECT email FROM admin_users WHERE id = system_logs.user_id) as user_email
            FROM system_logs 
            {$whereClause}
            ORDER BY {$sortBy} {$sortOrder}
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $pdo = $this->db->getConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $rows = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        $logs = [];
        foreach ($rows as $row) {
            $logs[] = [
                'id' => $row['id'],
                'type' => $row['type'],
                'level' => $row['level'],
                'message' => $row['message'],
                'context' => $row['context'] ? json_decode($row['context'], true) : null,
                'ip' => $row['ip'],
                'user_email' => $row['user_email'],
                'created_at' => $row['created_at'],
                'formatted_time' => $this->formatRelativeTime($row['created_at'])
            ];
        }
        
        return [
            'logs' => $logs,
            'total' => $this->getSystemLogsCount($filters)
        ];
    }

    /**
     * Get API logs with pagination
     */
    private function getApiLogsPaginated(int $page, int $limit, array $filters, string $sortBy, string $sortOrder): array
    {
        $offset = ($page - 1) * $limit;
        
        $whereConditions = [];
        $params = [];
        
        // Apply search filter
        if (!empty($filters['search'])) {
            $whereConditions[] = '(ip::text ILIKE :search OR url ILIKE :search OR visitor_hash ILIKE :search)';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        // Apply status filter
        if (!empty($filters['status'])) {
            if ($filters['status'] === 'success') {
                $whereConditions[] = 'response_status >= 200 AND response_status < 300';
            } elseif ($filters['status'] === 'error') {
                $whereConditions[] = 'response_status >= 400';
            }
        }
        
        // Apply date filters
        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= :date_from';
            $params['date_from'] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= :date_to';
            $params['date_to'] = $filters['date_to'] . ' 23:59:59';
        }
        
        // Build WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        // Apply sorting
        $validSortColumns = ['id', 'ip', 'response_status', 'response_time_ms', 'created_at'];
        if (!in_array($sortBy, $validSortColumns)) {
            $sortBy = 'created_at';
        }
        
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql = "
            SELECT *
            FROM api_requests 
            {$whereClause}
            ORDER BY {$sortBy} {$sortOrder}
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $pdo = $this->db->getConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $rows = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        $logs = [];
        foreach ($rows as $row) {
            $logs[] = [
                'id' => $row['id'],
                'ip' => $row['ip'],
                'visitor_hash' => $row['visitor_hash'],
                'plugin_id' => $row['plugin_id'],
                'install_id' => $row['install_id'],
                'url' => $row['url'],
                'response_status' => (int)$row['response_status'],
                'response_time_ms' => (int)$row['response_time_ms'],
                'freemius_valid' => (bool)$row['freemius_valid'],
                'created_at' => $row['created_at'],
                'formatted_time' => $this->formatRelativeTime($row['created_at']),
                'status_class' => $this->getStatusClass((int)$row['response_status'])
            ];
        }
        
        return [
            'logs' => $logs,
            'total' => $this->getApiLogsCount($filters)
        ];
    }

    /**
     * Get system logs count with filters
     */
    private function getSystemLogsCount(array $filters): int
    {
        $whereConditions = [];
        $params = [];
        
        // Apply same filters as main query
        if (!empty($filters['search'])) {
            $whereConditions[] = '(message ILIKE :search OR context::text ILIKE :search OR ip::text ILIKE :search)';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['type'])) {
            $whereConditions[] = 'type = :type';
            $params['type'] = $filters['type'];
        }
        
        if (!empty($filters['level'])) {
            $whereConditions[] = 'level = :level';
            $params['level'] = $filters['level'];
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= :date_from';
            $params['date_from'] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= :date_to';
            $params['date_to'] = $filters['date_to'] . ' 23:59:59';
        }
        
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        $sql = "SELECT COUNT(*) as count FROM system_logs {$whereClause}";
        
        $pdo = $this->db->getConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $row = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        return (int)($row['count'] ?? 0);
    }

    /**
     * Get API logs count with filters
     */
    private function getApiLogsCount(array $filters): int
    {
        $whereConditions = [];
        $params = [];
        
        // Apply same filters as main query
        if (!empty($filters['search'])) {
            $whereConditions[] = '(ip::text ILIKE :search OR url ILIKE :search OR visitor_hash ILIKE :search)';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['status'])) {
            if ($filters['status'] === 'success') {
                $whereConditions[] = 'response_status >= 200 AND response_status < 300';
            } elseif ($filters['status'] === 'error') {
                $whereConditions[] = 'response_status >= 400';
            }
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= :date_from';
            $params['date_from'] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= :date_to';
            $params['date_to'] = $filters['date_to'] . ' 23:59:59';
        }
        
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        $sql = "SELECT COUNT(*) as count FROM api_requests {$whereClause}";
        
        $pdo = $this->db->getConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $row = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        return (int)($row['count'] ?? 0);
    }

    /**
     * Get logs statistics
     */
    private function getLogsStatistics(): array
    {
        $pdo = $this->db->getConnection();
        
        // System logs stats
        $systemStmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN level = 'error' THEN 1 END) as errors,
                COUNT(CASE WHEN level = 'warning' THEN 1 END) as warnings,
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as last_24h
            FROM system_logs
        ");
        $systemStmt->execute();
        $systemStats = $systemStmt->fetch(\PDO::FETCH_ASSOC);
        
        // API logs stats
        $apiStmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN response_status >= 200 AND response_status < 300 THEN 1 END) as success,
                COUNT(CASE WHEN response_status >= 400 THEN 1 END) as errors,
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as last_24h,
                AVG(response_time_ms) as avg_response_time
            FROM api_requests
        ");
        $apiStmt->execute();
        $apiStats = $apiStmt->fetch(\PDO::FETCH_ASSOC);
        
        return [
            'system_logs' => [
                'total' => (int)$systemStats['total'],
                'errors' => (int)$systemStats['errors'],
                'warnings' => (int)$systemStats['warnings'],
                'last_24h' => (int)$systemStats['last_24h']
            ],
            'api_logs' => [
                'total' => (int)$apiStats['total'],
                'success' => (int)$apiStats['success'],
                'errors' => (int)$apiStats['errors'],
                'last_24h' => (int)$apiStats['last_24h'],
                'avg_response_time' => round((float)$apiStats['avg_response_time'], 2)
            ]
        ];
    }

    /**
     * Get system logs for export
     */
    private function getSystemLogsForExport(array $filters, int $limit): array
    {
        $whereConditions = [];
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= :date_from';
            $params['date_from'] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= :date_to';
            $params['date_to'] = $filters['date_to'] . ' 23:59:59';
        }
        
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        $sql = "
            SELECT 
                id, type, level, message, context, ip, created_at,
                (SELECT email FROM admin_users WHERE id = system_logs.user_id) as user_email
            FROM system_logs 
            {$whereClause}
            ORDER BY created_at DESC
            LIMIT :limit
        ";
        
        $params['limit'] = $limit;
        
        $pdo = $this->db->getConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * Get API logs for export
     */
    private function getApiLogsForExport(array $filters, int $limit): array
    {
        $whereConditions = [];
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= :date_from';
            $params['date_from'] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= :date_to';
            $params['date_to'] = $filters['date_to'] . ' 23:59:59';
        }
        
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        $sql = "
            SELECT *
            FROM api_requests 
            {$whereClause}
            ORDER BY created_at DESC
            LIMIT :limit
        ";
        
        $params['limit'] = $limit;
        
        $pdo = $this->db->getConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * Export logs as CSV
     */
    private function exportAsCsv(array $logs, string $filename, string $type): void
    {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        if ($type === 'api') {
            // API logs CSV headers
            fputcsv($output, ['ID', 'IP', 'Visitor Hash', 'Plugin ID', 'Install ID', 'URL', 'Response Status', 'Response Time (ms)', 'Freemius Valid', 'Created At']);
            
            foreach ($logs as $log) {
                fputcsv($output, [
                    $log['id'],
                    $log['ip'],
                    $log['visitor_hash'],
                    $log['plugin_id'],
                    $log['install_id'],
                    $log['url'],
                    $log['response_status'],
                    $log['response_time_ms'],
                    $log['freemius_valid'] ? 'Yes' : 'No',
                    $log['created_at']
                ]);
            }
        } else {
            // System logs CSV headers
            fputcsv($output, ['ID', 'Type', 'Level', 'Message', 'IP', 'User Email', 'Created At']);
            
            foreach ($logs as $log) {
                fputcsv($output, [
                    $log['id'],
                    $log['type'],
                    $log['level'],
                    $log['message'],
                    $log['ip'],
                    $log['user_email'],
                    $log['created_at']
                ]);
            }
        }
        
        fclose($output);
        exit;
    }

    /**
     * Export logs as JSON
     */
    private function exportAsJson(array $logs, string $filename): void
    {
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '.json"');
        
        echo json_encode([
            'export_date' => date('Y-m-d H:i:s'),
            'total_records' => count($logs),
            'logs' => $logs
        ], JSON_PRETTY_PRINT);
        
        exit;
    }

    /**
     * Get status class for response status
     */
    private function getStatusClass(int $status): string
    {
        if ($status >= 200 && $status < 300) {
            return 'text-green-600';
        } elseif ($status >= 400 && $status < 500) {
            return 'text-yellow-600';
        } elseif ($status >= 500) {
            return 'text-red-600';
        }
        
        return 'text-gray-600';
    }

    /**
     * Format relative time
     */
    private function formatRelativeTime(string $timestamp): string
    {
        $time = new \DateTime($timestamp);
        $now = new \DateTime();
        $diff = $now->diff($time);

        if ($diff->days > 0) {
            return $diff->days . ' day' . ($diff->days > 1 ? 's' : '') . ' ago';
        } elseif ($diff->h > 0) {
            return $diff->h . ' hour' . ($diff->h > 1 ? 's' : '') . ' ago';
        } elseif ($diff->i > 0) {
            return $diff->i . ' minute' . ($diff->i > 1 ? 's' : '') . ' ago';
        } else {
            return 'Just now';
        }
    }

    /**
     * Require authentication and return status
     *
     * @return bool
     */
    private function requireAuthentication(): bool
    {
        if (!$this->authMiddleware->isAuthenticated()) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Authentication required'
            ], 401);
            return false;
        }

        return true;
    }

    /**
     * Send JSON response
     *
     * @param array $data
     * @param int $statusCode
     * @return void
     */
    private function sendJsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Create instance with dependencies
     *
     * @return self
     */
    public static function create(): self
    {
        $logger = new LoggingService();
        $db = new DatabaseConnection();
        $sessionManager = new \Skpassegna\GuardgeoApi\Utils\SessionManager();
        $passwordValidator = new \Skpassegna\GuardgeoApi\Utils\PasswordValidator();
        $emailValidator = new \Skpassegna\GuardgeoApi\Utils\EmailDomainValidator();
        
        $authService = new \Skpassegna\GuardgeoApi\Services\AuthService(
            $db,
            $sessionManager,
            $passwordValidator,
            $emailValidator,
            $logger
        );
        
        $authMiddleware = new AuthMiddleware($authService);
        $roleManager = new RoleManager();

        return new self($authMiddleware, $roleManager, $db, $logger);
    }
}