<?php

namespace Skpassegna\GuardgeoApi\Models;

use DateTime;

/**
 * Freemius Installation Model
 * 
 * Represents a Freemius installation with complete field mapping
 * based on the Freemius OpenAPI specification.
 */
class InstallationModel extends BaseModel
{
    // Core identification fields
    public ?int $id = null;
    public ?string $secret_key = null;
    public ?string $public_key = null;
    public ?DateTime $created = null;
    public ?DateTime $updated = null;
    
    // Installation relationships
    public ?int $site_id = null;
    public ?int $plugin_id = null;
    public ?int $user_id = null;
    
    // Site information
    public ?string $url = null;
    public ?string $title = null;
    public ?string $version = null;
    
    // License and subscription information
    public ?int $plan_id = null;
    public ?int $license_id = null;
    public ?int $trial_plan_id = null;
    public ?DateTime $trial_ends = null;
    public ?int $subscription_id = null;
    public float $gross = 0.00;
    
    // Location and language
    public ?string $country_code = null;
    public ?string $language = null;
    
    // Technical information
    public ?string $platform_version = null;
    public ?string $sdk_version = null;
    public ?string $programming_language_version = null;
    
    // Status flags
    public bool $is_active = true;
    public bool $is_disconnected = false;
    public bool $is_premium = false;
    public bool $is_uninstalled = false;
    public bool $is_locked = false;
    public bool $is_beta = false;
    
    // Migration and source tracking
    public int $source = 0;
    
    // Timestamps
    public ?DateTime $upgraded = null;
    public ?DateTime $last_seen_at = null;
    public ?string $last_served_update_version = null;
    
    // Cache management
    public ?DateTime $cached_at = null;
    public ?array $raw_data = null; // Complete Freemius response

    /**
     * Constructor
     */
    public function __construct(array $data = [])
    {
        // Core identification
        $this->id = $this->toNullableInt($data['id'] ?? null);
        $this->secret_key = $this->toNullableString($data['secret_key'] ?? null);
        $this->public_key = $this->toNullableString($data['public_key'] ?? null);
        $this->created = isset($data['created']) ? $this->dbToDateTime($data['created']) : null;
        $this->updated = isset($data['updated']) ? $this->dbToDateTime($data['updated']) : null;
        
        // Installation relationships
        $this->site_id = $this->toNullableInt($data['site_id'] ?? null);
        $this->plugin_id = $this->toNullableInt($data['plugin_id'] ?? null);
        $this->user_id = $this->toNullableInt($data['user_id'] ?? null);
        
        // Site information
        $this->url = ModelValidator::sanitizeUrl($data['url'] ?? null);
        $this->title = $this->toNullableString($data['title'] ?? null);
        $this->version = $this->toNullableString($data['version'] ?? null);
        
        // License and subscription information
        $this->plan_id = $this->toNullableInt($data['plan_id'] ?? null);
        $this->license_id = $this->toNullableInt($data['license_id'] ?? null);
        $this->trial_plan_id = $this->toNullableInt($data['trial_plan_id'] ?? null);
        $this->trial_ends = isset($data['trial_ends']) ? $this->dbToDateTime($data['trial_ends']) : null;
        $this->subscription_id = $this->toNullableInt($data['subscription_id'] ?? null);
        $this->gross = $this->toFloat($data['gross'] ?? 0.00);
        
        // Location and language
        $this->country_code = $this->toNullableString($data['country_code'] ?? null);
        $this->language = $this->toNullableString($data['language'] ?? null);
        
        // Technical information
        $this->platform_version = $this->toNullableString($data['platform_version'] ?? null);
        $this->sdk_version = $this->toNullableString($data['sdk_version'] ?? null);
        $this->programming_language_version = $this->toNullableString($data['programming_language_version'] ?? null);
        
        // Status flags
        $this->is_active = $this->toBool($data['is_active'] ?? true);
        $this->is_disconnected = $this->toBool($data['is_disconnected'] ?? false);
        $this->is_premium = $this->toBool($data['is_premium'] ?? false);
        $this->is_uninstalled = $this->toBool($data['is_uninstalled'] ?? false);
        $this->is_locked = $this->toBool($data['is_locked'] ?? false);
        $this->is_beta = $this->toBool($data['is_beta'] ?? false);
        
        // Migration and source tracking
        $this->source = $this->toInt($data['source'] ?? 0);
        
        // Timestamps
        $this->upgraded = isset($data['upgraded']) ? $this->dbToDateTime($data['upgraded']) : null;
        $this->last_seen_at = isset($data['last_seen_at']) ? $this->dbToDateTime($data['last_seen_at']) : null;
        $this->last_served_update_version = $this->toNullableString($data['last_served_update_version'] ?? null);
        
        // Cache management
        $this->cached_at = isset($data['cached_at']) ? $this->dbToDateTime($data['cached_at']) : null;
        $this->raw_data = $data['raw_data'] ?? null;
        
        // If raw_data is a JSON string, decode it
        if (is_string($this->raw_data)) {
            $this->raw_data = json_decode($this->raw_data, true);
        }
    }
    
    /**
     * Create InstallationModel from Freemius API response
     */
    public static function fromFreemiusResponse(array $data): self
    {
        $processedData = $data;
        $processedData['cached_at'] = (new DateTime())->format('Y-m-d H:i:s');
        $processedData['raw_data'] = $data;
        
        return new self($processedData);
    }
    
    /**
     * Create InstallationModel from database row
     */
    public static function fromDatabaseRow(array $row): self
    {
        return new self($row);
    }
    
    /**
     * Convert to database array for insertion/update
     */
    public function toDatabaseArray(): array
    {
        return [
            'id' => $this->id,
            'secret_key' => $this->secret_key,
            'public_key' => $this->public_key,
            'created' => $this->dateTimeToDb($this->created),
            'updated' => $this->dateTimeToDb($this->updated),
            'site_id' => $this->site_id,
            'plugin_id' => $this->plugin_id,
            'user_id' => $this->user_id,
            'url' => $this->url,
            'title' => $this->title,
            'version' => $this->version,
            'plan_id' => $this->plan_id,
            'license_id' => $this->license_id,
            'trial_plan_id' => $this->trial_plan_id,
            'trial_ends' => $this->dateTimeToDb($this->trial_ends),
            'subscription_id' => $this->subscription_id,
            'gross' => $this->gross,
            'country_code' => $this->country_code,
            'language' => $this->language,
            'platform_version' => $this->platform_version,
            'sdk_version' => $this->sdk_version,
            'programming_language_version' => $this->programming_language_version,
            'is_active' => $this->is_active,
            'is_disconnected' => $this->is_disconnected,
            'is_premium' => $this->is_premium,
            'is_uninstalled' => $this->is_uninstalled,
            'is_locked' => $this->is_locked,
            'is_beta' => $this->is_beta,
            'source' => $this->source,
            'upgraded' => $this->dateTimeToDb($this->upgraded),
            'last_seen_at' => $this->dateTimeToDb($this->last_seen_at),
            'last_served_update_version' => $this->last_served_update_version,
            'cached_at' => $this->dateTimeToDb($this->cached_at),
            'raw_data' => $this->raw_data ? json_encode($this->raw_data) : null
        ];
    }
    
    /**
     * Comprehensive validation
     */
    public function validate(): array
    {
        $errors = [];
        
        // Required fields validation
        if ($error = $this->validateRequired($this->id, 'id')) {
            $errors[] = $error;
        } elseif ($error = $this->validatePositiveInt($this->id, 'id')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->secret_key, 'secret_key')) {
            $errors[] = $error;
        } elseif ($error = $this->validateStringLength($this->secret_key, 255, 'secret_key')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->public_key, 'public_key')) {
            $errors[] = $error;
        } elseif ($error = $this->validateStringLength($this->public_key, 255, 'public_key')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->version, 'version')) {
            $errors[] = $error;
        } elseif ($this->version && !ModelValidator::validateVersion($this->version)) {
            $errors[] = 'version must be a valid version string';
        }
        
        if ($error = $this->validateRequired($this->site_id, 'site_id')) {
            $errors[] = $error;
        } elseif ($error = $this->validatePositiveInt($this->site_id, 'site_id')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->plugin_id, 'plugin_id')) {
            $errors[] = $error;
        } elseif ($error = $this->validatePositiveInt($this->plugin_id, 'plugin_id')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateRequired($this->user_id, 'user_id')) {
            $errors[] = $error;
        } elseif ($error = $this->validatePositiveInt($this->user_id, 'user_id')) {
            $errors[] = $error;
        }
        
        // Numeric validation
        if ($error = $this->validateNonNegativeFloat($this->gross, 'gross')) {
            $errors[] = $error;
        }
        
        if ($error = $this->validateNonNegativeInt($this->source, 'source')) {
            $errors[] = $error;
        }
        
        // Country code validation
        if ($this->country_code && !ModelValidator::validateCountryCode($this->country_code)) {
            $errors[] = 'country_code must be a valid 2-character country code';
        }
        
        // Language code validation
        if ($this->language && !ModelValidator::validateLanguageCode($this->language)) {
            $errors[] = 'language must be a valid language code';
        }
        
        // URL validation
        if ($this->url && ($error = $this->validateUrl($this->url, 'url'))) {
            $errors[] = $error;
        }
        
        // String length validations
        if ($this->url && ($error = $this->validateStringLength($this->url, 500, 'url'))) {
            $errors[] = $error;
        }
        
        if ($this->title && ($error = $this->validateStringLength($this->title, 255, 'title'))) {
            $errors[] = $error;
        }
        
        if ($this->version && ($error = $this->validateStringLength($this->version, 50, 'version'))) {
            $errors[] = $error;
        }
        
        if ($this->platform_version && ($error = $this->validateStringLength($this->platform_version, 50, 'platform_version'))) {
            $errors[] = $error;
        }
        
        if ($this->sdk_version && ($error = $this->validateStringLength($this->sdk_version, 50, 'sdk_version'))) {
            $errors[] = $error;
        }
        
        if ($this->programming_language_version && ($error = $this->validateStringLength($this->programming_language_version, 50, 'programming_language_version'))) {
            $errors[] = $error;
        }
        
        if ($this->last_served_update_version && ($error = $this->validateStringLength($this->last_served_update_version, 50, 'last_served_update_version'))) {
            $errors[] = $error;
        }
        
        // Optional ID validations
        if ($this->plan_id !== null && ($error = $this->validatePositiveInt($this->plan_id, 'plan_id'))) {
            $errors[] = $error;
        }
        
        if ($this->license_id !== null && ($error = $this->validatePositiveInt($this->license_id, 'license_id'))) {
            $errors[] = $error;
        }
        
        if ($this->trial_plan_id !== null && ($error = $this->validatePositiveInt($this->trial_plan_id, 'trial_plan_id'))) {
            $errors[] = $error;
        }
        
        if ($this->subscription_id !== null && ($error = $this->validatePositiveInt($this->subscription_id, 'subscription_id'))) {
            $errors[] = $error;
        }
        
        return $errors;
    }
    
    /**
     * Check if installation data is expired and needs refresh
     */
    public function isExpired(int $maxAgeHours = 6): bool
    {
        $now = new DateTime();
        $ageInHours = ($now->getTimestamp() - $this->cached_at->getTimestamp()) / 3600;
        
        return $ageInHours > $maxAgeHours;
    }
    
    /**
     * Check if installation has an active trial
     */
    public function hasActiveTrial(): bool
    {
        if ($this->trial_plan_id === null || $this->trial_ends === null) {
            return false;
        }
        
        return $this->trial_ends > new DateTime();
    }
    
    /**
     * Check if installation is premium (has license or subscription)
     */
    public function isPremiumInstallation(): bool
    {
        return $this->is_premium || $this->license_id !== null || $this->subscription_id !== null;
    }
    
    /**
     * Get installation status summary
     */
    public function getStatusSummary(): array
    {
        return [
            'is_active' => $this->is_active,
            'is_premium' => $this->isPremiumInstallation(),
            'has_trial' => $this->hasActiveTrial(),
            'is_disconnected' => $this->is_disconnected,
            'is_uninstalled' => $this->is_uninstalled,
            'is_locked' => $this->is_locked,
            'is_beta' => $this->is_beta
        ];
    }
    
    /**
     * Get days since last seen
     */
    public function getDaysSinceLastSeen(): ?int
    {
        if ($this->last_seen_at === null) {
            return null;
        }
        
        $now = new DateTime();
        $diff = $now->diff($this->last_seen_at);
        
        return $diff->days;
    }
    
    /**
     * JSON serialization
     */
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'secret_key' => $this->secret_key,
            'public_key' => $this->public_key,
            'created' => $this->dateTimeToJson($this->created),
            'updated' => $this->dateTimeToJson($this->updated),
            'site_id' => $this->site_id,
            'plugin_id' => $this->plugin_id,
            'user_id' => $this->user_id,
            'url' => $this->url,
            'title' => $this->title,
            'version' => $this->version,
            'plan_id' => $this->plan_id,
            'license_id' => $this->license_id,
            'trial_plan_id' => $this->trial_plan_id,
            'trial_ends' => $this->dateTimeToJson($this->trial_ends),
            'subscription_id' => $this->subscription_id,
            'gross' => $this->gross,
            'country_code' => $this->country_code,
            'language' => $this->language,
            'platform_version' => $this->platform_version,
            'sdk_version' => $this->sdk_version,
            'programming_language_version' => $this->programming_language_version,
            'is_active' => $this->is_active,
            'is_disconnected' => $this->is_disconnected,
            'is_premium' => $this->is_premium,
            'is_uninstalled' => $this->is_uninstalled,
            'is_locked' => $this->is_locked,
            'is_beta' => $this->is_beta,
            'source' => $this->source,
            'upgraded' => $this->dateTimeToJson($this->upgraded),
            'last_seen_at' => $this->dateTimeToJson($this->last_seen_at),
            'last_served_update_version' => $this->last_served_update_version,
            'cached_at' => $this->dateTimeToJson($this->cached_at),
            'is_expired' => $this->isExpired(),
            'status_summary' => $this->getStatusSummary(),
            'days_since_last_seen' => $this->getDaysSinceLastSeen(),
            'has_active_trial' => $this->hasActiveTrial(),
            'is_premium_installation' => $this->isPremiumInstallation()
        ];
    }

    /**
     * Transform for external API integration (Freemius format)
     */
    public function toFreemiusApiFormat(): array
    {
        return [
            'id' => $this->id,
            'secret_key' => $this->secret_key,
            'public_key' => $this->public_key,
            'site_id' => $this->site_id,
            'plugin_id' => $this->plugin_id,
            'user_id' => $this->user_id,
            'url' => $this->url,
            'version' => $this->version,
            'is_active' => $this->is_active,
            'is_premium' => $this->is_premium,
            'is_uninstalled' => $this->is_uninstalled
        ];
    }

    /**
     * Get safe array for API responses (excludes sensitive data)
     */
    public function toSafeArray(): array
    {
        return [
            'id' => $this->id,
            'plugin_id' => $this->plugin_id,
            'url' => $this->url,
            'title' => $this->title,
            'version' => $this->version,
            'is_active' => $this->is_active,
            'is_premium' => $this->is_premium,
            'is_uninstalled' => $this->is_uninstalled,
            'country_code' => $this->country_code,
            'language' => $this->language,
            'cached_at' => $this->dateTimeToJson($this->cached_at),
            'is_expired' => $this->isExpired(),
            'status_summary' => $this->getStatusSummary()
        ];
    }

    /**
     * Handle foreign key relationships
     */
    public function getRelatedProduct(): ?ProductModel
    {
        // This would be implemented by the repository layer
        return null;
    }

    /**
     * Validate foreign key constraints
     */
    public function validateForeignKeys(): array
    {
        $errors = [];
        
        // plugin_id must reference a valid product
        if ($this->plugin_id === null) {
            $errors[] = 'plugin_id is required and must reference a valid product';
        }
        
        return $errors;
    }
}