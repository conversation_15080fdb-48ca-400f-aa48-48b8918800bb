<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Models\IpDataModel;
use Skpassegna\GuardgeoApi\Utils\Logger;

/**
 * Batch Processor
 * 
 * Handles batch processing capabilities for multiple IP lookups
 * with efficient API usage and error handling.
 */
class BatchProcessor
{
    private IpRegistryApiClient $apiClient;
    private IpRegistryService $ipService;
    private Logger $logger;
    
    // Batch configuration
    private int $maxBatchSize;
    private int $concurrentBatches;
    private float $delayBetweenBatches;
    
    /**
     * Constructor
     */
    public function __construct(
        IpRegistryApiClient $apiClient,
        IpRegistryService $ipService,
        int $maxBatchSize = 100,
        int $concurrentBatches = 1,
        float $delayBetweenBatches = 0.5
    ) {
        $this->apiClient = $apiClient;
        $this->ipService = $ipService;
        $this->logger = new Logger();
        
        $this->maxBatchSize = $maxBatchSize;
        $this->concurrentBatches = $concurrentBatches;
        $this->delayBetweenBatches = $delayBetweenBatches;
    }
    
    /**
     * Process multiple IPs in batches
     */
    public function processBatch(array $ips): array
    {
        $this->logger->info("Starting batch IP processing", [
            'total_ips' => count($ips),
            'max_batch_size' => $this->maxBatchSize
        ]);
        
        $results = [
            'total_requested' => count($ips),
            'successful' => 0,
            'failed' => 0,
            'cached' => 0,
            'api_calls' => 0,
            'data' => [],
            'errors' => []
        ];
        
        // Validate and filter IPs
        $validIps = $this->validateIps($ips);
        $results['invalid_ips'] = array_diff($ips, $validIps);
        
        if (empty($validIps)) {
            $this->logger->warning("No valid IPs to process");
            return $results;
        }
        
        // Split into batches
        $batches = array_chunk($validIps, $this->maxBatchSize);
        $results['batch_count'] = count($batches);
        
        $this->logger->info("Processing IPs in batches", [
            'valid_ips' => count($validIps),
            'batch_count' => count($batches)
        ]);
        
        // Process each batch
        foreach ($batches as $batchIndex => $batch) {
            $this->logger->info("Processing batch", [
                'batch_index' => $batchIndex + 1,
                'batch_size' => count($batch)
            ]);
            
            $batchResults = $this->processSingleBatch($batch);
            
            // Merge results
            $results['successful'] += $batchResults['successful'];
            $results['failed'] += $batchResults['failed'];
            $results['cached'] += $batchResults['cached'];
            $results['api_calls'] += $batchResults['api_calls'];
            $results['data'] = array_merge($results['data'], $batchResults['data']);
            $results['errors'] = array_merge($results['errors'], $batchResults['errors']);
            
            // Delay between batches to avoid rate limiting
            if ($batchIndex < count($batches) - 1 && $this->delayBetweenBatches > 0) {
                usleep((int) ($this->delayBetweenBatches * 1000000));
            }
        }
        
        $this->logger->info("Batch processing completed", $results);
        
        return $results;
    }
   
    /**
     * Process a single batch of IPs
     */
    private function processSingleBatch(array $ips): array
    {
        $results = [
            'successful' => 0,
            'failed' => 0,
            'cached' => 0,
            'api_calls' => 0,
            'data' => [],
            'errors' => []
        ];
        
        $ipsNeedingApi = [];
        
        // Check cache first
        foreach ($ips as $ip) {
            try {
                $cachedData = $this->ipService->getCachedIpData($ip);
                
                if ($cachedData !== null && !$cachedData->needsRefresh()) {
                    // Use cached data
                    $results['data'][$ip] = $cachedData;
                    $results['cached']++;
                    $results['successful']++;
                    
                    $this->logger->debug("Using cached data", ['ip' => $ip]);
                } else {
                    // Need API call
                    $ipsNeedingApi[] = $ip;
                }
                
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Failed to check cache for {$ip}: " . $e->getMessage();
                $this->logger->error("Cache check failed", [
                    'ip' => $ip,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        // Make API call for remaining IPs
        if (!empty($ipsNeedingApi)) {
            $results['api_calls']++;
            
            try {
                $apiResults = $this->apiClient->getBatchIpData($ipsNeedingApi);
                $apiProcessed = $this->processApiResults($apiResults, $ipsNeedingApi);
                
                $results['data'] = array_merge($results['data'], $apiProcessed['data']);
                $results['successful'] += $apiProcessed['successful'];
                $results['failed'] += $apiProcessed['failed'];
                $results['errors'] = array_merge($results['errors'], $apiProcessed['errors']);
                
            } catch (IpRegistryApiException $e) {
                $results['failed'] += count($ipsNeedingApi);
                $results['errors'][] = "Batch API call failed: " . $e->getMessage();
                
                $this->logger->error("Batch API call failed", [
                    'ips' => $ipsNeedingApi,
                    'error' => $e->getMessage()
                ]);
                
                // Try to use stale cached data as fallback
                foreach ($ipsNeedingApi as $ip) {
                    $staleData = $this->ipService->getCachedIpData($ip);
                    if ($staleData !== null) {
                        $results['data'][$ip] = $staleData;
                        $results['successful']++;
                        $results['failed']--;
                        
                        $this->logger->warning("Using stale cached data as fallback", ['ip' => $ip]);
                    }
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Process API results and update cache
     */
    private function processApiResults(array $apiResults, array $requestedIps): array
    {
        $results = [
            'successful' => 0,
            'failed' => 0,
            'data' => [],
            'errors' => []
        ];
        
        if (isset($apiResults['results']) && is_array($apiResults['results'])) {
            // Multiple IPs response format
            foreach ($apiResults['results'] as $apiData) {
                if (isset($apiData['ip'])) {
                    $processed = $this->processSingleApiResult($apiData);
                    
                    if ($processed['success']) {
                        $results['data'][$apiData['ip']] = $processed['data'];
                        $results['successful']++;
                    } else {
                        $results['failed']++;
                        $results['errors'][] = $processed['error'];
                    }
                }
            }
        } else {
            // Single IP response format (fallback)
            if (count($requestedIps) === 1 && isset($apiResults['ip'])) {
                $processed = $this->processSingleApiResult($apiResults);
                
                if ($processed['success']) {
                    $results['data'][$apiResults['ip']] = $processed['data'];
                    $results['successful']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = $processed['error'];
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Process single API result
     */
    private function processSingleApiResult(array $apiData): array
    {
        try {
            $ipModel = IpDataModel::fromApiResponse($apiData);
            
            if (!$ipModel->isValid()) {
                $errors = implode(', ', $ipModel->validate());
                return [
                    'success' => false,
                    'error' => "Invalid IP data for {$apiData['ip']}: {$errors}"
                ];
            }
            
            // Update cache
            $existingData = $this->ipService->getCachedIpData($ipModel->ip);
            if ($existingData !== null) {
                // Update existing
                if (!$this->ipService->updateCachedData($ipModel)) {
                    $this->logger->warning("Failed to update cache", ['ip' => $ipModel->ip]);
                }
            } else {
                // Create new
                if (!$this->ipService->createCachedData($ipModel)) {
                    $this->logger->warning("Failed to create cache entry", ['ip' => $ipModel->ip]);
                }
            }
            
            return [
                'success' => true,
                'data' => $ipModel
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to process IP data for {$apiData['ip']}: " . $e->getMessage()
            ];
        }
    }
    
    /**
     * Validate IP addresses
     */
    private function validateIps(array $ips): array
    {
        $valid = [];
        
        foreach ($ips as $ip) {
            if ($this->apiClient->isValidIp($ip)) {
                $valid[] = $ip;
            } else {
                $this->logger->warning("Invalid IP address", ['ip' => $ip]);
            }
        }
        
        return $valid;
    }
    
    /**
     * Get batch processing statistics
     */
    public function getStatistics(): array
    {
        return [
            'max_batch_size' => $this->maxBatchSize,
            'concurrent_batches' => $this->concurrentBatches,
            'delay_between_batches' => $this->delayBetweenBatches
        ];
    }
    
    /**
     * Configure batch processing parameters
     */
    public function configure(array $config): void
    {
        if (isset($config['max_batch_size'])) {
            $this->maxBatchSize = (int) $config['max_batch_size'];
        }
        
        if (isset($config['concurrent_batches'])) {
            $this->concurrentBatches = (int) $config['concurrent_batches'];
        }
        
        if (isset($config['delay_between_batches'])) {
            $this->delayBetweenBatches = (float) $config['delay_between_batches'];
        }
        
        $this->logger->info("Updated batch processing configuration", [
            'max_batch_size' => $this->maxBatchSize,
            'concurrent_batches' => $this->concurrentBatches,
            'delay_between_batches' => $this->delayBetweenBatches
        ]);
    }
}