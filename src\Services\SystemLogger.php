<?php

namespace Skpassegna\GuardgeoApi\Services;

/**
 * System Logger
 * 
 * Specialized logger for system events, performance monitoring, and operational activities.
 * Provides comprehensive system activity tracking and performance metrics.
 */
class SystemLogger implements LoggerInterface
{
    private LoggingService $loggingService;
    
    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }
    
    /**
     * Log debug message
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_DEBUG, $message, $context);
    }
    
    /**
     * Log info message
     */
    public function info(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_INFO, $message, $context);
    }
    
    /**
     * Log warning message
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_WARNING, $message, $context);
    }
    
    /**
     * Log error message
     */
    public function error(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_ERROR, $message, $context);
    }
    
    /**
     * Log critical message
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_CRITICAL, $message, $context);
    }
    
    /**
     * Log system startup
     */
    public function logSystemStartup(array $config = []): void
    {
        $message = 'System startup initiated';
        
        $context = [
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'config' => $config,
            'server_info' => [
                'software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
                'protocol' => $_SERVER['SERVER_PROTOCOL'] ?? 'unknown'
            ]
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log system shutdown
     */
    public function logSystemShutdown(array $stats = []): void
    {
        $message = 'System shutdown initiated';
        
        $context = array_merge($stats, [
            'uptime' => $this->getUptime(),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ]);
        
        $this->info($message, $context);
    }
    
    /**
     * Log performance metrics
     */
    public function logPerformanceMetrics(string $operation, float $duration, array $metrics = []): void
    {
        $durationMs = round($duration * 1000, 2);
        $message = sprintf(
            'Performance: %s completed in %.2fms',
            $operation,
            $durationMs
        );
        
        $context = array_merge($metrics, [
            'operation' => $operation,
            'duration_ms' => $durationMs,
            'memory_before' => $metrics['memory_before'] ?? null,
            'memory_after' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true)
        ]);
        
        // Log as warning if operation took too long
        $level = $duration > 1.0 ? LoggingService::LEVEL_WARNING : LoggingService::LEVEL_INFO;
        $this->log($level, $message, $context);
    }
    
    /**
     * Log cache operation
     */
    public function logCacheOperation(string $operation, string $key, bool $hit = null, int $size = null): void
    {
        $message = sprintf(
            'Cache %s: %s%s',
            $operation,
            $key,
            $hit !== null ? ($hit ? ' (HIT)' : ' (MISS)') : ''
        );
        
        $context = [
            'operation' => $operation,
            'key' => $key,
            'hit' => $hit,
            'size' => $size
        ];
        
        $this->debug($message, $context);
    }
    
    /**
     * Log database operation
     */
    public function logDatabaseOperation(string $operation, string $table, int $affectedRows = null, float $duration = null): void
    {
        $message = sprintf(
            'Database %s on %s%s%s',
            $operation,
            $table,
            $affectedRows !== null ? " ({$affectedRows} rows)" : '',
            $duration !== null ? sprintf(' in %.2fms', $duration * 1000) : ''
        );
        
        $context = [
            'operation' => $operation,
            'table' => $table,
            'affected_rows' => $affectedRows,
            'duration_ms' => $duration ? round($duration * 1000, 2) : null
        ];
        
        $this->debug($message, $context);
    }
    
    /**
     * Log external service call
     */
    public function logExternalServiceCall(string $service, string $endpoint, int $responseCode, float $duration, int $responseSize = null): void
    {
        $message = sprintf(
            'External service: %s %s returned %d in %.2fms',
            $service,
            $endpoint,
            $responseCode,
            $duration * 1000
        );
        
        $context = [
            'service' => $service,
            'endpoint' => $endpoint,
            'response_code' => $responseCode,
            'duration_ms' => round($duration * 1000, 2),
            'response_size' => $responseSize,
            'success' => $responseCode >= 200 && $responseCode < 300
        ];
        
        $level = $responseCode >= 400 ? LoggingService::LEVEL_WARNING : LoggingService::LEVEL_INFO;
        $this->log($level, $message, $context);
    }
    
    /**
     * Log resource usage
     */
    public function logResourceUsage(string $context = 'general'): void
    {
        $message = sprintf('Resource usage check: %s', $context);
        
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        $contextData = [
            'context' => $context,
            'memory_usage' => $memoryUsage,
            'memory_usage_formatted' => $this->formatBytes($memoryUsage),
            'memory_peak' => $memoryPeak,
            'memory_peak_formatted' => $this->formatBytes($memoryPeak),
            'memory_limit' => $memoryLimit,
            'memory_limit_formatted' => ini_get('memory_limit'),
            'memory_usage_percent' => $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 2) : null,
            'load_average' => $this->getLoadAverage()
        ];
        
        // Log as warning if memory usage is high
        $level = ($contextData['memory_usage_percent'] ?? 0) > 80 
            ? LoggingService::LEVEL_WARNING 
            : LoggingService::LEVEL_DEBUG;
        
        $this->log($level, $message, $contextData);
    }
    
    /**
     * Log scheduled task execution
     */
    public function logScheduledTask(string $taskName, bool $success, float $duration = null, array $results = []): void
    {
        $message = sprintf(
            'Scheduled task %s: %s%s',
            $taskName,
            $success ? 'SUCCESS' : 'FAILED',
            $duration ? sprintf(' in %.2fs', $duration) : ''
        );
        
        $context = [
            'task_name' => $taskName,
            'success' => $success,
            'duration' => $duration,
            'results' => $results
        ];
        
        $level = $success ? LoggingService::LEVEL_INFO : LoggingService::LEVEL_ERROR;
        $this->log($level, $message, $context);
    }
    
    /**
     * Log configuration change
     */
    public function logConfigurationChange(string $setting, $oldValue, $newValue, int $userId = null): void
    {
        $message = sprintf('Configuration changed: %s', $setting);
        
        $context = [
            'setting' => $setting,
            'old_value' => $this->sanitizeConfigValue($setting, $oldValue),
            'new_value' => $this->sanitizeConfigValue($setting, $newValue),
            'changed_by' => $userId
        ];
        
        $this->info($message, $context);
    }
    
    /**
     * Log health check
     */
    public function logHealthCheck(array $checks): void
    {
        $passed = array_filter($checks, fn($check) => $check['status'] === 'ok');
        $failed = array_filter($checks, fn($check) => $check['status'] !== 'ok');
        
        $message = sprintf(
            'Health check: %d passed, %d failed',
            count($passed),
            count($failed)
        );
        
        $context = [
            'checks' => $checks,
            'passed_count' => count($passed),
            'failed_count' => count($failed),
            'overall_status' => empty($failed) ? 'healthy' : 'unhealthy'
        ];
        
        $level = empty($failed) ? LoggingService::LEVEL_INFO : LoggingService::LEVEL_WARNING;
        $this->log($level, $message, $context);
    }
    
    /**
     * Log backup operation
     */
    public function logBackupOperation(string $type, bool $success, int $size = null, float $duration = null): void
    {
        $message = sprintf(
            'Backup %s: %s%s%s',
            $type,
            $success ? 'SUCCESS' : 'FAILED',
            $size ? sprintf(' (%s)', $this->formatBytes($size)) : '',
            $duration ? sprintf(' in %.2fs', $duration) : ''
        );
        
        $context = [
            'backup_type' => $type,
            'success' => $success,
            'size' => $size,
            'duration' => $duration
        ];
        
        $level = $success ? LoggingService::LEVEL_INFO : LoggingService::LEVEL_ERROR;
        $this->log($level, $message, $context);
    }
    
    /**
     * Get system uptime (if available)
     */
    private function getUptime(): ?float
    {
        if (function_exists('sys_getloadavg') && file_exists('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            return $uptime ? (float)explode(' ', $uptime)[0] : null;
        }
        
        return null;
    }
    
    /**
     * Get system load average (if available)
     */
    private function getLoadAverage(): ?array
    {
        if (function_exists('sys_getloadavg')) {
            return sys_getloadavg();
        }
        
        return null;
    }
    
    /**
     * Parse memory limit string to bytes
     */
    private function parseMemoryLimit(string $limit): int
    {
        if ($limit === '-1') {
            return -1; // Unlimited
        }
        
        $unit = strtolower(substr($limit, -1));
        $value = (int)substr($limit, 0, -1);
        
        return match($unit) {
            'g' => $value * 1024 * 1024 * 1024,
            'm' => $value * 1024 * 1024,
            'k' => $value * 1024,
            default => (int)$limit
        };
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }
    
    /**
     * Sanitize configuration values for logging
     */
    private function sanitizeConfigValue(string $setting, $value): string
    {
        // Hide sensitive configuration values
        $sensitiveSettings = ['password', 'key', 'secret', 'token', 'credential'];
        
        foreach ($sensitiveSettings as $sensitive) {
            if (stripos($setting, $sensitive) !== false) {
                return '[REDACTED]';
            }
        }
        
        return is_scalar($value) ? (string)$value : json_encode($value);
    }
    
    /**
     * Internal log method
     */
    private function log(string $level, string $message, array $context): void
    {
        $this->loggingService->log(LoggingService::TYPE_SYSTEM, $level, $message, $context);
    }
}