<?php

namespace Skpassegna\GuardgeoApi\Views\Components;

/**
 * Base Component
 * 
 * Abstract base class for all UI components providing common functionality
 * for rendering, data binding, and component composition.
 */
abstract class BaseComponent
{
    protected array $props;
    protected array $attributes;
    protected string $cssClasses;

    public function __construct(array $props = [], array $attributes = [])
    {
        $this->props = $props;
        $this->attributes = $attributes;
        $this->cssClasses = $this->getDefaultClasses();
    }

    /**
     * Render the component
     *
     * @return string
     */
    abstract public function render(): string;

    /**
     * Get default CSS classes for the component
     *
     * @return string
     */
    protected function getDefaultClasses(): string
    {
        return '';
    }

    /**
     * Add CSS classes to the component
     *
     * @param string $classes
     * @return self
     */
    public function addClass(string $classes): self
    {
        $this->cssClasses .= ' ' . $classes;
        return $this;
    }

    /**
     * Set CSS classes for the component
     *
     * @param string $classes
     * @return self
     */
    public function setClasses(string $classes): self
    {
        $this->cssClasses = $classes;
        return $this;
    }

    /**
     * Get all CSS classes
     *
     * @return string
     */
    protected function getClasses(): string
    {
        return trim($this->cssClasses);
    }

    /**
     * Render HTML attributes
     *
     * @return string
     */
    protected function renderAttributes(): string
    {
        $attrs = [];
        
        foreach ($this->attributes as $key => $value) {
            if ($value === null || $value === false) {
                continue;
            }
            
            if ($value === true) {
                $attrs[] = htmlspecialchars($key, ENT_QUOTES, 'UTF-8');
            } else {
                $attrs[] = htmlspecialchars($key, ENT_QUOTES, 'UTF-8') . '="' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '"';
            }
        }
        
        return implode(' ', $attrs);
    }

    /**
     * Escape HTML content
     *
     * @param string $content
     * @return string
     */
    protected function escape(string $content): string
    {
        return htmlspecialchars($content, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Get prop value with default
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function prop(string $key, $default = null)
    {
        return $this->props[$key] ?? $default;
    }

    /**
     * Check if prop exists and is truthy
     *
     * @param string $key
     * @return bool
     */
    protected function hasProp(string $key): bool
    {
        return isset($this->props[$key]) && $this->props[$key];
    }
}