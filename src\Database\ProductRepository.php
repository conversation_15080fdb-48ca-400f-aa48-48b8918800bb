<?php

namespace Skpassegna\GuardgeoApi\Database;

use Skpassegna\GuardgeoApi\Models\ProductModel;

/**
 * Product Repository
 * 
 * Handles database operations for Freemius products.
 */
class ProductRepository extends BaseRepository
{
    protected string $table = 'freemius_products';
    
    /**
     * Find product by Freemius product ID
     */
    public function findByProductId(int $productId): ?ProductModel
    {
        $row = $this->findById($productId);
        
        return $row ? ProductModel::fromDatabaseRow($row) : null;
    }
    
    /**
     * Save product model to database
     */
    public function save(ProductModel $product): ProductModel
    {
        $data = $product->toDatabaseArray();
        
        // Check if product exists
        $existing = $this->findById($product->id);
        
        if ($existing) {
            // Update existing product
            $this->update($product->id, $data);
        } else {
            // Insert new product
            $this->insert($data);
        }
        
        // Return fresh copy from database
        return $this->findByProductId($product->id);
    }
    
    /**
     * Find products that need refresh (expired cache)
     */
    public function findExpiredProducts(int $maxAgeHours = 24): array
    {
        $cutoffTime = date('Y-m-d H:i:s', time() - ($maxAgeHours * 3600));
        
        $sql = "SELECT * FROM {$this->table} WHERE cached_at < :cutoff_time ORDER BY cached_at ASC";
        $statement = $this->executeQuery($sql, ['cutoff_time' => $cutoffTime]);
        
        $products = [];
        while ($row = $statement->fetch()) {
            $products[] = ProductModel::fromDatabaseRow($row);
        }
        
        return $products;
    }
    
    /**
     * Get product statistics
     */
    public function getStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN is_released = true THEN 1 END) as released_products,
                COUNT(CASE WHEN is_premium = true THEN 1 END) as premium_products,
                SUM(installs_count) as total_installs,
                SUM(active_installs_count) as total_active_installs,
                AVG(EXTRACT(EPOCH FROM (NOW() - cached_at))/3600) as avg_cache_age_hours
            FROM {$this->table}
        ";
        
        $statement = $this->executeQuery($sql);
        return $statement->fetch();
    }
    
    /**
     * Update product cache timestamp
     */
    public function updateCacheTimestamp(int $productId): bool
    {
        return $this->update($productId, ['cached_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * Find products by developer ID
     */
    public function findByDeveloperId(int $developerId): array
    {
        $rows = $this->findBy(['developer_id' => $developerId]);
        
        $products = [];
        foreach ($rows as $row) {
            $products[] = ProductModel::fromDatabaseRow($row);
        }
        
        return $products;
    }
    
    /**
     * Search products by title or slug
     */
    public function search(string $query, int $limit = 50): array
    {
        $sql = "
            SELECT * FROM {$this->table} 
            WHERE title ILIKE :query OR slug ILIKE :query 
            ORDER BY title ASC 
            LIMIT :limit
        ";
        
        $statement = $this->executeQuery($sql, [
            'query' => "%{$query}%",
            'limit' => $limit
        ]);
        
        $products = [];
        while ($row = $statement->fetch()) {
            $products[] = ProductModel::fromDatabaseRow($row);
        }
        
        return $products;
    }

    /**
     * Get products with pagination and filtering for admin interface
     */
    public function getProductsPaginated(int $page, int $limit, array $filters = [], string $sortBy = 'created', string $sortOrder = 'desc'): array
    {
        $offset = ($page - 1) * $limit;
        
        $whereConditions = [];
        $params = [];
        
        // Apply search filter
        if (!empty($filters['search'])) {
            $whereConditions[] = '(title ILIKE :search OR slug ILIKE :search)';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        // Build WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        // Apply sorting
        $validSortColumns = ['id', 'title', 'slug', 'created', 'updated', 'installs_count', 'active_installs_count'];
        if (!in_array($sortBy, $validSortColumns)) {
            $sortBy = 'created';
        }
        
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql = "
            SELECT * FROM {$this->table} 
            {$whereClause}
            ORDER BY {$sortBy} {$sortOrder}
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        try {
            $statement = $this->executeQuery($sql, $params);
            $rows = $statement->fetchAll();
            
            $products = [];
            foreach ($rows as $row) {
                $products[] = $this->mapRowToAdminArray($row);
            }
            
            return [
                'products' => $products,
                'total' => $this->getFilteredCount($filters)
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get paginated products", [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [
                'products' => [],
                'total' => 0
            ];
        }
    }

    /**
     * Get product by ID for admin interface
     */
    public function getById(string $productId): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = :id";
        
        try {
            $statement = $this->executeQuery($sql, ['id' => $productId]);
            $row = $statement->fetch();
            
            if (!$row) {
                return null;
            }
            
            return $this->mapRowToAdminArray($row);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get product by ID", [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get filtered count for pagination
     */
    private function getFilteredCount(array $filters): int
    {
        $whereConditions = [];
        $params = [];
        
        // Apply same filters as main query
        if (!empty($filters['search'])) {
            $whereConditions[] = '(title ILIKE :search OR slug ILIKE :search)';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        // Build WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        $sql = "SELECT COUNT(*) as count FROM {$this->table} {$whereClause}";
        
        try {
            $statement = $this->executeQuery($sql, $params);
            $row = $statement->fetch();
            return (int)($row['count'] ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Map database row to admin-friendly array
     */
    private function mapRowToAdminArray(array $row): array
    {
        return [
            'id' => $row['id'],
            'title' => $row['title'],
            'slug' => $row['slug'],
            'type' => $row['type'],
            'environment' => $row['environment'],
            'is_released' => (bool)$row['is_released'],
            'is_sdk_required' => (bool)$row['is_sdk_required'],
            'is_pricing_visible' => (bool)$row['is_pricing_visible'],
            'is_wp_org_compliant' => (bool)$row['is_wp_org_compliant'],
            'installs_count' => (int)$row['installs_count'],
            'active_installs_count' => (int)$row['active_installs_count'],
            'free_releases_count' => (int)$row['free_releases_count'],
            'premium_releases_count' => (int)$row['premium_releases_count'],
            'total_purchases' => (int)$row['total_purchases'],
            'total_subscriptions' => (int)$row['total_subscriptions'],
            'total_renewals' => (int)$row['total_renewals'],
            'earnings' => $row['earnings'],
            'created' => $row['created'],
            'updated' => $row['updated'],
            'cached_at' => $row['cached_at'],
            'developer_id' => $row['developer_id'],
            'store_id' => $row['store_id'],
            'default_plan_id' => $row['default_plan_id'],
            'plans' => $row['plans'],
            'features' => $row['features'],
            'money_back_period' => (int)$row['money_back_period'],
            'refund_policy' => $row['refund_policy'],
            'annual_renewals_discount' => $row['annual_renewals_discount'],
            'renewals_discount_type' => $row['renewals_discount_type'],
            'icon' => $row['icon'],
            'raw_data' => json_decode($row['raw_data'] ?? 'null', true)
        ];
    }
}