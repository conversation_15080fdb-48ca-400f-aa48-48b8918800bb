<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Session Security Manager
 * 
 * Enhanced session security with timeout handling, fingerprinting,
 * and advanced security measures for admin sessions.
 */
class SessionSecurityManager
{
    private LoggingService $logger;
    private array $config;

    // Default session configuration
    private const DEFAULT_CONFIG = [
        'session_timeout' => 3600,        // 1 hour
        'idle_timeout' => 1800,           // 30 minutes
        'regenerate_interval' => 300,     // 5 minutes
        'max_concurrent_sessions' => 3,   // Maximum concurrent sessions per user
        'enable_fingerprinting' => true,  // Enable session fingerprinting
        'strict_ip_check' => false,       // Strict IP checking (disabled for mobile)
        'secure_cookies' => true,         // Force secure cookies
        'httponly_cookies' => true,       // HTTP-only cookies
        'samesite_policy' => 'Strict'     // SameSite cookie policy
    ];

    public function __construct(LoggingService $logger, array $config = [])
    {
        $this->logger = $logger;
        $this->config = array_merge(self::DEFAULT_CONFIG, $config);
    }

    /**
     * Initialize secure session with enhanced security
     *
     * @return bool Success status
     */
    public function initializeSecureSession(): bool
    {
        try {
            // Configure session settings before starting
            $this->configureSessionSettings();

            // Start session if not already started
            if (session_status() !== PHP_SESSION_ACTIVE) {
                session_start();
            }

            // Initialize session security data
            $this->initializeSessionSecurity();

            return true;
        } catch (\Exception $e) {
            $this->logger->logError('Session initialization failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Validate session security and check for threats
     *
     * @return array Validation result
     */
    public function validateSessionSecurity(): array
    {
        $result = [
            'valid' => true,
            'warnings' => [],
            'actions_taken' => []
        ];

        if (session_status() !== PHP_SESSION_ACTIVE) {
            $result['valid'] = false;
            $result['warnings'][] = 'No active session';
            return $result;
        }

        // Check session timeout
        $timeoutCheck = $this->checkSessionTimeout();
        if (!$timeoutCheck['valid']) {
            $result['valid'] = false;
            $result['warnings'] = array_merge($result['warnings'], $timeoutCheck['warnings']);
            $result['actions_taken'] = array_merge($result['actions_taken'], $timeoutCheck['actions_taken']);
        }

        // Check session fingerprint
        $fingerprintCheck = $this->validateSessionFingerprint();
        if (!$fingerprintCheck['valid']) {
            $result['valid'] = false;
            $result['warnings'] = array_merge($result['warnings'], $fingerprintCheck['warnings']);
            $result['actions_taken'] = array_merge($result['actions_taken'], $fingerprintCheck['actions_taken']);
        }

        // Check for session hijacking indicators
        $hijackCheck = $this->checkSessionHijacking();
        if (!$hijackCheck['valid']) {
            $result['valid'] = false;
            $result['warnings'] = array_merge($result['warnings'], $hijackCheck['warnings']);
            $result['actions_taken'] = array_merge($result['actions_taken'], $hijackCheck['actions_taken']);
        }

        // Regenerate session ID if needed
        if ($this->shouldRegenerateSessionId()) {
            $this->regenerateSessionId();
            $result['actions_taken'][] = 'session_id_regenerated';
        }

        return $result;
    }

    /**
     * Create secure session for user
     *
     * @param array $userData User data to store in session
     * @return string Session ID
     */
    public function createUserSession(array $userData): string
    {
        $this->initializeSecureSession();

        // Clear any existing session data
        session_unset();
        
        // Regenerate session ID for security
        $this->regenerateSessionId();

        // Store user data with security metadata
        $_SESSION['user_data'] = $userData;
        $_SESSION['security'] = [
            'created_at' => time(),
            'last_activity' => time(),
            'last_regeneration' => time(),
            'fingerprint' => $this->generateSessionFingerprint(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'csrf_token' => $this->generateCsrfToken()
        ];

        $sessionId = session_id();

        $this->logger->logAdminAction('session_created', [
            'user_id' => $userData['user_id'] ?? 'unknown',
            'session_id' => $sessionId,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);

        return $sessionId;
    } 
   /**
     * Destroy session securely
     *
     * @return bool Success status
     */
    public function destroySession(): bool
    {
        try {
            if (session_status() === PHP_SESSION_ACTIVE) {
                $sessionId = session_id();
                $userData = $_SESSION['user_data'] ?? [];

                // Clear session data
                session_unset();
                session_destroy();

                // Clear session cookie
                $sessionName = session_name();
                if (isset($_COOKIE[$sessionName])) {
                    setcookie(
                        $sessionName,
                        '',
                        time() - 3600,
                        '/',
                        '',
                        $this->config['secure_cookies'] && $this->isHttps(),
                        $this->config['httponly_cookies']
                    );
                }

                $this->logger->logAdminAction('session_destroyed', [
                    'user_id' => $userData['user_id'] ?? 'unknown',
                    'session_id' => $sessionId,
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
            }

            return true;
        } catch (\Exception $e) {
            $this->logger->logError('Session destruction failed', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Configure secure session settings
     *
     * @return void
     */
    private function configureSessionSettings(): void
    {
        // Session cookie configuration
        ini_set('session.cookie_httponly', $this->config['httponly_cookies'] ? '1' : '0');
        ini_set('session.cookie_secure', ($this->config['secure_cookies'] && $this->isHttps()) ? '1' : '0');
        ini_set('session.cookie_samesite', $this->config['samesite_policy']);
        ini_set('session.use_strict_mode', '1');
        ini_set('session.use_only_cookies', '1');
        ini_set('session.cookie_lifetime', '0'); // Session cookies only
        ini_set('session.gc_maxlifetime', (string)$this->config['session_timeout']);
        ini_set('session.name', 'GUARDGEO_ADMIN_SESSION');

        // Additional security settings
        ini_set('session.entropy_length', '32');
        ini_set('session.hash_function', 'sha256');
        ini_set('session.hash_bits_per_character', '6');
    }

    /**
     * Initialize session security metadata
     *
     * @return void
     */
    private function initializeSessionSecurity(): void
    {
        if (!isset($_SESSION['security'])) {
            $_SESSION['security'] = [
                'created_at' => time(),
                'last_activity' => time(),
                'last_regeneration' => time(),
                'fingerprint' => $this->generateSessionFingerprint(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'csrf_token' => $this->generateCsrfToken()
            ];
        } else {
            $_SESSION['security']['last_activity'] = time();
        }
    }

    /**
     * Check session timeout
     *
     * @return array Timeout check result
     */
    private function checkSessionTimeout(): array
    {
        $result = ['valid' => true, 'warnings' => [], 'actions_taken' => []];
        $now = time();

        if (!isset($_SESSION['security'])) {
            $result['valid'] = false;
            $result['warnings'][] = 'Missing session security data';
            return $result;
        }

        $security = $_SESSION['security'];

        // Check absolute session timeout
        if (($now - $security['created_at']) > $this->config['session_timeout']) {
            $result['valid'] = false;
            $result['warnings'][] = 'Session expired (absolute timeout)';
            $result['actions_taken'][] = 'session_expired';
            
            $this->logger->logAdminAction('session_timeout', [
                'type' => 'absolute',
                'duration' => $now - $security['created_at'],
                'limit' => $this->config['session_timeout']
            ]);
        }

        // Check idle timeout
        if (($now - $security['last_activity']) > $this->config['idle_timeout']) {
            $result['valid'] = false;
            $result['warnings'][] = 'Session expired (idle timeout)';
            $result['actions_taken'][] = 'session_idle_expired';
            
            $this->logger->logAdminAction('session_timeout', [
                'type' => 'idle',
                'idle_duration' => $now - $security['last_activity'],
                'limit' => $this->config['idle_timeout']
            ]);
        }

        return $result;
    }

    /**
     * Validate session fingerprint
     *
     * @return array Fingerprint validation result
     */
    private function validateSessionFingerprint(): array
    {
        $result = ['valid' => true, 'warnings' => [], 'actions_taken' => []];

        if (!$this->config['enable_fingerprinting']) {
            return $result;
        }

        if (!isset($_SESSION['security']['fingerprint'])) {
            $result['valid'] = false;
            $result['warnings'][] = 'Missing session fingerprint';
            return $result;
        }

        $currentFingerprint = $this->generateSessionFingerprint();
        $sessionFingerprint = $_SESSION['security']['fingerprint'];

        if ($currentFingerprint !== $sessionFingerprint) {
            $result['valid'] = false;
            $result['warnings'][] = 'Session fingerprint mismatch';
            $result['actions_taken'][] = 'fingerprint_mismatch_detected';
            
            $this->logger->logError('Session fingerprint mismatch detected', [
                'current_fingerprint' => $currentFingerprint,
                'session_fingerprint' => $sessionFingerprint,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
        }

        return $result;
    }

    /**
     * Check for session hijacking indicators
     *
     * @return array Hijacking check result
     */
    private function checkSessionHijacking(): array
    {
        $result = ['valid' => true, 'warnings' => [], 'actions_taken' => []];

        if (!isset($_SESSION['security'])) {
            return $result;
        }

        $security = $_SESSION['security'];
        $currentIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

        // Check IP address consistency (if strict mode enabled)
        if ($this->config['strict_ip_check'] && $currentIp !== $security['ip_address']) {
            $result['valid'] = false;
            $result['warnings'][] = 'IP address changed during session';
            $result['actions_taken'][] = 'ip_change_detected';
            
            $this->logger->logError('Session IP address change detected', [
                'original_ip' => $security['ip_address'],
                'current_ip' => $currentIp,
                'user_id' => $_SESSION['user_data']['user_id'] ?? 'unknown'
            ]);
        }

        // Check user agent consistency
        if ($currentUserAgent !== $security['user_agent']) {
            $result['warnings'][] = 'User agent changed during session';
            $result['actions_taken'][] = 'user_agent_change_detected';
            
            $this->logger->logWarning('Session user agent change detected', [
                'original_user_agent' => substr($security['user_agent'], 0, 100),
                'current_user_agent' => substr($currentUserAgent, 0, 100),
                'user_id' => $_SESSION['user_data']['user_id'] ?? 'unknown'
            ]);
        }

        return $result;
    }

    /**
     * Check if session ID should be regenerated
     *
     * @return bool True if regeneration is needed
     */
    private function shouldRegenerateSessionId(): bool
    {
        if (!isset($_SESSION['security']['last_regeneration'])) {
            return true;
        }

        $timeSinceRegeneration = time() - $_SESSION['security']['last_regeneration'];
        return $timeSinceRegeneration > $this->config['regenerate_interval'];
    }

    /**
     * Regenerate session ID securely
     *
     * @return void
     */
    private function regenerateSessionId(): void
    {
        $oldSessionId = session_id();
        session_regenerate_id(true);
        $newSessionId = session_id();

        if (isset($_SESSION['security'])) {
            $_SESSION['security']['last_regeneration'] = time();
        }

        $this->logger->logInfo('Session ID regenerated', [
            'old_session_id' => $oldSessionId,
            'new_session_id' => $newSessionId,
            'user_id' => $_SESSION['user_data']['user_id'] ?? 'unknown'
        ]);
    }

    /**
     * Generate session fingerprint
     *
     * @return string Session fingerprint
     */
    private function generateSessionFingerprint(): string
    {
        $components = [
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
            $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '',
            $_SERVER['HTTP_ACCEPT'] ?? ''
        ];

        return hash('sha256', implode('|', $components));
    }

    /**
     * Generate CSRF token
     *
     * @return string CSRF token
     */
    private function generateCsrfToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Check if connection is HTTPS
     *
     * @return bool True if HTTPS
     */
    private function isHttps(): bool
    {
        return (
            (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
            $_SERVER['SERVER_PORT'] == 443 ||
            (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
            (!empty($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on')
        );
    }

    /**
     * Get session security status
     *
     * @return array Session security status
     */
    public function getSessionSecurityStatus(): array
    {
        if (session_status() !== PHP_SESSION_ACTIVE || !isset($_SESSION['security'])) {
            return [
                'active' => false,
                'error' => 'No active session or missing security data'
            ];
        }

        $security = $_SESSION['security'];
        $now = time();

        return [
            'active' => true,
            'created_at' => date('Y-m-d H:i:s', $security['created_at']),
            'last_activity' => date('Y-m-d H:i:s', $security['last_activity']),
            'session_age' => $now - $security['created_at'],
            'idle_time' => $now - $security['last_activity'],
            'time_until_timeout' => max(0, $this->config['session_timeout'] - ($now - $security['created_at'])),
            'time_until_idle_timeout' => max(0, $this->config['idle_timeout'] - ($now - $security['last_activity'])),
            'fingerprint_valid' => $this->config['enable_fingerprinting'] ? 
                ($this->generateSessionFingerprint() === $security['fingerprint']) : null,
            'ip_address' => $security['ip_address'],
            'user_agent' => substr($security['user_agent'], 0, 100)
        ];
    }

    /**
     * Update session configuration
     *
     * @param array $newConfig
     * @return void
     */
    public function updateConfiguration(array $newConfig): void
    {
        $this->config = array_merge($this->config, $newConfig);
    }

    /**
     * Get current configuration
     *
     * @return array Current configuration
     */
    public function getConfiguration(): array
    {
        return $this->config;
    }
}