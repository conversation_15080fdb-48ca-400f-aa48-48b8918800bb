# SSL/TLS Configuration for GuardGeo Admin Platform
# This file contains recommended SSL/TLS settings for production deployment

# Apache SSL Configuration
<IfModule mod_ssl.c>
    # Enable SSL
    SSLEngine on
    
    # SSL Protocol Configuration
    SSLProtocol all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384
    SSLHonorCipherOrder off
    SSLSessionTickets off
    
    # SSL Certificate Configuration (update paths as needed)
    # SSLCertificateFile /path/to/your/certificate.crt
    # SSLCertificateKeyFile /path/to/your/private.key
    # SSLCertificateChainFile /path/to/your/chain.crt
    
    # OCSP Stapling
    SSLUseStapling on
    SSLStaplingCache "shmcb:logs/stapling-cache(150000)"
    
    # Security Headers (additional to .htaccess)
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # Disable SSL compression
    SSLCompression off
    
    # Enable HTTP/2
    Protocols h2 http/1.1
</IfModule>

# Nginx SSL Configuration (alternative)
# server {
#     listen 443 ssl http2;
#     server_name server-domain.tld;
#     
#     # SSL Configuration
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # SSL Security
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     
#     # SSL Session
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     ssl_session_tickets off;
#     
#     # OCSP Stapling
#     ssl_stapling on;
#     ssl_stapling_verify on;
#     
#     # Security Headers
#     add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
#     add_header X-Content-Type-Options nosniff always;
#     add_header X-Frame-Options DENY always;
#     add_header X-XSS-Protection "1; mode=block" always;
#     add_header Referrer-Policy "strict-origin-when-cross-origin" always;
#     
#     # Content Security Policy
#     add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests" always;
#     
#     # PHP Configuration
#     location ~ \.php$ {
#         fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
#         fastcgi_index index.php;
#         fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#         include fastcgi_params;
#         
#         # Security
#         fastcgi_hide_header X-Powered-By;
#     }
#     
#     # Static files
#     location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
#         expires 1y;
#         add_header Cache-Control "public, immutable";
#     }
#     
#     # Deny access to sensitive files
#     location ~ /\. {
#         deny all;
#     }
#     
#     location ~ \.(env|log|sql|md|json|lock|yaml|yml|ini|conf)$ {
#         deny all;
#     }
# }

# SSL Certificate Generation Commands (Let's Encrypt)
# certbot --apache -d server-domain.tld
# or
# certbot --nginx -d server-domain.tld

# SSL Test Commands
# Test SSL configuration:
# openssl s_client -connect server-domain.tld:443 -servername server-domain.tld
# 
# Check certificate:
# openssl x509 -in certificate.crt -text -noout
# 
# Test SSL Labs rating:
# https://www.ssllabs.com/ssltest/analyze.html?d=server-domain.tld

# Recommended SSL Cipher Suites (Modern)
# ECDHE-ECDSA-AES128-GCM-SHA256
# ECDHE-RSA-AES128-GCM-SHA256
# ECDHE-ECDSA-AES256-GCM-SHA384
# ECDHE-RSA-AES256-GCM-SHA384
# ECDHE-ECDSA-CHACHA20-POLY1305
# ECDHE-RSA-CHACHA20-POLY1305
# DHE-RSA-AES128-GCM-SHA256
# DHE-RSA-AES256-GCM-SHA384

# Security Considerations:
# 1. Use TLS 1.2 and 1.3 only
# 2. Disable SSL compression
# 3. Enable OCSP stapling
# 4. Use strong cipher suites
# 5. Enable HSTS with preload
# 6. Regular certificate renewal
# 7. Monitor SSL Labs rating
# 8. Implement certificate transparency monitoring