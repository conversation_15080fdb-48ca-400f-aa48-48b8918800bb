<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Services\SystemSettingsService;
use Skpassegna\GuardgeoApi\Services\AuthService;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;
use Skpassegna\GuardgeoApi\Utils\RequestValidator;

/**
 * System Settings API Controller
 * 
 * Handles API requests for system configuration management
 * through the admin interface.
 */
class SystemSettingsApiController
{
    private SystemSettingsService $settingsService;
    private AuthService $authService;
    private ResponseFormatter $responseFormatter;
    private RequestValidator $requestValidator;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->settingsService = new SystemSettingsService();
        $this->authService = new AuthService();
        $this->responseFormatter = new ResponseFormatter();
        $this->requestValidator = new RequestValidator();
    }
    
    /**
     * Get all system settings
     */
    public function getAllSettings(): void
    {
        try {
            // Check authentication
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            // Check permissions (Super Admin only for sensitive settings)
            $includeSensitive = $user->getRole() === 'super_admin';
            
            $settings = $this->settingsService->getAllSettings();
            
            // Filter sensitive settings for non-super admins
            if (!$includeSensitive) {
                $settings = $this->filterSensitiveSettings($settings);
            }
            
            $this->responseFormatter->sendSuccess([
                'settings' => $settings,
                'can_edit_sensitive' => $includeSensitive
            ]);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to get system settings: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Get settings for a specific category
     */
    public function getCategorySettings(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            $category = $_GET['category'] ?? '';
            if (empty($category)) {
                $this->responseFormatter->sendError('Category parameter is required', 400);
                return;
            }
            
            $settings = $this->settingsService->getCategorySettings($category);
            
            // Filter sensitive settings for non-super admins
            if ($user->getRole() !== 'super_admin') {
                $settings = array_filter($settings, function($setting) {
                    return !$setting['is_sensitive'];
                });
            }
            
            $this->responseFormatter->sendSuccess([
                'category' => $category,
                'settings' => $settings
            ]);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to get category settings: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Update a system setting
     */
    public function updateSetting(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            $validation = $this->requestValidator->validate($input, [
                'key' => 'required|string',
                'value' => 'required'
            ]);
            
            if (!$validation['valid']) {
                $this->responseFormatter->sendError('Validation failed', 400, $validation['errors']);
                return;
            }
            
            $key = $input['key'];
            $value = $input['value'];
            
            // Check if setting is sensitive and user has permission
            $setting = $this->settingsService->getSetting($key);
            if (!$setting) {
                $this->responseFormatter->sendError('Setting not found', 404);
                return;
            }
            
            if ($setting['is_sensitive'] && $user->getRole() !== 'super_admin') {
                $this->responseFormatter->sendError('Insufficient permissions to modify sensitive settings', 403);
                return;
            }
            
            $result = $this->settingsService->updateSetting($key, $value, $user);
            
            if ($result) {
                $this->responseFormatter->sendSuccess([
                    'message' => 'Setting updated successfully',
                    'key' => $key
                ]);
            } else {
                $this->responseFormatter->sendError('Failed to update setting', 500);
            }
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to update setting: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Update multiple settings
     */
    public function updateMultipleSettings(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['settings']) || !is_array($input['settings'])) {
                $this->responseFormatter->sendError('Settings array is required', 400);
                return;
            }
            
            // Check permissions for sensitive settings
            foreach ($input['settings'] as $key => $value) {
                $setting = $this->settingsService->getSetting($key);
                if ($setting && $setting['is_sensitive'] && $user->getRole() !== 'super_admin') {
                    $this->responseFormatter->sendError("Insufficient permissions to modify sensitive setting: {$key}", 403);
                    return;
                }
            }
            
            $result = $this->settingsService->updateMultipleSettings($input['settings'], $user);
            
            $this->responseFormatter->sendSuccess([
                'message' => 'Settings update completed',
                'results' => $result['results'],
                'errors' => $result['errors'],
                'success_count' => count(array_filter($result['results'])),
                'error_count' => count($result['errors'])
            ]);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to update settings: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Add a new setting
     */
    public function addSetting(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || $user->getRole() !== 'super_admin') {
                $this->responseFormatter->sendError('Super Admin access required', 403);
                return;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            $validation = $this->requestValidator->validate($input, [
                'key' => 'required|string',
                'value' => 'required',
                'type' => 'required|string|in:string,integer,float,boolean,json,array',
                'description' => 'string',
                'is_sensitive' => 'boolean'
            ]);
            
            if (!$validation['valid']) {
                $this->responseFormatter->sendError('Validation failed', 400, $validation['errors']);
                return;
            }
            
            $result = $this->settingsService->addSetting(
                $input['key'],
                $input['value'],
                $input['type'],
                $input['description'] ?? '',
                $input['is_sensitive'] ?? false,
                $user
            );
            
            if ($result) {
                $this->responseFormatter->sendSuccess([
                    'message' => 'Setting added successfully',
                    'key' => $input['key']
                ]);
            } else {
                $this->responseFormatter->sendError('Failed to add setting', 500);
            }
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to add setting: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Delete a setting
     */
    public function deleteSetting(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || $user->getRole() !== 'super_admin') {
                $this->responseFormatter->sendError('Super Admin access required', 403);
                return;
            }
            
            $key = $_GET['key'] ?? '';
            if (empty($key)) {
                $this->responseFormatter->sendError('Key parameter is required', 400);
                return;
            }
            
            $result = $this->settingsService->deleteSetting($key, $user);
            
            if ($result) {
                $this->responseFormatter->sendSuccess([
                    'message' => 'Setting deleted successfully',
                    'key' => $key
                ]);
            } else {
                $this->responseFormatter->sendError('Failed to delete setting', 500);
            }
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to delete setting: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Get configuration summary
     */
    public function getConfigurationSummary(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            $summary = $this->settingsService->getConfigurationSummary();
            
            $this->responseFormatter->sendSuccess($summary);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to get configuration summary: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Export settings
     */
    public function exportSettings(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            $includeSensitive = ($user->getRole() === 'super_admin') && 
                               (($_GET['include_sensitive'] ?? 'false') === 'true');
            
            $settings = $this->settingsService->exportSettings($includeSensitive);
            
            // Set headers for file download
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="guardgeo-settings-' . date('Y-m-d-H-i-s') . '.json"');
            
            echo json_encode([
                'export_date' => date('c'),
                'exported_by' => $user->getEmail(),
                'include_sensitive' => $includeSensitive,
                'settings' => $settings
            ], JSON_PRETTY_PRINT);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to export settings: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Import settings
     */
    public function importSettings(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user || $user->getRole() !== 'super_admin') {
                $this->responseFormatter->sendError('Super Admin access required', 403);
                return;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['settings']) || !is_array($input['settings'])) {
                $this->responseFormatter->sendError('Settings array is required', 400);
                return;
            }
            
            $result = $this->settingsService->importSettings($input['settings'], $user);
            
            $this->responseFormatter->sendSuccess([
                'message' => 'Settings import completed',
                'results' => $result['results'],
                'errors' => $result['errors'],
                'success_count' => count(array_filter($result['results'])),
                'error_count' => count($result['errors'])
            ]);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to import settings: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Test configuration connectivity
     */
    public function testConfiguration(): void
    {
        try {
            $user = $this->authService->getCurrentUser();
            if (!$user) {
                $this->responseFormatter->sendError('Authentication required', 401);
                return;
            }
            
            $testType = $_GET['type'] ?? 'all';
            $results = [];
            
            switch ($testType) {
                case 'database':
                    $results['database'] = $this->testDatabaseConnection();
                    break;
                    
                case 'freemius':
                    $results['freemius'] = $this->testFreemiusConnection();
                    break;
                    
                case 'ipregistry':
                    $results['ipregistry'] = $this->testIpRegistryConnection();
                    break;
                    
                case 'all':
                default:
                    $results['database'] = $this->testDatabaseConnection();
                    $results['freemius'] = $this->testFreemiusConnection();
                    $results['ipregistry'] = $this->testIpRegistryConnection();
                    break;
            }
            
            $this->responseFormatter->sendSuccess([
                'test_type' => $testType,
                'results' => $results,
                'overall_status' => $this->getOverallStatus($results)
            ]);
            
        } catch (\Exception $e) {
            $this->responseFormatter->sendError('Failed to test configuration: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Filter sensitive settings for non-super admins
     */
    private function filterSensitiveSettings(array $settings): array
    {
        $filtered = [];
        
        foreach ($settings as $category => $categorySettings) {
            $filtered[$category] = array_filter($categorySettings, function($setting) {
                return !$setting['is_sensitive'];
            });
        }
        
        return $filtered;
    }
    
    /**
     * Test database connection
     */
    private function testDatabaseConnection(): array
    {
        try {
            $db = \Skpassegna\GuardgeoApi\Database\DatabaseConnection::getInstance();
            $result = $db->query("SELECT 1 as test");
            $row = $result->fetch(\PDO::FETCH_ASSOC);
            
            return [
                'status' => 'success',
                'message' => 'Database connection successful',
                'response_time' => 0 // Could add timing if needed
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Test Freemius API connection
     */
    private function testFreemiusConnection(): array
    {
        try {
            $freemiusService = new \Skpassegna\GuardgeoApi\Services\FreemiusService();
            // This would need a test method in FreemiusService
            
            return [
                'status' => 'success',
                'message' => 'Freemius API connection successful'
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Freemius API connection failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Test ipRegistry API connection
     */
    private function testIpRegistryConnection(): array
    {
        try {
            $ipService = new \Skpassegna\GuardgeoApi\Services\IpRegistryService();
            // This would need a test method in IpRegistryService
            
            return [
                'status' => 'success',
                'message' => 'ipRegistry API connection successful'
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'ipRegistry API connection failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get overall status from test results
     */
    private function getOverallStatus(array $results): string
    {
        $statuses = array_column($results, 'status');
        
        if (in_array('error', $statuses)) {
            return 'error';
        }
        
        return 'success';
    }
}