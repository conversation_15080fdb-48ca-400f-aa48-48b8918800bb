<?php

namespace Skpassegna\GuardgeoApi\Database;

use PDO;
use PDOException;
use Skpassegna\GuardgeoApi\Utils\Logger;

/**
 * Database Connection Manager
 * 
 * Handles PostgreSQL database connections with connection pooling,
 * error handling, and logging mechanisms.
 */
class DatabaseConnection
{
    private static ?PDO $connection = null;
    private static array $config = [];
    private static Logger $logger;
    
    /**
     * Initialize database configuration
     */
    public static function initialize(array $config): void
    {
        self::$config = array_merge([
            'host' => 'localhost',
            'port' => 5432,
            'database' => 'guardgeo_admin',
            'username' => 'postgres',
            'password' => '',
            'charset' => 'utf8',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => true, // Connection pooling
                PDO::ATTR_TIMEOUT => 30,
            ]
        ], $config);
        
        self::$logger = new Logger();
    }
    
    /**
     * Get database connection instance
     */
    public static function getConnection(): PDO
    {
        if (self::$connection === null) {
            self::connect();
        }
        
        // Test connection and reconnect if needed
        try {
            self::$connection->query('SELECT 1');
        } catch (PDOException $e) {
            self::$logger->error('Database connection lost, reconnecting', [
                'error' => $e->getMessage()
            ]);
            self::connect();
        }
        
        return self::$connection;
    }
    
    /**
     * Establish database connection
     */
    private static function connect(): void
    {
        try {
            $dsn = sprintf(
                'pgsql:host=%s;port=%d;dbname=%s;charset=%s',
                self::$config['host'],
                self::$config['port'],
                self::$config['database'],
                self::$config['charset']
            );
            
            self::$connection = new PDO(
                $dsn,
                self::$config['username'],
                self::$config['password'],
                self::$config['options']
            );
            
            self::$logger->info('Database connection established', [
                'host' => self::$config['host'],
                'database' => self::$config['database']
            ]);
            
        } catch (PDOException $e) {
            self::$logger->error('Database connection failed', [
                'error' => $e->getMessage(),
                'host' => self::$config['host'],
                'database' => self::$config['database']
            ]);
            
            throw new DatabaseException(
                'Failed to connect to database: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }
    
    /**
     * Execute a prepared statement with parameters
     */
    public static function execute(string $sql, array $params = []): \PDOStatement
    {
        try {
            $connection = self::getConnection();
            $statement = $connection->prepare($sql);
            
            // Log query for debugging (without sensitive data)
            self::$logger->debug('Executing database query', [
                'sql' => $sql,
                'param_count' => count($params)
            ]);
            
            $statement->execute($params);
            return $statement;
            
        } catch (PDOException $e) {
            self::$logger->error('Database query execution failed', [
                'sql' => $sql,
                'error' => $e->getMessage(),
                'param_count' => count($params)
            ]);
            
            throw new DatabaseException(
                'Query execution failed: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }
    
    /**
     * Begin database transaction
     */
    public static function beginTransaction(): bool
    {
        try {
            $result = self::getConnection()->beginTransaction();
            self::$logger->debug('Database transaction started');
            return $result;
        } catch (PDOException $e) {
            self::$logger->error('Failed to start transaction', [
                'error' => $e->getMessage()
            ]);
            throw new DatabaseException('Failed to start transaction: ' . $e->getMessage());
        }
    }
    
    /**
     * Commit database transaction
     */
    public static function commit(): bool
    {
        try {
            $result = self::getConnection()->commit();
            self::$logger->debug('Database transaction committed');
            return $result;
        } catch (PDOException $e) {
            self::$logger->error('Failed to commit transaction', [
                'error' => $e->getMessage()
            ]);
            throw new DatabaseException('Failed to commit transaction: ' . $e->getMessage());
        }
    }
    
    /**
     * Rollback database transaction
     */
    public static function rollback(): bool
    {
        try {
            $result = self::getConnection()->rollback();
            self::$logger->debug('Database transaction rolled back');
            return $result;
        } catch (PDOException $e) {
            self::$logger->error('Failed to rollback transaction', [
                'error' => $e->getMessage()
            ]);
            throw new DatabaseException('Failed to rollback transaction: ' . $e->getMessage());
        }
    }
    
    /**
     * Get the last inserted ID
     */
    public static function lastInsertId(string $sequence = null): string
    {
        return self::getConnection()->lastInsertId($sequence);
    }
    
    /**
     * Check if we're currently in a transaction
     */
    public static function inTransaction(): bool
    {
        return self::getConnection()->inTransaction();
    }
    
    /**
     * Close database connection
     */
    public static function close(): void
    {
        if (self::$connection !== null) {
            self::$connection = null;
            self::$logger->info('Database connection closed');
        }
    }
    
    /**
     * Get connection statistics for monitoring
     */
    public static function getConnectionStats(): array
    {
        $connection = self::getConnection();
        
        return [
            'server_info' => $connection->getAttribute(PDO::ATTR_SERVER_INFO),
            'connection_status' => $connection->getAttribute(PDO::ATTR_CONNECTION_STATUS),
            'in_transaction' => $connection->inTransaction(),
            'config' => [
                'host' => self::$config['host'],
                'database' => self::$config['database'],
                'persistent' => self::$config['options'][PDO::ATTR_PERSISTENT] ?? false
            ]
        ];
    }
    
    /**
     * Perform database health check
     */
    public static function healthCheck(): array
    {
        try {
            $startTime = microtime(true);
            
            // Test basic connectivity
            $connection = self::getConnection();
            $statement = $connection->query('SELECT 1 as test, NOW() as server_time');
            $result = $statement->fetch();
            
            $responseTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds
            
            // Get database version and size
            $versionStatement = $connection->query('SELECT version() as version');
            $versionResult = $versionStatement->fetch();
            
            $sizeStatement = $connection->query('SELECT pg_size_pretty(pg_database_size(current_database())) as size');
            $sizeResult = $sizeStatement->fetch();
            
            // Get connection count
            $connStatement = $connection->query('SELECT count(*) as connections FROM pg_stat_activity WHERE datname = current_database()');
            $connResult = $connStatement->fetch();
            
            return [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'server_time' => $result['server_time'],
                'version' => $versionResult['version'],
                'database_size' => $sizeResult['size'],
                'active_connections' => (int)$connResult['connections'],
                'config' => [
                    'host' => self::$config['host'],
                    'database' => self::$config['database'],
                    'port' => self::$config['port']
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
        } catch (\Exception $e) {
            self::$logger->error('Database health check failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    /**
     * Execute database maintenance operations
     */
    public static function runMaintenance(): array
    {
        try {
            $connection = self::getConnection();
            
            // Call the database maintenance function
            $statement = $connection->query('SELECT run_maintenance() as result');
            $result = $statement->fetch();
            
            $maintenanceResult = json_decode($result['result'], true);
            
            self::$logger->info('Database maintenance completed', $maintenanceResult);
            
            return $maintenanceResult;
            
        } catch (\Exception $e) {
            self::$logger->error('Database maintenance failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    /**
     * Get database performance metrics
     */
    public static function getPerformanceMetrics(): array
    {
        try {
            $connection = self::getConnection();
            
            // Get table sizes
            $tableSizeQuery = "
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            ";
            
            $statement = $connection->query($tableSizeQuery);
            $tableSizes = $statement->fetchAll();
            
            // Get index usage statistics
            $indexQuery = "
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    idx_scan,
                    idx_tup_read,
                    idx_tup_fetch
                FROM pg_stat_user_indexes
                ORDER BY idx_scan DESC
                LIMIT 10
            ";
            
            $indexStatement = $connection->query($indexQuery);
            $indexStats = $indexStatement->fetchAll();
            
            return [
                'table_sizes' => $tableSizes,
                'top_indexes' => $indexStats,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
        } catch (\Exception $e) {
            self::$logger->error('Failed to get performance metrics', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    /**
     * Validate database integrity
     */
    public static function validateIntegrity(): array
    {
        try {
            $connection = self::getConnection();
            
            // Call the database integrity validation function
            $statement = $connection->query('SELECT validate_database_integrity() as result');
            $result = $statement->fetch();
            
            return json_decode($result['result'], true);
            
        } catch (\Exception $e) {
            self::$logger->error('Database integrity validation failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
    }
}