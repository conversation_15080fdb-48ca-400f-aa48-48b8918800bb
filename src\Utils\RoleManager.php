<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Models\AdminUserModel;

/**
 * Role Manager
 * 
 * Manages role-based access control for Super Admin, Dev, Marketing, and Sales roles
 * with comprehensive permission checking and feature access restrictions.
 */
class RoleManager
{
    // Permission categories
    public const CATEGORY_DASHBOARD = 'dashboard';
    public const CATEGORY_USERS = 'users';
    public const CATEGORY_IP_INTELLIGENCE = 'ip_intelligence';
    public const CATEGORY_FREEMIUS = 'freemius';
    public const CATEGORY_LOGS = 'logs';
    public const CATEGORY_SETTINGS = 'settings';
    public const CATEGORY_SYSTEM = 'system';

    // Permission actions
    public const ACTION_VIEW = 'view';
    public const ACTION_CREATE = 'create';
    public const ACTION_EDIT = 'edit';
    public const ACTION_DELETE = 'delete';
    public const ACTION_MANAGE = 'manage';
    public const ACTION_EXPORT = 'export';
    public const ACTION_MAINTENANCE = 'maintenance';

    /**
     * Role permission matrix
     * Defines what each role can do
     */
    private array $rolePermissions = [
        AdminUserModel::ROLE_SUPER_ADMIN => [
            'dashboard' => ['view'],
            'users' => ['view', 'create', 'edit', 'delete'],
            'ip_intelligence' => ['view', 'manage', 'export'],
            'freemius' => ['view', 'manage', 'export'],
            'logs' => ['view', 'export'],
            'settings' => ['view', 'edit'],
            'system' => ['maintenance']
        ],
        AdminUserModel::ROLE_DEV => [
            'dashboard' => ['view'],
            'ip_intelligence' => ['view', 'manage'],
            'freemius' => ['view', 'manage'],
            'logs' => ['view'],
            'system' => ['maintenance']
        ],
        AdminUserModel::ROLE_MARKETING => [
            'dashboard' => ['view'],
            'ip_intelligence' => ['view'],
            'freemius' => ['view'],
            'logs' => ['view']
        ],
        AdminUserModel::ROLE_SALES => [
            'dashboard' => ['view'],
            'ip_intelligence' => ['view'],
            'freemius' => ['view'],
            'logs' => ['view']
        ]
    ];

    /**
     * Feature access matrix
     * Defines which features each role can access
     */
    private array $featureAccess = [
        AdminUserModel::ROLE_SUPER_ADMIN => [
            'admin_dashboard',
            'user_management',
            'ip_intelligence_viewer',
            'ip_intelligence_manager',
            'freemius_viewer',
            'freemius_manager',
            'log_viewer',
            'log_exporter',
            'system_settings',
            'system_maintenance'
        ],
        AdminUserModel::ROLE_DEV => [
            'admin_dashboard',
            'ip_intelligence_viewer',
            'ip_intelligence_manager',
            'freemius_viewer',
            'freemius_manager',
            'log_viewer',
            'system_maintenance'
        ],
        AdminUserModel::ROLE_MARKETING => [
            'admin_dashboard',
            'ip_intelligence_viewer',
            'freemius_viewer',
            'log_viewer'
        ],
        AdminUserModel::ROLE_SALES => [
            'admin_dashboard',
            'ip_intelligence_viewer',
            'freemius_viewer',
            'log_viewer'
        ]
    ];

    /**
     * Check if user has specific permission
     *
     * @param string $role
     * @param string $category
     * @param string $action
     * @return bool
     */
    public function hasPermission(string $role, string $category, string $action): bool
    {
        if (!isset($this->rolePermissions[$role])) {
            return false;
        }

        if (!isset($this->rolePermissions[$role][$category])) {
            return false;
        }

        return in_array($action, $this->rolePermissions[$role][$category]);
    }

    /**
     * Check if user can access specific feature
     *
     * @param string $role
     * @param string $feature
     * @return bool
     */
    public function canAccessFeature(string $role, string $feature): bool
    {
        if (!isset($this->featureAccess[$role])) {
            return false;
        }

        return in_array($feature, $this->featureAccess[$role]);
    }

    /**
     * Get all permissions for a role
     *
     * @param string $role
     * @return array
     */
    public function getRolePermissions(string $role): array
    {
        return $this->rolePermissions[$role] ?? [];
    }

    /**
     * Get all features accessible by a role
     *
     * @param string $role
     * @return array
     */
    public function getRoleFeatures(string $role): array
    {
        return $this->featureAccess[$role] ?? [];
    }

    /**
     * Get formatted permission list for a role
     *
     * @param string $role
     * @return array
     */
    public function getFormattedPermissions(string $role): array
    {
        $permissions = [];
        $rolePerms = $this->getRolePermissions($role);

        foreach ($rolePerms as $category => $actions) {
            foreach ($actions as $action) {
                $permissions[] = "{$category}.{$action}";
            }
        }

        return $permissions;
    }

    /**
     * Check if role is valid
     *
     * @param string $role
     * @return bool
     */
    public function isValidRole(string $role): bool
    {
        return in_array($role, AdminUserModel::VALID_ROLES);
    }

    /**
     * Get role hierarchy level (higher number = more permissions)
     *
     * @param string $role
     * @return int
     */
    public function getRoleLevel(string $role): int
    {
        return match ($role) {
            AdminUserModel::ROLE_SUPER_ADMIN => 100,
            AdminUserModel::ROLE_DEV => 75,
            AdminUserModel::ROLE_MARKETING => 50,
            AdminUserModel::ROLE_SALES => 50,
            default => 0
        };
    }

    /**
     * Check if role A has higher privileges than role B
     *
     * @param string $roleA
     * @param string $roleB
     * @return bool
     */
    public function hasHigherPrivileges(string $roleA, string $roleB): bool
    {
        return $this->getRoleLevel($roleA) > $this->getRoleLevel($roleB);
    }

    /**
     * Get roles that can be managed by given role
     *
     * @param string $role
     * @return array
     */
    public function getManageableRoles(string $role): array
    {
        if ($role === AdminUserModel::ROLE_SUPER_ADMIN) {
            return AdminUserModel::VALID_ROLES;
        }

        // Only Super Admin can manage other users
        return [];
    }

    /**
     * Check if user can manage another user
     *
     * @param string $managerRole
     * @param string $targetRole
     * @return bool
     */
    public function canManageUser(string $managerRole, string $targetRole): bool
    {
        // Only Super Admin can manage users
        if ($managerRole !== AdminUserModel::ROLE_SUPER_ADMIN) {
            return false;
        }

        // Super Admin can manage all roles
        return $this->isValidRole($targetRole);
    }

    /**
     * Get navigation menu items for role
     *
     * @param string $role
     * @return array
     */
    public function getNavigationMenu(string $role): array
    {
        $menu = [];

        // Dashboard (all roles)
        if ($this->canAccessFeature($role, 'admin_dashboard')) {
            $menu[] = [
                'id' => 'dashboard',
                'title' => 'Dashboard',
                'url' => '/admin/dashboard',
                'icon' => 'dashboard',
                'order' => 1
            ];
        }

        // IP Intelligence
        if ($this->canAccessFeature($role, 'ip_intelligence_viewer')) {
            $menu[] = [
                'id' => 'ip_intelligence',
                'title' => 'IP Intelligence',
                'url' => '/admin/ip-intelligence',
                'icon' => 'globe',
                'order' => 2
            ];
        }

        // Freemius Integration
        if ($this->canAccessFeature($role, 'freemius_viewer')) {
            $menu[] = [
                'id' => 'freemius',
                'title' => 'Freemius',
                'url' => '/admin/freemius',
                'icon' => 'plugin',
                'order' => 3
            ];
        }

        // Logs
        if ($this->canAccessFeature($role, 'log_viewer')) {
            $menu[] = [
                'id' => 'logs',
                'title' => 'Logs',
                'url' => '/admin/logs',
                'icon' => 'file-text',
                'order' => 4
            ];
        }

        // User Management (Super Admin only)
        if ($this->canAccessFeature($role, 'user_management')) {
            $menu[] = [
                'id' => 'users',
                'title' => 'Users',
                'url' => '/admin/users',
                'icon' => 'users',
                'order' => 5
            ];
        }

        // Settings (Super Admin only)
        if ($this->canAccessFeature($role, 'system_settings')) {
            $menu[] = [
                'id' => 'settings',
                'title' => 'Settings',
                'url' => '/admin/settings',
                'icon' => 'settings',
                'order' => 6
            ];
        }

        // Sort by order
        usort($menu, fn($a, $b) => $a['order'] <=> $b['order']);

        return $menu;
    }

    /**
     * Get role-specific dashboard widgets
     *
     * @param string $role
     * @return array
     */
    public function getDashboardWidgets(string $role): array
    {
        $widgets = [];

        // System Overview (all roles)
        $widgets[] = [
            'id' => 'system_overview',
            'title' => 'System Overview',
            'component' => 'SystemOverviewWidget',
            'size' => 'large',
            'order' => 1
        ];

        // API Usage Stats (all roles)
        $widgets[] = [
            'id' => 'api_usage',
            'title' => 'API Usage',
            'component' => 'ApiUsageWidget',
            'size' => 'medium',
            'order' => 2
        ];

        // IP Intelligence Stats (all roles)
        if ($this->canAccessFeature($role, 'ip_intelligence_viewer')) {
            $widgets[] = [
                'id' => 'ip_stats',
                'title' => 'IP Intelligence',
                'component' => 'IpStatsWidget',
                'size' => 'medium',
                'order' => 3
            ];
        }

        // Freemius Stats (all roles)
        if ($this->canAccessFeature($role, 'freemius_viewer')) {
            $widgets[] = [
                'id' => 'freemius_stats',
                'title' => 'Freemius Integration',
                'component' => 'FreemiusStatsWidget',
                'size' => 'medium',
                'order' => 4
            ];
        }

        // Recent Activity (all roles)
        $widgets[] = [
            'id' => 'recent_activity',
            'title' => 'Recent Activity',
            'component' => 'RecentActivityWidget',
            'size' => 'large',
            'order' => 5
        ];

        // User Management (Super Admin only)
        if ($this->canAccessFeature($role, 'user_management')) {
            $widgets[] = [
                'id' => 'user_management',
                'title' => 'User Management',
                'component' => 'UserManagementWidget',
                'size' => 'medium',
                'order' => 6
            ];
        }

        // System Health (Dev and Super Admin)
        if ($this->canAccessFeature($role, 'system_maintenance')) {
            $widgets[] = [
                'id' => 'system_health',
                'title' => 'System Health',
                'component' => 'SystemHealthWidget',
                'size' => 'medium',
                'order' => 7
            ];
        }

        // Sort by order
        usort($widgets, fn($a, $b) => $a['order'] <=> $b['order']);

        return $widgets;
    }

    /**
     * Get role description
     *
     * @param string $role
     * @return string
     */
    public function getRoleDescription(string $role): string
    {
        return match ($role) {
            AdminUserModel::ROLE_SUPER_ADMIN => 'Full system access with user management and system configuration capabilities',
            AdminUserModel::ROLE_DEV => 'Technical access with IP intelligence and Freemius management, plus system maintenance',
            AdminUserModel::ROLE_MARKETING => 'Read-only access to IP intelligence, Freemius data, and system logs for marketing insights',
            AdminUserModel::ROLE_SALES => 'Read-only access to IP intelligence, Freemius data, and system logs for sales insights',
            default => 'Unknown role'
        };
    }

    /**
     * Get all available roles with descriptions
     *
     * @return array
     */
    public function getAllRoles(): array
    {
        $roles = [];

        foreach (AdminUserModel::VALID_ROLES as $role) {
            $roles[] = [
                'value' => $role,
                'label' => (new AdminUserModel())->setRole($role)->getRoleDisplayName(),
                'description' => $this->getRoleDescription($role),
                'level' => $this->getRoleLevel($role),
                'permissions' => $this->getFormattedPermissions($role),
                'features' => $this->getRoleFeatures($role)
            ];
        }

        // Sort by level (highest first)
        usort($roles, fn($a, $b) => $b['level'] <=> $a['level']);

        return $roles;
    }

    /**
     * Validate role transition (for role changes)
     *
     * @param string $currentRole
     * @param string $newRole
     * @param string $changerRole
     * @return array Validation result
     */
    public function validateRoleTransition(string $currentRole, string $newRole, string $changerRole): array
    {
        $result = [
            'valid' => false,
            'errors' => []
        ];

        // Only Super Admin can change roles
        if ($changerRole !== AdminUserModel::ROLE_SUPER_ADMIN) {
            $result['errors'][] = 'Only Super Administrators can change user roles';
            return $result;
        }

        // Validate new role
        if (!$this->isValidRole($newRole)) {
            $result['errors'][] = 'Invalid role specified';
            return $result;
        }

        // Validate current role
        if (!$this->isValidRole($currentRole)) {
            $result['errors'][] = 'Current role is invalid';
            return $result;
        }

        $result['valid'] = true;
        return $result;
    }
}