<?php

namespace Skpassegna\GuardgeoApi\Views\Pages;

use Skpassegna\GuardgeoApi\Views\Templates\BaseTemplate;
use Skpassegna\GuardgeoApi\Views\Components\Card;
use Skpassegna\GuardgeoApi\Views\Components\Button;

/**
 * Dashboard Page Template
 * 
 * Main dashboard page with system overview, metrics, and recent activity
 * using the component-based design system.
 */
class DashboardPage extends BaseTemplate
{
    public function render(): string
    {
        $widgets = $this->get('widgets', []);
        
        return $this->renderMetricsGrid() . 
               $this->renderChartsSection() . 
               $this->renderRecentActivity() . 
               $this->renderDashboardScript();
    }

    private function renderMetricsGrid(): string
    {
        $systemStatusCard = Card::stat([
            'icon' => 'fas fa-server',
            'iconColor' => 'bg-gray-300',
            'title' => 'System Status',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('system-status-card');

        $apiRequestsCard = Card::stat([
            'icon' => 'fas fa-chart-line',
            'iconColor' => 'bg-blue-500',
            'title' => 'API Requests',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('api-requests-card');

        $ipIntelligenceCard = Card::stat([
            'icon' => 'fas fa-globe',
            'iconColor' => 'bg-purple-500',
            'title' => 'IP Intelligence',
            'value' => 'Loading...',
            'subtitle' => ''
        ])->addClass('ip-intelligence-card');

        return <<<HTML
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {$systemStatusCard->render()}
            {$apiRequestsCard->render()}
            {$ipIntelligenceCard->render()}
        </div>
HTML;
    }

    private function renderChartsSection(): string
    {
        $apiUsageCard = (new Card([
            'title' => 'API Usage (This Week)',
            'content' => '<div id="apiUsageChart" class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                <p class="text-gray-500">Loading API usage data...</p>
            </div>'
        ]))->render();

        $ipCacheCard = (new Card([
            'title' => 'IP Intelligence Cache',
            'content' => '<div id="ipCacheChart" class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                <p class="text-gray-500">Loading cache data...</p>
            </div>'
        ]))->render();

        return <<<HTML
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {$apiUsageCard}
            {$ipCacheCard}
        </div>
HTML;
    }

    private function renderRecentActivity(): string
    {
        $recentActivityCard = (new Card([
            'title' => 'Recent Activity',
            'content' => '<div id="recentActivityContainer">
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
                    <p class="text-gray-500">Loading recent activity...</p>
                </div>
            </div>'
        ]))->render();

        return $recentActivityCard;
    }

    private function renderDashboardScript(): string
    {
        return <<<HTML
        <script>
            class DashboardManager {
                constructor() {
                    this.refreshInterval = 30000; // 30 seconds
                    this.init();
                }

                init() {
                    this.loadDashboardData();
                    this.startAutoRefresh();
                }

                startAutoRefresh() {
                    setInterval(() => {
                        this.loadDashboardData();
                    }, this.refreshInterval);
                }

                async loadDashboardData() {
                    try {
                        await Promise.all([
                            this.loadSystemOverview(),
                            this.loadApiUsageStats(),
                            this.loadIpIntelligenceStats(),
                            this.loadRecentActivity()
                        ]);
                    } catch (error) {
                        console.error('Error loading dashboard data:', error);
                    }
                }

                async loadSystemOverview() {
                    try {
                        const response = await fetch('/admin/api/dashboard/overview');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateSystemOverview(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading system overview:', error);
                    }
                }

                async loadApiUsageStats() {
                    try {
                        const response = await fetch('/admin/api/dashboard/api-usage?period=week');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateApiUsageChart(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading API usage stats:', error);
                    }
                }

                async loadIpIntelligenceStats() {
                    try {
                        const response = await fetch('/admin/api/dashboard/ip-stats');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateIpIntelligenceChart(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading IP intelligence stats:', error);
                    }
                }

                async loadRecentActivity() {
                    try {
                        const response = await fetch('/admin/api/dashboard/activity?limit=10');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.updateRecentActivity(data.data);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading recent activity:', error);
                    }
                }

                updateSystemOverview(data) {
                    const statusCard = document.querySelector('.system-status-card');
                    if (!statusCard) return;

                    const iconElement = statusCard.querySelector('.w-8.h-8');
                    const valueElement = statusCard.querySelector('.text-2xl');
                    const subtitleElement = statusCard.querySelector('.text-sm.text-gray-600');

                    if (data.system_status === 'operational') {
                        iconElement.className = 'w-8 h-8 bg-green-500 rounded-full flex items-center justify-center';
                        iconElement.innerHTML = '<i class="fas fa-check text-white text-sm"></i>';
                        valueElement.textContent = 'Operational';
                        valueElement.className = 'text-2xl font-bold text-green-600';
                        subtitleElement.textContent = 'All systems running';
                    } else {
                        iconElement.className = 'w-8 h-8 bg-red-500 rounded-full flex items-center justify-center';
                        iconElement.innerHTML = '<i class="fas fa-exclamation text-white text-sm"></i>';
                        valueElement.textContent = 'Error';
                        valueElement.className = 'text-2xl font-bold text-red-600';
                        subtitleElement.textContent = 'System issues detected';
                    }

                    // Update API requests card
                    const apiCard = document.querySelector('.api-requests-card');
                    if (apiCard && data.api_requests) {
                        const apiValue = apiCard.querySelector('.text-2xl');
                        const apiSubtitle = apiCard.querySelector('.text-sm.text-gray-600');
                        apiValue.textContent = data.api_requests.today;
                        apiSubtitle.textContent = data.api_requests.week + ' this week';
                    }

                    // Update IP intelligence card
                    const ipCard = document.querySelector('.ip-intelligence-card');
                    if (ipCard && data.ip_intelligence) {
                        const ipValue = ipCard.querySelector('.text-2xl');
                        const ipSubtitle = ipCard.querySelector('.text-sm.text-gray-600');
                        ipValue.textContent = data.ip_intelligence.total_records;
                        ipSubtitle.textContent = 'Cache: ' + data.ip_intelligence.cache_status;
                    }
                }

                updateApiUsageChart(data) {
                    const container = document.getElementById('apiUsageChart');
                    if (!container) return;

                    if (data.total_requests === 0) {
                        container.innerHTML = '<p class="text-gray-500 text-center py-8">No API requests this week</p>';
                        return;
                    }

                    const successRate = data.total_requests > 0 ? Math.round((data.success_requests / data.total_requests) * 100) : 0;

                    container.innerHTML = `
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">Total Requests</span>
                                <span class="text-lg font-bold text-gray-900">\${data.total_requests}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">Success Rate</span>
                                <span class="text-lg font-bold text-green-600">\${successRate}%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">Avg Response Time</span>
                                <span class="text-lg font-bold text-blue-600">\${data.avg_response_time}ms</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: \${successRate}%"></div>
                            </div>
                        </div>
                    `;
                }

                updateIpIntelligenceChart(data) {
                    const container = document.getElementById('ipCacheChart');
                    if (!container) return;

                    if (data.total_records === 0) {
                        container.innerHTML = '<p class="text-gray-500 text-center py-8">No IP records cached</p>';
                        return;
                    }

                    container.innerHTML = `
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">Total Records</span>
                                <span class="text-lg font-bold text-gray-900">\${data.total_records}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">Fresh Records</span>
                                <span class="text-lg font-bold text-green-600">\${data.fresh_records}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">Cache Efficiency</span>
                                <span class="text-lg font-bold text-purple-600">\${data.cache_efficiency}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: \${data.cache_efficiency}%"></div>
                            </div>
                        </div>
                    `;
                }

                updateRecentActivity(activities) {
                    const container = document.getElementById('recentActivityContainer');
                    if (!container) return;

                    if (activities.length === 0) {
                        container.innerHTML = '<p class="text-gray-500 text-center py-8">No recent activity</p>';
                        return;
                    }

                    let html = '<div class="space-y-4">';

                    activities.forEach(activity => {
                        const levelColor = activity.level === 'error' ? 'text-red-600' : 
                                         activity.level === 'warning' ? 'text-yellow-600' : 'text-gray-600';
                        const typeIcon = activity.type === 'api' ? 'fa-plug' : 'fa-user-shield';

                        html += `
                            <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <i class="fas \${typeIcon} \${levelColor}"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">\${activity.message}</p>
                                    <p class="text-xs text-gray-500">\${activity.formatted_time}</p>
                                    \${activity.ip ? `<p class="text-xs text-gray-400">IP: \${activity.ip}</p>` : ''}
                                </div>
                            </div>
                        `;
                    });

                    html += '</div>';
                    container.innerHTML = html;
                }
            }

            // Initialize dashboard when DOM is loaded
            document.addEventListener('DOMContentLoaded', function() {
                new DashboardManager();
            });
        </script>
HTML;
    }
}