<?php

namespace Skpassegna\GuardgeoApi\Utils;

class RequestValidator
{
    /**
     * Validate the analyze API request parameters with comprehensive validation
     */
    public function validateAnalyzeRequest(array $data): array
    {
        $errors = [];
        $warnings = [];
        $valid = true;

        // Validate required fields first
        $requiredFields = ['ip', 'visitor_hash', 'plugin_id', 'install_id', 'url'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || $data[$field] === '' || $data[$field] === null) {
                $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
                $valid = false;
            }
        }

        // If required fields are missing, return early
        if (!$valid) {
            return [
                'valid' => false,
                'errors' => $errors,
                'warnings' => $warnings,
                'field_count' => count($data),
                'validation_summary' => 'Missing required fields'
            ];
        }

        // Validate IP address with detailed checks
        $ipValidation = $this->validateIpAddress($data['ip']);
        if (!$ipValidation['valid']) {
            $errors['ip'] = $ipValidation['error'];
            $valid = false;
        } else {
            if (isset($ipValidation['warnings'])) {
                $warnings['ip'] = $ipValidation['warnings'];
            }
        }

        // Validate visitor_hash with enhanced checks
        $visitorHashValidation = $this->validateVisitorHash($data['visitor_hash']);
        if (!$visitorHashValidation['valid']) {
            $errors['visitor_hash'] = $visitorHashValidation['error'];
            $valid = false;
        }

        // Validate plugin_id with range checks
        $pluginIdValidation = $this->validatePluginId($data['plugin_id']);
        if (!$pluginIdValidation['valid']) {
            $errors['plugin_id'] = $pluginIdValidation['error'];
            $valid = false;
        }

        // Validate install_id with range checks
        $installIdValidation = $this->validateInstallId($data['install_id']);
        if (!$installIdValidation['valid']) {
            $errors['install_id'] = $installIdValidation['error'];
            $valid = false;
        }

        // Validate URL with comprehensive checks
        $urlValidation = $this->validateUrl($data['url']);
        if (!$urlValidation['valid']) {
            $errors['url'] = $urlValidation['error'];
            $valid = false;
        } else {
            if (isset($urlValidation['warnings'])) {
                $warnings['url'] = $urlValidation['warnings'];
            }
        }

        // Check for unexpected fields
        $allowedFields = ['ip', 'visitor_hash', 'plugin_id', 'install_id', 'url'];
        $unexpectedFields = array_diff(array_keys($data), $allowedFields);
        if (!empty($unexpectedFields)) {
            $warnings['unexpected_fields'] = 'Unexpected fields found: ' . implode(', ', $unexpectedFields);
        }

        // Validate data types and formats
        $typeValidation = $this->validateDataTypes($data);
        if (!$typeValidation['valid']) {
            $errors = array_merge($errors, $typeValidation['errors']);
            $valid = false;
        }

        return [
            'valid' => $valid,
            'errors' => $errors,
            'warnings' => $warnings,
            'field_count' => count($data),
            'unexpected_fields' => $unexpectedFields ?? [],
            'validation_summary' => $valid ? 'All validations passed' : 'Validation failed with ' . count($errors) . ' errors'
        ];
    }

    /**
     * Enhanced IP address validation
     */
    private function validateIpAddress(string $ip): array
    {
        $result = ['valid' => true, 'warnings' => []];

        // Basic format validation
        if (!filter_var($ip, FILTER_VALIDATE_IP)) {
            return ['valid' => false, 'error' => 'Invalid IP address format'];
        }

        // Check for private/reserved IP ranges
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE)) {
            // Public IP - good
        } else {
            $result['warnings'][] = 'Private IP address detected';
        }

        // Check for reserved ranges
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_RES_RANGE)) {
            $result['warnings'][] = 'Reserved IP address range detected';
        }

        // Check IP version
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            $result['ip_version'] = 'IPv4';
        } elseif (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            $result['ip_version'] = 'IPv6';
        }

        // Check for localhost
        if (in_array($ip, ['127.0.0.1', '::1', 'localhost'])) {
            $result['warnings'][] = 'Localhost IP address detected';
        }

        return $result;
    }

    /**
     * Enhanced visitor hash validation
     */
    private function validateVisitorHash(string $hash): array
    {
        // Check length
        if (strlen($hash) < 8) {
            return ['valid' => false, 'error' => 'Visitor hash too short (minimum 8 characters)'];
        }

        if (strlen($hash) > 128) {
            return ['valid' => false, 'error' => 'Visitor hash too long (maximum 128 characters)'];
        }

        // Check format - allow alphanumeric, hyphens, underscores
        if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $hash)) {
            return ['valid' => false, 'error' => 'Visitor hash contains invalid characters (only alphanumeric, hyphens, and underscores allowed)'];
        }

        // Check for common patterns
        if (preg_match('/^[0-9]+$/', $hash)) {
            return ['valid' => false, 'error' => 'Visitor hash cannot be purely numeric'];
        }

        return ['valid' => true];
    }

    /**
     * Enhanced plugin ID validation
     */
    private function validatePluginId($pluginId): array
    {
        // Check if numeric
        if (!is_numeric($pluginId)) {
            return ['valid' => false, 'error' => 'Plugin ID must be numeric'];
        }

        $id = (int)$pluginId;

        // Check range
        if ($id <= 0) {
            return ['valid' => false, 'error' => 'Plugin ID must be a positive integer'];
        }

        if ($id > 999999999) {
            return ['valid' => false, 'error' => 'Plugin ID exceeds maximum allowed value'];
        }

        return ['valid' => true, 'normalized_value' => $id];
    }

    /**
     * Enhanced install ID validation
     */
    private function validateInstallId($installId): array
    {
        // Check if numeric
        if (!is_numeric($installId)) {
            return ['valid' => false, 'error' => 'Install ID must be numeric'];
        }

        $id = (int)$installId;

        // Check range
        if ($id <= 0) {
            return ['valid' => false, 'error' => 'Install ID must be a positive integer'];
        }

        if ($id > 999999999) {
            return ['valid' => false, 'error' => 'Install ID exceeds maximum allowed value'];
        }

        return ['valid' => true, 'normalized_value' => $id];
    }

    /**
     * Enhanced URL validation
     */
    private function validateUrl(string $url): array
    {
        $result = ['valid' => true, 'warnings' => []];

        // Basic URL validation
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return ['valid' => false, 'error' => 'Invalid URL format'];
        }

        // Parse URL components
        $parsed = parse_url($url);
        if ($parsed === false) {
            return ['valid' => false, 'error' => 'Unable to parse URL'];
        }

        // Check scheme
        if (!isset($parsed['scheme']) || !in_array(strtolower($parsed['scheme']), ['http', 'https'])) {
            return ['valid' => false, 'error' => 'URL must use HTTP or HTTPS protocol'];
        }

        // Warn about HTTP (not HTTPS)
        if (strtolower($parsed['scheme']) === 'http') {
            $result['warnings'][] = 'Non-secure HTTP protocol detected';
        }

        // Check host
        if (!isset($parsed['host']) || empty($parsed['host'])) {
            return ['valid' => false, 'error' => 'URL must contain a valid host'];
        }

        // Check for localhost/local development
        $localHosts = ['localhost', '127.0.0.1', '::1'];
        if (in_array(strtolower($parsed['host']), $localHosts) || 
            preg_match('/^192\.168\./', $parsed['host']) ||
            preg_match('/^10\./', $parsed['host']) ||
            preg_match('/^172\.(1[6-9]|2[0-9]|3[0-1])\./', $parsed['host'])) {
            $result['warnings'][] = 'Local/private network URL detected';
        }

        // Check URL length
        if (strlen($url) > 2048) {
            return ['valid' => false, 'error' => 'URL exceeds maximum length (2048 characters)'];
        }

        return $result;
    }

    /**
     * Validate data types
     */
    private function validateDataTypes(array $data): array
    {
        $errors = [];
        $valid = true;

        // Check that plugin_id and install_id are integers when converted
        if (isset($data['plugin_id']) && !is_numeric($data['plugin_id'])) {
            $errors['plugin_id_type'] = 'Plugin ID must be numeric';
            $valid = false;
        }

        if (isset($data['install_id']) && !is_numeric($data['install_id'])) {
            $errors['install_id_type'] = 'Install ID must be numeric';
            $valid = false;
        }

        // Check that strings are actually strings
        $stringFields = ['ip', 'visitor_hash', 'url'];
        foreach ($stringFields as $field) {
            if (isset($data[$field]) && !is_string($data[$field])) {
                $errors[$field . '_type'] = ucfirst(str_replace('_', ' ', $field)) . ' must be a string';
                $valid = false;
            }
        }

        return ['valid' => $valid, 'errors' => $errors];
    }

    /**
     * Validate IP address (IPv4 or IPv6)
     */
    private function isValidIpAddress(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * Validate visitor hash (should be a UUID-like string)
     */
    private function isValidVisitorHash(string $hash): bool
    {
        // Allow UUID format or similar hash strings (alphanumeric with hyphens)
        return preg_match('/^[a-zA-Z0-9\-_]{8,64}$/', $hash) === 1;
    }

    /**
     * Validate plugin ID (should be a positive integer)
     */
    private function isValidPluginId($pluginId): bool
    {
        return is_numeric($pluginId) && (int)$pluginId > 0;
    }

    /**
     * Validate install ID (should be a positive integer)
     */
    private function isValidInstallId($installId): bool
    {
        return is_numeric($installId) && (int)$installId > 0;
    }

    /**
     * Validate URL format
     */
    private function isValidUrl(string $url): bool
    {
        // Basic URL validation - must be HTTP or HTTPS
        return filter_var($url, FILTER_VALIDATE_URL) !== false && 
               (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0);
    }

    /**
     * Sanitize string input to prevent XSS and other attacks
     */
    public function sanitizeString(string $input): string
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Validate and sanitize integer input
     */
    public function sanitizeInteger($input): ?int
    {
        if (!is_numeric($input)) {
            return null;
        }
        
        return (int)filter_var($input, FILTER_SANITIZE_NUMBER_INT);
    }

    /**
     * Validate JSON structure for API requests
     */
    public function isValidJson(string $json): bool
    {
        json_decode($json);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Check if request content type is JSON
     */
    public function isJsonContentType(): bool
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        return strpos($contentType, 'application/json') !== false;
    }

    /**
     * Validate request method
     */
    public function isValidMethod(string $expectedMethod): bool
    {
        return $_SERVER['REQUEST_METHOD'] === strtoupper($expectedMethod);
    }

    /**
     * Get client IP address (considering proxies)
     */
    public function getClientIpAddress(): string
    {
        // Check for various proxy headers
        $ipKeys = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR'
        ];

        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = trim($_SERVER[$key]);
                
                // Handle comma-separated IPs (X-Forwarded-For can contain multiple IPs)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if ($this->isValidIpAddress($ip)) {
                    return $ip;
                }
            }
        }

        return '127.0.0.1'; // Fallback
    }
}