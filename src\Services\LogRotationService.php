<?php

namespace Skpassegna\GuardgeoApi\Services;

use DateTime;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Database\DatabaseException;
use Skpassegna\GuardgeoApi\Utils\Logger;

/**
 * Log Rotation Service
 * 
 * Implements comprehensive log rotation and retention policies for both
 * file-based and database logs. Provides automated cleanup, archiving,
 * and maintenance of log data according to configurable retention rules.
 */
class LogRotationService
{
    private Logger $logger;
    private array $config;
    
    // Default retention periods (in days)
    private const DEFAULT_RETENTION = [
        'system_logs' => 30,
        'api_requests' => 90,
        'file_logs' => 7,
        'error_logs' => 60
    ];
    
    // Default file size limits (in bytes)
    private const DEFAULT_SIZE_LIMITS = [
        'combined_log' => 50 * 1024 * 1024, // 50MB
        'error_log' => 20 * 1024 * 1024,    // 20MB
    ];
    
    // Archive compression settings
    private const COMPRESSION_ENABLED = true;
    private const ARCHIVE_FORMAT = 'gzip';
    
    public function __construct(array $config = [])
    {
        $this->logger = new Logger();
        $this->config = array_merge([
            'retention_days' => self::DEFAULT_RETENTION,
            'size_limits' => self::DEFAULT_SIZE_LIMITS,
            'archive_path' => $this->getArchivePath(),
            'compression' => self::COMPRESSION_ENABLED,
            'max_archives' => 12 // Keep 12 archived files
        ], $config);
    }
    
    /**
     * Perform complete log rotation and cleanup
     */
    public function performLogRotation(): array
    {
        $results = [
            'database_cleanup' => [],
            'file_rotation' => [],
            'archive_cleanup' => [],
            'errors' => [],
            'total_space_freed' => 0,
            'execution_time' => 0
        ];
        
        $startTime = microtime(true);
        
        try {
            // Clean up database logs
            $results['database_cleanup'] = $this->cleanupDatabaseLogs();
            
            // Rotate file logs
            $results['file_rotation'] = $this->rotateFileLogs();
            
            // Clean up old archives
            $results['archive_cleanup'] = $this->cleanupOldArchives();
            
            // Calculate total space freed
            $results['total_space_freed'] = 
                ($results['database_cleanup']['space_freed'] ?? 0) +
                ($results['file_rotation']['space_freed'] ?? 0) +
                ($results['archive_cleanup']['space_freed'] ?? 0);
            
            $results['execution_time'] = microtime(true) - $startTime;
            
            $this->logger->info('Log rotation completed successfully', $results);
            
        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            $this->logger->error('Log rotation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        return $results;
    }
    
    /**
     * Clean up old database log entries
     */
    public function cleanupDatabaseLogs(): array
    {
        $results = [
            'system_logs_deleted' => 0,
            'api_requests_deleted' => 0,
            'space_freed' => 0,
            'errors' => []
        ];
        
        try {
            // Clean system logs
            $systemLogsDeleted = $this->cleanupSystemLogs();
            $results['system_logs_deleted'] = $systemLogsDeleted;
            
            // Clean API request logs
            $apiRequestsDeleted = $this->cleanupApiRequestLogs();
            $results['api_requests_deleted'] = $apiRequestsDeleted;
            
            // Estimate space freed (rough calculation)
            $results['space_freed'] = ($systemLogsDeleted + $apiRequestsDeleted) * 1024; // 1KB per record estimate
            
            $this->logger->info('Database log cleanup completed', [
                'system_logs_deleted' => $systemLogsDeleted,
                'api_requests_deleted' => $apiRequestsDeleted
            ]);
            
        } catch (DatabaseException $e) {
            $results['errors'][] = "Database cleanup failed: " . $e->getMessage();
            $this->logger->error('Database log cleanup failed', [
                'error' => $e->getMessage()
            ]);
        }
        
        return $results;
    }
    
    /**
     * Rotate file-based logs
     */
    public function rotateFileLogs(): array
    {
        $results = [
            'files_rotated' => [],
            'archives_created' => [],
            'space_freed' => 0,
            'errors' => []
        ];
        
        $logFiles = [
            'combined' => $this->getLogPath('combined.log'),
            'error' => $this->getLogPath('error.log')
        ];
        
        foreach ($logFiles as $type => $filePath) {
            try {
                if (!file_exists($filePath)) {
                    continue;
                }
                
                $fileSize = filesize($filePath);
                $sizeLimit = $this->config['size_limits'][$type . '_log'] ?? 
                           self::DEFAULT_SIZE_LIMITS[$type . '_log'] ?? 
                           10 * 1024 * 1024; // 10MB default
                
                // Check if rotation is needed
                if ($fileSize > $sizeLimit) {
                    $archiveResult = $this->rotateLogFile($filePath, $type);
                    
                    if ($archiveResult['success']) {
                        $results['files_rotated'][] = $filePath;
                        $results['archives_created'][] = $archiveResult['archive_path'];
                        $results['space_freed'] += $archiveResult['space_freed'];
                    } else {
                        $results['errors'][] = $archiveResult['error'];
                    }
                }
                
            } catch (\Exception $e) {
                $results['errors'][] = "Failed to rotate $filePath: " . $e->getMessage();
            }
        }
        
        return $results;
    }
    
    /**
     * Clean up old archive files
     */
    public function cleanupOldArchives(): array
    {
        $results = [
            'archives_deleted' => [],
            'space_freed' => 0,
            'errors' => []
        ];
        
        try {
            $archivePath = $this->config['archive_path'];
            
            if (!is_dir($archivePath)) {
                return $results;
            }
            
            $maxArchives = $this->config['max_archives'];
            $archiveFiles = glob($archivePath . '/*.gz');
            
            if (count($archiveFiles) > $maxArchives) {
                // Sort by modification time (oldest first)
                usort($archiveFiles, function($a, $b) {
                    return filemtime($a) - filemtime($b);
                });
                
                // Delete oldest archives
                $filesToDelete = array_slice($archiveFiles, 0, count($archiveFiles) - $maxArchives);
                
                foreach ($filesToDelete as $file) {
                    $fileSize = filesize($file);
                    
                    if (unlink($file)) {
                        $results['archives_deleted'][] = basename($file);
                        $results['space_freed'] += $fileSize;
                    } else {
                        $results['errors'][] = "Failed to delete archive: " . basename($file);
                    }
                }
            }
            
        } catch (\Exception $e) {
            $results['errors'][] = "Archive cleanup failed: " . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Clean up system logs older than retention period
     */
    private function cleanupSystemLogs(): int
    {
        $retentionDays = $this->config['retention_days']['system_logs'];
        $cutoffDate = (new DateTime())->modify("-{$retentionDays} days")->format('Y-m-d H:i:s');
        
        $sql = "DELETE FROM system_logs WHERE created_at < ?";
        $statement = DatabaseConnection::execute($sql, [$cutoffDate]);
        
        return $statement->rowCount();
    }
    
    /**
     * Clean up API request logs older than retention period
     */
    private function cleanupApiRequestLogs(): int
    {
        $retentionDays = $this->config['retention_days']['api_requests'];
        $cutoffDate = (new DateTime())->modify("-{$retentionDays} days")->format('Y-m-d H:i:s');
        
        $sql = "DELETE FROM api_requests WHERE created_at < ?";
        $statement = DatabaseConnection::execute($sql, [$cutoffDate]);
        
        return $statement->rowCount();
    }
    
    /**
     * Rotate a single log file
     */
    private function rotateLogFile(string $filePath, string $type): array
    {
        $result = [
            'success' => false,
            'archive_path' => null,
            'space_freed' => 0,
            'error' => null
        ];
        
        try {
            $fileSize = filesize($filePath);
            $timestamp = date('Y-m-d_H-i-s');
            $archiveName = "{$type}_{$timestamp}.log";
            
            if ($this->config['compression']) {
                $archiveName .= '.gz';
            }
            
            $archivePath = $this->config['archive_path'] . DIRECTORY_SEPARATOR . $archiveName;
            
            // Ensure archive directory exists
            if (!is_dir($this->config['archive_path'])) {
                mkdir($this->config['archive_path'], 0755, true);
            }
            
            // Create archive
            if ($this->config['compression']) {
                $this->compressLogFile($filePath, $archivePath);
            } else {
                copy($filePath, $archivePath);
            }
            
            // Clear original log file
            file_put_contents($filePath, '');
            
            $result['success'] = true;
            $result['archive_path'] = $archivePath;
            $result['space_freed'] = $fileSize;
            
        } catch (\Exception $e) {
            $result['error'] = $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Compress log file using gzip
     */
    private function compressLogFile(string $sourcePath, string $archivePath): void
    {
        $sourceHandle = fopen($sourcePath, 'rb');
        $archiveHandle = gzopen($archivePath, 'wb9'); // Maximum compression
        
        if (!$sourceHandle || !$archiveHandle) {
            throw new \Exception("Failed to open files for compression");
        }
        
        try {
            while (!feof($sourceHandle)) {
                $chunk = fread($sourceHandle, 8192);
                gzwrite($archiveHandle, $chunk);
            }
        } finally {
            fclose($sourceHandle);
            gzclose($archiveHandle);
        }
    }
    
    /**
     * Get log statistics for monitoring
     */
    public function getLogStatistics(): array
    {
        $stats = [
            'database_logs' => $this->getDatabaseLogStats(),
            'file_logs' => $this->getFileLogStats(),
            'archives' => $this->getArchiveStats(),
            'retention_config' => $this->config['retention_days']
        ];
        
        return $stats;
    }
    
    /**
     * Get database log statistics
     */
    private function getDatabaseLogStats(): array
    {
        try {
            // System logs stats
            $systemLogsSql = "SELECT 
                COUNT(*) as total_count,
                COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as last_7_days,
                COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as last_30_days,
                MIN(created_at) as oldest_entry,
                MAX(created_at) as newest_entry
                FROM system_logs";
            
            $systemLogsStmt = DatabaseConnection::execute($systemLogsSql);
            $systemLogsStats = $systemLogsStmt->fetch();
            
            // API requests stats
            $apiRequestsSql = "SELECT 
                COUNT(*) as total_count,
                COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as last_7_days,
                COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as last_30_days,
                MIN(created_at) as oldest_entry,
                MAX(created_at) as newest_entry
                FROM api_requests";
            
            $apiRequestsStmt = DatabaseConnection::execute($apiRequestsSql);
            $apiRequestsStats = $apiRequestsStmt->fetch();
            
            return [
                'system_logs' => $systemLogsStats,
                'api_requests' => $apiRequestsStats
            ];
            
        } catch (DatabaseException $e) {
            return [
                'error' => 'Failed to retrieve database log statistics: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get file log statistics
     */
    private function getFileLogStats(): array
    {
        $stats = [];
        
        $logFiles = [
            'combined' => $this->getLogPath('combined.log'),
            'error' => $this->getLogPath('error.log')
        ];
        
        foreach ($logFiles as $type => $filePath) {
            if (file_exists($filePath)) {
                $stats[$type] = [
                    'size' => filesize($filePath),
                    'size_formatted' => $this->formatBytes(filesize($filePath)),
                    'lines' => count(file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES)),
                    'last_modified' => date('Y-m-d H:i:s', filemtime($filePath)),
                    'needs_rotation' => filesize($filePath) > ($this->config['size_limits'][$type . '_log'] ?? 0)
                ];
            } else {
                $stats[$type] = [
                    'exists' => false
                ];
            }
        }
        
        return $stats;
    }
    
    /**
     * Get archive statistics
     */
    private function getArchiveStats(): array
    {
        $archivePath = $this->config['archive_path'];
        
        if (!is_dir($archivePath)) {
            return [
                'archive_directory_exists' => false,
                'total_archives' => 0,
                'total_size' => 0
            ];
        }
        
        $archiveFiles = glob($archivePath . '/*');
        $totalSize = 0;
        $archiveDetails = [];
        
        foreach ($archiveFiles as $file) {
            if (is_file($file)) {
                $size = filesize($file);
                $totalSize += $size;
                
                $archiveDetails[] = [
                    'name' => basename($file),
                    'size' => $size,
                    'size_formatted' => $this->formatBytes($size),
                    'created' => date('Y-m-d H:i:s', filemtime($file))
                ];
            }
        }
        
        // Sort by creation time (newest first)
        usort($archiveDetails, function($a, $b) {
            return strtotime($b['created']) - strtotime($a['created']);
        });
        
        return [
            'archive_directory_exists' => true,
            'total_archives' => count($archiveDetails),
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'max_archives_allowed' => $this->config['max_archives'],
            'archives' => $archiveDetails
        ];
    }
    
    /**
     * Update retention configuration
     */
    public function updateRetentionConfig(array $newConfig): void
    {
        $this->config['retention_days'] = array_merge(
            $this->config['retention_days'],
            $newConfig
        );
        
        $this->logger->info('Log retention configuration updated', [
            'new_config' => $newConfig
        ]);
    }
    
    /**
     * Get default archive path
     */
    private function getArchivePath(): string
    {
        $rootDir = dirname(__DIR__, 2);
        return $rootDir . DIRECTORY_SEPARATOR . 'logs' . DIRECTORY_SEPARATOR . 'archives';
    }
    
    /**
     * Get log file path
     */
    private function getLogPath(string $filename): string
    {
        $rootDir = dirname(__DIR__, 2);
        return $rootDir . DIRECTORY_SEPARATOR . 'logs' . DIRECTORY_SEPARATOR . $filename;
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }
}