{"name": "skpassegna/guardgeo_api", "description": "GuardGeo Backend to interact with Freemius and ipRegistry", "type": "project", "require": {"guzzlehttp/guzzle": "^7.9", "monolog/monolog": "^3.9", "sentry/sentry": "^4.14"}, "license": "proprietary", "autoload": {"psr-4": {"Skpassegna\\GuardgeoApi\\": "src/"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require-dev": {"phpunit/phpunit": "^12"}}