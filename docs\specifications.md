# **GuardGeo-API Platform Specification**

**Type:** Private proprietary project
**Purpose:** Internal IP intelligence platform for GuardGeo, tightly integrated with [ipRegistry](https://ipregistry.co/) and the [Freemius API](https://freemius.com/).

---

## **1. Overview**

The **GuardGeo-API** platform is a **private** backend system designed exclusively for:

* The GuardGeo WordPress plugin
* Authorized **Admins** of GuardGeo (no public or consumer access)

Its primary purpose is to **validate incoming plugin requests via Freemius**, fetch and store **IP intelligence data** from ipRegistry, and return structured results.

There are **no** public registration pages or client accounts.
Every action (API, web, etc.) must be logged in detail.

---

## **2. Account Roles**

All roles are **Admin-level** (no consumer roles).

| Role            | Permissions                                                                                            |
| --------------- | ------------------------------------------------------------------------------------------------------ |
| **Super Admin** | Full access to all features, configurations, and platform setup. Can create/manage all other accounts. |
| **Dev**         | Developer access to code-related and technical admin tools.                                            |
| **Marketing**   | Access to marketing-related analytics and tools.                                                       |
| **Sales**       | Access to sales-related analytics and data.                                                            |

**Account Creation Rules:**

* Only the **Super Admin** account is inserted manually via SQL at setup.
* All other accounts must be created **exclusively** by the Super Admin.
* Authentication: Email (restricted to a configured domain only) + robust password (minimum **12 characters**).

---

## **3. Technology Stack & Restrictions**

* **Backend:** Pure PHP (no frameworks)
* **Allowed PHP Libraries:** `GuzzleHttp`, monolog/monolog, sentry/sentry, phpunit/phpunit (only)
* **Frontend:** HTML5, CSS3, TailwindCSS (CDN), Pure JavaScript, Icon Fonts, Google Fonts
* **Database:** PostgreSQL (schema provided in SQL format)
* **Cache/Queue:** Redis (for caching Freemius data and queueing webhook events)
* **Testing:** No automated tests; manual verification only
* **Architecture:** Production-grade, commercial-ready, following **world-class best practices**

---

## **4. Core Functionalities**

### **4.1 Internal REST API**

**Endpoint:**

```
POST /api/v1/analyze
```

**Request Parameters:**

| Field          | Type   | Description                                                            |
| -------------- | ------ | ---------------------------------------------------------------------- |
| `ip`           | string | IPv4/IPv6 of the visitor                                               |
| `visitor_hash` | string | UUID generated by the plugin (based on session & cookies)              |
| `plugin_id`    | int    | Freemius Product ID (from the plugin’s Freemius SDK)                   |
| `install_id`   | int    | Freemius Installation ID (from the plugin’s Freemius SDK)              |
| `url`          | string | WordPress site URL (must match Freemius API data for the installation) |

---

#### **Request Validation (via Freemius API)**

A request is accepted **only** if **all** conditions are met:

1. `plugin_id` exists and is an active product in Freemius
2. `install_id` belongs to the given `plugin_id`
3. Installation has an active paid subscription/license in Freemius
4. Installation is not uninstalled or inactive

**Freemius Cache:**

* Cached installation/product data in Redis
* Cache expiration: based on webhook updates or forced invalidation

---

#### **IP Data Retrieval Logic:**

1. **Check Database:** If IP data exists and is **less than 3 days old**, use cached data.
2. **If missing/stale:** Request updated data from ipRegistry.
3. **Store/Update:** Save new data in PostgreSQL, including the entire ipRegistry payload.

---

#### **API Response Format:**

* Always return **full IP data** (from cache or ipRegistry) if validation succeeds
* All responses must follow **world-class JSON REST API best practices** (clear codes, consistent error messages)

---

### **4.2 Database Requirements**

Must store:

* Complete IP data from ipRegistry
* API request logs (with request payload, status, and timestamps)
* Freemius data:

  * `products` table
  * `installations` table
  * Full compliance with Freemius terminology and specifications

---

### **4.3 Freemius Webhooks**

**Endpoint:**

```
POST /webhooks/freemius
```

**Workflow:**

1. Validate incoming webhook via HMAC
2. Push event to Redis Queue
3. Background Worker processes event:

   * Update relevant Freemius data in PostgreSQL
   * Invalidate affected cache entries

---

## **5. System Behavior & Flow**

### **5.1 Plugin Request Flow**

```mermaid
sequenceDiagram
    participant WP as WordPress Plugin
    participant API as GuardGeo-API
    participant FR as Freemius API
    participant IP as ipRegistry
    participant DB as Database
    participant Cache as Redis

    WP->>API: POST /api/v1/analyze
    API->>Cache: Check Freemius Cache
    alt Cache Hit
        Cache-->>API: Installation Data
    else Cache Miss
        API->>FR: Validate Installation
        FR-->>API: Installation Status
        API->>Cache: Store Installation Data
    end

    alt Valid Installation
        API->>DB: Check IP History
        alt Recent Data Available
            DB-->>API: Return Cached Data
        else Need Fresh Data
            API->>IP: Fetch IP Data
            IP-->>API: IP Registry Data
            API->>DB: Store New Data
        end
    else Invalid Installation
        API-->>WP: 403 Forbidden
    end
    API-->>WP: JSON Response
```

---

### **5.2 Webhook Processing Flow**

```mermaid
sequenceDiagram
    participant FR as Freemius
    participant API as GuardGeo-API
    participant Queue as Redis Queue
    participant Worker as Background Worker
    participant DB as Database
    participant Cache as Redis Cache

    FR->>API: POST /webhooks/freemius
    API->>API: Validate HMAC Signature
    API->>Queue: Enqueue Event
    Queue->>Worker: Process Event
    Worker->>DB: Update Installation Data
    Worker->>Cache: Invalidate Related Cache
```

---

## **6. Admin Web Interface**

**Access:**

* Authenticated Admins only (email + strong password)
* No public access

**Features:**

* Dashboard with API usage statistics (requests, successes, failures)
* Search and view logs of all API requests
* Search or manually add IP data (via ipRegistry request)
* Trigger manual refresh tasks:

  * Update all stored IPs
  * Sync product and installation data from Freemius

---

## **7. Additional Notes**

* This server **makes no “decisions”** other than validation and data retrieval—business logic is defined in GuardGeo docs
* All implementation must follow **production-grade coding, architecture, and design standards**
* Responses must be **commercial-level quality**
* Use `swagger-mcp` to read Freemius OpenAPI specifications for exact parameter and response structures