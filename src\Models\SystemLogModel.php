<?php

namespace Skpassegna\GuardgeoApi\Models;

use DateTime;

/**
 * System Log Model
 * 
 * Represents a system log entry with comprehensive logging capabilities.
 */
class SystemLogModel extends BaseModel
{
    // Properties
    public ?int $id = null;
    public ?string $type = null; // api, admin, error, system
    public ?string $level = null; // debug, info, warning, error, critical
    public ?string $message = null;
    public ?array $context = null;
    public ?string $ip = null;
    public ?int $user_id = null;
    public ?DateTime $created_at = null;

    /**
     * Constructor
     */
    public function __construct(array $data = [])
    {
        $this->id = $this->toNullableInt($data['id'] ?? null);
        $this->type = $this->toNullableString($data['type'] ?? null);
        $this->level = $this->toNullableString($data['level'] ?? null);
        $this->message = $this->toNullableString($data['message'] ?? null);
        $this->ip = $this->toNullableString($data['ip'] ?? null);
        $this->user_id = $this->toNullableInt($data['user_id'] ?? null);
        
        $this->created_at = isset($data['created_at']) 
            ? $this->dbToDateTime($data['created_at']) 
            : null;
        
        // Handle context data
        $this->context = $data['context'] ?? null;
        if (is_string($this->context)) {
            $this->context = json_decode($this->context, true);
        }
    }

    /**
     * Create from database row
     */
    public static function fromDatabaseRow(array $row): self
    {
        return new self($row);
    }

    /**
     * Create from log data
     */
    public static function fromLogData(string $type, string $level, string $message, array $context = [], ?string $ip = null, ?int $userId = null): self
    {
        return new self([
            'type' => $type,
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'ip' => $ip,
            'user_id' => $userId,
            'created_at' => (new DateTime())->format('Y-m-d H:i:s')
        ]);
    }

    /**
     * Comprehensive validation
     */
    public function validate(): array
    {
        $errors = [];

        // Required fields validation
        if ($error = $this->validateRequired($this->type, 'type')) {
            $errors[] = $error;
        } elseif ($error = $this->validateEnum($this->type, ModelValidator::LOG_TYPES, 'type')) {
            $errors[] = $error;
        }

        if ($error = $this->validateRequired($this->level, 'level')) {
            $errors[] = $error;
        } elseif ($error = $this->validateEnum($this->level, ModelValidator::LOG_LEVELS, 'level')) {
            $errors[] = $error;
        }

        if ($error = $this->validateRequired($this->message, 'message')) {
            $errors[] = $error;
        }

        // IP validation
        if ($this->ip && !ModelValidator::validateIpAddress($this->ip)) {
            $errors[] = 'ip must be a valid IP address';
        }

        // User ID validation
        if ($this->user_id !== null && ($error = $this->validatePositiveInt($this->user_id, 'user_id'))) {
            $errors[] = $error;
        }

        return $errors;
    }

    /**
     * Convert to database array
     */
    public function toDatabaseArray(): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'level' => $this->level,
            'message' => $this->message,
            'context' => $this->context ? json_encode($this->context) : null,
            'ip' => $this->ip,
            'user_id' => $this->user_id,
            'created_at' => $this->dateTimeToDb($this->created_at)
        ];
    }

    /**
     * JSON serialization
     */
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'level' => $this->level,
            'message' => $this->message,
            'context' => $this->context,
            'ip' => $this->ip,
            'user_id' => $this->user_id,
            'created_at' => $this->dateTimeToJson($this->created_at)
        ];
    }

    /**
     * Transform for external API integration
     */
    public function toExternalApiFormat(): array
    {
        return [
            'log_id' => $this->id,
            'type' => $this->type,
            'level' => $this->level,
            'message' => $this->message,
            'timestamp' => $this->dateTimeToJson($this->created_at),
            'context' => $this->context
        ];
    }

    /**
     * Get safe array for API responses (excludes sensitive data)
     */
    public function toSafeArray(): array
    {
        $safeContext = $this->context;
        
        // Remove sensitive information from context
        if (is_array($safeContext)) {
            unset($safeContext['password'], $safeContext['secret_key'], $safeContext['api_key']);
        }

        return [
            'id' => $this->id,
            'type' => $this->type,
            'level' => $this->level,
            'message' => $this->message,
            'context' => $safeContext,
            'created_at' => $this->dateTimeToJson($this->created_at)
        ];
    }

    /**
     * Handle foreign key relationships
     */
    public function getRelatedUser(): ?AdminUserModel
    {
        // This would be implemented by the repository layer
        return null;
    }

    /**
     * Validate foreign key constraints
     */
    public function validateForeignKeys(): array
    {
        $errors = [];
        
        // user_id should reference a valid admin user if set
        // This validation would be handled by the repository layer
        
        return $errors;
    }

    /**
     * Check if log is critical
     */
    public function isCritical(): bool
    {
        return $this->level === 'critical';
    }

    /**
     * Check if log is error level or higher
     */
    public function isError(): bool
    {
        return in_array($this->level, ['error', 'critical']);
    }

    /**
     * Get log severity score (higher = more severe)
     */
    public function getSeverityScore(): int
    {
        return match ($this->level) {
            'debug' => 1,
            'info' => 2,
            'warning' => 3,
            'error' => 4,
            'critical' => 5,
            default => 0
        };
    }

    /**
     * Get formatted message with context
     */
    public function getFormattedMessage(): string
    {
        $message = $this->message;
        
        if ($this->context && is_array($this->context)) {
            foreach ($this->context as $key => $value) {
                if (is_scalar($value)) {
                    $message = str_replace("{{$key}}", (string) $value, $message);
                }
            }
        }
        
        return $message;
    }
}