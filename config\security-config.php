<?php

/**
 * Security Configuration for GuardGeo Admin Platform
 * 
 * This file contains comprehensive security settings and configurations
 * for production deployment.
 */

return [
    // Input Validation Settings
    'input_validation' => [
        'max_input_length' => 10000,
        'max_request_size' => 10485760, // 10MB
        'allowed_file_types' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'text/plain',
            'application/pdf'
        ],
        'max_file_size' => 5242880, // 5MB
    ],

    // Rate Limiting Configuration
    'rate_limiting' => [
        'api_requests' => [
            'max_attempts' => 100,
            'time_window' => 3600, // 1 hour
        ],
        'login_attempts' => [
            'max_attempts' => 5,
            'time_window' => 900, // 15 minutes
            'lockout_duration' => 1800, // 30 minutes
        ],
        'password_reset' => [
            'max_attempts' => 3,
            'time_window' => 3600, // 1 hour
        ],
        'general_requests' => [
            'max_attempts' => 200,
            'time_window' => 3600, // 1 hour
        ],
    ],

    // Session Security
    'session' => [
        'name' => 'GUARDGEO_SESSION',
        'lifetime' => 3600, // 1 hour
        'regenerate_interval' => 300, // 5 minutes
        'cookie_secure' => true, // HTTPS only
        'cookie_httponly' => true,
        'cookie_samesite' => 'Strict',
        'use_strict_mode' => true,
    ],

    // CSRF Protection
    'csrf' => [
        'token_length' => 32,
        'token_lifetime' => 3600, // 1 hour
        'require_for_methods' => ['POST', 'PUT', 'PATCH', 'DELETE'],
        'exclude_paths' => [
            '/api/analyze', // Public API endpoint
        ],
    ],

    // Password Security
    'password' => [
        'min_length' => 12,
        'require_uppercase' => true,
        'require_lowercase' => true,
        'require_numbers' => true,
        'require_symbols' => true,
        'hash_algorithm' => PASSWORD_ARGON2ID,
        'hash_options' => [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,
            'threads' => 3,
        ],
    ],

    // IP Security
    'ip_security' => [
        'allowed_ranges' => [
            // Add your allowed IP ranges here
            // '***********/24',
            // '10.0.0.0/8',
        ],
        'blocked_ranges' => [
            // Add blocked IP ranges here
        ],
        'enable_geoblocking' => false,
        'allowed_countries' => [
            // Add allowed country codes here if geoblocking is enabled
            // 'US', 'CA', 'GB', 'DE', 'FR'
        ],
    ],

    // Security Headers
    'headers' => [
        'X-Content-Type-Options' => 'nosniff',
        'X-Frame-Options' => 'DENY',
        'X-XSS-Protection' => '1; mode=block',
        'Referrer-Policy' => 'strict-origin-when-cross-origin',
        'X-Permitted-Cross-Domain-Policies' => 'none',
        'Cross-Origin-Embedder-Policy' => 'require-corp',
        'Cross-Origin-Opener-Policy' => 'same-origin',
        'Cross-Origin-Resource-Policy' => 'same-origin',
        'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains; preload',
        'Permissions-Policy' => 'geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(self), sync-xhr=()',
    ],

    // Content Security Policy
    'csp' => [
        'default-src' => "'self'",
        'script-src' => "'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com",
        'style-src' => "'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com",
        'font-src' => "'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com",
        'img-src' => "'self' data: https:",
        'connect-src' => "'self'",
        'object-src' => "'none'",
        'base-uri' => "'self'",
        'form-action' => "'self'",
        'frame-ancestors' => "'none'",
        'upgrade-insecure-requests' => true,
    ],

    // Attack Detection
    'attack_detection' => [
        'enable_xss_detection' => true,
        'enable_sql_injection_detection' => true,
        'enable_path_traversal_detection' => true,
        'enable_command_injection_detection' => true,
        'enable_ldap_injection_detection' => true,
        'suspicious_user_agents' => [
            '/bot|crawler|spider|scraper/i',
            '/curl|wget|python|perl|ruby/i',
            '/scanner|exploit|hack|attack/i',
            '/sqlmap|nikto|nmap|masscan/i',
        ],
        'suspicious_paths' => [
            '/wp-admin', '/wp-login', '/phpmyadmin',
            '/.env', '/config', '/backup', '/test',
            '/shell', '/cmd', '/exec',
        ],
    ],

    // Logging and Monitoring
    'logging' => [
        'log_security_events' => true,
        'log_failed_logins' => true,
        'log_rate_limit_violations' => true,
        'log_attack_attempts' => true,
        'log_file_access_attempts' => true,
        'retention_days' => 90,
        'alert_thresholds' => [
            'failed_logins_per_hour' => 10,
            'attack_attempts_per_hour' => 5,
            'rate_limit_violations_per_hour' => 20,
        ],
    ],

    // Database Security
    'database' => [
        'use_prepared_statements' => true,
        'validate_all_inputs' => true,
        'escape_output' => true,
        'connection_encryption' => true,
        'connection_timeout' => 30,
        'query_timeout' => 60,
    ],

    // File Security
    'file_security' => [
        'upload_directory' => '/tmp/guardgeo_uploads',
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'txt'],
        'scan_uploads' => true,
        'quarantine_suspicious' => true,
        'max_filename_length' => 255,
    ],

    // API Security
    'api_security' => [
        'require_https' => true,
        'validate_content_type' => true,
        'max_payload_size' => 1048576, // 1MB
        'timeout' => 30,
        'enable_cors' => false,
        'allowed_origins' => [
            // Add allowed origins here if CORS is enabled
        ],
    ],

    // Environment-specific settings
    'environment' => [
        'production' => [
            'debug_mode' => false,
            'error_display' => false,
            'detailed_errors' => false,
            'log_level' => 'error',
        ],
        'staging' => [
            'debug_mode' => false,
            'error_display' => false,
            'detailed_errors' => true,
            'log_level' => 'warning',
        ],
        'development' => [
            'debug_mode' => true,
            'error_display' => true,
            'detailed_errors' => true,
            'log_level' => 'debug',
        ],
    ],

    // Backup and Recovery
    'backup' => [
        'encrypt_backups' => true,
        'backup_retention_days' => 30,
        'verify_backup_integrity' => true,
        'offsite_backup' => true,
    ],

    // Compliance Settings
    'compliance' => [
        'gdpr_compliance' => true,
        'data_retention_days' => 365,
        'anonymize_logs' => true,
        'audit_trail' => true,
        'data_encryption_at_rest' => true,
    ],

    // Security Monitoring
    'monitoring' => [
        'enable_intrusion_detection' => true,
        'enable_anomaly_detection' => true,
        'alert_email' => '<EMAIL>',
        'alert_webhook' => null,
        'monitoring_interval' => 300, // 5 minutes
    ],

    // Third-party Integrations
    'integrations' => [
        'freemius' => [
            'validate_ssl' => true,
            'timeout' => 30,
            'retry_attempts' => 3,
        ],
        'ipregistry' => [
            'validate_ssl' => true,
            'timeout' => 10,
            'retry_attempts' => 2,
        ],
    ],
];