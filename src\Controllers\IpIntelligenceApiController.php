<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Utils\AuthMiddleware;
use Skpassegna\GuardgeoApi\Utils\RoleManager;
use Skpassegna\GuardgeoApi\Services\IpRegistryService;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Database\IpIntelligenceRepository;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;

/**
 * IP Intelligence API Controller
 * 
 * Provides JSON API endpoints for IP intelligence management
 * with search, filtering, and cache management capabilities.
 */
class IpIntelligenceApiController
{
    private AuthMiddleware $authMiddleware;
    private RoleManager $roleManager;
    private IpIntelligenceRepository $ipRepository;
    private IpRegistryService $ipRegistryService;
    private LoggingService $logger;

    public function __construct(
        AuthMiddleware $authMiddleware,
        RoleManager $roleManager,
        IpIntelligenceRepository $ipRepository,
        IpRegistryService $ipRegistryService,
        LoggingService $logger
    ) {
        $this->authMiddleware = $authMiddleware;
        $this->roleManager = $roleManager;
        $this->ipRepository = $ipRepository;
        $this->ipRegistryService = $ipRegistryService;
        $this->logger = $logger;
    }

    /**
     * Get IP intelligence records with pagination and filtering
     *
     * @return void
     */
    public function getIpRecords(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'ip_intelligence_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            // Get query parameters
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = min(max(1, (int)($_GET['limit'] ?? 25)), 100);
            $search = $_GET['search'] ?? '';
            $status = $_GET['status'] ?? 'all'; // all, fresh, stale
            $sortBy = $_GET['sort'] ?? 'cached_at';
            $sortOrder = $_GET['order'] ?? 'desc';

            // Build filters
            $filters = [];
            
            if (!empty($search)) {
                $filters['search'] = $search;
            }
            
            if ($status !== 'all') {
                $filters['status'] = $status;
            }

            // Get records
            $result = $this->ipRepository->getIpRecordsPaginated(
                $page,
                $limit,
                $filters,
                $sortBy,
                $sortOrder
            );

            $this->sendJsonResponse([
                'success' => true,
                'data' => $result['records'],
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $result['total'],
                    'total_pages' => ceil($result['total'] / $limit)
                ],
                'filters' => [
                    'search' => $search,
                    'status' => $status,
                    'sort' => $sortBy,
                    'order' => $sortOrder
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('IP records API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load IP records'
            ], 500);
        }
    }

    /**
     * Get detailed IP intelligence for a specific IP
     *
     * @return void
     */
    public function getIpDetails(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'ip_intelligence_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            $ip = $_GET['ip'] ?? '';
            
            if (empty($ip) || !filter_var($ip, FILTER_VALIDATE_IP)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Valid IP address is required'
                ], 400);
                return;
            }

            $ipData = $this->ipRepository->getByIp($ip);
            
            if (!$ipData) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'IP address not found in cache'
                ], 404);
                return;
            }

            // Check if data is stale
            $isStale = $this->isIpDataStale($ipData);
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => [
                    'ip_data' => $ipData,
                    'is_stale' => $isStale,
                    'can_refresh' => $this->roleManager->canAccessFeature($user['role'], 'ip_intelligence_manager')
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('IP details API error', [
                'error' => $e->getMessage(),
                'user' => $user['email'],
                'ip' => $_GET['ip'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load IP details'
            ], 500);
        }
    }

    /**
     * Refresh IP intelligence data (manager role required)
     *
     * @return void
     */
    public function refreshIpData(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'ip_intelligence_manager')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Management access required'
            ], 403);
            return;
        }

        try {
            $requestData = json_decode(file_get_contents('php://input'), true);
            $ip = $requestData['ip'] ?? '';
            
            if (empty($ip) || !filter_var($ip, FILTER_VALIDATE_IP)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Valid IP address is required'
                ], 400);
                return;
            }

            // Force refresh from ipRegistry
            $ipData = $this->ipRegistryService->getIpIntelligence($ip, true);
            
            if (!$ipData) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Failed to refresh IP data from ipRegistry'
                ], 500);
                return;
            }

            // Log the refresh action
            $this->logger->logAdminAction('IP data refreshed', [
                'ip' => $ip,
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => true,
                'message' => 'IP data refreshed successfully',
                'data' => $ipData
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('IP refresh API error', [
                'error' => $e->getMessage(),
                'user' => $user['email'],
                'ip' => $requestData['ip'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to refresh IP data'
            ], 500);
        }
    }

    /**
     * Delete IP intelligence record (manager role required)
     *
     * @return void
     */
    public function deleteIpRecord(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'ip_intelligence_manager')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Management access required'
            ], 403);
            return;
        }

        try {
            $requestData = json_decode(file_get_contents('php://input'), true);
            $ip = $requestData['ip'] ?? '';
            
            if (empty($ip) || !filter_var($ip, FILTER_VALIDATE_IP)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Valid IP address is required'
                ], 400);
                return;
            }

            $success = $this->ipRepository->deleteByIp($ip);
            
            if (!$success) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'IP record not found or could not be deleted'
                ], 404);
                return;
            }

            // Log the deletion action
            $this->logger->logAdminAction('IP record deleted', [
                'ip' => $ip,
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => true,
                'message' => 'IP record deleted successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('IP delete API error', [
                'error' => $e->getMessage(),
                'user' => $user['email'],
                'ip' => $requestData['ip'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to delete IP record'
            ], 500);
        }
    }

    /**
     * Bulk refresh stale IP records (manager role required)
     *
     * @return void
     */
    public function bulkRefreshStale(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'ip_intelligence_manager')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Management access required'
            ], 403);
            return;
        }

        try {
            $requestData = json_decode(file_get_contents('php://input'), true);
            $limit = min(max(1, (int)($requestData['limit'] ?? 10)), 50);

            // Get stale IP records
            $staleIps = $this->ipRepository->getStaleIpAddresses($limit);
            
            $refreshed = 0;
            $errors = 0;

            foreach ($staleIps as $ip) {
                try {
                    $ipData = $this->ipRegistryService->getIpIntelligence($ip, true);
                    if ($ipData) {
                        $refreshed++;
                    } else {
                        $errors++;
                    }
                } catch (\Exception $e) {
                    $errors++;
                    $this->logger->logError('Bulk refresh error for IP', [
                        'ip' => $ip,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Log the bulk refresh action
            $this->logger->logAdminAction('Bulk IP refresh completed', [
                'refreshed' => $refreshed,
                'errors' => $errors,
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => true,
                'message' => "Bulk refresh completed: {$refreshed} refreshed, {$errors} errors",
                'data' => [
                    'refreshed' => $refreshed,
                    'errors' => $errors,
                    'total_processed' => count($staleIps)
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Bulk refresh API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to perform bulk refresh'
            ], 500);
        }
    }

    /**
     * Get IP intelligence statistics
     *
     * @return void
     */
    public function getStatistics(): void
    {
        if (!$this->requireAuthentication()) {
            return;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'ip_intelligence_viewer')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Access denied'
            ], 403);
            return;
        }

        try {
            $stats = $this->ipRepository->getStatistics();
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('IP statistics API error', [
                'error' => $e->getMessage(),
                'user' => $user['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load statistics'
            ], 500);
        }
    }

    /**
     * Check if IP data is stale
     *
     * @param array $ipData
     * @return bool
     */
    private function isIpDataStale(array $ipData): bool
    {
        $now = new \DateTime();
        
        // Check security data expiration (most critical)
        if (!empty($ipData['security_expires_at'])) {
            $securityExpires = new \DateTime($ipData['security_expires_at']);
            if ($now > $securityExpires) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Require authentication and return status
     *
     * @return bool
     */
    private function requireAuthentication(): bool
    {
        if (!$this->authMiddleware->isAuthenticated()) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Authentication required'
            ], 401);
            return false;
        }

        return true;
    }

    /**
     * Send JSON response
     *
     * @param array $data
     * @param int $statusCode
     * @return void
     */
    private function sendJsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Create instance with dependencies
     *
     * @return self
     */
    public static function create(): self
    {
        $logger = new LoggingService();
        $db = new DatabaseConnection();
        $sessionManager = new \Skpassegna\GuardgeoApi\Utils\SessionManager();
        $passwordValidator = new \Skpassegna\GuardgeoApi\Utils\PasswordValidator();
        $emailValidator = new \Skpassegna\GuardgeoApi\Utils\EmailDomainValidator();
        
        $authService = new \Skpassegna\GuardgeoApi\Services\AuthService(
            $db,
            $sessionManager,
            $passwordValidator,
            $emailValidator,
            $logger
        );
        
        $authMiddleware = new AuthMiddleware($authService);
        $roleManager = new RoleManager();
        $ipRepository = new IpIntelligenceRepository($db);
        $ipRegistryService = new IpRegistryService($logger);

        return new self($authMiddleware, $roleManager, $ipRepository, $ipRegistryService, $logger);
    }
}