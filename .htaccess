RewriteEngine On

# Security: Block access to sensitive directories and files
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

<FilesMatch "\.(env|log|sql|md|json|lock|yaml|yml|ini|conf)$">
    Require all denied
</FilesMatch>

<DirectoryMatch "/(vendor|src|logs|config|\.git|\.kiro)">
    Require all denied
</DirectoryMatch>

# Block access to specific sensitive files
<Files "composer.json">
    Require all denied
</Files>

<Files "composer.lock">
    Require all denied
</Files>

<Files ".env">
    Require all denied
</Files>

<Files ".env.example">
    Require all denied
</Files>

<Files "*.sql">
    Require all denied
</Files>

# Block suspicious request patterns
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} ^.*(\[|\]|\(|\)|<|>|ê|"|;|\?|\*|=$).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*("|'|<|>|\|).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*(%0|%A|%B|%C|%D|%E|%F|127\.0).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*(globals|encode|localhost|loopback).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*(request|select|insert|union|declare).* [NC]
RewriteRule ^(.*)$ - [F,L]

# Block suspicious user agents
RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget|python|perl|ruby|libwww|lwp|nikto|sqlmap|nmap|masscan) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (bot|crawler|spider|scraper|scanner|exploit|hack|attack) [NC]
RewriteRule ^(.*)$ - [F,L]

# Block requests with suspicious methods
RewriteCond %{REQUEST_METHOD} ^(TRACE|DELETE|TRACK|DEBUG) [NC]
RewriteRule ^(.*)$ - [F,L]

# Limit request size (10MB)
LimitRequestBody ********

# Admin interface routing
RewriteCond %{REQUEST_URI} ^/admin
RewriteRule ^admin(.*)$ admin.php [QSA,L]

# API routing
RewriteCond %{REQUEST_URI} ^/api
RewriteRule ^api(.*)$ api.php [QSA,L]

# Force HTTPS in production
<IfModule mod_env.c>
    SetEnvIf Host "^(.*)$" FORCE_SSL=1
</IfModule>

RewriteCond %{HTTPS} off
RewriteCond %{ENV:FORCE_SSL} 1
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

# Security headers
<IfModule mod_headers.c>
    # Basic security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Enhanced security headers
    Header always set X-Permitted-Cross-Domain-Policies none
    Header always set Cross-Origin-Embedder-Policy require-corp
    Header always set Cross-Origin-Opener-Policy same-origin
    Header always set Cross-Origin-Resource-Policy same-origin
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests"
    
    # HSTS (only if HTTPS)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" env=HTTPS
    
    # Permissions Policy
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(self), sync-xhr=()"
    
    # Cache control for sensitive pages
    <FilesMatch "\.(php)$">
        Header always set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
        Header always set Pragma "no-cache"
        Header always set Expires "Thu, 01 Jan 1970 00:00:00 GMT"
    </FilesMatch>
</IfModule>

# Disable server signature
ServerTokens Prod
ServerSignature Off

# Disable directory browsing
Options -Indexes

# Disable server-side includes
Options -Includes

# Disable CGI execution
Options -ExecCGI

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Set proper MIME types
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType application/json .json
    AddType application/xml .xml
    AddType text/xml .xml
</IfModule>