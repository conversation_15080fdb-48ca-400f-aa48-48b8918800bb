<?php

namespace Skpassegna\GuardgeoApi\Utils;

class ResponseFormatter
{
    /**
     * Format successful API response with comprehensive metadata
     */
    public function formatSuccess(array $data, array $metadata = []): string
    {
        $response = [
            'success' => true,
            'data' => $this->sanitizeForJson($data),
            'meta' => array_merge([
                'timestamp' => $this->getCurrentTimestamp(),
                'version' => '1.0',
                'request_id' => $this->getRequestId()
            ], $metadata),
            'links' => [
                'self' => $this->getCurrentUrl()
            ]
        ];

        // Add performance metadata if available
        if (isset($metadata['performance'])) {
            $response['meta']['performance'] = $metadata['performance'];
        }

        return json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Format comprehensive error API response
     */
    public function formatError(string $code, string $message, array $details = [], int $statusCode = null): string
    {
        $response = [
            'success' => false,
            'error' => [
                'code' => $code,
                'message' => $message,
                'type' => $this->getErrorType($code),
                'status' => $statusCode ?? $this->getStatusCodeFromErrorCode($code),
                'severity' => $this->getErrorSeverity($code)
            ],
            'meta' => [
                'timestamp' => $this->getCurrentTimestamp(),
                'version' => '1.0',
                'request_id' => $this->getRequestId(),
                'environment' => $this->getEnvironmentInfo()
            ],
            'links' => [
                'self' => $this->getCurrentUrl(),
                'documentation' => $this->getDocumentationUrl($code)
            ]
        ];

        if (!empty($details)) {
            $response['error']['details'] = $this->sanitizeForJson($details);
        }

        // Add help information for common errors
        $helpInfo = $this->getErrorHelpInfo($code);
        if ($helpInfo) {
            $response['error']['help'] = $helpInfo;
        }

        // Add recovery suggestions
        $recoveryInfo = $this->getRecoveryInfo($code);
        if ($recoveryInfo) {
            $response['error']['recovery'] = $recoveryInfo;
        }

        return json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Format validation error response
     */
    public function formatValidationError(array $validationErrors): string
    {
        return $this->formatError(
            'VALIDATION_ERROR',
            'Request validation failed',
            ['validation_errors' => $validationErrors]
        );
    }

    /**
     * Format authentication error response
     */
    public function formatAuthenticationError(string $message = 'Authentication failed'): string
    {
        return $this->formatError(
            'AUTHENTICATION_ERROR',
            $message
        );
    }

    /**
     * Format authorization error response
     */
    public function formatAuthorizationError(string $message = 'Access denied'): string
    {
        return $this->formatError(
            'AUTHORIZATION_ERROR',
            $message
        );
    }

    /**
     * Format external API error response
     */
    public function formatExternalApiError(string $service, string $message): string
    {
        return $this->formatError(
            'EXTERNAL_API_ERROR',
            "External service error: {$service}",
            ['service' => $service, 'message' => $message]
        );
    }

    /**
     * Format internal server error response
     */
    public function formatInternalError(string $errorId = null): string
    {
        $details = [];
        if ($errorId) {
            $details['error_id'] = $errorId;
        }

        return $this->formatError(
            'INTERNAL_ERROR',
            'An internal server error occurred',
            $details
        );
    }

    /**
     * Format rate limit error response
     */
    public function formatRateLimitError(int $retryAfter = null): string
    {
        $details = [];
        if ($retryAfter) {
            $details['retry_after'] = $retryAfter;
        }

        return $this->formatError(
            'RATE_LIMIT_EXCEEDED',
            'Rate limit exceeded',
            $details
        );
    }

    /**
     * Format method not allowed error response
     */
    public function formatMethodNotAllowedError(array $allowedMethods): string
    {
        return $this->formatError(
            'METHOD_NOT_ALLOWED',
            'HTTP method not allowed',
            ['allowed_methods' => $allowedMethods]
        );
    }

    /**
     * Format not found error response
     */
    public function formatNotFoundError(string $resource = 'Resource'): string
    {
        return $this->formatError(
            'NOT_FOUND',
            "{$resource} not found"
        );
    }

    /**
     * Format maintenance mode response
     */
    public function formatMaintenanceResponse(): string
    {
        return $this->formatError(
            'MAINTENANCE_MODE',
            'Service temporarily unavailable for maintenance'
        );
    }

    /**
     * Get current timestamp in ISO 8601 format
     */
    private function getCurrentTimestamp(): string
    {
        return date('c'); // ISO 8601 format (e.g., 2025-01-30T12:00:00+00:00)
    }

    /**
     * Set appropriate HTTP headers for JSON API responses
     */
    public function setJsonHeaders(): void
    {
        header('Content-Type: application/json; charset=utf-8');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: 0');
    }

    /**
     * Set CORS headers for API responses
     */
    public function setCorsHeaders(array $allowedOrigins = ['*'], array $allowedMethods = ['GET', 'POST']): void
    {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        
        if (in_array('*', $allowedOrigins) || in_array($origin, $allowedOrigins)) {
            header('Access-Control-Allow-Origin: ' . ($origin ?: '*'));
        }
        
        header('Access-Control-Allow-Methods: ' . implode(', ', $allowedMethods));
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        header('Access-Control-Max-Age: 86400'); // 24 hours
    }

    /**
     * Set security headers
     */
    public function setSecurityHeaders(): void
    {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }

    /**
     * Format response with pagination metadata
     */
    public function formatPaginatedResponse(array $data, int $page, int $perPage, int $total): string
    {
        $totalPages = ceil($total / $perPage);
        
        $response = [
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_items' => $total,
                'total_pages' => $totalPages,
                'has_next' => $page < $totalPages,
                'has_previous' => $page > 1
            ],
            'timestamp' => $this->getCurrentTimestamp()
        ];

        return json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }

    /**
     * Validate and clean data before JSON encoding
     */
    public function sanitizeForJson($data)
    {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeForJson'], $data);
        }
        
        if (is_string($data)) {
            // Ensure UTF-8 encoding and remove null bytes
            return mb_convert_encoding(str_replace("\0", '', $data), 'UTF-8', 'UTF-8');
        }
        
        return $data;
    }

    /**
     * Get error type from error code
     */
    private function getErrorType(string $code): string
    {
        $errorTypes = [
            'VALIDATION_ERROR' => 'client_error',
            'AUTHENTICATION_ERROR' => 'authentication_error',
            'AUTHORIZATION_ERROR' => 'authorization_error',
            'INVALID_INSTALLATION' => 'authentication_error',
            'RATE_LIMIT_EXCEEDED' => 'rate_limit_error',
            'METHOD_NOT_ALLOWED' => 'client_error',
            'NOT_FOUND' => 'client_error',
            'INTERNAL_ERROR' => 'server_error',
            'EXTERNAL_API_ERROR' => 'server_error',
            'IP_INTELLIGENCE_ERROR' => 'server_error',
            'MAINTENANCE_MODE' => 'server_error'
        ];

        return $errorTypes[$code] ?? 'unknown_error';
    }

    /**
     * Get HTTP status code from error code
     */
    private function getStatusCodeFromErrorCode(string $code): int
    {
        $statusCodes = [
            'VALIDATION_ERROR' => 400,
            'INVALID_REQUEST' => 400,
            'AUTHENTICATION_ERROR' => 401,
            'INVALID_INSTALLATION' => 401,
            'AUTHORIZATION_ERROR' => 403,
            'NOT_FOUND' => 404,
            'METHOD_NOT_ALLOWED' => 405,
            'RATE_LIMIT_EXCEEDED' => 429,
            'INTERNAL_ERROR' => 500,
            'EXTERNAL_API_ERROR' => 502,
            'IP_INTELLIGENCE_ERROR' => 502,
            'MAINTENANCE_MODE' => 503
        ];

        return $statusCodes[$code] ?? 500;
    }

    /**
     * Get help information for common errors
     */
    private function getErrorHelpInfo(string $code): ?array
    {
        $helpInfo = [
            'VALIDATION_ERROR' => [
                'message' => 'Check that all required fields are present and properly formatted',
                'required_fields' => ['ip', 'visitor_hash', 'plugin_id', 'install_id', 'url']
            ],
            'INVALID_INSTALLATION' => [
                'message' => 'Ensure your Freemius installation is active and has a valid subscription',
                'common_causes' => [
                    'Installation is not active',
                    'Subscription has expired',
                    'Plugin has been uninstalled',
                    'Invalid plugin_id or install_id'
                ]
            ],
            'RATE_LIMIT_EXCEEDED' => [
                'message' => 'You have exceeded the API rate limit',
                'suggestion' => 'Wait before making additional requests or contact support for higher limits'
            ],
            'IP_INTELLIGENCE_ERROR' => [
                'message' => 'Unable to retrieve IP intelligence data',
                'suggestion' => 'This is usually temporary. Try again in a few moments.'
            ]
        ];

        return $helpInfo[$code] ?? null;
    }

    /**
     * Get documentation URL for error code
     */
    private function getDocumentationUrl(string $code): string
    {
        return "https://docs.guardgeo.com/api/errors#" . strtolower($code);
    }

    /**
     * Get error severity level
     */
    private function getErrorSeverity(string $code): string
    {
        $severityMap = [
            'VALIDATION_ERROR' => 'low',
            'AUTHENTICATION_ERROR' => 'medium',
            'AUTHORIZATION_ERROR' => 'medium',
            'RATE_LIMIT_EXCEEDED' => 'medium',
            'INTERNAL_ERROR' => 'high',
            'DATABASE_ERROR' => 'high',
            'EXTERNAL_API_ERROR' => 'medium',
            'SECURITY_VIOLATION' => 'critical',
            'SQL_INJECTION_ATTEMPT' => 'critical'
        ];

        return $severityMap[$code] ?? 'medium';
    }

    /**
     * Get environment information
     */
    private function getEnvironmentInfo(): array
    {
        return [
            'mode' => $_ENV['APP_ENV'] ?? 'production',
            'debug' => ($_ENV['APP_DEBUG'] ?? 'false') === 'true'
        ];
    }

    /**
     * Get recovery information for error code
     */
    private function getRecoveryInfo(string $code): ?array
    {
        $recoveryInfo = [
            'VALIDATION_ERROR' => [
                'action' => 'validate_input',
                'message' => 'Please check your request parameters and try again',
                'retry_recommended' => false
            ],
            'AUTHENTICATION_ERROR' => [
                'action' => 'check_credentials',
                'message' => 'Please verify your authentication credentials',
                'retry_recommended' => false
            ],
            'RATE_LIMIT_EXCEEDED' => [
                'action' => 'wait_and_retry',
                'message' => 'Please wait before making additional requests',
                'retry_recommended' => true,
                'retry_after' => 60
            ],
            'EXTERNAL_API_ERROR' => [
                'action' => 'retry_later',
                'message' => 'This is usually temporary. Please try again in a few moments',
                'retry_recommended' => true,
                'retry_after' => 30
            ],
            'INTERNAL_ERROR' => [
                'action' => 'contact_support',
                'message' => 'If this problem persists, please contact support',
                'retry_recommended' => true,
                'retry_after' => 60
            ]
        ];

        return $recoveryInfo[$code] ?? null;
    }

    /**
     * Get current request URL
     */
    private function getCurrentUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        
        return $protocol . '://' . $host . $uri;
    }

    /**
     * Get or generate request ID
     */
    private function getRequestId(): string
    {
        static $requestId = null;
        
        if ($requestId === null) {
            $requestId = $_SERVER['HTTP_X_REQUEST_ID'] ?? uniqid('req_', true);
        }
        
        return $requestId;
    }

    /**
     * Format comprehensive validation error with field-specific details
     */
    public function formatValidationErrorDetailed(array $validationResult): string
    {
        $details = [
            'field_errors' => $validationResult['errors'] ?? [],
            'field_warnings' => $validationResult['warnings'] ?? [],
            'field_count' => $validationResult['field_count'] ?? 0,
            'validation_summary' => $validationResult['validation_summary'] ?? 'Validation failed'
        ];

        if (!empty($validationResult['unexpected_fields'])) {
            $details['unexpected_fields'] = $validationResult['unexpected_fields'];
        }

        return $this->formatError(
            'VALIDATION_ERROR',
            'Request validation failed',
            $details,
            400
        );
    }

    /**
     * Format response with comprehensive HTTP status code handling
     */
    public function formatResponseWithStatus(bool $success, $data, int $statusCode, array $metadata = []): string
    {
        // Set the HTTP status code
        http_response_code($statusCode);

        if ($success) {
            $metadata['status_code'] = $statusCode;
            return $this->formatSuccess($data, $metadata);
        } else {
            // For error responses, data should contain error information
            return $this->formatError(
                $data['code'] ?? 'UNKNOWN_ERROR',
                $data['message'] ?? 'An error occurred',
                $data['details'] ?? [],
                $statusCode
            );
        }
    }

    /**
     * Format response with performance metrics
     */
    public function formatSuccessWithPerformance(array $data, array $performanceMetrics): string
    {
        $metadata = [
            'performance' => [
                'response_time_ms' => $performanceMetrics['total_duration_ms'] ?? null,
                'memory_usage_mb' => $performanceMetrics['memory_usage_mb'] ?? null,
                'database_queries' => $performanceMetrics['database_performance']['total_queries'] ?? 0,
                'external_api_calls' => $performanceMetrics['external_api_performance']['total_calls'] ?? 0
            ]
        ];

        return $this->formatSuccess($data, $metadata);
    }
}