<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Config\ConfigManager;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Models\AdminUserModel;

/**
 * System Settings Service
 * 
 * Manages system configuration settings that can be modified through
 * the admin interface, including validation and persistence.
 */
class SystemSettingsService
{
    private ConfigManager $configManager;
    private DatabaseConnection $db;
    private LoggingService $logger;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->configManager = ConfigManager::getInstance();
        $this->db = DatabaseConnection::getInstance();
        $this->logger = LoggingService::getInstance();
    }
    
    /**
     * Get all system settings organized by category
     */
    public function getAllSettings(): array
    {
        try {
            $query = "
                SELECT config_key, config_value, config_type, description, is_sensitive
                FROM system_config 
                WHERE is_active = true 
                ORDER BY config_key
            ";
            
            $result = $this->db->query($query);
            $settings = [];
            
            while ($row = $result->fetch(\PDO::FETCH_ASSOC)) {
                $category = explode('.', $row['config_key'])[0];
                
                if (!isset($settings[$category])) {
                    $settings[$category] = [];
                }
                
                $settings[$category][] = [
                    'key' => $row['config_key'],
                    'value' => $this->parseConfigValue($row['config_value'], $row['config_type']),
                    'type' => $row['config_type'],
                    'description' => $row['description'],
                    'is_sensitive' => (bool) $row['is_sensitive'],
                    'display_value' => $row['is_sensitive'] ? $this->maskSensitiveValue($row['config_value']) : $row['config_value']
                ];
            }
            
            return $settings;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to get system settings', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Get settings for a specific category
     */
    public function getCategorySettings(string $category): array
    {
        try {
            $query = "
                SELECT config_key, config_value, config_type, description, is_sensitive
                FROM system_config 
                WHERE is_active = true AND config_key LIKE ? 
                ORDER BY config_key
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$category . '.%']);
            
            $settings = [];
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                $settings[] = [
                    'key' => $row['config_key'],
                    'value' => $this->parseConfigValue($row['config_value'], $row['config_type']),
                    'type' => $row['config_type'],
                    'description' => $row['description'],
                    'is_sensitive' => (bool) $row['is_sensitive'],
                    'display_value' => $row['is_sensitive'] ? $this->maskSensitiveValue($row['config_value']) : $row['config_value']
                ];
            }
            
            return $settings;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to get category settings', [
                'category' => $category,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Update a system setting
     */
    public function updateSetting(string $key, $value, AdminUserModel $user): bool
    {
        try {
            // Get current setting info
            $currentSetting = $this->getSetting($key);
            if (!$currentSetting) {
                throw new \InvalidArgumentException("Setting '{$key}' not found");
            }
            
            // Validate the new value
            $this->validateSettingValue($key, $value, $currentSetting['type']);
            
            // Convert value to string for storage
            $stringValue = $this->convertValueToString($value, $currentSetting['type']);
            
            // Update in database
            $query = "
                UPDATE system_config 
                SET config_value = ?, updated_at = CURRENT_TIMESTAMP, updated_by = ?
                WHERE config_key = ? AND is_active = true
            ";
            
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([$stringValue, $user->getId(), $key]);
            
            if ($result) {
                // Log the change
                $this->logger->info('System setting updated', [
                    'key' => $key,
                    'old_value' => $currentSetting['is_sensitive'] ? '[MASKED]' : $currentSetting['value'],
                    'new_value' => $currentSetting['is_sensitive'] ? '[MASKED]' : $value,
                    'updated_by' => $user->getEmail(),
                    'user_id' => $user->getId()
                ]);
                
                // Reload configuration to pick up changes
                $this->configManager->reload();
                
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to update system setting', [
                'key' => $key,
                'error' => $e->getMessage(),
                'user_id' => $user->getId()
            ]);
            throw $e;
        }
    }
    
    /**
     * Update multiple settings at once
     */
    public function updateMultipleSettings(array $settings, AdminUserModel $user): array
    {
        $results = [];
        $errors = [];
        
        $this->db->beginTransaction();
        
        try {
            foreach ($settings as $key => $value) {
                try {
                    $this->updateSetting($key, $value, $user);
                    $results[$key] = true;
                } catch (\Exception $e) {
                    $results[$key] = false;
                    $errors[$key] = $e->getMessage();
                }
            }
            
            if (empty($errors)) {
                $this->db->commit();
                $this->logger->info('Multiple system settings updated successfully', [
                    'count' => count($settings),
                    'updated_by' => $user->getEmail()
                ]);
            } else {
                $this->db->rollback();
                $this->logger->warning('Some system settings failed to update', [
                    'errors' => $errors,
                    'updated_by' => $user->getEmail()
                ]);
            }
            
        } catch (\Exception $e) {
            $this->db->rollback();
            $this->logger->error('Failed to update multiple system settings', [
                'error' => $e->getMessage(),
                'updated_by' => $user->getEmail()
            ]);
            throw $e;
        }
        
        return ['results' => $results, 'errors' => $errors];
    }
    
    /**
     * Get a specific setting
     */
    public function getSetting(string $key): ?array
    {
        try {
            $query = "
                SELECT config_key, config_value, config_type, description, is_sensitive
                FROM system_config 
                WHERE config_key = ? AND is_active = true
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$key]);
            $row = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$row) {
                return null;
            }
            
            return [
                'key' => $row['config_key'],
                'value' => $this->parseConfigValue($row['config_value'], $row['config_type']),
                'type' => $row['config_type'],
                'description' => $row['description'],
                'is_sensitive' => (bool) $row['is_sensitive'],
                'display_value' => $row['is_sensitive'] ? $this->maskSensitiveValue($row['config_value']) : $row['config_value']
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to get setting', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Add a new setting
     */
    public function addSetting(string $key, $value, string $type, string $description, bool $isSensitive, AdminUserModel $user): bool
    {
        try {
            // Validate the value
            $this->validateSettingValue($key, $value, $type);
            
            // Convert value to string for storage
            $stringValue = $this->convertValueToString($value, $type);
            
            $query = "
                INSERT INTO system_config (config_key, config_value, config_type, description, is_sensitive, updated_by)
                VALUES (?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([$key, $stringValue, $type, $description, $isSensitive, $user->getId()]);
            
            if ($result) {
                $this->logger->info('New system setting added', [
                    'key' => $key,
                    'type' => $type,
                    'is_sensitive' => $isSensitive,
                    'added_by' => $user->getEmail()
                ]);
                
                // Reload configuration
                $this->configManager->reload();
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to add system setting', [
                'key' => $key,
                'error' => $e->getMessage(),
                'user_id' => $user->getId()
            ]);
            throw $e;
        }
    }
    
    /**
     * Delete a setting
     */
    public function deleteSetting(string $key, AdminUserModel $user): bool
    {
        try {
            $query = "UPDATE system_config SET is_active = false WHERE config_key = ?";
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([$key]);
            
            if ($result) {
                $this->logger->info('System setting deleted', [
                    'key' => $key,
                    'deleted_by' => $user->getEmail()
                ]);
                
                // Reload configuration
                $this->configManager->reload();
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to delete system setting', [
                'key' => $key,
                'error' => $e->getMessage(),
                'user_id' => $user->getId()
            ]);
            throw $e;
        }
    }
    
    /**
     * Get system configuration summary
     */
    public function getConfigurationSummary(): array
    {
        $health = $this->configManager->getHealthStatus();
        $secureKeys = $this->configManager->getSecureKeys();
        
        return [
            'environment' => $this->configManager->getEnvironment(),
            'health_status' => $health,
            'api_keys' => $secureKeys,
            'database_config' => [
                'host' => $this->configManager->get('database.host'),
                'database' => $this->configManager->get('database.database'),
                'port' => $this->configManager->get('database.port'),
            ],
            'cache_config' => [
                'enabled' => $this->configManager->get('cache.enabled'),
                'driver' => $this->configManager->get('cache.driver'),
                'default_ttl' => $this->configManager->get('cache.default_ttl'),
            ],
            'monitoring' => [
                'enabled' => $this->configManager->get('monitoring.enabled'),
                'performance_tracking' => $this->configManager->get('monitoring.performance_tracking'),
                'error_reporting' => $this->configManager->get('monitoring.error_reporting'),
            ]
        ];
    }
    
    /**
     * Validate setting value based on type and constraints
     */
    private function validateSettingValue(string $key, $value, string $type): void
    {
        switch ($type) {
            case 'integer':
                if (!is_numeric($value) || (int) $value != $value) {
                    throw new \InvalidArgumentException("Value for '{$key}' must be an integer");
                }
                
                // Specific validations for certain keys
                if (strpos($key, 'days') !== false && (int) $value < 1) {
                    throw new \InvalidArgumentException("Days value must be at least 1");
                }
                if (strpos($key, 'timeout') !== false && (int) $value < 1) {
                    throw new \InvalidArgumentException("Timeout value must be at least 1 second");
                }
                break;
                
            case 'float':
                if (!is_numeric($value)) {
                    throw new \InvalidArgumentException("Value for '{$key}' must be a number");
                }
                break;
                
            case 'boolean':
                if (!is_bool($value) && !in_array(strtolower($value), ['true', 'false', '1', '0'])) {
                    throw new \InvalidArgumentException("Value for '{$key}' must be a boolean");
                }
                break;
                
            case 'json':
                if (is_string($value)) {
                    json_decode($value);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        throw new \InvalidArgumentException("Value for '{$key}' must be valid JSON");
                    }
                }
                break;
                
            case 'array':
                if (is_string($value)) {
                    // Allow comma-separated values
                    $value = explode(',', $value);
                }
                if (!is_array($value)) {
                    throw new \InvalidArgumentException("Value for '{$key}' must be an array");
                }
                break;
        }
    }
    
    /**
     * Convert value to string for database storage
     */
    private function convertValueToString($value, string $type): string
    {
        switch ($type) {
            case 'boolean':
                return is_bool($value) ? ($value ? 'true' : 'false') : (string) $value;
            case 'json':
                return is_string($value) ? $value : json_encode($value);
            case 'array':
                return is_array($value) ? implode(',', $value) : (string) $value;
            default:
                return (string) $value;
        }
    }
    
    /**
     * Parse configuration value based on type
     */
    private function parseConfigValue(string $value, string $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            case 'array':
                return explode(',', $value);
            default:
                return $value;
        }
    }
    
    /**
     * Mask sensitive values for display
     */
    private function maskSensitiveValue(string $value): string
    {
        if (strlen($value) <= 8) {
            return str_repeat('*', strlen($value));
        }
        
        return substr($value, 0, 4) . str_repeat('*', strlen($value) - 8) . substr($value, -4);
    }
    
    /**
     * Export settings for backup
     */
    public function exportSettings(bool $includeSensitive = false): array
    {
        try {
            $query = "
                SELECT config_key, config_value, config_type, description, is_sensitive
                FROM system_config 
                WHERE is_active = true 
                ORDER BY config_key
            ";
            
            $result = $this->db->query($query);
            $settings = [];
            
            while ($row = $result->fetch(\PDO::FETCH_ASSOC)) {
                if (!$includeSensitive && $row['is_sensitive']) {
                    continue;
                }
                
                $settings[] = [
                    'key' => $row['config_key'],
                    'value' => $row['config_value'],
                    'type' => $row['config_type'],
                    'description' => $row['description'],
                    'is_sensitive' => (bool) $row['is_sensitive']
                ];
            }
            
            return $settings;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to export settings', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Import settings from backup
     */
    public function importSettings(array $settings, AdminUserModel $user): array
    {
        $results = [];
        $errors = [];
        
        $this->db->beginTransaction();
        
        try {
            foreach ($settings as $setting) {
                try {
                    $key = $setting['key'];
                    $value = $setting['value'];
                    $type = $setting['type'];
                    $description = $setting['description'] ?? '';
                    $isSensitive = $setting['is_sensitive'] ?? false;
                    
                    // Check if setting exists
                    $existing = $this->getSetting($key);
                    
                    if ($existing) {
                        // Update existing
                        $this->updateSetting($key, $this->parseConfigValue($value, $type), $user);
                    } else {
                        // Add new
                        $this->addSetting($key, $this->parseConfigValue($value, $type), $type, $description, $isSensitive, $user);
                    }
                    
                    $results[$key] = true;
                    
                } catch (\Exception $e) {
                    $results[$key] = false;
                    $errors[$key] = $e->getMessage();
                }
            }
            
            if (empty($errors)) {
                $this->db->commit();
                $this->logger->info('Settings imported successfully', [
                    'count' => count($settings),
                    'imported_by' => $user->getEmail()
                ]);
            } else {
                $this->db->rollback();
                $this->logger->warning('Some settings failed to import', [
                    'errors' => $errors,
                    'imported_by' => $user->getEmail()
                ]);
            }
            
        } catch (\Exception $e) {
            $this->db->rollback();
            $this->logger->error('Failed to import settings', [
                'error' => $e->getMessage(),
                'imported_by' => $user->getEmail()
            ]);
            throw $e;
        }
        
        return ['results' => $results, 'errors' => $errors];
    }
}