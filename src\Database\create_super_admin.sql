-- Super Admin Account <PERSON> Script
-- This script creates the initial Super Admin account for the GuardGeo Admin Platform
-- Run this script after creating the database schema

-- Insert Super Admin account
-- Default credentials: <EMAIL> / SuperAdmin123!
-- IMPORTANT: Change these credentials immediately after first login
INSERT INTO admin_users (
    email,
    password_hash,
    role,
    created_at,
    updated_at,
    is_active
) VALUES (
    '<EMAIL>',
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- Password: SuperAdmin123!
    'super_admin',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    true
) ON CONFLICT (email) DO NOTHING;

-- Verify the Super Admin account was created
SELECT 
    id,
    email,
    role,
    created_at,
    is_active
FROM admin_users 
WHERE role = 'super_admin' 
AND email = '<EMAIL>';

-- Display success message
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM admin_users WHERE email = '<EMAIL>' AND role = 'super_admin') THEN
        RAISE NOTICE 'Super Admin account created successfully!';
        RAISE NOTICE 'Email: <EMAIL>';
        RAISE NOTICE 'Password: SuperAdmin123!';
        RAISE NOTICE 'IMPORTANT: Change these credentials immediately after first login!';
    ELSE
        RAISE NOTICE 'Super Admin account already exists or creation failed.';
    END IF;
END $$;