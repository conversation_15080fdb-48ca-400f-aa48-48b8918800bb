<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Utils\AuthMiddleware;
use Skpassegna\GuardgeoApi\Utils\RoleManager;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Database\AdminUserRepository;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Models\AdminUserModel;
use Skpassegna\GuardgeoApi\Utils\PasswordValidator;
use Skpassegna\GuardgeoApi\Utils\EmailDomainValidator;

/**
 * User Management API Controller
 * 
 * Provides JSON API endpoints for admin user management
 * (Super Admin only) with user creation, editing, and role management.
 */
class UserManagementApiController
{
    private AuthMiddleware $authMiddleware;
    private RoleManager $roleManager;
    private AdminUserRepository $userRepository;
    private PasswordValidator $passwordValidator;
    private EmailDomainValidator $emailValidator;
    private LoggingService $logger;

    public function __construct(
        AuthMiddleware $authMiddleware,
        RoleManager $roleManager,
        AdminUserRepository $userRepository,
        PasswordValidator $passwordValidator,
        EmailDomainValidator $emailValidator,
        LoggingService $logger
    ) {
        $this->authMiddleware = $authMiddleware;
        $this->roleManager = $roleManager;
        $this->userRepository = $userRepository;
        $this->passwordValidator = $passwordValidator;
        $this->emailValidator = $emailValidator;
        $this->logger = $logger;
    }

    /**
     * Get admin users with pagination and filtering
     *
     * @return void
     */
    public function getUsers(): void
    {
        if (!$this->requireSuperAdmin()) {
            return;
        }

        try {
            // Get query parameters
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = min(max(1, (int)($_GET['limit'] ?? 25)), 100);
            $search = $_GET['search'] ?? '';
            $role = $_GET['role'] ?? 'all';
            $status = $_GET['status'] ?? 'all'; // all, active, inactive
            $sortBy = $_GET['sort'] ?? 'created_at';
            $sortOrder = $_GET['order'] ?? 'desc';

            // Build filters
            $filters = [];
            
            if (!empty($search)) {
                $filters['search'] = $search;
            }
            
            if ($role !== 'all') {
                $filters['role'] = $role;
            }
            
            if ($status !== 'all') {
                $filters['status'] = $status;
            }

            // Get users
            $result = $this->getUsersPaginated(
                $page,
                $limit,
                $filters,
                $sortBy,
                $sortOrder
            );

            $this->sendJsonResponse([
                'success' => true,
                'data' => $result['users'],
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $result['total'],
                    'total_pages' => ceil($result['total'] / $limit)
                ],
                'filters' => [
                    'search' => $search,
                    'role' => $role,
                    'status' => $status,
                    'sort' => $sortBy,
                    'order' => $sortOrder
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('User management API error', [
                'error' => $e->getMessage(),
                'user' => $this->authMiddleware->getCurrentUser()['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load users'
            ], 500);
        }
    }

    /**
     * Get detailed user information
     *
     * @return void
     */
    public function getUserDetails(): void
    {
        if (!$this->requireSuperAdmin()) {
            return;
        }

        try {
            $userId = $_GET['id'] ?? '';
            
            if (empty($userId)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'User ID is required'
                ], 400);
                return;
            }

            $user = $this->userRepository->getById($userId);
            
            if (!$user) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'User not found'
                ], 404);
                return;
            }

            // Get user activity stats
            $activityStats = $this->getUserActivityStats($userId);
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => [
                    'user' => $user,
                    'activity_stats' => $activityStats,
                    'available_roles' => $this->roleManager->getAllRoles()
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('User details API error', [
                'error' => $e->getMessage(),
                'user' => $this->authMiddleware->getCurrentUser()['email'],
                'target_user_id' => $_GET['id'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load user details'
            ], 500);
        }
    }

    /**
     * Create new admin user
     *
     * @return void
     */
    public function createUser(): void
    {
        if (!$this->requireSuperAdmin()) {
            return;
        }

        try {
            $requestData = json_decode(file_get_contents('php://input'), true);
            
            // Validate required fields
            $validation = $this->validateUserData($requestData, true);
            if (!$validation['valid']) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Validation failed',
                    'errors' => $validation['errors']
                ], 400);
                return;
            }

            // Check if email already exists
            $existingUser = $this->userRepository->findByEmail($requestData['email']);
            if ($existingUser) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Email address already exists'
                ], 400);
                return;
            }

            // Create user model
            $userData = [
                'email' => $requestData['email'],
                'password_hash' => password_hash($requestData['password'], PASSWORD_DEFAULT),
                'role' => $requestData['role'],
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $user = new AdminUserModel($userData);
            $savedUser = $this->userRepository->save($user);

            // Log the creation
            $currentUser = $this->authMiddleware->getCurrentUser();
            $this->logger->logAdminAction('Admin user created', [
                'created_user_id' => $savedUser->id,
                'created_user_email' => $savedUser->email,
                'created_user_role' => $savedUser->role,
                'created_by' => $currentUser['email']
            ]);

            $this->sendJsonResponse([
                'success' => true,
                'message' => 'User created successfully',
                'data' => [
                    'id' => $savedUser->id,
                    'email' => $savedUser->email,
                    'role' => $savedUser->role
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('User creation API error', [
                'error' => $e->getMessage(),
                'user' => $this->authMiddleware->getCurrentUser()['email'],
                'request_data' => $requestData ?? []
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to create user'
            ], 500);
        }
    }

    /**
     * Update existing admin user
     *
     * @return void
     */
    public function updateUser(): void
    {
        if (!$this->requireSuperAdmin()) {
            return;
        }

        try {
            $requestData = json_decode(file_get_contents('php://input'), true);
            $userId = $requestData['id'] ?? '';
            
            if (empty($userId)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'User ID is required'
                ], 400);
                return;
            }

            // Get existing user
            $existingUser = $this->userRepository->findById($userId);
            if (!$existingUser) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'User not found'
                ], 404);
                return;
            }

            // Validate update data
            $validation = $this->validateUserData($requestData, false);
            if (!$validation['valid']) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Validation failed',
                    'errors' => $validation['errors']
                ], 400);
                return;
            }

            // Check if email change conflicts with existing user
            if (isset($requestData['email']) && $requestData['email'] !== $existingUser->email) {
                $emailConflict = $this->userRepository->findByEmail($requestData['email']);
                if ($emailConflict && $emailConflict->id !== $userId) {
                    $this->sendJsonResponse([
                        'success' => false,
                        'error' => 'Email address already exists'
                    ], 400);
                    return;
                }
            }

            // Prepare update data
            $updateData = [];
            
            if (isset($requestData['email'])) {
                $updateData['email'] = $requestData['email'];
            }
            
            if (isset($requestData['role'])) {
                $updateData['role'] = $requestData['role'];
            }
            
            if (isset($requestData['is_active'])) {
                $updateData['is_active'] = (bool)$requestData['is_active'];
            }
            
            if (!empty($requestData['password'])) {
                $updateData['password_hash'] = password_hash($requestData['password'], PASSWORD_DEFAULT);
            }
            
            $updateData['updated_at'] = date('Y-m-d H:i:s');

            // Update user
            $existingUser->updateFromArray($updateData);
            $updatedUser = $this->userRepository->save($existingUser);

            // Log the update
            $currentUser = $this->authMiddleware->getCurrentUser();
            $this->logger->logAdminAction('Admin user updated', [
                'updated_user_id' => $updatedUser->id,
                'updated_user_email' => $updatedUser->email,
                'updated_fields' => array_keys($updateData),
                'updated_by' => $currentUser['email']
            ]);

            $this->sendJsonResponse([
                'success' => true,
                'message' => 'User updated successfully',
                'data' => [
                    'id' => $updatedUser->id,
                    'email' => $updatedUser->email,
                    'role' => $updatedUser->role,
                    'is_active' => $updatedUser->is_active
                ]
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('User update API error', [
                'error' => $e->getMessage(),
                'user' => $this->authMiddleware->getCurrentUser()['email'],
                'target_user_id' => $requestData['id'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to update user'
            ], 500);
        }
    }

    /**
     * Delete admin user
     *
     * @return void
     */
    public function deleteUser(): void
    {
        if (!$this->requireSuperAdmin()) {
            return;
        }

        try {
            $requestData = json_decode(file_get_contents('php://input'), true);
            $userId = $requestData['id'] ?? '';
            
            if (empty($userId)) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'User ID is required'
                ], 400);
                return;
            }

            // Get user to delete
            $userToDelete = $this->userRepository->findById($userId);
            if (!$userToDelete) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'User not found'
                ], 404);
                return;
            }

            // Prevent self-deletion
            $currentUser = $this->authMiddleware->getCurrentUser();
            if ($userId == $currentUser['id']) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Cannot delete your own account'
                ], 400);
                return;
            }

            // Delete user
            $success = $this->userRepository->delete($userId);
            
            if (!$success) {
                $this->sendJsonResponse([
                    'success' => false,
                    'error' => 'Failed to delete user'
                ], 500);
                return;
            }

            // Log the deletion
            $this->logger->logAdminAction('Admin user deleted', [
                'deleted_user_id' => $userId,
                'deleted_user_email' => $userToDelete->email,
                'deleted_user_role' => $userToDelete->role,
                'deleted_by' => $currentUser['email']
            ]);

            $this->sendJsonResponse([
                'success' => true,
                'message' => 'User deleted successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('User deletion API error', [
                'error' => $e->getMessage(),
                'user' => $this->authMiddleware->getCurrentUser()['email'],
                'target_user_id' => $requestData['id'] ?? 'unknown'
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to delete user'
            ], 500);
        }
    }

    /**
     * Get user management statistics
     *
     * @return void
     */
    public function getUserStatistics(): void
    {
        if (!$this->requireSuperAdmin()) {
            return;
        }

        try {
            $stats = $this->getUserStats();
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('User statistics API error', [
                'error' => $e->getMessage(),
                'user' => $this->authMiddleware->getCurrentUser()['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load user statistics'
            ], 500);
        }
    }

    /**
     * Get available roles for user creation/editing
     *
     * @return void
     */
    public function getAvailableRoles(): void
    {
        if (!$this->requireSuperAdmin()) {
            return;
        }

        try {
            $roles = $this->roleManager->getAllRoles();
            
            $this->sendJsonResponse([
                'success' => true,
                'data' => $roles
            ]);

        } catch (\Exception $e) {
            $this->logger->logError('Available roles API error', [
                'error' => $e->getMessage(),
                'user' => $this->authMiddleware->getCurrentUser()['email']
            ]);

            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Unable to load available roles'
            ], 500);
        }
    }

    /**
     * Get users with pagination
     */
    private function getUsersPaginated(int $page, int $limit, array $filters, string $sortBy, string $sortOrder): array
    {
        $offset = ($page - 1) * $limit;
        
        $whereConditions = [];
        $params = [];
        
        // Apply search filter
        if (!empty($filters['search'])) {
            $whereConditions[] = 'email ILIKE :search';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        // Apply role filter
        if (!empty($filters['role'])) {
            $whereConditions[] = 'role = :role';
            $params['role'] = $filters['role'];
        }
        
        // Apply status filter
        if (!empty($filters['status'])) {
            if ($filters['status'] === 'active') {
                $whereConditions[] = 'is_active = true';
            } elseif ($filters['status'] === 'inactive') {
                $whereConditions[] = 'is_active = false';
            }
        }
        
        // Build WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        // Apply sorting
        $validSortColumns = ['id', 'email', 'role', 'created_at', 'updated_at', 'last_login'];
        if (!in_array($sortBy, $validSortColumns)) {
            $sortBy = 'created_at';
        }
        
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql = "
            SELECT 
                id, email, role, is_active, created_at, updated_at, last_login
            FROM admin_users 
            {$whereClause}
            ORDER BY {$sortBy} {$sortOrder}
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $db = new DatabaseConnection();
        $pdo = $db->getConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $rows = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        $users = [];
        foreach ($rows as $row) {
            $users[] = [
                'id' => $row['id'],
                'email' => $row['email'],
                'role' => $row['role'],
                'role_display' => $this->getRoleDisplayName($row['role']),
                'is_active' => (bool)$row['is_active'],
                'created_at' => $row['created_at'],
                'updated_at' => $row['updated_at'],
                'last_login' => $row['last_login'],
                'formatted_created' => $this->formatRelativeTime($row['created_at']),
                'formatted_last_login' => $row['last_login'] ? $this->formatRelativeTime($row['last_login']) : 'Never'
            ];
        }
        
        return [
            'users' => $users,
            'total' => $this->getUsersCount($filters)
        ];
    }

    /**
     * Get users count with filters
     */
    private function getUsersCount(array $filters): int
    {
        $whereConditions = [];
        $params = [];
        
        // Apply same filters as main query
        if (!empty($filters['search'])) {
            $whereConditions[] = 'email ILIKE :search';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['role'])) {
            $whereConditions[] = 'role = :role';
            $params['role'] = $filters['role'];
        }
        
        if (!empty($filters['status'])) {
            if ($filters['status'] === 'active') {
                $whereConditions[] = 'is_active = true';
            } elseif ($filters['status'] === 'inactive') {
                $whereConditions[] = 'is_active = false';
            }
        }
        
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        $sql = "SELECT COUNT(*) as count FROM admin_users {$whereClause}";
        
        $db = new DatabaseConnection();
        $pdo = $db->getConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $row = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        return (int)($row['count'] ?? 0);
    }

    /**
     * Get user activity statistics
     */
    private function getUserActivityStats(string $userId): array
    {
        $db = new DatabaseConnection();
        $pdo = $db->getConnection();
        
        // Get login count and recent activity
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_actions,
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as actions_last_30_days,
                MAX(created_at) as last_activity
            FROM system_logs 
            WHERE user_id = :user_id
        ");
        $stmt->execute(['user_id' => $userId]);
        $stats = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        return [
            'total_actions' => (int)$stats['total_actions'],
            'actions_last_30_days' => (int)$stats['actions_last_30_days'],
            'last_activity' => $stats['last_activity'],
            'formatted_last_activity' => $stats['last_activity'] ? $this->formatRelativeTime($stats['last_activity']) : 'No activity'
        ];
    }

    /**
     * Get user statistics
     */
    private function getUserStats(): array
    {
        $db = new DatabaseConnection();
        $pdo = $db->getConnection();
        
        // Get user counts by role and status
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
                COUNT(CASE WHEN role = 'super_admin' THEN 1 END) as super_admins,
                COUNT(CASE WHEN role = 'dev' THEN 1 END) as dev_users,
                COUNT(CASE WHEN role = 'marketing' THEN 1 END) as marketing_users,
                COUNT(CASE WHEN role = 'sales' THEN 1 END) as sales_users,
                COUNT(CASE WHEN last_login >= NOW() - INTERVAL '30 days' THEN 1 END) as active_last_30_days
            FROM admin_users
        ");
        $stmt->execute();
        $stats = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        return [
            'total_users' => (int)$stats['total_users'],
            'active_users' => (int)$stats['active_users'],
            'inactive_users' => (int)$stats['total_users'] - (int)$stats['active_users'],
            'roles' => [
                'super_admin' => (int)$stats['super_admins'],
                'dev' => (int)$stats['dev_users'],
                'marketing' => (int)$stats['marketing_users'],
                'sales' => (int)$stats['sales_users']
            ],
            'active_last_30_days' => (int)$stats['active_last_30_days']
        ];
    }

    /**
     * Validate user data
     */
    private function validateUserData(array $data, bool $isCreate): array
    {
        $errors = [];
        
        // Email validation
        if ($isCreate || isset($data['email'])) {
            $email = $data['email'] ?? '';
            if (empty($email)) {
                $errors['email'] = 'Email is required';
            } elseif (!$this->emailValidator->isValidEmail($email)) {
                $errors['email'] = 'Invalid email address or domain not authorized';
            }
        }
        
        // Password validation
        if ($isCreate || (!empty($data['password']))) {
            $password = $data['password'] ?? '';
            if ($isCreate && empty($password)) {
                $errors['password'] = 'Password is required';
            } elseif (!empty($password)) {
                $passwordValidation = $this->passwordValidator->validate($password);
                if (!$passwordValidation['valid']) {
                    $errors['password'] = implode(', ', $passwordValidation['errors']);
                }
            }
        }
        
        // Role validation
        if ($isCreate || isset($data['role'])) {
            $role = $data['role'] ?? '';
            if (empty($role)) {
                $errors['role'] = 'Role is required';
            } elseif (!$this->roleManager->isValidRole($role)) {
                $errors['role'] = 'Invalid role';
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Get role display name
     */
    private function getRoleDisplayName(string $role): string
    {
        return match ($role) {
            'super_admin' => 'Super Admin',
            'dev' => 'Developer',
            'marketing' => 'Marketing',
            'sales' => 'Sales',
            default => ucfirst($role)
        };
    }

    /**
     * Format relative time
     */
    private function formatRelativeTime(string $timestamp): string
    {
        $time = new \DateTime($timestamp);
        $now = new \DateTime();
        $diff = $now->diff($time);

        if ($diff->days > 0) {
            return $diff->days . ' day' . ($diff->days > 1 ? 's' : '') . ' ago';
        } elseif ($diff->h > 0) {
            return $diff->h . ' hour' . ($diff->h > 1 ? 's' : '') . ' ago';
        } elseif ($diff->i > 0) {
            return $diff->i . ' minute' . ($diff->i > 1 ? 's' : '') . ' ago';
        } else {
            return 'Just now';
        }
    }

    /**
     * Require Super Admin access
     *
     * @return bool
     */
    private function requireSuperAdmin(): bool
    {
        if (!$this->authMiddleware->isAuthenticated()) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Authentication required'
            ], 401);
            return false;
        }

        $user = $this->authMiddleware->getCurrentUser();
        
        if (!$this->roleManager->canAccessFeature($user['role'], 'user_management')) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Super Admin access required'
            ], 403);
            return false;
        }

        return true;
    }

    /**
     * Send JSON response
     *
     * @param array $data
     * @param int $statusCode
     * @return void
     */
    private function sendJsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Create instance with dependencies
     *
     * @return self
     */
    public static function create(): self
    {
        $logger = new LoggingService();
        $db = new DatabaseConnection();
        $sessionManager = new \Skpassegna\GuardgeoApi\Utils\SessionManager();
        $passwordValidator = new PasswordValidator();
        $emailValidator = new EmailDomainValidator();
        
        $authService = new \Skpassegna\GuardgeoApi\Services\AuthService(
            $db,
            $sessionManager,
            $passwordValidator,
            $emailValidator,
            $logger
        );
        
        $authMiddleware = new AuthMiddleware($authService);
        $roleManager = new RoleManager();
        $userRepository = new AdminUserRepository($db);

        return new self(
            $authMiddleware,
            $roleManager,
            $userRepository,
            $passwordValidator,
            $emailValidator,
            $logger
        );
    }
}