<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\ErrorLogger;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Utils\SecurityAuditLogger;

/**
 * Error Handler
 * 
 * Comprehensive error handling system with recovery mechanisms,
 * graceful degradation, and production-ready error management.
 */
class ErrorHandler
{
    private ErrorClassifier $classifier;
    private ErrorLogger $errorLogger;
    private ResponseFormatter $responseFormatter;
    private SecurityAuditLogger $securityLogger;
    private LoggingService $logger;
    
    // Recovery strategies
    private array $recoveryStrategies = [];
    
    // Circuit breaker states for external services
    private array $circuitBreakers = [];
    
    // Error rate tracking
    private array $errorRates = [];

    public function __construct(
        ErrorClassifier $classifier,
        ErrorLogger $errorLogger,
        ResponseFormatter $responseFormatter,
        SecurityAuditLogger $securityLogger,
        LoggingService $logger
    ) {
        $this->classifier = $classifier;
        $this->errorLogger = $errorLogger;
        $this->responseFormatter = $responseFormatter;
        $this->securityLogger = $securityLogger;
        $this->logger = $logger;
        
        $this->initializeRecoveryStrategies();
        $this->registerErrorHandlers();
    }

    /**
     * Handle an error with comprehensive processing
     */
    public function handleError(string $errorCode, string $message, array $context = [], ?\Throwable $exception = null): array
    {
        $errorId = uniqid('err_');
        $classification = $this->classifier->classifyError($errorCode);
        
        // Track error rate
        $this->trackErrorRate($errorCode);
        
        // Log the error appropriately
        $this->logError($errorCode, $message, $context, $exception, $errorId, $classification);
        
        // Handle security incidents
        if ($classification['category'] === ErrorClassifier::CATEGORY_SECURITY) {
            $this->handleSecurityIncident($errorCode, $message, $context, $errorId);
        }
        
        // Attempt recovery if possible
        $recoveryResult = $this->attemptRecovery($errorCode, $context);
        
        // Check for circuit breaker activation
        $this->updateCircuitBreaker($errorCode, $classification);
        
        // Prepare response
        $response = $this->prepareErrorResponse($errorCode, $message, $context, $errorId, $classification, $recoveryResult);
        
        return $response;
    }

    /**
     * Handle exceptions with automatic classification
     */
    public function handleException(\Throwable $exception, array $additionalContext = []): array
    {
        $classification = $this->classifier->classifyException($exception);
        $errorCode = $this->getErrorCodeFromException($exception);
        
        $context = array_merge($additionalContext, [
            'exception_class' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $this->sanitizeStackTrace($exception->getTrace())
        ]);
        
        return $this->handleError($errorCode, $exception->getMessage(), $context, $exception);
    }

    /**
     * Handle critical system errors
     */
    public function handleCriticalError(string $message, array $context = []): void
    {
        $errorId = uniqid('crit_');
        
        // Log critical error
        $this->errorLogger->critical('Critical system error', array_merge($context, [
            'error_id' => $errorId,
            'message' => $message,
            'timestamp' => date('c'),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ]));
        
        // Notify administrators (if notification system exists)
        $this->notifyAdministrators($message, $errorId, $context);
        
        // Attempt graceful shutdown if necessary
        if ($this->shouldInitiateGracefulShutdown($context)) {
            $this->initiateGracefulShutdown($errorId);
        }
    }

    /**
     * Get fallback response when primary systems fail
     */
    public function getFallbackResponse(string $operation, array $context = []): array
    {
        $fallbackStrategies = [
            'ip_intelligence' => function($context) {
                return [
                    'success' => false,
                    'error' => [
                        'code' => 'IP_INTELLIGENCE_UNAVAILABLE',
                        'message' => 'IP intelligence service temporarily unavailable',
                        'fallback_used' => true
                    ],
                    'data' => $this->getBasicIpData($context['ip'] ?? null)
                ];
            },
            'freemius_validation' => function($context) {
                return [
                    'success' => false,
                    'error' => [
                        'code' => 'FREEMIUS_UNAVAILABLE',
                        'message' => 'Freemius validation service temporarily unavailable',
                        'fallback_used' => true
                    ]
                ];
            },
            'database_operation' => function($context) {
                return [
                    'success' => false,
                    'error' => [
                        'code' => 'DATABASE_UNAVAILABLE',
                        'message' => 'Database temporarily unavailable',
                        'fallback_used' => true
                    ]
                ];
            }
        ];
        
        if (isset($fallbackStrategies[$operation])) {
            $this->logger->warning('Using fallback response', [
                'operation' => $operation,
                'context' => $context
            ]);
            
            return $fallbackStrategies[$operation]($context);
        }
        
        return [
            'success' => false,
            'error' => [
                'code' => 'SERVICE_UNAVAILABLE',
                'message' => 'Service temporarily unavailable',
                'fallback_used' => true
            ]
        ];
    }

    /**
     * Check if system is in degraded mode
     */
    public function isSystemDegraded(): bool
    {
        $errorThreshold = 0.1; // 10% error rate threshold
        $timeWindow = 300; // 5 minutes
        
        $recentErrors = $this->getRecentErrorRate($timeWindow);
        
        return $recentErrors > $errorThreshold;
    }

    /**
     * Get system health status
     */
    public function getSystemHealthStatus(): array
    {
        $status = [
            'overall_status' => 'healthy',
            'error_rate' => $this->getRecentErrorRate(300),
            'circuit_breakers' => $this->getCircuitBreakerStatus(),
            'degraded_services' => [],
            'timestamp' => date('c')
        ];
        
        // Check if system is degraded
        if ($this->isSystemDegraded()) {
            $status['overall_status'] = 'degraded';
            $status['degraded_services'] = $this->getDegradedServices();
        }
        
        // Check for critical issues
        if ($this->hasCriticalIssues()) {
            $status['overall_status'] = 'critical';
        }
        
        return $status;
    }

    /**
     * Initialize recovery strategies
     */
    private function initializeRecoveryStrategies(): void
    {
        $this->recoveryStrategies = [
            'DATABASE_ERROR' => function($context) {
                // Attempt database reconnection
                return $this->attemptDatabaseRecovery($context);
            },
            'EXTERNAL_API_ERROR' => function($context) {
                // Implement retry with exponential backoff
                return $this->attemptApiRetry($context);
            },
            'MEMORY_ERROR' => function($context) {
                // Clear caches and free memory
                return $this->attemptMemoryRecovery($context);
            },
            'RATE_LIMIT_EXCEEDED' => function($context) {
                // Implement backoff strategy
                return $this->implementRateLimitBackoff($context);
            }
        ];
    }

    /**
     * Register global error handlers
     */
    private function registerErrorHandlers(): void
    {
        // Register PHP error handler
        set_error_handler([$this, 'handlePhpError']);
        
        // Register exception handler
        set_exception_handler([$this, 'handleUncaughtException']);
        
        // Register shutdown handler for fatal errors
        register_shutdown_function([$this, 'handleShutdown']);
    }

    /**
     * Handle PHP errors
     */
    public function handlePhpError(int $severity, string $message, string $file, int $line): bool
    {
        // Don't handle suppressed errors
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $context = [
            'severity' => $severity,
            'file' => $file,
            'line' => $line,
            'error_type' => $this->getErrorTypeName($severity)
        ];
        
        $this->errorLogger->error("PHP Error: $message", $context);
        
        // For fatal errors, handle as critical
        if (in_array($severity, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
            $this->handleCriticalError($message, $context);
        }
        
        return true;
    }

    /**
     * Handle uncaught exceptions
     */
    public function handleUncaughtException(\Throwable $exception): void
    {
        $this->handleException($exception, ['uncaught' => true]);
        
        // Send error response if headers not sent
        if (!headers_sent()) {
            http_response_code(500);
            header('Content-Type: application/json');
            echo $this->responseFormatter->formatInternalError();
        }
    }

    /**
     * Handle shutdown errors
     */
    public function handleShutdown(): void
    {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            $this->handleCriticalError($error['message'], [
                'file' => $error['file'],
                'line' => $error['line'],
                'type' => $error['type']
            ]);
        }
    }

    /**
     * Log error with appropriate level and context
     */
    private function logError(string $errorCode, string $message, array $context, ?\Throwable $exception, string $errorId, array $classification): void
    {
        $logContext = array_merge($context, [
            'error_id' => $errorId,
            'error_code' => $errorCode,
            'classification' => $classification,
            'timestamp' => date('c')
        ]);
        
        if ($exception) {
            $this->errorLogger->logException($exception, $logContext);
        } else {
            $logLevel = $classification['log_level'];
            $this->errorLogger->$logLevel($message, $logContext);
        }
    }

    /**
     * Handle security incidents
     */
    private function handleSecurityIncident(string $errorCode, string $message, array $context, string $errorId): void
    {
        $securityLevel = $this->classifier->getSecurityLevel($errorCode);
        
        // Log security event
        $this->securityLogger->logSecurityEvent($errorCode, [
            'message' => $message,
            'context' => $context,
            'error_id' => $errorId,
            'severity' => $securityLevel,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('c')
        ]);
        
        // Implement immediate response for critical security issues
        if ($securityLevel === ErrorClassifier::SECURITY_CRITICAL) {
            $this->implementSecurityResponse($errorCode, $context);
        }
    }

    /**
     * Attempt error recovery
     */
    private function attemptRecovery(string $errorCode, array $context): array
    {
        if (!isset($this->recoveryStrategies[$errorCode])) {
            return ['attempted' => false, 'success' => false];
        }
        
        try {
            $result = $this->recoveryStrategies[$errorCode]($context);
            
            $this->logger->info('Recovery attempted', [
                'error_code' => $errorCode,
                'success' => $result['success'] ?? false,
                'details' => $result['details'] ?? null
            ]);
            
            return array_merge(['attempted' => true], $result);
            
        } catch (\Exception $e) {
            $this->logger->error('Recovery attempt failed', [
                'error_code' => $errorCode,
                'recovery_error' => $e->getMessage()
            ]);
            
            return ['attempted' => true, 'success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Prepare error response for client
     */
    private function prepareErrorResponse(string $errorCode, string $message, array $context, string $errorId, array $classification, array $recoveryResult): array
    {
        $httpCode = $classification['http_code'];
        $userMessage = $classification['user_message'];
        
        // Sanitize details for client
        $sanitizedDetails = $this->classifier->getSanitizedDetails($errorCode, array_merge($context, [
            'error_id' => $errorId,
            'recovery_attempted' => $recoveryResult['attempted'] ?? false,
            'recovery_successful' => $recoveryResult['success'] ?? false
        ]));
        
        return [
            'http_code' => $httpCode,
            'error_code' => $errorCode,
            'message' => $userMessage,
            'details' => $sanitizedDetails,
            'classification' => [
                'category' => $classification['category'],
                'security_level' => $classification['security_level']
            ]
        ];
    }

    /**
     * Track error rates for monitoring
     */
    private function trackErrorRate(string $errorCode): void
    {
        $minute = floor(time() / 60);
        
        if (!isset($this->errorRates[$minute])) {
            $this->errorRates[$minute] = [];
        }
        
        if (!isset($this->errorRates[$minute][$errorCode])) {
            $this->errorRates[$minute][$errorCode] = 0;
        }
        
        $this->errorRates[$minute][$errorCode]++;
        
        // Clean old data (keep last 10 minutes)
        $cutoff = $minute - 10;
        foreach ($this->errorRates as $time => $data) {
            if ($time < $cutoff) {
                unset($this->errorRates[$time]);
            }
        }
    }

    /**
     * Get recent error rate
     */
    private function getRecentErrorRate(int $timeWindow): float
    {
        $now = time();
        $cutoff = $now - $timeWindow;
        $totalErrors = 0;
        $totalRequests = 1; // Avoid division by zero
        
        foreach ($this->errorRates as $minute => $errors) {
            if (($minute * 60) >= $cutoff) {
                $totalErrors += array_sum($errors);
            }
        }
        
        // This is a simplified calculation - in production you'd track total requests too
        return min($totalErrors / $totalRequests, 1.0);
    }

    /**
     * Update circuit breaker state
     */
    private function updateCircuitBreaker(string $errorCode, array $classification): void
    {
        if ($classification['category'] === ErrorClassifier::CATEGORY_EXTERNAL) {
            $service = $this->getServiceFromErrorCode($errorCode);
            
            if (!isset($this->circuitBreakers[$service])) {
                $this->circuitBreakers[$service] = [
                    'state' => 'closed',
                    'failure_count' => 0,
                    'last_failure' => null,
                    'next_attempt' => null
                ];
            }
            
            $this->circuitBreakers[$service]['failure_count']++;
            $this->circuitBreakers[$service]['last_failure'] = time();
            
            // Open circuit breaker after 5 failures
            if ($this->circuitBreakers[$service]['failure_count'] >= 5) {
                $this->circuitBreakers[$service]['state'] = 'open';
                $this->circuitBreakers[$service]['next_attempt'] = time() + 300; // 5 minutes
            }
        }
    }

    /**
     * Get error code from exception
     */
    private function getErrorCodeFromException(\Throwable $exception): string
    {
        $class = get_class($exception);
        
        if (strpos($class, 'Database') !== false) return 'DATABASE_ERROR';
        if (strpos($class, 'FreemiusApi') !== false) return 'FREEMIUS_API_ERROR';
        if (strpos($class, 'IpRegistryApi') !== false) return 'IP_INTELLIGENCE_ERROR';
        if (strpos($class, 'InvalidArgument') !== false) return 'VALIDATION_ERROR';
        
        return 'INTERNAL_ERROR';
    }

    /**
     * Sanitize stack trace for logging
     */
    private function sanitizeStackTrace(array $trace): array
    {
        return array_slice(array_map(function($frame) {
            return [
                'file' => basename($frame['file'] ?? 'unknown'),
                'line' => $frame['line'] ?? 0,
                'function' => $frame['function'] ?? 'unknown',
                'class' => $frame['class'] ?? null
            ];
        }, $trace), 0, 10);
    }

    /**
     * Get basic IP data for fallback
     */
    private function getBasicIpData(?string $ip): array
    {
        if (!$ip) return [];
        
        return [
            'ip' => $ip,
            'type' => filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) ? 'IPv4' : 'IPv6',
            'fallback' => true,
            'message' => 'Limited data available - service temporarily unavailable'
        ];
    }

    /**
     * Get error type name from severity
     */
    private function getErrorTypeName(int $severity): string
    {
        $types = [
            E_ERROR => 'E_ERROR',
            E_WARNING => 'E_WARNING',
            E_PARSE => 'E_PARSE',
            E_NOTICE => 'E_NOTICE',
            E_CORE_ERROR => 'E_CORE_ERROR',
            E_CORE_WARNING => 'E_CORE_WARNING',
            E_COMPILE_ERROR => 'E_COMPILE_ERROR',
            E_COMPILE_WARNING => 'E_COMPILE_WARNING',
            E_USER_ERROR => 'E_USER_ERROR',
            E_USER_WARNING => 'E_USER_WARNING',
            E_USER_NOTICE => 'E_USER_NOTICE',
            E_STRICT => 'E_STRICT',
            E_RECOVERABLE_ERROR => 'E_RECOVERABLE_ERROR',
            E_DEPRECATED => 'E_DEPRECATED',
            E_USER_DEPRECATED => 'E_USER_DEPRECATED'
        ];
        
        return $types[$severity] ?? 'UNKNOWN';
    }

    /**
     * Placeholder methods for recovery strategies
     */
    private function attemptDatabaseRecovery(array $context): array
    {
        return ['success' => false, 'details' => 'Database recovery not implemented'];
    }

    private function attemptApiRetry(array $context): array
    {
        return ['success' => false, 'details' => 'API retry not implemented'];
    }

    private function attemptMemoryRecovery(array $context): array
    {
        gc_collect_cycles();
        return ['success' => true, 'details' => 'Memory cleanup performed'];
    }

    private function implementRateLimitBackoff(array $context): array
    {
        return ['success' => false, 'details' => 'Rate limit backoff implemented'];
    }

    private function implementSecurityResponse(string $errorCode, array $context): void
    {
        // Placeholder for security response implementation
    }

    private function notifyAdministrators(string $message, string $errorId, array $context): void
    {
        // Placeholder for administrator notification
    }

    private function shouldInitiateGracefulShutdown(array $context): bool
    {
        return false; // Placeholder
    }

    private function initiateGracefulShutdown(string $errorId): void
    {
        // Placeholder for graceful shutdown
    }

    private function getCircuitBreakerStatus(): array
    {
        return $this->circuitBreakers;
    }

    private function getDegradedServices(): array
    {
        return []; // Placeholder
    }

    private function hasCriticalIssues(): bool
    {
        return false; // Placeholder
    }

    private function getServiceFromErrorCode(string $errorCode): string
    {
        $mapping = [
            'FREEMIUS_API_ERROR' => 'freemius',
            'IP_INTELLIGENCE_ERROR' => 'ipregistry',
            'DATABASE_ERROR' => 'database'
        ];
        
        return $mapping[$errorCode] ?? 'unknown';
    }
}