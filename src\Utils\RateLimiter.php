<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Rate Limiter
 * 
 * Advanced rate limiting implementation with configurable thresholds,
 * sliding window algorithm, and persistent storage for production use.
 */
class RateLimiter
{
    private DatabaseConnection $db;
    private LoggingService $logger;
    private array $config;

    // Default rate limiting configurations
    private const DEFAULT_LIMITS = [
        'api_request' => ['requests' => 100, 'window' => 3600], // 100 requests per hour
        'login_attempt' => ['requests' => 5, 'window' => 900],  // 5 attempts per 15 minutes
        'admin_action' => ['requests' => 50, 'window' => 3600], // 50 actions per hour
        'general' => ['requests' => 10, 'window' => 300]        // 10 requests per 5 minutes
    ];

    public function __construct(
        DatabaseConnection $db,
        LoggingService $logger,
        array $config = []
    ) {
        $this->db = $db;
        $this->logger = $logger;
        $this->config = array_merge(self::DEFAULT_LIMITS, $config);
    }

    /**
     * Check if request is within rate limits
     *
     * @param string $identifier Unique identifier (IP, user ID, etc.)
     * @param string $action Action type (api_request, login_attempt, etc.)
     * @param int|null $customLimit Custom request limit
     * @param int|null $customWindow Custom time window in seconds
     * @return bool True if within limits, false if exceeded
     */
    public function checkLimit(
        string $identifier,
        string $action = 'general',
        ?int $customLimit = null,
        ?int $customWindow = null
    ): bool {
        $config = $this->config[$action] ?? $this->config['general'];
        $limit = $customLimit ?? $config['requests'];
        $window = $customWindow ?? $config['window'];

        $key = $this->generateKey($identifier, $action);
        $now = time();

        try {
            // Clean old entries first
            $this->cleanOldEntries($key, $now - $window);

            // Count current requests in window
            $currentCount = $this->getCurrentCount($key, $now - $window);

            if ($currentCount >= $limit) {
                $this->logRateLimitExceeded($identifier, $action, $currentCount, $limit);
                return false;
            }

            // Record this request
            $this->recordRequest($key, $now);
            return true;

        } catch (\Exception $e) {
            $this->logger->logError('Rate limiter error', [
                'identifier' => $identifier,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            
            // Fail open - allow request if rate limiter fails
            return true;
        }
    }    
/**
     * Get current request count for identifier and action
     *
     * @param string $identifier
     * @param string $action
     * @return int Current request count
     */
    public function getCurrentRequestCount(string $identifier, string $action = 'general'): int
    {
        $config = $this->config[$action] ?? $this->config['general'];
        $window = $config['window'];
        $key = $this->generateKey($identifier, $action);
        $now = time();

        try {
            return $this->getCurrentCount($key, $now - $window);
        } catch (\Exception $e) {
            $this->logger->logError('Rate limiter count error', [
                'identifier' => $identifier,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * Reset rate limit for identifier and action
     *
     * @param string $identifier
     * @param string $action
     * @return bool Success status
     */
    public function resetLimit(string $identifier, string $action = 'general'): bool
    {
        $key = $this->generateKey($identifier, $action);

        try {
            $query = "DELETE FROM rate_limits WHERE rate_key = ?";
            $this->db->query($query, [$key]);

            $this->logger->logAdminAction('rate_limit_reset', [
                'identifier' => $identifier,
                'action' => $action
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->logError('Rate limiter reset error', [
                'identifier' => $identifier,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get rate limit status for identifier and action
     *
     * @param string $identifier
     * @param string $action
     * @return array Rate limit status information
     */
    public function getStatus(string $identifier, string $action = 'general'): array
    {
        $config = $this->config[$action] ?? $this->config['general'];
        $limit = $config['requests'];
        $window = $config['window'];
        $key = $this->generateKey($identifier, $action);
        $now = time();

        try {
            $currentCount = $this->getCurrentCount($key, $now - $window);
            $remaining = max(0, $limit - $currentCount);
            $resetTime = $this->getNextResetTime($key, $window);

            return [
                'identifier' => $identifier,
                'action' => $action,
                'limit' => $limit,
                'window' => $window,
                'current_count' => $currentCount,
                'remaining' => $remaining,
                'reset_time' => $resetTime,
                'is_limited' => $currentCount >= $limit
            ];
        } catch (\Exception $e) {
            $this->logger->logError('Rate limiter status error', [
                'identifier' => $identifier,
                'action' => $action,
                'error' => $e->getMessage()
            ]);

            return [
                'identifier' => $identifier,
                'action' => $action,
                'error' => 'Unable to retrieve status'
            ];
        }
    }

    /**
     * Generate unique key for rate limiting
     *
     * @param string $identifier
     * @param string $action
     * @return string Unique key
     */
    private function generateKey(string $identifier, string $action): string
    {
        return hash('sha256', $identifier . ':' . $action);
    }

    /**
     * Clean old entries from rate limit table
     *
     * @param string $key
     * @param int $cutoffTime
     * @return void
     */
    private function cleanOldEntries(string $key, int $cutoffTime): void
    {
        $query = "DELETE FROM rate_limits WHERE rate_key = ? AND request_time < ?";
        $this->db->query($query, [$key, $cutoffTime]);
    }

    /**
     * Get current count of requests in window
     *
     * @param string $key
     * @param int $windowStart
     * @return int Request count
     */
    private function getCurrentCount(string $key, int $windowStart): int
    {
        $query = "SELECT COUNT(*) FROM rate_limits WHERE rate_key = ? AND request_time >= ?";
        $result = $this->db->query($query, [$key, $windowStart]);
        return (int)$result->fetchColumn();
    }

    /**
     * Record a request in the rate limit table
     *
     * @param string $key
     * @param int $timestamp
     * @return void
     */
    private function recordRequest(string $key, int $timestamp): void
    {
        $query = "INSERT INTO rate_limits (rate_key, request_time) VALUES (?, ?)";
        $this->db->query($query, [$key, $timestamp]);
    }

    /**
     * Get next reset time for rate limit
     *
     * @param string $key
     * @param int $window
     * @return int|null Next reset timestamp
     */
    private function getNextResetTime(string $key, int $window): ?int
    {
        $query = "SELECT MIN(request_time) FROM rate_limits WHERE rate_key = ?";
        $result = $this->db->query($query, [$key]);
        $oldestRequest = $result->fetchColumn();

        if ($oldestRequest) {
            return (int)$oldestRequest + $window;
        }

        return null;
    }

    /**
     * Log rate limit exceeded event
     *
     * @param string $identifier
     * @param string $action
     * @param int $currentCount
     * @param int $limit
     * @return void
     */
    private function logRateLimitExceeded(
        string $identifier,
        string $action,
        int $currentCount,
        int $limit
    ): void {
        $this->logger->logError('Rate limit exceeded', [
            'identifier' => $identifier,
            'action' => $action,
            'current_count' => $currentCount,
            'limit' => $limit,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ]);
    }

    /**
     * Bulk cleanup of old rate limit entries
     *
     * @param int $olderThan Timestamp - entries older than this will be deleted
     * @return int Number of entries deleted
     */
    public function cleanupOldEntries(int $olderThan = null): int
    {
        $olderThan = $olderThan ?? (time() - 86400); // Default: 24 hours ago

        try {
            $query = "DELETE FROM rate_limits WHERE request_time < ?";
            $result = $this->db->query($query, [$olderThan]);
            $deletedCount = $result->rowCount();

            $this->logger->logInfo('Rate limit cleanup completed', [
                'deleted_entries' => $deletedCount,
                'cutoff_time' => date('Y-m-d H:i:s', $olderThan)
            ]);

            return $deletedCount;
        } catch (\Exception $e) {
            $this->logger->logError('Rate limit cleanup error', [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * Get rate limiting configuration
     *
     * @return array Current configuration
     */
    public function getConfiguration(): array
    {
        return $this->config;
    }

    /**
     * Update rate limiting configuration
     *
     * @param array $newConfig
     * @return void
     */
    public function updateConfiguration(array $newConfig): void
    {
        $this->config = array_merge($this->config, $newConfig);
    }
}