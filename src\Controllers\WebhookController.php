<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Services\FreemiusWebhookHandler;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;
use Skpassegna\GuardgeoApi\Utils\Logger;

/**
 * Webhook Controller
 * 
 * Handles incoming webhooks from external services like Freemius.
 */
class WebhookController
{
    private FreemiusWebhookHandler $webhookHandler;
    private ResponseFormatter $responseFormatter;
    private Logger $logger;
    
    /**
     * Constructor
     */
    public function __construct(
        ?FreemiusWebhookHandler $webhookHandler = null,
        ?ResponseFormatter $responseFormatter = null
    ) {
        $this->webhookHandler = $webhookHandler ?? new FreemiusWebhookHandler();
        $this->responseFormatter = $responseFormatter ?? new ResponseFormatter();
        $this->logger = new Logger();
    }
    
    /**
     * Handle Freemius webhook
     */
    public function handleFreemiusWebhook(): void
    {
        $this->logger->info("Freemius webhook received", [
            'method' => $_SERVER['REQUEST_METHOD'],
            'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        try {
            // Validate request method
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->responseFormatter->sendError(
                    'Method not allowed',
                    405,
                    'INVALID_METHOD',
                    ['allowed_methods' => ['POST']]
                );
                return;
            }
            
            // Get request headers
            $headers = $this->getAllHeaders();
            
            // Get request payload
            $payload = file_get_contents('php://input');
            
            if (empty($payload)) {
                $this->responseFormatter->sendError(
                    'Empty payload',
                    400,
                    'EMPTY_PAYLOAD'
                );
                return;
            }
            
            // Process the webhook
            $result = $this->webhookHandler->processWebhook($headers, $payload);
            
            if ($result['success']) {
                $this->logger->info("Webhook processed successfully", [
                    'event_type' => $result['event_type'] ?? 'unknown',
                    'result_count' => is_array($result['result']) ? count($result['result']) : 0
                ]);
                
                $this->responseFormatter->sendSuccess([
                    'message' => 'Webhook processed successfully',
                    'event_type' => $result['event_type'] ?? null,
                    'processed_objects' => is_array($result['result']) ? count($result['result']) : 0
                ]);
            } else {
                $this->logger->warning("Webhook processing failed", [
                    'error' => $result['error'],
                    'code' => $result['code']
                ]);
                
                // Determine appropriate HTTP status code based on error
                $statusCode = match($result['code']) {
                    'INVALID_SIGNATURE' => 401,
                    'INVALID_JSON', 'INVALID_STRUCTURE', 'EMPTY_PAYLOAD' => 400,
                    'UNSUPPORTED_EVENT' => 200, // Return 200 for unsupported events to avoid retries
                    default => 500
                };
                
                $this->responseFormatter->sendError(
                    $result['error'],
                    $statusCode,
                    $result['code']
                );
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Webhook controller error", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->responseFormatter->sendError(
                'Internal server error',
                500,
                'INTERNAL_ERROR'
            );
        }
    }
    
    /**
     * Get webhook status and configuration
     */
    public function getWebhookStatus(): void
    {
        try {
            $statistics = $this->webhookHandler->getWebhookStatistics();
            $configuration = $this->webhookHandler->validateConfiguration();
            
            $this->responseFormatter->sendSuccess([
                'webhook_status' => [
                    'configuration_valid' => $configuration['valid'],
                    'configuration_issues' => $configuration['issues'],
                    'supported_events' => $statistics['supported_events'],
                    'webhook_secret_configured' => $statistics['webhook_secret_configured'],
                    'last_processed' => $statistics['last_processed']
                ]
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get webhook status", [
                'error' => $e->getMessage()
            ]);
            
            $this->responseFormatter->sendError(
                'Failed to retrieve webhook status',
                500,
                'STATUS_ERROR'
            );
        }
    }
    
    /**
     * Test webhook configuration
     */
    public function testWebhookConfiguration(): void
    {
        try {
            $configuration = $this->webhookHandler->validateConfiguration();
            
            $testResults = [
                'configuration_valid' => $configuration['valid'],
                'issues' => $configuration['issues'],
                'supported_events' => $configuration['supported_events'],
                'test_timestamp' => date('c')
            ];
            
            if ($configuration['valid']) {
                $this->responseFormatter->sendSuccess([
                    'message' => 'Webhook configuration is valid',
                    'test_results' => $testResults
                ]);
            } else {
                $this->responseFormatter->sendError(
                    'Webhook configuration has issues',
                    400,
                    'CONFIGURATION_INVALID',
                    $testResults
                );
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to test webhook configuration", [
                'error' => $e->getMessage()
            ]);
            
            $this->responseFormatter->sendError(
                'Failed to test webhook configuration',
                500,
                'TEST_ERROR'
            );
        }
    }
    
    /**
     * Get all HTTP headers (cross-platform)
     */
    private function getAllHeaders(): array
    {
        $headers = [];
        
        // Try getallheaders() first (Apache)
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
        } else {
            // Fallback for other web servers
            foreach ($_SERVER as $key => $value) {
                if (strpos($key, 'HTTP_') === 0) {
                    $headerName = str_replace('HTTP_', '', $key);
                    $headerName = str_replace('_', '-', $headerName);
                    $headers[$headerName] = $value;
                }
            }
        }
        
        // Create a normalized version with both original and lowercase keys
        $normalizedHeaders = [];
        foreach ($headers as $key => $value) {
            $normalizedHeaders[$key] = $value; // Keep original case
            $normalizedHeaders[strtolower($key)] = $value; // Add lowercase version
            
            // Also add the HTTP_ prefixed version for compatibility
            $httpKey = 'HTTP_' . strtoupper(str_replace('-', '_', $key));
            $normalizedHeaders[$httpKey] = $value;
            $normalizedHeaders[strtolower($httpKey)] = $value;
        }
        
        return $normalizedHeaders;
    }
}