<?php

namespace Skpassegna\GuardgeoApi\Database;

use Skpassegna\GuardgeoApi\Models\InstallationModel;

/**
 * Installation Repository
 * 
 * Handles database operations for Freemius installations.
 */
class InstallationRepository extends BaseRepository
{
    protected string $table = 'freemius_installations';
    
    /**
     * Find installation by Freemius installation ID
     */
    public function findByInstallationId(int $installationId): ?InstallationModel
    {
        $row = $this->findById($installationId);
        
        return $row ? InstallationModel::fromDatabaseRow($row) : null;
    }
    
    /**
     * Find installation by plugin ID and install ID combination
     */
    public function findByPluginAndInstallId(int $pluginId, int $installId): ?InstallationModel
    {
        $row = $this->findOneBy([
            'plugin_id' => $pluginId,
            'id' => $installId
        ]);
        
        return $row ? InstallationModel::fromDatabaseRow($row) : null;
    }
    
    /**
     * Save installation model to database
     */
    public function save(InstallationModel $installation): InstallationModel
    {
        $data = $installation->toDatabaseArray();
        
        // Check if installation exists
        $existing = $this->findById($installation->id);
        
        if ($existing) {
            // Update existing installation
            $this->update($installation->id, $data);
        } else {
            // Insert new installation
            $this->insert($data);
        }
        
        // Return fresh copy from database
        return $this->findByInstallationId($installation->id);
    }
    
    /**
     * Find installations by product ID
     */
    public function findByProductId(int $productId): array
    {
        $rows = $this->findBy(['plugin_id' => $productId]);
        
        $installations = [];
        foreach ($rows as $row) {
            $installations[] = InstallationModel::fromDatabaseRow($row);
        }
        
        return $installations;
    }
    
    /**
     * Find active installations by product ID
     */
    public function findActiveByProductId(int $productId): array
    {
        $rows = $this->findBy([
            'plugin_id' => $productId,
            'is_active' => true,
            'is_uninstalled' => false
        ]);
        
        $installations = [];
        foreach ($rows as $row) {
            $installations[] = InstallationModel::fromDatabaseRow($row);
        }
        
        return $installations;
    }
    
    /**
     * Find installations that need refresh (expired cache)
     */
    public function findExpiredInstallations(int $maxAgeHours = 6): array
    {
        $cutoffTime = date('Y-m-d H:i:s', time() - ($maxAgeHours * 3600));
        
        $sql = "SELECT * FROM {$this->table} WHERE cached_at < :cutoff_time ORDER BY cached_at ASC";
        $statement = $this->executeQuery($sql, ['cutoff_time' => $cutoffTime]);
        
        $installations = [];
        while ($row = $statement->fetch()) {
            $installations[] = InstallationModel::fromDatabaseRow($row);
        }
        
        return $installations;
    }
    
    /**
     * Get installation statistics
     */
    public function getStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_installations,
                COUNT(CASE WHEN is_active = true THEN 1 END) as active_installations,
                COUNT(CASE WHEN is_premium = true THEN 1 END) as premium_installations,
                COUNT(CASE WHEN is_uninstalled = true THEN 1 END) as uninstalled_installations,
                COUNT(CASE WHEN trial_plan_id IS NOT NULL AND trial_ends > NOW() THEN 1 END) as active_trials,
                AVG(gross) as avg_gross,
                AVG(EXTRACT(EPOCH FROM (NOW() - cached_at))/3600) as avg_cache_age_hours
            FROM {$this->table}
        ";
        
        $statement = $this->executeQuery($sql);
        return $statement->fetch();
    }
    
    /**
     * Get installation statistics by product
     */
    public function getStatisticsByProduct(int $productId): array
    {
        $sql = "
            SELECT 
                plugin_id,
                COUNT(*) as total_installations,
                COUNT(CASE WHEN is_active = true THEN 1 END) as active_installations,
                COUNT(CASE WHEN is_premium = true THEN 1 END) as premium_installations,
                COUNT(CASE WHEN is_uninstalled = true THEN 1 END) as uninstalled_installations,
                COUNT(CASE WHEN trial_plan_id IS NOT NULL AND trial_ends > NOW() THEN 1 END) as active_trials,
                AVG(gross) as avg_gross
            FROM {$this->table}
            WHERE plugin_id = :product_id
            GROUP BY plugin_id
        ";
        
        $statement = $this->executeQuery($sql, ['product_id' => $productId]);
        return $statement->fetch() ?: [];
    }
    
    /**
     * Update installation cache timestamp
     */
    public function updateCacheTimestamp(int $installationId): bool
    {
        return $this->update($installationId, ['cached_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * Find installations by user ID
     */
    public function findByUserId(int $userId): array
    {
        $rows = $this->findBy(['user_id' => $userId]);
        
        $installations = [];
        foreach ($rows as $row) {
            $installations[] = InstallationModel::fromDatabaseRow($row);
        }
        
        return $installations;
    }
    
    /**
     * Find installations by site URL
     */
    public function findBySiteUrl(string $url): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE url = :url ORDER BY created DESC";
        $statement = $this->executeQuery($sql, ['url' => $url]);
        
        $installations = [];
        while ($row = $statement->fetch()) {
            $installations[] = InstallationModel::fromDatabaseRow($row);
        }
        
        return $installations;
    }
    
    /**
     * Find installations that haven't been seen recently
     */
    public function findStaleInstallations(int $daysSinceLastSeen = 30): array
    {
        $cutoffTime = date('Y-m-d H:i:s', time() - ($daysSinceLastSeen * 24 * 3600));
        
        $sql = "
            SELECT * FROM {$this->table} 
            WHERE last_seen_at < :cutoff_time 
            AND is_active = true 
            AND is_uninstalled = false
            ORDER BY last_seen_at ASC
        ";
        
        $statement = $this->executeQuery($sql, ['cutoff_time' => $cutoffTime]);
        
        $installations = [];
        while ($row = $statement->fetch()) {
            $installations[] = InstallationModel::fromDatabaseRow($row);
        }
        
        return $installations;
    }
    
    /**
     * Count installations by status
     */
    public function countByStatus(): array
    {
        $sql = "
            SELECT 
                CASE 
                    WHEN is_uninstalled = true THEN 'uninstalled'
                    WHEN is_active = false THEN 'inactive'
                    WHEN is_premium = true THEN 'premium'
                    ELSE 'free'
                END as status,
                COUNT(*) as count
            FROM {$this->table}
            GROUP BY status
            ORDER BY count DESC
        ";
        
        $statement = $this->executeQuery($sql);
        
        $results = [];
        while ($row = $statement->fetch()) {
            $results[$row['status']] = (int) $row['count'];
        }
        
        return $results;
    }

    /**
     * Get installations with pagination and filtering for admin interface
     */
    public function getInstallationsPaginated(int $page, int $limit, array $filters = [], string $sortBy = 'created', string $sortOrder = 'desc'): array
    {
        $offset = ($page - 1) * $limit;
        
        $whereConditions = [];
        $params = [];
        
        // Apply search filter
        if (!empty($filters['search'])) {
            $whereConditions[] = '(url ILIKE :search OR title ILIKE :search OR id::text = :search_exact)';
            $params['search'] = '%' . $filters['search'] . '%';
            $params['search_exact'] = $filters['search'];
        }
        
        // Apply product filter
        if (!empty($filters['product_id'])) {
            $whereConditions[] = 'plugin_id = :product_id';
            $params['product_id'] = $filters['product_id'];
        }
        
        // Apply status filter
        if (!empty($filters['status'])) {
            switch ($filters['status']) {
                case 'active':
                    $whereConditions[] = 'is_active = true AND is_uninstalled = false';
                    break;
                case 'inactive':
                    $whereConditions[] = 'is_active = false';
                    break;
                case 'premium':
                    $whereConditions[] = 'is_premium = true';
                    break;
                case 'trial':
                    $whereConditions[] = 'trial_plan_id IS NOT NULL AND trial_ends > NOW()';
                    break;
            }
        }
        
        // Build WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        // Apply sorting
        $validSortColumns = ['id', 'created', 'updated', 'url', 'version', 'is_active', 'is_premium', 'last_seen_at'];
        if (!in_array($sortBy, $validSortColumns)) {
            $sortBy = 'created';
        }
        
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql = "
            SELECT * FROM {$this->table} 
            {$whereClause}
            ORDER BY {$sortBy} {$sortOrder}
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        try {
            $statement = $this->executeQuery($sql, $params);
            $rows = $statement->fetchAll();
            
            $installations = [];
            foreach ($rows as $row) {
                $installations[] = $this->mapRowToAdminArray($row);
            }
            
            return [
                'installations' => $installations,
                'total' => $this->getFilteredCount($filters)
            ];
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get paginated installations", [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [
                'installations' => [],
                'total' => 0
            ];
        }
    }

    /**
     * Get installation by ID for admin interface
     */
    public function getById(string $installationId): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = :id";
        
        try {
            $statement = $this->executeQuery($sql, ['id' => $installationId]);
            $row = $statement->fetch();
            
            if (!$row) {
                return null;
            }
            
            return $this->mapRowToAdminArray($row);
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get installation by ID", [
                'installation_id' => $installationId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get count of installations by product ID
     */
    public function getCountByProductId(string $productId): int
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE plugin_id = :product_id";
        
        try {
            $statement = $this->executeQuery($sql, ['product_id' => $productId]);
            $row = $statement->fetch();
            return (int)($row['count'] ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get filtered count for pagination
     */
    private function getFilteredCount(array $filters): int
    {
        $whereConditions = [];
        $params = [];
        
        // Apply same filters as main query
        if (!empty($filters['search'])) {
            $whereConditions[] = '(url ILIKE :search OR title ILIKE :search OR id::text = :search_exact)';
            $params['search'] = '%' . $filters['search'] . '%';
            $params['search_exact'] = $filters['search'];
        }
        
        if (!empty($filters['product_id'])) {
            $whereConditions[] = 'plugin_id = :product_id';
            $params['product_id'] = $filters['product_id'];
        }
        
        if (!empty($filters['status'])) {
            switch ($filters['status']) {
                case 'active':
                    $whereConditions[] = 'is_active = true AND is_uninstalled = false';
                    break;
                case 'inactive':
                    $whereConditions[] = 'is_active = false';
                    break;
                case 'premium':
                    $whereConditions[] = 'is_premium = true';
                    break;
                case 'trial':
                    $whereConditions[] = 'trial_plan_id IS NOT NULL AND trial_ends > NOW()';
                    break;
            }
        }
        
        // Build WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        $sql = "SELECT COUNT(*) as count FROM {$this->table} {$whereClause}";
        
        try {
            $statement = $this->executeQuery($sql, $params);
            $row = $statement->fetch();
            return (int)($row['count'] ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Map database row to admin-friendly array
     */
    private function mapRowToAdminArray(array $row): array
    {
        return [
            'id' => $row['id'],
            'site_id' => $row['site_id'],
            'plugin_id' => $row['plugin_id'],
            'user_id' => $row['user_id'],
            'url' => $row['url'],
            'title' => $row['title'],
            'version' => $row['version'],
            'plan_id' => $row['plan_id'],
            'license_id' => $row['license_id'],
            'trial_plan_id' => $row['trial_plan_id'],
            'trial_ends' => $row['trial_ends'],
            'subscription_id' => $row['subscription_id'],
            'gross' => (float)$row['gross'],
            'country_code' => $row['country_code'],
            'language' => $row['language'],
            'platform_version' => $row['platform_version'],
            'sdk_version' => $row['sdk_version'],
            'programming_language_version' => $row['programming_language_version'],
            'is_active' => (bool)$row['is_active'],
            'is_disconnected' => (bool)$row['is_disconnected'],
            'is_premium' => (bool)$row['is_premium'],
            'is_uninstalled' => (bool)$row['is_uninstalled'],
            'is_locked' => (bool)$row['is_locked'],
            'is_beta' => (bool)$row['is_beta'],
            'source' => (int)$row['source'],
            'upgraded' => $row['upgraded'],
            'last_seen_at' => $row['last_seen_at'],
            'last_served_update_version' => $row['last_served_update_version'],
            'created' => $row['created'],
            'updated' => $row['updated'],
            'cached_at' => $row['cached_at'],
            'status' => $this->determineStatus($row),
            'raw_data' => json_decode($row['raw_data'] ?? 'null', true)
        ];
    }

    /**
     * Determine installation status
     */
    private function determineStatus(array $row): string
    {
        if ($row['is_uninstalled']) {
            return 'uninstalled';
        }
        
        if (!$row['is_active']) {
            return 'inactive';
        }
        
        if ($row['trial_plan_id'] && $row['trial_ends'] && strtotime($row['trial_ends']) > time()) {
            return 'trial';
        }
        
        if ($row['is_premium']) {
            return 'premium';
        }
        
        return 'free';
    }
}