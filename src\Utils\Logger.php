<?php

namespace Skpassegna\GuardgeoApi\Utils;

use DateTime;

/**
 * Logger Utility
 * 
 * Provides logging functionality for database operations and system events.
 * Supports both file and database logging.
 */
class Logger
{
    private const LOG_LEVELS = [
        'debug' => 0,
        'info' => 1,
        'warning' => 2,
        'error' => 3,
        'critical' => 4
    ];
    
    private string $logPath;
    private string $errorLogPath;
    private int $minLevel;
    
    public function __construct(string $logPath = null, int $minLevel = 1)
    {
        $this->logPath = $logPath ?? $this->getDefaultLogPath('combined.log');
        $this->errorLogPath = $this->getDefaultLogPath('error.log');
        $this->minLevel = $minLevel;
        
        // Ensure log directory exists
        $this->ensureLogDirectoryExists();
    }
    
    /**
     * Log debug message
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log('debug', $message, $context);
    }
    
    /**
     * Log info message
     */
    public function info(string $message, array $context = []): void
    {
        $this->log('info', $message, $context);
    }
    
    /**
     * Log warning message
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log('warning', $message, $context);
    }
    
    /**
     * Log error message
     */
    public function error(string $message, array $context = []): void
    {
        $this->log('error', $message, $context);
    }
    
    /**
     * Log critical message
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log('critical', $message, $context);
    }
    
    /**
     * Log message with specified level
     */
    public function log(string $level, string $message, array $context = []): void
    {
        $level = strtolower($level);
        
        // Check if level should be logged
        if (!isset(self::LOG_LEVELS[$level]) || self::LOG_LEVELS[$level] < $this->minLevel) {
            return;
        }
        
        $logEntry = $this->formatLogEntry($level, $message, $context);
        
        // Write to main log file
        $this->writeToFile($this->logPath, $logEntry);
        
        // Write errors and critical messages to error log
        if (in_array($level, ['error', 'critical'])) {
            $this->writeToFile($this->errorLogPath, $logEntry);
        }
        
        // Also log to database if connection is available
        $this->logToDatabase($level, $message, $context);
    }
    
    /**
     * Format log entry
     */
    private function formatLogEntry(string $level, string $message, array $context): string
    {
        $timestamp = (new DateTime())->format('Y-m-d H:i:s');
        $levelUpper = strtoupper($level);
        
        $entry = "[$timestamp] $levelUpper: $message";
        
        if (!empty($context)) {
            $entry .= ' ' . json_encode($context, JSON_UNESCAPED_SLASHES);
        }
        
        return $entry . PHP_EOL;
    }
    
    /**
     * Write log entry to file
     */
    private function writeToFile(string $filePath, string $logEntry): void
    {
        try {
            file_put_contents($filePath, $logEntry, FILE_APPEND | LOCK_EX);
        } catch (\Exception $e) {
            // Fallback to error_log if file writing fails
            error_log("Logger: Failed to write to $filePath - " . $e->getMessage());
            error_log("Logger: " . trim($logEntry));
        }
    }
    
    /**
     * Log to database (if connection is available)
     */
    private function logToDatabase(string $level, string $message, array $context): void
    {
        try {
            // Only attempt database logging if we're not in a database operation
            // to avoid infinite recursion
            if ($this->isDatabaseOperation($context)) {
                return;
            }
            
            // Use DatabaseConnection if available
            if (class_exists('\Skpassegna\GuardgeoApi\Database\DatabaseConnection')) {
                $sql = "INSERT INTO system_logs (type, level, message, context, ip, user_id, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
                
                $params = [
                    'system',
                    $level,
                    $message,
                    json_encode($context, JSON_UNESCAPED_SLASHES),
                    $context['ip'] ?? null,
                    $context['user_id'] ?? null
                ];
                
                \Skpassegna\GuardgeoApi\Database\DatabaseConnection::execute($sql, $params);
            }
            
        } catch (\Exception $e) {
            // Silently fail database logging - file logging is primary
        }
    }
    
    /**
     * Check if this is a database operation to avoid recursion
     */
    private function isDatabaseOperation(array $context): bool
    {
        return isset($context['sql']) || 
               isset($context['database']) || 
               isset($context['query']) ||
               str_contains(json_encode($context), 'database');
    }
    
    /**
     * Get default log path
     */
    private function getDefaultLogPath(string $filename): string
    {
        $rootDir = dirname(__DIR__, 2); // Go up two levels from src/Utils
        return $rootDir . DIRECTORY_SEPARATOR . 'logs' . DIRECTORY_SEPARATOR . $filename;
    }
    
    /**
     * Ensure log directory exists
     */
    private function ensureLogDirectoryExists(): void
    {
        $logDir = dirname($this->logPath);
        
        if (!is_dir($logDir)) {
            try {
                mkdir($logDir, 0755, true);
            } catch (\Exception $e) {
                error_log("Logger: Failed to create log directory $logDir - " . $e->getMessage());
            }
        }
    }
    
    /**
     * Get log file contents
     */
    public function getLogContents(string $type = 'combined', int $lines = 100): array
    {
        $filePath = $type === 'error' ? $this->errorLogPath : $this->logPath;
        
        if (!file_exists($filePath)) {
            return [];
        }
        
        try {
            $content = file_get_contents($filePath);
            $logLines = explode(PHP_EOL, trim($content));
            
            // Return last N lines
            return array_slice($logLines, -$lines);
            
        } catch (\Exception $e) {
            error_log("Logger: Failed to read log file $filePath - " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Clear log file
     */
    public function clearLog(string $type = 'combined'): bool
    {
        $filePath = $type === 'error' ? $this->errorLogPath : $this->logPath;
        
        try {
            return file_put_contents($filePath, '') !== false;
        } catch (\Exception $e) {
            error_log("Logger: Failed to clear log file $filePath - " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get log statistics
     */
    public function getLogStats(): array
    {
        $stats = [
            'combined' => $this->getFileStats($this->logPath),
            'error' => $this->getFileStats($this->errorLogPath)
        ];
        
        return $stats;
    }
    
    /**
     * Get file statistics
     */
    private function getFileStats(string $filePath): array
    {
        if (!file_exists($filePath)) {
            return [
                'exists' => false,
                'size' => 0,
                'lines' => 0,
                'last_modified' => null
            ];
        }
        
        try {
            $size = filesize($filePath);
            $lines = count(file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES));
            $lastModified = filemtime($filePath);
            
            return [
                'exists' => true,
                'size' => $size,
                'size_formatted' => $this->formatBytes($size),
                'lines' => $lines,
                'last_modified' => date('Y-m-d H:i:s', $lastModified)
            ];
            
        } catch (\Exception $e) {
            return [
                'exists' => true,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }
}