<?php
/**
 * GuardGeo Admin Platform Performance Monitor
 * 
 * This script monitors system performance metrics and generates reports.
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Skpassegna\GuardgeoApi\Config\ConfigManager;
use Skpassegna\GuardgeoApi\Database\DatabaseManager;
use Skpassegna\GuardgeoApi\Services\LoggingService;

class PerformanceMonitor
{
    private $config;
    private $logger;
    private $db;
    private $metrics = [];

    public function __construct()
    {
        $this->config = ConfigManager::getInstance();
        $this->logger = new LoggingService();
        $this->db = DatabaseManager::getInstance();
    }

    public function collectMetrics(): array
    {
        $this->collectSystemMetrics();
        $this->collectDatabaseMetrics();
        $this->collectApplicationMetrics();
        $this->collectApiMetrics();

        $report = [
            'timestamp' => date('c'),
            'server' => gethostname(),
            'environment' => $this->config->get('app.env', 'unknown'),
            'metrics' => $this->metrics,
            'summary' => $this->generateSummary()
        ];

        // Store metrics in database for historical analysis
        $this->storeMetrics($report);

        return $report;
    }

    private function collectSystemMetrics(): void
    {
        // CPU Usage
        $cpuUsage = $this->getCpuUsage();
        
        // Memory Usage
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        // Disk Usage
        $diskFree = disk_free_space(__DIR__ . '/../../');
        $diskTotal = disk_total_space(__DIR__ . '/../../');
        $diskUsed = $diskTotal - $diskFree;
        
        // Load Average (Unix systems only)
        $loadAverage = null;
        if (function_exists('sys_getloadavg')) {
            $loadAverage = sys_getloadavg();
        }

        $this->metrics['system'] = [
            'cpu_usage_percent' => $cpuUsage,
            'memory' => [
                'current_bytes' => $memoryUsage,
                'peak_bytes' => $memoryPeak,
                'limit_bytes' => $memoryLimit,
                'usage_percent' => ($memoryUsage / $memoryLimit) * 100
            ],
            'disk' => [
                'free_bytes' => $diskFree,
                'used_bytes' => $diskUsed,
                'total_bytes' => $diskTotal,
                'usage_percent' => ($diskUsed / $diskTotal) * 100
            ],
            'load_average' => $loadAverage,
            'uptime_seconds' => $this->getSystemUptime()
        ];
    }

    private function collectDatabaseMetrics(): void
    {
        try {
            $connection = $this->db->getConnection();
            
            // Database size and connection stats
            $stmt = $connection->query("
                SELECT 
                    pg_size_pretty(pg_database_size(current_database())) as db_size,
                    pg_database_size(current_database()) as db_size_bytes,
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections,
                    (SELECT count(*) FROM pg_stat_activity) as total_connections
            ");
            $dbStats = $stmt->fetch();

            // Query performance stats
            $stmt = $connection->query("
                SELECT 
                    schemaname,
                    tablename,
                    seq_scan,
                    seq_tup_read,
                    idx_scan,
                    idx_tup_fetch,
                    n_tup_ins,
                    n_tup_upd,
                    n_tup_del,
                    n_live_tup,
                    n_dead_tup
                FROM pg_stat_user_tables 
                ORDER BY seq_tup_read + idx_tup_fetch DESC 
                LIMIT 10
            ");
            $tableStats = $stmt->fetchAll();

            // Slow queries (if pg_stat_statements is available)
            $slowQueries = [];
            try {
                $stmt = $connection->query("
                    SELECT 
                        query,
                        calls,
                        total_time,
                        mean_time,
                        rows
                    FROM pg_stat_statements 
                    ORDER BY mean_time DESC 
                    LIMIT 5
                ");
                $slowQueries = $stmt->fetchAll();
            } catch (Exception $e) {
                // pg_stat_statements not available
            }

            // Index usage
            $stmt = $connection->query("
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    idx_scan,
                    idx_tup_read,
                    idx_tup_fetch
                FROM pg_stat_user_indexes 
                WHERE idx_scan > 0
                ORDER BY idx_scan DESC 
                LIMIT 10
            ");
            $indexStats = $stmt->fetchAll();

            $this->metrics['database'] = [
                'size' => $dbStats['db_size'],
                'size_bytes' => (int)$dbStats['db_size_bytes'],
                'connections' => [
                    'active' => (int)$dbStats['active_connections'],
                    'idle' => (int)$dbStats['idle_connections'],
                    'total' => (int)$dbStats['total_connections']
                ],
                'table_stats' => $tableStats,
                'slow_queries' => $slowQueries,
                'index_usage' => $indexStats
            ];

        } catch (Exception $e) {
            $this->metrics['database'] = [
                'error' => $e->getMessage()
            ];
        }
    }

    private function collectApplicationMetrics(): void
    {
        // PHP-FPM stats (if available)
        $phpFpmStats = $this->getPhpFpmStats();
        
        // OPcache stats
        $opcacheStats = null;
        if (function_exists('opcache_get_status')) {
            $opcacheStats = opcache_get_status();
        }

        // Session stats
        $sessionStats = [
            'save_handler' => ini_get('session.save_handler'),
            'save_path' => ini_get('session.save_path'),
            'gc_probability' => ini_get('session.gc_probability'),
            'gc_divisor' => ini_get('session.gc_divisor'),
            'gc_maxlifetime' => ini_get('session.gc_maxlifetime')
        ];

        // Log file sizes
        $logStats = $this->getLogFileStats();

        $this->metrics['application'] = [
            'php_version' => PHP_VERSION,
            'php_fpm' => $phpFpmStats,
            'opcache' => $opcacheStats,
            'sessions' => $sessionStats,
            'logs' => $logStats,
            'extensions' => get_loaded_extensions()
        ];
    }

    private function collectApiMetrics(): void
    {
        try {
            $connection = $this->db->getConnection();
            
            // API request stats for the last hour
            $stmt = $connection->prepare("
                SELECT 
                    COUNT(*) as total_requests,
                    AVG(response_time_ms) as avg_response_time,
                    MAX(response_time_ms) as max_response_time,
                    MIN(response_time_ms) as min_response_time,
                    COUNT(CASE WHEN response_status >= 200 AND response_status < 300 THEN 1 END) as success_count,
                    COUNT(CASE WHEN response_status >= 400 AND response_status < 500 THEN 1 END) as client_error_count,
                    COUNT(CASE WHEN response_status >= 500 THEN 1 END) as server_error_count
                FROM api_requests 
                WHERE created_at > NOW() - INTERVAL '1 hour'
            ");
            $stmt->execute();
            $apiStats = $stmt->fetch();

            // Top IPs by request count
            $stmt = $connection->prepare("
                SELECT 
                    ip,
                    COUNT(*) as request_count,
                    AVG(response_time_ms) as avg_response_time
                FROM api_requests 
                WHERE created_at > NOW() - INTERVAL '1 hour'
                GROUP BY ip 
                ORDER BY request_count DESC 
                LIMIT 10
            ");
            $stmt->execute();
            $topIps = $stmt->fetchAll();

            // Response status distribution
            $stmt = $connection->prepare("
                SELECT 
                    response_status,
                    COUNT(*) as count
                FROM api_requests 
                WHERE created_at > NOW() - INTERVAL '1 hour'
                GROUP BY response_status 
                ORDER BY response_status
            ");
            $stmt->execute();
            $statusDistribution = $stmt->fetchAll();

            // Freemius validation success rate
            $stmt = $connection->prepare("
                SELECT 
                    COUNT(*) as total_validations,
                    COUNT(CASE WHEN freemius_valid = true THEN 1 END) as successful_validations,
                    (COUNT(CASE WHEN freemius_valid = true THEN 1 END) * 100.0 / COUNT(*)) as success_rate
                FROM api_requests 
                WHERE created_at > NOW() - INTERVAL '1 hour'
                AND plugin_id IS NOT NULL
            ");
            $stmt->execute();
            $freemiusStats = $stmt->fetch();

            $this->metrics['api'] = [
                'requests_last_hour' => [
                    'total' => (int)$apiStats['total_requests'],
                    'success_count' => (int)$apiStats['success_count'],
                    'client_error_count' => (int)$apiStats['client_error_count'],
                    'server_error_count' => (int)$apiStats['server_error_count'],
                    'success_rate' => $apiStats['total_requests'] > 0 ? 
                        ($apiStats['success_count'] / $apiStats['total_requests']) * 100 : 0
                ],
                'response_times' => [
                    'average_ms' => round((float)$apiStats['avg_response_time'], 2),
                    'max_ms' => (int)$apiStats['max_response_time'],
                    'min_ms' => (int)$apiStats['min_response_time']
                ],
                'top_ips' => $topIps,
                'status_distribution' => $statusDistribution,
                'freemius_validation' => [
                    'total_attempts' => (int)$freemiusStats['total_validations'],
                    'successful' => (int)$freemiusStats['successful_validations'],
                    'success_rate' => round((float)$freemiusStats['success_rate'], 2)
                ]
            ];

        } catch (Exception $e) {
            $this->metrics['api'] = [
                'error' => $e->getMessage()
            ];
        }
    }

    private function getCpuUsage(): ?float
    {
        if (PHP_OS_FAMILY === 'Linux') {
            $load = sys_getloadavg();
            return $load ? $load[0] * 100 : null;
        }
        
        // For other systems, try to get CPU usage via system commands
        if (PHP_OS_FAMILY === 'Windows') {
            $output = shell_exec('wmic cpu get loadpercentage /value');
            if (preg_match('/LoadPercentage=(\d+)/', $output, $matches)) {
                return (float)$matches[1];
            }
        }
        
        return null;
    }

    private function getSystemUptime(): ?int
    {
        if (PHP_OS_FAMILY === 'Linux') {
            $uptime = file_get_contents('/proc/uptime');
            if ($uptime) {
                return (int)floatval(explode(' ', $uptime)[0]);
            }
        }
        
        return null;
    }

    private function getPhpFpmStats(): ?array
    {
        // Try to get PHP-FPM status via HTTP (if configured)
        $statusUrl = $this->config->get('monitoring.php_fpm_status_url');
        if (!$statusUrl) {
            return null;
        }

        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'user_agent' => 'GuardGeo-Monitor/1.0'
                ]
            ]);
            
            $response = file_get_contents($statusUrl, false, $context);
            if ($response) {
                return json_decode($response, true);
            }
        } catch (Exception $e) {
            // PHP-FPM status not available
        }

        return null;
    }

    private function getLogFileStats(): array
    {
        $logDir = __DIR__ . '/../../logs/';
        $logFiles = ['combined.log', 'error.log'];
        $stats = [];

        foreach ($logFiles as $logFile) {
            $filePath = $logDir . $logFile;
            
            if (file_exists($filePath)) {
                $stats[$logFile] = [
                    'size_bytes' => filesize($filePath),
                    'last_modified' => date('c', filemtime($filePath)),
                    'lines_count' => $this->countFileLines($filePath)
                ];
            } else {
                $stats[$logFile] = [
                    'exists' => false
                ];
            }
        }

        return $stats;
    }

    private function countFileLines(string $filePath): int
    {
        $lineCount = 0;
        $handle = fopen($filePath, 'r');
        
        if ($handle) {
            while (!feof($handle)) {
                fgets($handle);
                $lineCount++;
            }
            fclose($handle);
        }
        
        return $lineCount;
    }

    private function generateSummary(): array
    {
        $summary = [
            'overall_health' => 'healthy',
            'alerts' => []
        ];

        // Check system metrics
        if (isset($this->metrics['system'])) {
            $system = $this->metrics['system'];
            
            if ($system['memory']['usage_percent'] > 90) {
                $summary['overall_health'] = 'critical';
                $summary['alerts'][] = 'High memory usage: ' . round($system['memory']['usage_percent'], 1) . '%';
            } elseif ($system['memory']['usage_percent'] > 80) {
                if ($summary['overall_health'] === 'healthy') {
                    $summary['overall_health'] = 'warning';
                }
                $summary['alerts'][] = 'Elevated memory usage: ' . round($system['memory']['usage_percent'], 1) . '%';
            }

            if ($system['disk']['usage_percent'] > 90) {
                $summary['overall_health'] = 'critical';
                $summary['alerts'][] = 'High disk usage: ' . round($system['disk']['usage_percent'], 1) . '%';
            } elseif ($system['disk']['usage_percent'] > 80) {
                if ($summary['overall_health'] === 'healthy') {
                    $summary['overall_health'] = 'warning';
                }
                $summary['alerts'][] = 'Elevated disk usage: ' . round($system['disk']['usage_percent'], 1) . '%';
            }
        }

        // Check API metrics
        if (isset($this->metrics['api']['requests_last_hour'])) {
            $api = $this->metrics['api']['requests_last_hour'];
            
            if ($api['success_rate'] < 95 && $api['total'] > 10) {
                $summary['overall_health'] = 'critical';
                $summary['alerts'][] = 'Low API success rate: ' . round($api['success_rate'], 1) . '%';
            } elseif ($api['success_rate'] < 98 && $api['total'] > 10) {
                if ($summary['overall_health'] === 'healthy') {
                    $summary['overall_health'] = 'warning';
                }
                $summary['alerts'][] = 'Reduced API success rate: ' . round($api['success_rate'], 1) . '%';
            }
        }

        // Check response times
        if (isset($this->metrics['api']['response_times']['average_ms'])) {
            $avgResponseTime = $this->metrics['api']['response_times']['average_ms'];
            
            if ($avgResponseTime > 2000) {
                $summary['overall_health'] = 'critical';
                $summary['alerts'][] = 'High average response time: ' . $avgResponseTime . 'ms';
            } elseif ($avgResponseTime > 1000) {
                if ($summary['overall_health'] === 'healthy') {
                    $summary['overall_health'] = 'warning';
                }
                $summary['alerts'][] = 'Elevated average response time: ' . $avgResponseTime . 'ms';
            }
        }

        return $summary;
    }

    private function storeMetrics(array $report): void
    {
        try {
            $connection = $this->db->getConnection();
            
            $stmt = $connection->prepare("
                INSERT INTO performance_metrics (
                    timestamp, 
                    server, 
                    environment, 
                    metrics_data, 
                    overall_health
                ) VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $report['timestamp'],
                $report['server'],
                $report['environment'],
                json_encode($report['metrics']),
                $report['summary']['overall_health']
            ]);

        } catch (Exception $e) {
            $this->logger->logError('Failed to store performance metrics', [
                'error' => $e->getMessage()
            ]);
        }
    }

    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;

        switch ($last) {
            case 'g':
                $limit *= 1024;
            case 'm':
                $limit *= 1024;
            case 'k':
                $limit *= 1024;
        }

        return $limit;
    }

    public function generateReport(int $hours = 24): array
    {
        try {
            $connection = $this->db->getConnection();
            
            $stmt = $connection->prepare("
                SELECT 
                    timestamp,
                    server,
                    environment,
                    metrics_data,
                    overall_health
                FROM performance_metrics 
                WHERE timestamp > NOW() - INTERVAL ? HOUR
                ORDER BY timestamp DESC
            ");
            $stmt->execute([$hours]);
            $historicalData = $stmt->fetchAll();

            return [
                'report_period_hours' => $hours,
                'total_data_points' => count($historicalData),
                'historical_data' => $historicalData,
                'trends' => $this->analyzeTrends($historicalData)
            ];

        } catch (Exception $e) {
            return [
                'error' => 'Failed to generate performance report: ' . $e->getMessage()
            ];
        }
    }

    private function analyzeTrends(array $historicalData): array
    {
        if (empty($historicalData)) {
            return [];
        }

        $trends = [
            'memory_usage' => [],
            'disk_usage' => [],
            'api_success_rate' => [],
            'response_time' => []
        ];

        foreach ($historicalData as $dataPoint) {
            $metrics = json_decode($dataPoint['metrics_data'], true);
            
            if (isset($metrics['system']['memory']['usage_percent'])) {
                $trends['memory_usage'][] = $metrics['system']['memory']['usage_percent'];
            }
            
            if (isset($metrics['system']['disk']['usage_percent'])) {
                $trends['disk_usage'][] = $metrics['system']['disk']['usage_percent'];
            }
            
            if (isset($metrics['api']['requests_last_hour']['success_rate'])) {
                $trends['api_success_rate'][] = $metrics['api']['requests_last_hour']['success_rate'];
            }
            
            if (isset($metrics['api']['response_times']['average_ms'])) {
                $trends['response_time'][] = $metrics['api']['response_times']['average_ms'];
            }
        }

        // Calculate trend statistics
        foreach ($trends as $metric => $values) {
            if (!empty($values)) {
                $trends[$metric] = [
                    'current' => $values[0],
                    'average' => array_sum($values) / count($values),
                    'min' => min($values),
                    'max' => max($values),
                    'trend' => $this->calculateTrend($values)
                ];
            }
        }

        return $trends;
    }

    private function calculateTrend(array $values): string
    {
        if (count($values) < 2) {
            return 'stable';
        }

        $recent = array_slice($values, 0, min(5, count($values)));
        $older = array_slice($values, -min(5, count($values)));

        $recentAvg = array_sum($recent) / count($recent);
        $olderAvg = array_sum($older) / count($older);

        $change = (($recentAvg - $olderAvg) / $olderAvg) * 100;

        if ($change > 5) {
            return 'increasing';
        } elseif ($change < -5) {
            return 'decreasing';
        } else {
            return 'stable';
        }
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    $monitor = new PerformanceMonitor();
    
    if (isset($argv[1]) && $argv[1] === 'report') {
        $hours = isset($argv[2]) ? (int)$argv[2] : 24;
        $report = $monitor->generateReport($hours);
        echo json_encode($report, JSON_PRETTY_PRINT) . PHP_EOL;
    } else {
        $metrics = $monitor->collectMetrics();
        echo json_encode($metrics, JSON_PRETTY_PRINT) . PHP_EOL;
    }
}