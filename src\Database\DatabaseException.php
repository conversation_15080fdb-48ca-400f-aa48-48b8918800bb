<?php

namespace Skpassegna\GuardgeoApi\Database;

use Exception;

/**
 * Database Exception
 * 
 * Custom exception class for database-related errors
 */
class DatabaseException extends Exception
{
    private array $context = [];
    
    public function __construct(string $message = "", int $code = 0, ?Exception $previous = null, array $context = [])
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }
    
    /**
     * Get additional context information
     */
    public function getContext(): array
    {
        return $this->context;
    }
    
    /**
     * Set additional context information
     */
    public function setContext(array $context): void
    {
        $this->context = $context;
    }
}