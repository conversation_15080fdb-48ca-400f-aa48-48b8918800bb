<?php

namespace Skpassegna\GuardgeoApi\Utils;

/**
 * Form Validator
 * 
 * Comprehensive form validation utility for admin interface forms
 * with support for complex validation rules and custom error messages.
 */
class FormValidator
{
    private array $errors = [];
    private array $warnings = [];
    private array $data = [];
    private array $rules = [];
    private array $customMessages = [];

    /**
     * Validate form data against rules
     *
     * @param array $data Form data to validate
     * @param array $rules Validation rules
     * @param array $customMessages Custom error messages
     * @return array Validation result
     */
    public function validate(array $data, array $rules, array $customMessages = []): array
    {
        $this->data = $data;
        $this->rules = $rules;
        $this->customMessages = $customMessages;
        $this->errors = [];
        $this->warnings = [];

        foreach ($rules as $field => $fieldRules) {
            $this->validateField($field, $fieldRules);
        }

        return [
            'valid' => empty($this->errors),
            'errors' => $this->errors,
            'warnings' => $this->warnings,
            'validated_data' => $this->getValidatedData()
        ];
    }

    /**
     * Validate a single field
     *
     * @param string $field Field name
     * @param string|array $rules Validation rules
     * @return void
     */
    private function validateField(string $field, $rules): void
    {
        if (is_string($rules)) {
            $rules = explode('|', $rules);
        }

        $value = $this->data[$field] ?? null;

        foreach ($rules as $rule) {
            $this->applyRule($field, $value, $rule);
        }
    }

    /**
     * Apply a validation rule
     *
     * @param string $field Field name
     * @param mixed $value Field value
     * @param string $rule Validation rule
     * @return void
     */
    private function applyRule(string $field, $value, string $rule): void
    {
        // Parse rule and parameters
        $ruleParts = explode(':', $rule, 2);
        $ruleName = $ruleParts[0];
        $parameters = isset($ruleParts[1]) ? explode(',', $ruleParts[1]) : [];

        switch ($ruleName) {
            case 'required':
                $this->validateRequired($field, $value);
                break;
            case 'email':
                $this->validateEmail($field, $value);
                break;
            case 'min':
                $this->validateMin($field, $value, (int)$parameters[0]);
                break;
            case 'max':
                $this->validateMax($field, $value, (int)$parameters[0]);
                break;
            case 'numeric':
                $this->validateNumeric($field, $value);
                break;
            case 'integer':
                $this->validateInteger($field, $value);
                break;
            case 'url':
                $this->validateUrl($field, $value);
                break;
            case 'in':
                $this->validateIn($field, $value, $parameters);
                break;
            case 'regex':
                $this->validateRegex($field, $value, $parameters[0]);
                break;
            case 'confirmed':
                $this->validateConfirmed($field, $value);
                break;
            case 'unique':
                $this->validateUnique($field, $value, $parameters);
                break;
            case 'password':
                $this->validatePassword($field, $value);
                break;
            case 'role':
                $this->validateRole($field, $value);
                break;
            case 'domain':
                $this->validateDomain($field, $value);
                break;
        }
    }

    /**
     * Validate required field
     */
    private function validateRequired(string $field, $value): void
    {
        if ($value === null || $value === '' || (is_array($value) && empty($value))) {
            $this->addError($field, 'required', 'The :field field is required.');
        }
    }

    /**
     * Validate email format
     */
    private function validateEmail(string $field, $value): void
    {
        if ($value !== null && $value !== '' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $this->addError($field, 'email', 'The :field must be a valid email address.');
        }
    }

    /**
     * Validate minimum length/value
     */
    private function validateMin(string $field, $value, int $min): void
    {
        if ($value === null || $value === '') {
            return;
        }

        if (is_string($value) && strlen($value) < $min) {
            $this->addError($field, 'min', "The :field must be at least {$min} characters.");
        } elseif (is_numeric($value) && $value < $min) {
            $this->addError($field, 'min', "The :field must be at least {$min}.");
        }
    }

    /**
     * Validate maximum length/value
     */
    private function validateMax(string $field, $value, int $max): void
    {
        if ($value === null || $value === '') {
            return;
        }

        if (is_string($value) && strlen($value) > $max) {
            $this->addError($field, 'max', "The :field may not be greater than {$max} characters.");
        } elseif (is_numeric($value) && $value > $max) {
            $this->addError($field, 'max', "The :field may not be greater than {$max}.");
        }
    }

    /**
     * Validate numeric value
     */
    private function validateNumeric(string $field, $value): void
    {
        if ($value !== null && $value !== '' && !is_numeric($value)) {
            $this->addError($field, 'numeric', 'The :field must be a number.');
        }
    }

    /**
     * Validate integer value
     */
    private function validateInteger(string $field, $value): void
    {
        if ($value !== null && $value !== '' && !filter_var($value, FILTER_VALIDATE_INT)) {
            $this->addError($field, 'integer', 'The :field must be an integer.');
        }
    }

    /**
     * Validate URL format
     */
    private function validateUrl(string $field, $value): void
    {
        if ($value !== null && $value !== '' && !filter_var($value, FILTER_VALIDATE_URL)) {
            $this->addError($field, 'url', 'The :field must be a valid URL.');
        }
    }

    /**
     * Validate value is in allowed list
     */
    private function validateIn(string $field, $value, array $allowed): void
    {
        if ($value !== null && $value !== '' && !in_array($value, $allowed)) {
            $allowedStr = implode(', ', $allowed);
            $this->addError($field, 'in', "The :field must be one of: {$allowedStr}.");
        }
    }

    /**
     * Validate regex pattern
     */
    private function validateRegex(string $field, $value, string $pattern): void
    {
        if ($value !== null && $value !== '' && !preg_match($pattern, $value)) {
            $this->addError($field, 'regex', 'The :field format is invalid.');
        }
    }

    /**
     * Validate confirmed field (password confirmation)
     */
    private function validateConfirmed(string $field, $value): void
    {
        $confirmField = $field . '_confirmation';
        $confirmValue = $this->data[$confirmField] ?? null;

        if ($value !== $confirmValue) {
            $this->addError($field, 'confirmed', 'The :field confirmation does not match.');
        }
    }

    /**
     * Validate unique value (requires database check)
     */
    private function validateUnique(string $field, $value, array $parameters): void
    {
        if ($value === null || $value === '') {
            return;
        }

        $table = $parameters[0] ?? '';
        $column = $parameters[1] ?? $field;
        $except = $parameters[2] ?? null;

        if (empty($table)) {
            return;
        }

        try {
            $db = new \Skpassegna\GuardgeoApi\Database\DatabaseConnection();
            $query = "SELECT COUNT(*) FROM {$table} WHERE {$column} = ?";
            $params = [$value];

            if ($except) {
                $query .= " AND id != ?";
                $params[] = $except;
            }

            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $count = $stmt->fetchColumn();

            if ($count > 0) {
                $this->addError($field, 'unique', 'The :field has already been taken.');
            }
        } catch (\Exception $e) {
            // Log error but don't fail validation
            error_log("Unique validation error: " . $e->getMessage());
        }
    }

    /**
     * Validate password strength
     */
    private function validatePassword(string $field, $value): void
    {
        if ($value === null || $value === '') {
            return;
        }

        $passwordValidator = new PasswordValidator();
        $result = $passwordValidator->validatePassword($value);

        if (!$result['valid']) {
            foreach ($result['errors'] as $error) {
                $this->addError($field, 'password', $error);
            }
        }

        if (!empty($result['warnings'])) {
            foreach ($result['warnings'] as $warning) {
                $this->addWarning($field, $warning);
            }
        }
    }

    /**
     * Validate user role
     */
    private function validateRole(string $field, $value): void
    {
        if ($value === null || $value === '') {
            return;
        }

        $allowedRoles = ['super_admin', 'dev', 'marketing', 'sales'];
        if (!in_array($value, $allowedRoles)) {
            $this->addError($field, 'role', 'The :field must be a valid user role.');
        }
    }

    /**
     * Validate email domain
     */
    private function validateDomain(string $field, $value): void
    {
        if ($value === null || $value === '') {
            return;
        }

        try {
            $emailValidator = new EmailDomainValidator();
            $result = $emailValidator->validateEmailDomain($value);

            if (!$result['valid']) {
                foreach ($result['errors'] as $error) {
                    $this->addError($field, 'domain', $error);
                }
            }

            if (!empty($result['warnings'])) {
                foreach ($result['warnings'] as $warning) {
                    $this->addWarning($field, $warning);
                }
            }
        } catch (\Exception $e) {
            // Log error but don't fail validation
            error_log("Domain validation error: " . $e->getMessage());
        }
    }

    /**
     * Add validation error
     */
    private function addError(string $field, string $rule, string $message): void
    {
        $customKey = "{$field}.{$rule}";
        $finalMessage = $this->customMessages[$customKey] ?? $message;
        $finalMessage = str_replace(':field', ucfirst(str_replace('_', ' ', $field)), $finalMessage);

        $this->errors[$field][] = $finalMessage;
    }

    /**
     * Add validation warning
     */
    private function addWarning(string $field, string $message): void
    {
        $this->warnings[$field][] = $message;
    }

    /**
     * Get validated data (only fields that passed validation)
     */
    private function getValidatedData(): array
    {
        $validated = [];
        
        foreach ($this->rules as $field => $rules) {
            if (!isset($this->errors[$field]) && isset($this->data[$field])) {
                $validated[$field] = $this->data[$field];
            }
        }

        return $validated;
    }

    /**
     * Get all errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get all warnings
     */
    public function getWarnings(): array
    {
        return $this->warnings;
    }

    /**
     * Check if validation passed
     */
    public function passes(): bool
    {
        return empty($this->errors);
    }

    /**
     * Check if validation failed
     */
    public function fails(): bool
    {
        return !empty($this->errors);
    }

    /**
     * Get first error for a field
     */
    public function first(string $field): ?string
    {
        return $this->errors[$field][0] ?? null;
    }

    /**
     * Get all errors for a field
     */
    public function get(string $field): array
    {
        return $this->errors[$field] ?? [];
    }
}