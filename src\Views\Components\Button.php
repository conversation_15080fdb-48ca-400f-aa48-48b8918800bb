<?php

namespace Skpassegna\GuardgeoApi\Views\Components;

/**
 * Button Component
 * 
 * Reusable button component with various styles, sizes, and states
 * following the design system guidelines.
 */
class Button extends BaseComponent
{
    protected function getDefaultClasses(): string
    {
        $variant = $this->prop('variant', 'primary');
        $size = $this->prop('size', 'md');
        $disabled = $this->prop('disabled', false);

        $baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
        
        // Size classes
        $sizeClasses = match ($size) {
            'xs' => 'px-2.5 py-1.5 text-xs',
            'sm' => 'px-3 py-2 text-sm',
            'md' => 'px-4 py-2 text-sm',
            'lg' => 'px-4 py-2 text-base',
            'xl' => 'px-6 py-3 text-base',
            default => 'px-4 py-2 text-sm'
        };

        // Variant classes
        $variantClasses = match ($variant) {
            'primary' => 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
            'secondary' => 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
            'success' => 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
            'danger' => 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
            'warning' => 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500',
            'outline' => 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',
            'ghost' => 'text-gray-700 hover:bg-gray-100 focus:ring-blue-500',
            default => 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
        };

        // Disabled state
        if ($disabled) {
            $variantClasses = 'bg-gray-300 text-gray-500 cursor-not-allowed';
        }

        return trim($baseClasses . ' ' . $sizeClasses . ' ' . $variantClasses);
    }

    public function render(): string
    {
        $text = $this->prop('text', '');
        $icon = $this->prop('icon');
        $iconPosition = $this->prop('iconPosition', 'left');
        $href = $this->prop('href');
        $onclick = $this->prop('onclick');
        $type = $this->prop('type', 'button');
        $disabled = $this->prop('disabled', false);
        $loading = $this->prop('loading', false);

        // Determine tag
        $tag = $href ? 'a' : 'button';
        
        $html = '<' . $tag;
        $html .= ' class="' . $this->getClasses() . '"';

        // Add attributes based on tag type
        if ($tag === 'a' && $href) {
            $html .= ' href="' . $this->escape($href) . '"';
        } else {
            $html .= ' type="' . $this->escape($type) . '"';
            if ($disabled || $loading) {
                $html .= ' disabled';
            }
        }

        if ($onclick) {
            $html .= ' onclick="' . $this->escape($onclick) . '"';
        }

        // Add custom attributes
        if ($this->attributes) {
            $html .= ' ' . $this->renderAttributes();
        }

        $html .= '>';

        // Loading state
        if ($loading) {
            $html .= '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">';
            $html .= '<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>';
            $html .= '<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>';
            $html .= '</svg>';
            $html .= 'Loading...';
        } else {
            // Icon and text
            if ($icon && $iconPosition === 'left') {
                $html .= '<i class="' . $this->escape($icon) . ($text ? ' mr-2' : '') . '"></i>';
            }
            
            if ($text) {
                $html .= $this->escape($text);
            }
            
            if ($icon && $iconPosition === 'right') {
                $html .= '<i class="' . $this->escape($icon) . ($text ? ' ml-2' : '') . '"></i>';
            }
        }

        $html .= '</' . $tag . '>';

        return $html;
    }

    /**
     * Create a primary button
     */
    public static function primary(string $text, array $props = []): self
    {
        return new self(array_merge(['text' => $text, 'variant' => 'primary'], $props));
    }

    /**
     * Create a secondary button
     */
    public static function secondary(string $text, array $props = []): self
    {
        return new self(array_merge(['text' => $text, 'variant' => 'secondary'], $props));
    }

    /**
     * Create a danger button
     */
    public static function danger(string $text, array $props = []): self
    {
        return new self(array_merge(['text' => $text, 'variant' => 'danger'], $props));
    }

    /**
     * Create an outline button
     */
    public static function outline(string $text, array $props = []): self
    {
        return new self(array_merge(['text' => $text, 'variant' => 'outline'], $props));
    }
}