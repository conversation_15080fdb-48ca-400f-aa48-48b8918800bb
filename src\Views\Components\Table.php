<?php

namespace Skpassegna\GuardgeoApi\Views\Components;

/**
 * Table Component
 * 
 * Reusable table component with sorting, pagination, and filtering support
 * following the design system guidelines.
 */
class Table extends BaseComponent
{
    protected function getDefaultClasses(): string
    {
        return 'min-w-full divide-y divide-gray-200';
    }

    public function render(): string
    {
        $columns = $this->prop('columns', []);
        $data = $this->prop('data', []);
        $sortable = $this->prop('sortable', false);
        $hoverable = $this->prop('hoverable', true);
        $striped = $this->prop('striped', false);

        $html = '<div class="overflow-x-auto">';
        $html .= '<table class="' . $this->getClasses() . '"';
        
        if ($this->attributes) {
            $html .= ' ' . $this->renderAttributes();
        }
        
        $html .= '>';

        // Header
        if (!empty($columns)) {
            $html .= '<thead class="bg-gray-50">';
            $html .= '<tr>';
            
            foreach ($columns as $column) {
                $html .= '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"';
                
                if ($sortable && isset($column['sortable']) && $column['sortable']) {
                    $html .= ' class="cursor-pointer hover:bg-gray-100"';
                    if (isset($column['onclick'])) {
                        $html .= ' onclick="' . $this->escape($column['onclick']) . '"';
                    }
                }
                
                $html .= '>';
                $html .= $this->escape($column['title'] ?? $column['key'] ?? '');
                
                if ($sortable && isset($column['sorted'])) {
                    $icon = $column['sorted'] === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
                    $html .= ' <i class="' . $icon . ' ml-1"></i>';
                }
                
                $html .= '</th>';
            }
            
            $html .= '</tr>';
            $html .= '</thead>';
        }

        // Body
        $html .= '<tbody class="bg-white divide-y divide-gray-200">';
        
        if (empty($data)) {
            $html .= '<tr>';
            $html .= '<td colspan="' . count($columns) . '" class="px-6 py-12 text-center text-gray-500">';
            $html .= $this->prop('emptyMessage', 'No data available');
            $html .= '</td>';
            $html .= '</tr>';
        } else {
            foreach ($data as $index => $row) {
                $rowClass = 'bg-white';
                
                if ($hoverable) {
                    $rowClass .= ' hover:bg-gray-50';
                }
                
                if ($striped && $index % 2 === 1) {
                    $rowClass = 'bg-gray-50';
                    if ($hoverable) {
                        $rowClass .= ' hover:bg-gray-100';
                    }
                }
                
                $html .= '<tr class="' . $rowClass . '">';
                
                foreach ($columns as $column) {
                    $key = $column['key'] ?? '';
                    $value = $row[$key] ?? '';
                    
                    $html .= '<td class="px-6 py-4 whitespace-nowrap">';
                    
                    if (isset($column['render']) && is_callable($column['render'])) {
                        $html .= $column['render']($value, $row, $index);
                    } else {
                        $html .= '<div class="text-sm text-gray-900">' . $this->escape($value) . '</div>';
                    }
                    
                    $html .= '</td>';
                }
                
                $html .= '</tr>';
            }
        }
        
        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Create a simple data table
     */
    public static function simple(array $columns, array $data, array $props = []): self
    {
        return new self(array_merge([
            'columns' => $columns,
            'data' => $data
        ], $props));
    }

    /**
     * Create a sortable table
     */
    public static function sortable(array $columns, array $data, array $props = []): self
    {
        return new self(array_merge([
            'columns' => $columns,
            'data' => $data,
            'sortable' => true
        ], $props));
    }
}