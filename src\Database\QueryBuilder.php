<?php

namespace Skpassegna\GuardgeoApi\Database;

use PDOStatement;

/**
 * Query Builder
 * 
 * Provides a fluent interface for building complex SQL queries
 * with prepared statement support.
 */
class QueryBuilder
{
    private string $table = '';
    private array $select = ['*'];
    private array $joins = [];
    private array $where = [];
    private array $groupBy = [];
    private array $having = [];
    private array $orderBy = [];
    private ?int $limit = null;
    private ?int $offset = null;
    private array $parameters = [];
    private int $parameterIndex = 0;
    
    /**
     * Set the table to query
     */
    public function table(string $table): self
    {
        $this->table = $table;
        return $this;
    }
    
    /**
     * Set SELECT columns
     */
    public function select(array|string $columns = ['*']): self
    {
        $this->select = is_array($columns) ? $columns : [$columns];
        return $this;
    }
    
    /**
     * Add JOIN clause
     */
    public function join(string $table, string $condition, string $type = 'INNER'): self
    {
        $this->joins[] = [
            'type' => strtoupper($type),
            'table' => $table,
            'condition' => $condition
        ];
        return $this;
    }
    
    /**
     * Add LEFT JOIN clause
     */
    public function leftJoin(string $table, string $condition): self
    {
        return $this->join($table, $condition, 'LEFT');
    }
    
    /**
     * Add RIGHT JOIN clause
     */
    public function rightJoin(string $table, string $condition): self
    {
        return $this->join($table, $condition, 'RIGHT');
    }
    
    /**
     * Add WHERE clause
     */
    public function where(string $column, mixed $operator, mixed $value = null): self
    {
        // If only two parameters, assume equals operator
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $paramName = $this->addParameter($value);
        $this->where[] = [
            'type' => 'AND',
            'condition' => "$column $operator :$paramName"
        ];
        
        return $this;
    }
    
    /**
     * Add OR WHERE clause
     */
    public function orWhere(string $column, mixed $operator, mixed $value = null): self
    {
        // If only two parameters, assume equals operator
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $paramName = $this->addParameter($value);
        $this->where[] = [
            'type' => 'OR',
            'condition' => "$column $operator :$paramName"
        ];
        
        return $this;
    }
    
    /**
     * Add WHERE IN clause
     */
    public function whereIn(string $column, array $values): self
    {
        $paramNames = [];
        foreach ($values as $value) {
            $paramNames[] = ':' . $this->addParameter($value);
        }
        
        $this->where[] = [
            'type' => 'AND',
            'condition' => "$column IN (" . implode(', ', $paramNames) . ")"
        ];
        
        return $this;
    }
    
    /**
     * Add WHERE NOT IN clause
     */
    public function whereNotIn(string $column, array $values): self
    {
        $paramNames = [];
        foreach ($values as $value) {
            $paramNames[] = ':' . $this->addParameter($value);
        }
        
        $this->where[] = [
            'type' => 'AND',
            'condition' => "$column NOT IN (" . implode(', ', $paramNames) . ")"
        ];
        
        return $this;
    }
    
    /**
     * Add WHERE NULL clause
     */
    public function whereNull(string $column): self
    {
        $this->where[] = [
            'type' => 'AND',
            'condition' => "$column IS NULL"
        ];
        
        return $this;
    }
    
    /**
     * Add WHERE NOT NULL clause
     */
    public function whereNotNull(string $column): self
    {
        $this->where[] = [
            'type' => 'AND',
            'condition' => "$column IS NOT NULL"
        ];
        
        return $this;
    }
    
    /**
     * Add WHERE LIKE clause
     */
    public function whereLike(string $column, string $pattern): self
    {
        $paramName = $this->addParameter($pattern);
        $this->where[] = [
            'type' => 'AND',
            'condition' => "$column LIKE :$paramName"
        ];
        
        return $this;
    }
    
    /**
     * Add WHERE BETWEEN clause
     */
    public function whereBetween(string $column, mixed $min, mixed $max): self
    {
        $minParam = $this->addParameter($min);
        $maxParam = $this->addParameter($max);
        
        $this->where[] = [
            'type' => 'AND',
            'condition' => "$column BETWEEN :$minParam AND :$maxParam"
        ];
        
        return $this;
    }
    
    /**
     * Add raw WHERE clause
     */
    public function whereRaw(string $condition, array $parameters = []): self
    {
        foreach ($parameters as $key => $value) {
            $this->parameters[$key] = $value;
        }
        
        $this->where[] = [
            'type' => 'AND',
            'condition' => $condition
        ];
        
        return $this;
    }
    
    /**
     * Add GROUP BY clause
     */
    public function groupBy(string|array $columns): self
    {
        $columns = is_array($columns) ? $columns : [$columns];
        $this->groupBy = array_merge($this->groupBy, $columns);
        return $this;
    }
    
    /**
     * Add HAVING clause
     */
    public function having(string $condition): self
    {
        $this->having[] = $condition;
        return $this;
    }
    
    /**
     * Add ORDER BY clause
     */
    public function orderBy(string $column, string $direction = 'ASC'): self
    {
        $direction = strtoupper($direction) === 'DESC' ? 'DESC' : 'ASC';
        $this->orderBy[] = "$column $direction";
        return $this;
    }
    
    /**
     * Set LIMIT clause
     */
    public function limit(int $limit): self
    {
        $this->limit = $limit;
        return $this;
    }
    
    /**
     * Set OFFSET clause
     */
    public function offset(int $offset): self
    {
        $this->offset = $offset;
        return $this;
    }
    
    /**
     * Build and execute the query
     */
    public function get(): array
    {
        $sql = $this->buildSelectQuery();
        $statement = DatabaseConnection::execute($sql, $this->parameters);
        return $statement->fetchAll();
    }
    
    /**
     * Get first result
     */
    public function first(): ?array
    {
        $this->limit(1);
        $results = $this->get();
        return $results[0] ?? null;
    }
    
    /**
     * Count results
     */
    public function count(): int
    {
        $originalSelect = $this->select;
        $this->select = ['COUNT(*) as count'];
        
        $sql = $this->buildSelectQuery();
        $statement = DatabaseConnection::execute($sql, $this->parameters);
        $result = $statement->fetch();
        
        $this->select = $originalSelect; // Restore original select
        return (int) $result['count'];
    }
    
    /**
     * Check if any results exist
     */
    public function exists(): bool
    {
        return $this->count() > 0;
    }
    
    /**
     * Execute raw SQL query
     */
    public function raw(string $sql, array $parameters = []): PDOStatement
    {
        return DatabaseConnection::execute($sql, $parameters);
    }
    
    /**
     * Build SELECT query
     */
    private function buildSelectQuery(): string
    {
        $sql = 'SELECT ' . implode(', ', $this->select);
        $sql .= ' FROM ' . $this->table;
        
        // Add JOINs
        foreach ($this->joins as $join) {
            $sql .= " {$join['type']} JOIN {$join['table']} ON {$join['condition']}";
        }
        
        // Add WHERE clauses
        if (!empty($this->where)) {
            $sql .= ' WHERE ';
            $whereConditions = [];
            
            foreach ($this->where as $index => $where) {
                if ($index === 0) {
                    $whereConditions[] = $where['condition'];
                } else {
                    $whereConditions[] = $where['type'] . ' ' . $where['condition'];
                }
            }
            
            $sql .= implode(' ', $whereConditions);
        }
        
        // Add GROUP BY
        if (!empty($this->groupBy)) {
            $sql .= ' GROUP BY ' . implode(', ', $this->groupBy);
        }
        
        // Add HAVING
        if (!empty($this->having)) {
            $sql .= ' HAVING ' . implode(' AND ', $this->having);
        }
        
        // Add ORDER BY
        if (!empty($this->orderBy)) {
            $sql .= ' ORDER BY ' . implode(', ', $this->orderBy);
        }
        
        // Add LIMIT
        if ($this->limit !== null) {
            $sql .= ' LIMIT ' . $this->limit;
        }
        
        // Add OFFSET
        if ($this->offset !== null) {
            $sql .= ' OFFSET ' . $this->offset;
        }
        
        return $sql;
    }
    
    /**
     * Add parameter and return parameter name
     */
    private function addParameter(mixed $value): string
    {
        $paramName = 'param_' . $this->parameterIndex++;
        $this->parameters[$paramName] = $value;
        return $paramName;
    }
    
    /**
     * Reset query builder state
     */
    public function reset(): self
    {
        $this->table = '';
        $this->select = ['*'];
        $this->joins = [];
        $this->where = [];
        $this->groupBy = [];
        $this->having = [];
        $this->orderBy = [];
        $this->limit = null;
        $this->offset = null;
        $this->parameters = [];
        $this->parameterIndex = 0;
        
        return $this;
    }
    
    /**
     * Get the built SQL query (for debugging)
     */
    public function toSql(): string
    {
        return $this->buildSelectQuery();
    }
    
    /**
     * Get query parameters (for debugging)
     */
    public function getParameters(): array
    {
        return $this->parameters;
    }
}