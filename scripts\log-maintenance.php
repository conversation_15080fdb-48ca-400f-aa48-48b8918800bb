<?php

/**
 * Log Maintenance Script
 * 
 * Comprehensive log maintenance including rotation, cleanup, and archiving.
 * Handles both database logs and file logs with configurable retention policies.
 * 
 * Usage:
 * php scripts/log-maintenance.php [--task=rotate|cleanup|stats|all] [--verbose] [--dry-run]
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Skpassegna\GuardgeoApi\Services\LogRotationService;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Utils\Logger;

// Parse command line arguments
$options = getopt('', ['task:', 'verbose', 'dry-run', 'help', 'retention-days:', 'max-size:']);

if (isset($options['help'])) {
    showHelp();
    exit(0);
}

$task = $options['task'] ?? 'all';
$verbose = isset($options['verbose']);
$dryRun = isset($options['dry-run']);
$retentionDays = isset($options['retention-days']) ? (int)$options['retention-days'] : null;
$maxSize = isset($options['max-size']) ? parseSize($options['max-size']) : null;

// Initialize services
$logger = new Logger();
$loggingService = new LoggingService();

// Configure log rotation service
$config = [];
if ($retentionDays) {
    $config['retention_days'] = [
        'system_logs' => $retentionDays,
        'api_requests' => $retentionDays,
        'file_logs' => $retentionDays,
        'error_logs' => $retentionDays
    ];
}
if ($maxSize) {
    $config['size_limits'] = [
        'combined_log' => $maxSize,
        'error_log' => $maxSize
    ];
}

$logRotationService = new LogRotationService($config);

$logger->info("Starting log maintenance script", [
    'task' => $task,
    'verbose' => $verbose,
    'dry_run' => $dryRun,
    'retention_days' => $retentionDays,
    'max_size' => $maxSize
]);

try {
    switch ($task) {
        case 'rotate':
            performLogRotation($logRotationService, $logger, $verbose, $dryRun);
            break;
            
        case 'cleanup':
            performLogCleanup($logRotationService, $loggingService, $logger, $verbose, $dryRun);
            break;
            
        case 'stats':
            showLogStatistics($logRotationService, $loggingService, $verbose);
            break;
            
        case 'all':
            performLogRotation($logRotationService, $logger, $verbose, $dryRun);
            performLogCleanup($logRotationService, $loggingService, $logger, $verbose, $dryRun);
            showLogStatistics($logRotationService, $loggingService, $verbose);
            break;
            
        default:
            echo "Error: Unknown task '{$task}'. Use --help for usage information.\n";
            exit(1);
    }
    
    $logger->info("Log maintenance script completed successfully");
    echo "Log maintenance completed successfully.\n";
    
} catch (Exception $e) {
    $logger->error("Log maintenance script failed", [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    echo "Error: Log maintenance failed - " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Perform log rotation
 */
function performLogRotation(LogRotationService $logRotationService, Logger $logger, bool $verbose, bool $dryRun): void
{
    if ($verbose) {
        echo "Starting log rotation" . ($dryRun ? " (DRY RUN)" : "") . "...\n";
    }
    
    if ($dryRun) {
        // Show what would be rotated without actually doing it
        $stats = $logRotationService->getLogStatistics();
        
        if ($verbose) {
            echo "Files that would be rotated:\n";
            foreach ($stats['file_logs'] as $type => $fileStats) {
                if ($fileStats['needs_rotation'] ?? false) {
                    echo "- {$type}.log ({$fileStats['size_formatted']})\n";
                }
            }
            
            echo "Database records that would be cleaned:\n";
            if (isset($stats['database_logs']['system_logs'])) {
                $oldRecords = $stats['database_logs']['system_logs']['total_count'] - 
                             $stats['database_logs']['system_logs']['last_30_days'];
                echo "- System logs: ~{$oldRecords} old records\n";
            }
            if (isset($stats['database_logs']['api_requests'])) {
                $oldRecords = $stats['database_logs']['api_requests']['total_count'] - 
                             $stats['database_logs']['api_requests']['last_30_days'];
                echo "- API requests: ~{$oldRecords} old records\n";
            }
        }
        
        return;
    }
    
    $results = $logRotationService->performLogRotation();
    
    if ($verbose) {
        echo "Log Rotation Results:\n";
        echo "- Files rotated: " . count($results['file_rotation']['files_rotated']) . "\n";
        echo "- Archives created: " . count($results['file_rotation']['archives_created']) . "\n";
        echo "- Database records cleaned: " . 
             ($results['database_cleanup']['system_logs_deleted'] + 
              $results['database_cleanup']['api_requests_deleted']) . "\n";
        echo "- Space freed: " . formatBytes($results['total_space_freed']) . "\n";
        echo "- Execution time: " . round($results['execution_time'], 2) . "s\n";
        
        if (!empty($results['errors'])) {
            echo "Errors:\n";
            foreach ($results['errors'] as $error) {
                echo "- {$error}\n";
            }
        }
    }
    
    $logger->info("Log rotation completed", $results);
}

/**
 * Perform log cleanup
 */
function performLogCleanup(LogRotationService $logRotationService, LoggingService $loggingService, Logger $logger, bool $verbose, bool $dryRun): void
{
    if ($verbose) {
        echo "Starting log cleanup" . ($dryRun ? " (DRY RUN)" : "") . "...\n";
    }
    
    if ($dryRun) {
        echo "DRY RUN: Would clean up old log entries and archives\n";
        return;
    }
    
    // Clean up old logs using LoggingService
    $oldLogsDeleted = $loggingService->clearOldLogs(30); // 30 days default
    
    // Clean up old archives
    $archiveResults = $logRotationService->cleanupOldArchives();
    
    if ($verbose) {
        echo "Cleanup Results:\n";
        echo "- Old log entries deleted: {$oldLogsDeleted}\n";
        echo "- Archives deleted: " . count($archiveResults['archives_deleted']) . "\n";
        echo "- Space freed from archives: " . formatBytes($archiveResults['space_freed']) . "\n";
        
        if (!empty($archiveResults['errors'])) {
            echo "Errors:\n";
            foreach ($archiveResults['errors'] as $error) {
                echo "- {$error}\n";
            }
        }
    }
    
    $logger->info("Log cleanup completed", [
        'old_logs_deleted' => $oldLogsDeleted,
        'archive_results' => $archiveResults
    ]);
}

/**
 * Show log statistics
 */
function showLogStatistics(LogRotationService $logRotationService, LoggingService $loggingService, bool $verbose): void
{
    echo "Log Statistics:\n";
    echo "===============\n\n";
    
    // Get statistics from both services
    $rotationStats = $logRotationService->getLogStatistics();
    $loggingStats = $loggingService->getLoggingStats();
    
    // Database logs
    echo "Database Logs:\n";
    if (isset($rotationStats['database_logs']['system_logs'])) {
        $systemLogs = $rotationStats['database_logs']['system_logs'];
        echo "- System Logs: {$systemLogs['total_count']} total, {$systemLogs['last_7_days']} last 7 days\n";
        echo "  Oldest: {$systemLogs['oldest_entry']}, Newest: {$systemLogs['newest_entry']}\n";
    }
    
    if (isset($rotationStats['database_logs']['api_requests'])) {
        $apiRequests = $rotationStats['database_logs']['api_requests'];
        echo "- API Requests: {$apiRequests['total_count']} total, {$apiRequests['last_7_days']} last 7 days\n";
        echo "  Oldest: {$apiRequests['oldest_entry']}, Newest: {$apiRequests['newest_entry']}\n";
    }
    
    echo "\n";
    
    // File logs
    echo "File Logs:\n";
    foreach ($rotationStats['file_logs'] as $type => $fileStats) {
        if ($fileStats['exists'] ?? true) {
            echo "- " . ucfirst($type) . " Log: {$fileStats['size_formatted']}, {$fileStats['lines']} lines\n";
            echo "  Last modified: {$fileStats['last_modified']}\n";
            echo "  Needs rotation: " . ($fileStats['needs_rotation'] ? 'YES' : 'NO') . "\n";
        } else {
            echo "- " . ucfirst($type) . " Log: Not found\n";
        }
    }
    
    echo "\n";
    
    // Archives
    echo "Archives:\n";
    $archiveStats = $rotationStats['archives'];
    if ($archiveStats['archive_directory_exists']) {
        echo "- Total archives: {$archiveStats['total_archives']}/{$archiveStats['max_archives_allowed']}\n";
        echo "- Total size: {$archiveStats['total_size_formatted']}\n";
        
        if ($verbose && !empty($archiveStats['archives'])) {
            echo "  Recent archives:\n";
            foreach (array_slice($archiveStats['archives'], 0, 5) as $archive) {
                echo "    {$archive['name']} ({$archive['size_formatted']}) - {$archive['created']}\n";
            }
        }
    } else {
        echo "- Archive directory does not exist\n";
    }
    
    echo "\n";
    
    // Retention configuration
    echo "Retention Configuration:\n";
    foreach ($rotationStats['retention_config'] as $type => $days) {
        echo "- " . ucfirst(str_replace('_', ' ', $type)) . ": {$days} days\n";
    }
    
    echo "\n";
    
    // Recent logging activity (if verbose)
    if ($verbose && isset($loggingStats['system_logs'])) {
        echo "Recent Logging Activity (Last 7 Days):\n";
        foreach ($loggingStats['system_logs'] as $logEntry) {
            echo "- {$logEntry['type']} {$logEntry['level']}: {$logEntry['count']} entries\n";
        }
        
        if (isset($loggingStats['api_requests'])) {
            echo "\nAPI Request Summary (Last 7 Days):\n";
            foreach ($loggingStats['api_requests'] as $apiStat) {
                $avgTime = $apiStat['avg_response_time'] ? round($apiStat['avg_response_time'], 2) . 'ms' : 'N/A';
                echo "- Status {$apiStat['response_status']}: {$apiStat['count']} requests (avg: {$avgTime})\n";
            }
        }
    }
}

/**
 * Parse size string (e.g., "50MB", "1GB") to bytes
 */
function parseSize(string $size): int
{
    $size = strtoupper(trim($size));
    $unit = substr($size, -2);
    $value = (int)substr($size, 0, -2);
    
    return match($unit) {
        'KB' => $value * 1024,
        'MB' => $value * 1024 * 1024,
        'GB' => $value * 1024 * 1024 * 1024,
        default => (int)$size
    };
}

/**
 * Format bytes to human readable format
 */
function formatBytes(int $bytes): string
{
    $units = ['B', 'KB', 'MB', 'GB'];
    $unitIndex = 0;
    
    while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
        $bytes /= 1024;
        $unitIndex++;
    }
    
    return round($bytes, 2) . ' ' . $units[$unitIndex];
}

/**
 * Show help information
 */
function showHelp(): void
{
    echo "Log Maintenance Script\n";
    echo "======================\n\n";
    echo "Usage: php scripts/log-maintenance.php [OPTIONS]\n\n";
    echo "Options:\n";
    echo "  --task=TASK           Task to perform (rotate|cleanup|stats|all) [default: all]\n";
    echo "  --retention-days=N    Override retention period in days\n";
    echo "  --max-size=SIZE       Override maximum file size (e.g., 50MB, 1GB)\n";
    echo "  --verbose             Show detailed output\n";
    echo "  --dry-run             Show what would be done without making changes\n";
    echo "  --help                Show this help message\n\n";
    echo "Tasks:\n";
    echo "  rotate                Rotate large log files and clean old database entries\n";
    echo "  cleanup               Clean up old log entries and archive files\n";
    echo "  stats                 Show comprehensive log statistics\n";
    echo "  all                   Perform all maintenance tasks\n\n";
    echo "Examples:\n";
    echo "  php scripts/log-maintenance.php --task=rotate --verbose\n";
    echo "  php scripts/log-maintenance.php --task=cleanup --retention-days=60\n";
    echo "  php scripts/log-maintenance.php --dry-run --verbose\n";
    echo "  php scripts/log-maintenance.php --task=stats\n";
}