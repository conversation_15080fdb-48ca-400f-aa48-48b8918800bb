<?php

namespace Skpassegna\GuardgeoApi\Views\Pages;

use Skpassegna\GuardgeoApi\Views\Components\BaseComponent;

/**
 * Maintenance Page
 * 
 * System maintenance and health monitoring interface for administrators.
 * Provides comprehensive system health checks, backup management, and maintenance tasks.
 */
class MaintenancePage extends BaseComponent
{
    /**
     * Render the maintenance page
     */
    public function render(array $data = []): string
    {
        $user = $data['user'] ?? [];
        $navigation = $data['navigation'] ?? [];
        $canManageBackups = $data['can_manage_backups'] ?? false;
        $canRunMigrations = $data['can_run_migrations'] ?? false;
        $canClearCache = $data['can_clear_cache'] ?? false;

        return <<<HTML
        <div class="space-y-6">
            <!-- Page Header -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">System Maintenance</h1>
                            <p class="mt-1 text-sm text-gray-600">Monitor system health and perform maintenance tasks</p>
                        </div>
                        <div class="flex space-x-3">
                            <button onclick="refreshHealthStatus()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-sync-alt mr-2"></i>
                                Refresh Status
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Health Overview -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Overall Health Status -->
                <div class="bg-white shadow rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div id="health-status-icon" class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-heart text-green-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">System Health</h3>
                            <p id="health-status-text" class="text-sm text-gray-600">Checking...</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="runComprehensiveHealthCheck()" class="text-sm text-blue-600 hover:text-blue-800">
                            Run comprehensive check →
                        </button>
                    </div>
                </div>

                <!-- Database Status -->
                <div class="bg-white shadow rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div id="db-status-icon" class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-database text-blue-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Database</h3>
                            <p id="db-status-text" class="text-sm text-gray-600">Checking...</p>
                        </div>
                    </div>
                </div>

                <!-- External APIs Status -->
                <div class="bg-white shadow rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div id="api-status-icon" class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-plug text-purple-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">External APIs</h3>
                            <p id="api-status-text" class="text-sm text-gray-600">Checking...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Health Checks -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Detailed Health Checks</h2>
                </div>
                <div class="p-6">
                    <div id="health-checks-container" class="space-y-4">
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>Loading health check results...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Maintenance Tasks -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Backup Management -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Backup Management</h2>
                    </div>
                    <div class="p-6">
                        {$this->renderBackupSection($canManageBackups)}
                    </div>
                </div>

                <!-- Cache Management -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Cache Management</h2>
                    </div>
                    <div class="p-6">
                        {$this->renderCacheSection($canClearCache)}
                    </div>
                </div>

                <!-- Database Maintenance -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Database Maintenance</h2>
                    </div>
                    <div class="p-6">
                        {$this->renderDatabaseSection($canRunMigrations)}
                    </div>
                </div>

                <!-- System Information -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">System Information</h2>
                    </div>
                    <div class="p-6">
                        {$this->renderSystemInfoSection()}
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Performance Metrics</h2>
                </div>
                <div class="p-6">
                    <div id="performance-metrics-container">
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-chart-line text-2xl mb-2"></i>
                            <p>Loading performance metrics...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {$this->renderMaintenanceScripts()}
        HTML;
    }

    /**
     * Render backup management section
     */
    private function renderBackupSection(bool $canManage): string
    {
        if (!$canManage) {
            return '<p class="text-gray-500">You do not have permission to manage backups.</p>';
        }

        return <<<HTML
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-medium text-gray-900">Latest Backup</h3>
                    <p id="latest-backup-info" class="text-sm text-gray-600">Loading...</p>
                </div>
                <button onclick="createBackup()" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-plus mr-2"></i>
                    Create Backup
                </button>
            </div>
            
            <div class="border-t pt-4">
                <div class="flex space-x-2">
                    <button onclick="listBackups()" class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-list mr-2"></i>
                        View All Backups
                    </button>
                    <button onclick="cleanupOldBackups()" class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-trash mr-2"></i>
                        Cleanup Old
                    </button>
                </div>
            </div>
        </div>
        HTML;
    }

    /**
     * Render cache management section
     */
    private function renderCacheSection(bool $canClear): string
    {
        if (!$canClear) {
            return '<p class="text-gray-500">You do not have permission to manage cache.</p>';
        }

        return <<<HTML
        <div class="space-y-4">
            <div>
                <h3 class="text-sm font-medium text-gray-900">Cache Statistics</h3>
                <div id="cache-stats" class="mt-2 text-sm text-gray-600">Loading...</div>
            </div>
            
            <div class="border-t pt-4">
                <div class="grid grid-cols-2 gap-2">
                    <button onclick="clearCache('ip')" class="inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-globe mr-2"></i>
                        Clear IP Cache
                    </button>
                    <button onclick="clearCache('freemius')" class="inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-key mr-2"></i>
                        Clear Freemius
                    </button>
                    <button onclick="clearCache('opcache')" class="inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-code mr-2"></i>
                        Clear OPcache
                    </button>
                    <button onclick="clearCache('all')" class="inline-flex justify-center items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50">
                        <i class="fas fa-broom mr-2"></i>
                        Clear All
                    </button>
                </div>
            </div>
        </div>
        HTML;
    }

    /**
     * Render database maintenance section
     */
    private function renderDatabaseSection(bool $canMigrate): string
    {
        if (!$canMigrate) {
            return '<p class="text-gray-500">You do not have permission to manage database.</p>';
        }

        return <<<HTML
        <div class="space-y-4">
            <div>
                <h3 class="text-sm font-medium text-gray-900">Migration Status</h3>
                <div id="migration-status" class="mt-2 text-sm text-gray-600">Loading...</div>
            </div>
            
            <div class="border-t pt-4">
                <div class="grid grid-cols-2 gap-2">
                    <button onclick="runMigrations()" class="inline-flex justify-center items-center px-3 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-white hover:bg-green-50">
                        <i class="fas fa-arrow-up mr-2"></i>
                        Run Migrations
                    </button>
                    <button onclick="validateMigrations()" class="inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-check mr-2"></i>
                        Validate
                    </button>
                    <button onclick="optimizeDatabase()" class="inline-flex justify-center items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Optimize DB
                    </button>
                    <button onclick="rollbackMigration()" class="inline-flex justify-center items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50">
                        <i class="fas fa-undo mr-2"></i>
                        Rollback
                    </button>
                </div>
            </div>
        </div>
        HTML;
    }

    /**
     * Render system information section
     */
    private function renderSystemInfoSection(): string
    {
        return <<<HTML
        <div class="space-y-4">
            <div>
                <h3 class="text-sm font-medium text-gray-900">System Overview</h3>
                <div id="system-info" class="mt-2 text-sm text-gray-600">Loading...</div>
            </div>
            
            <div class="border-t pt-4">
                <button onclick="getDetailedSystemInfo()" class="w-full inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-info-circle mr-2"></i>
                    View Detailed Information
                </button>
            </div>
        </div>
        HTML;
    }

    /**
     * Render maintenance JavaScript
     */
    private function renderMaintenanceScripts(): string
    {
        return <<<HTML
        <script>
        // Load initial data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadMaintenanceData();
        });

        async function loadMaintenanceData() {
            try {
                // Load health status
                await refreshHealthStatus();
                
                // Load backup info
                await loadBackupInfo();
                
                // Load cache stats
                await loadCacheStats();
                
                // Load migration status
                await loadMigrationStatus();
                
                // Load system info
                await loadSystemInfo();
                
                // Load performance metrics
                await loadPerformanceMetrics();
                
            } catch (error) {
                console.error('Error loading maintenance data:', error);
            }
        }

        async function refreshHealthStatus() {
            try {
                const response = await fetch('/admin/api/maintenance/health');
                const data = await response.json();
                
                if (data.success) {
                    updateHealthStatus(data.data);
                } else {
                    showError('Failed to load health status');
                }
            } catch (error) {
                console.error('Error loading health status:', error);
                showError('Failed to load health status');
            }
        }

        async function runComprehensiveHealthCheck() {
            try {
                showLoading('health-checks-container');
                
                const response = await fetch('/admin/api/maintenance/health?type=comprehensive');
                const data = await response.json();
                
                if (data.success) {
                    updateDetailedHealthChecks(data.data);
                } else {
                    showError('Failed to run comprehensive health check');
                }
            } catch (error) {
                console.error('Error running health check:', error);
                showError('Failed to run comprehensive health check');
            }
        }

        function updateHealthStatus(data) {
            const statusIcon = document.getElementById('health-status-icon');
            const statusText = document.getElementById('health-status-text');
            
            const status = data.overall_status || 'unknown';
            
            // Update icon and colors based on status
            statusIcon.className = 'w-8 h-8 rounded-full flex items-center justify-center';
            
            switch (status) {
                case 'healthy':
                    statusIcon.classList.add('bg-green-100');
                    statusIcon.innerHTML = '<i class="fas fa-heart text-green-600"></i>';
                    statusText.textContent = 'System is healthy';
                    statusText.className = 'text-sm text-green-600';
                    break;
                case 'degraded':
                    statusIcon.classList.add('bg-yellow-100');
                    statusIcon.innerHTML = '<i class="fas fa-exclamation-triangle text-yellow-600"></i>';
                    statusText.textContent = 'System has warnings';
                    statusText.className = 'text-sm text-yellow-600';
                    break;
                case 'error':
                    statusIcon.classList.add('bg-red-100');
                    statusIcon.innerHTML = '<i class="fas fa-times-circle text-red-600"></i>';
                    statusText.textContent = 'System has errors';
                    statusText.className = 'text-sm text-red-600';
                    break;
                default:
                    statusIcon.classList.add('bg-gray-100');
                    statusIcon.innerHTML = '<i class="fas fa-question text-gray-600"></i>';
                    statusText.textContent = 'Status unknown';
                    statusText.className = 'text-sm text-gray-600';
            }
            
            // Update database status
            if (data.checks && data.checks.database) {
                updateComponentStatus('db', data.checks.database);
            }
            
            // Update API status
            if (data.checks && data.checks.external_apis) {
                updateComponentStatus('api', data.checks.external_apis);
            }
        }

        function updateComponentStatus(component, status) {
            const icon = document.getElementById(component + '-status-icon');
            const text = document.getElementById(component + '-status-text');
            
            if (!icon || !text) return;
            
            const statusValue = status.status || 'unknown';
            
            switch (statusValue) {
                case 'healthy':
                    text.textContent = 'Operational';
                    text.className = 'text-sm text-green-600';
                    break;
                case 'degraded':
                    text.textContent = 'Degraded';
                    text.className = 'text-sm text-yellow-600';
                    break;
                case 'error':
                    text.textContent = 'Error';
                    text.className = 'text-sm text-red-600';
                    break;
                default:
                    text.textContent = 'Unknown';
                    text.className = 'text-sm text-gray-600';
            }
        }

        function updateDetailedHealthChecks(data) {
            const container = document.getElementById('health-checks-container');
            
            if (!data.checks) {
                container.innerHTML = '<p class="text-gray-500">No detailed health check data available.</p>';
                return;
            }
            
            let html = '';
            
            Object.entries(data.checks).forEach(([checkName, checkData]) => {
                const status = checkData.status || 'unknown';
                const message = checkData.message || 'No message';
                
                let statusClass = 'text-gray-600';
                let iconClass = 'fas fa-question text-gray-600';
                
                switch (status) {
                    case 'healthy':
                        statusClass = 'text-green-600';
                        iconClass = 'fas fa-check-circle text-green-600';
                        break;
                    case 'degraded':
                    case 'warning':
                        statusClass = 'text-yellow-600';
                        iconClass = 'fas fa-exclamation-triangle text-yellow-600';
                        break;
                    case 'error':
                        statusClass = 'text-red-600';
                        iconClass = 'fas fa-times-circle text-red-600';
                        break;
                }
                
                html += `
                    <div class="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg">
                        <div class="flex-shrink-0 mt-0.5">
                            <i class="${iconClass}"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900">${checkName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}</h4>
                            <p class="text-sm ${statusClass}">${message}</p>
                            ${checkData.details ? `<div class="mt-2 text-xs text-gray-500">${JSON.stringify(checkData.details, null, 2)}</div>` : ''}
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // Backup Management Functions
        async function createBackup() {
            if (!confirm('Create a new system backup? This may take several minutes.')) {
                return;
            }
            
            try {
                showLoading('latest-backup-info');
                
                const response = await fetch('/admin/api/maintenance/backup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        include_database: true,
                        include_configuration: true,
                        include_logs: false,
                        create_archive: true
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showSuccess('Backup created successfully');
                    await loadBackupInfo();
                } else {
                    showError('Failed to create backup: ' + (data.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error creating backup:', error);
                showError('Failed to create backup');
            }
        }

        async function loadBackupInfo() {
            try {
                const response = await fetch('/admin/api/maintenance/backups');
                const data = await response.json();
                
                if (data.success && data.data.backups.length > 0) {
                    const latest = data.data.backups[0];
                    const date = new Date(latest.created_at).toLocaleString();
                    document.getElementById('latest-backup-info').textContent = `${latest.backup_id} (${date})`;
                } else {
                    document.getElementById('latest-backup-info').textContent = 'No backups found';
                }
            } catch (error) {
                console.error('Error loading backup info:', error);
                document.getElementById('latest-backup-info').textContent = 'Error loading backup info';
            }
        }

        // Cache Management Functions
        async function clearCache(type) {
            if (!confirm(`Clear ${type} cache? This action cannot be undone.`)) {
                return;
            }
            
            try {
                const response = await fetch(`/admin/api/maintenance/clear-cache?type=${type}`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showSuccess(`${type} cache cleared successfully`);
                    await loadCacheStats();
                } else {
                    showError('Failed to clear cache: ' + (data.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error clearing cache:', error);
                showError('Failed to clear cache');
            }
        }

        async function loadCacheStats() {
            try {
                // This would load cache statistics from an API endpoint
                document.getElementById('cache-stats').innerHTML = 'Cache statistics loaded';
            } catch (error) {
                console.error('Error loading cache stats:', error);
            }
        }

        // Database Management Functions
        async function runMigrations() {
            if (!confirm('Run database migrations? This will update the database schema.')) {
                return;
            }
            
            try {
                showLoading('migration-status');
                
                const response = await fetch('/admin/api/maintenance/migrate', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showSuccess('Migrations completed successfully');
                    await loadMigrationStatus();
                } else {
                    showError('Migration failed: ' + (data.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error running migrations:', error);
                showError('Failed to run migrations');
            }
        }

        async function loadMigrationStatus() {
            try {
                const response = await fetch('/admin/api/maintenance/status');
                const data = await response.json();
                
                if (data.success) {
                    const status = data.data;
                    document.getElementById('migration-status').innerHTML = 
                        `Applied: ${status.applied_migrations || 0}, Pending: ${status.pending_migrations || 0}`;
                } else {
                    document.getElementById('migration-status').textContent = 'Error loading migration status';
                }
            } catch (error) {
                console.error('Error loading migration status:', error);
                document.getElementById('migration-status').textContent = 'Error loading migration status';
            }
        }

        async function loadSystemInfo() {
            try {
                const response = await fetch('/admin/api/maintenance/system-info');
                const data = await response.json();
                
                if (data.success) {
                    const info = data.data;
                    document.getElementById('system-info').innerHTML = `
                        PHP: ${info.php.version}<br>
                        Environment: ${info.application.environment}<br>
                        Memory: ${info.php.memory_limit}
                    `;
                } else {
                    document.getElementById('system-info').textContent = 'Error loading system info';
                }
            } catch (error) {
                console.error('Error loading system info:', error);
                document.getElementById('system-info').textContent = 'Error loading system info';
            }
        }

        async function loadPerformanceMetrics() {
            try {
                // This would load performance metrics from monitoring service
                document.getElementById('performance-metrics-container').innerHTML = 
                    '<p class="text-gray-600">Performance metrics will be displayed here</p>';
            } catch (error) {
                console.error('Error loading performance metrics:', error);
            }
        }

        // Utility Functions
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            }
        }

        function showSuccess(message) {
            // You could implement a toast notification system here
            alert('Success: ' + message);
        }

        function showError(message) {
            // You could implement a toast notification system here
            alert('Error: ' + message);
        }
        </script>
        HTML;
    }
}