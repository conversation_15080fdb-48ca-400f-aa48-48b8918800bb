<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->safeLoad();

$debug = (($_ENV['APP_DEBUG'] ?? 'false') === 'true');
error_reporting($debug ? E_ALL : 0);
ini_set('display_errors', $debug ? '1' : '0');

date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'UTC');

$router = new Skpassegna\GuardgeoApi\Router();
$router->dispatch($_SERVER['REQUEST_URI'] ?? '/', $_SERVER['REQUEST_METHOD'] ?? 'GET');