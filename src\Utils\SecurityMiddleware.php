<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Database\DatabaseConnection;

/**
 * Security Middleware
 * 
 * Comprehensive security middleware that applies all security measures
 * including input validation, CSRF protection, rate limiting, and attack detection.
 */
class SecurityMiddleware
{
    private SecurityManager $securityManager;
    private SqlInjectionPrevention $sqlInjectionPrevention;
    private RateLimiter $rateLimiter;
    private SecurityThreatDetector $threatDetector;
    private LoggingService $logger;
    private SecurityAuditLogger $auditLogger;
    private InputSanitizer $inputSanitizer;
    private SecurityHeadersManager $headersManager;
    private CsrfProtection $csrfProtection;
    
    // Security configuration
    private const MAX_REQUEST_SIZE = 10485760; // 10MB
    private const MAX_INPUT_LENGTH = 10000;
    private const RATE_LIMIT_ATTEMPTS = 10;
    private const RATE_LIMIT_WINDOW = 300; // 5 minutes
    
    public function __construct(
        LoggingService $logger = null,
        DatabaseConnection $db = null,
        RateLimiter $rateLimiter = null,
        SecurityThreatDetector $threatDetector = null
    ) {
        $this->logger = $logger ?? new LoggingService();
        $this->securityManager = new SecurityManager();
        $this->sqlInjectionPrevention = new SqlInjectionPrevention();
        $this->inputSanitizer = new InputSanitizer($this->logger);
        $this->headersManager = new SecurityHeadersManager($this->logger);
        $this->csrfProtection = new CsrfProtection($this->logger);
        
        if ($db) {
            $this->rateLimiter = $rateLimiter ?? new RateLimiter($db, $this->logger);
            $this->threatDetector = $threatDetector ?? new SecurityThreatDetector($this->logger);
            $this->auditLogger = new SecurityAuditLogger($this->logger, $db);
        }
    }

    /**
     * Apply all security measures to incoming request
     */
    public function applySecurityMeasures(): array
    {
        $result = [
            'allowed' => true,
            'errors' => [],
            'warnings' => []
        ];

        try {
            // 1. Configure secure session
            $this->securityManager->configureSecureSession();

            // 2. Set security headers
            $this->securityManager->setSecurityHeaders();

            // 3. Validate request size
            if (!$this->validateRequestSize()) {
                $result['allowed'] = false;
                $result['errors'][] = 'Request size exceeds maximum allowed limit';
                $this->logSecurityViolation('request_size_exceeded');
                return $result;
            }

            // 4. Check rate limiting
            $clientIp = $this->getClientIp();
            if ($this->rateLimiter && !$this->rateLimiter->checkLimit($clientIp, 'request', self::RATE_LIMIT_ATTEMPTS, self::RATE_LIMIT_WINDOW)) {
                $result['allowed'] = false;
                $result['errors'][] = 'Rate limit exceeded';
                $this->logSecurityViolation('rate_limit_exceeded', ['ip' => $clientIp]);
                return $result;
            }

            // 5. Validate and sanitize input
            $inputValidation = $this->validateAllInput();
            if (!$inputValidation['valid']) {
                $result['allowed'] = false;
                $result['errors'] = array_merge($result['errors'], $inputValidation['errors']);
                return $result;
            }

            // 6. Detect attack patterns
            $threats = $this->detectThreats();
            if (!empty($threats)) {
                $result['allowed'] = false;
                $result['errors'][] = 'Malicious content detected';
                $this->logSecurityViolation('attack_pattern_detected', ['threats' => $threats]);
                return $result;
            }

            // 7. Validate CSRF token for state-changing requests
            if ($this->isStateChangingRequest() && !$this->validateCsrfToken()) {
                $result['allowed'] = false;
                $result['errors'][] = 'CSRF token validation failed';
                $this->logSecurityViolation('csrf_validation_failed');
                return $result;
            }

            // 8. Additional security checks
            $additionalChecks = $this->performAdditionalSecurityChecks();
            if (!$additionalChecks['passed']) {
                $result['allowed'] = false;
                $result['errors'] = array_merge($result['errors'], $additionalChecks['errors']);
                return $result;
            }

        } catch (\Exception $e) {
            $this->logger->logError('Security middleware error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $result['allowed'] = false;
            $result['errors'][] = 'Security validation failed';
        }

        return $result;
    }

    /**
     * Validate request size
     */
    private function validateRequestSize(): bool
    {
        $contentLength = $_SERVER['CONTENT_LENGTH'] ?? 0;
        return $contentLength <= self::MAX_REQUEST_SIZE;
    }

    /**
     * Validate all input data
     */
    private function validateAllInput(): array
    {
        $result = ['valid' => true, 'errors' => []];
        
        // Combine all input sources
        $allInput = array_merge(
            $_GET ?? [],
            $_POST ?? [],
            $this->getJsonInput(),
            $_COOKIE ?? []
        );

        foreach ($allInput as $key => $value) {
            // Skip empty values
            if (is_null($value) || $value === '') {
                continue;
            }

            // Check input length
            if (is_string($value) && strlen($value) > self::MAX_INPUT_LENGTH) {
                $result['valid'] = false;
                $result['errors'][] = "Input '$key' exceeds maximum length";
                continue;
            }

            // SQL injection validation
            if (!$this->sqlInjectionPrevention->validateParameter($value)) {
                $result['valid'] = false;
                $result['errors'][] = "Input '$key' contains potentially dangerous content";
                $this->sqlInjectionPrevention->logInjectionAttempt((string)$value, $key);
                continue;
            }

            // Recursive validation for arrays
            if (is_array($value)) {
                $arrayValidation = $this->validateArrayRecursively($value, $key);
                if (!$arrayValidation['valid']) {
                    $result['valid'] = false;
                    $result['errors'] = array_merge($result['errors'], $arrayValidation['errors']);
                }
            }
        }

        return $result;
    }

    /**
     * Validate array input recursively
     */
    private function validateArrayRecursively(array $input, string $parentKey): array
    {
        $result = ['valid' => true, 'errors' => []];

        foreach ($input as $key => $value) {
            $fullKey = $parentKey . '[' . $key . ']';

            if (is_string($value) && strlen($value) > self::MAX_INPUT_LENGTH) {
                $result['valid'] = false;
                $result['errors'][] = "Input '$fullKey' exceeds maximum length";
                continue;
            }

            if (!$this->sqlInjectionPrevention->validateParameter($value)) {
                $result['valid'] = false;
                $result['errors'][] = "Input '$fullKey' contains potentially dangerous content";
                continue;
            }

            if (is_array($value)) {
                $nestedValidation = $this->validateArrayRecursively($value, $fullKey);
                if (!$nestedValidation['valid']) {
                    $result['valid'] = false;
                    $result['errors'] = array_merge($result['errors'], $nestedValidation['errors']);
                }
            }
        }

        return $result;
    }

    /**
     * Get JSON input data
     */
    private function getJsonInput(): array
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (strpos($contentType, 'application/json') === false) {
            return [];
        }

        $input = file_get_contents('php://input');
        if (empty($input)) {
            return [];
        }

        $decoded = json_decode($input, true);
        return is_array($decoded) ? $decoded : [];
    }

    /**
     * Detect various threat patterns
     */
    private function detectThreats(): array
    {
        if (!$this->threatDetector) {
            // Fallback to basic detection
            $allInput = array_merge(
                $_GET ?? [],
                $_POST ?? [],
                $this->getJsonInput()
            );
            return $this->securityManager->detectAttackPatterns($allInput);
        }

        $allInput = array_merge(
            $_GET ?? [],
            $_POST ?? [],
            $this->getJsonInput(),
            ['user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '']
        );

        $analysis = $this->threatDetector->analyzeRequest($allInput);
        
        // Log high-risk threats
        if ($analysis['risk_score'] >= 50) {
            foreach ($analysis['threats'] as $threat) {
                $this->threatDetector->logThreat($threat);
            }
        }

        return $analysis['threats'];
    }

    /**
     * Check if request is state-changing (requires CSRF protection)
     */
    private function isStateChangingRequest(): bool
    {
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        return in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE']);
    }

    /**
     * Validate CSRF token
     */
    private function validateCsrfToken(): bool
    {
        // Skip CSRF validation for API endpoints (they use different authentication)
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($uri, '/api/') !== false && strpos($uri, '/admin/api/') === false) {
            return true;
        }

        $token = $this->getCsrfTokenFromRequest();
        if (empty($token)) {
            return false;
        }

        return $this->securityManager->validateCsrfTokenWithRotation($token);
    }

    /**
     * Get CSRF token from request
     */
    private function getCsrfTokenFromRequest(): ?string
    {
        // Check POST data
        if (isset($_POST['csrf_token'])) {
            return $_POST['csrf_token'];
        }

        // Check JSON data
        $jsonInput = $this->getJsonInput();
        if (isset($jsonInput['csrf_token'])) {
            return $jsonInput['csrf_token'];
        }

        // Check headers
        if (isset($_SERVER['HTTP_X_CSRF_TOKEN'])) {
            return $_SERVER['HTTP_X_CSRF_TOKEN'];
        }

        return null;
    }

    /**
     * Perform additional security checks
     */
    private function performAdditionalSecurityChecks(): array
    {
        $result = ['passed' => true, 'errors' => []];

        // Check for suspicious user agents
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if ($this->isSuspiciousUserAgent($userAgent)) {
            $result['passed'] = false;
            $result['errors'][] = 'Suspicious user agent detected';
            $this->logSecurityViolation('suspicious_user_agent', ['user_agent' => $userAgent]);
        }

        // Check for suspicious request patterns
        if ($this->hasSuspiciousRequestPattern()) {
            $result['passed'] = false;
            $result['errors'][] = 'Suspicious request pattern detected';
            $this->logSecurityViolation('suspicious_request_pattern');
        }

        // Check for known malicious IPs (if configured)
        $clientIp = $this->getClientIp();
        if ($this->isKnownMaliciousIp($clientIp)) {
            $result['passed'] = false;
            $result['errors'][] = 'Request from known malicious IP';
            $this->logSecurityViolation('malicious_ip_detected', ['ip' => $clientIp]);
        }

        return $result;
    }

    /**
     * Check for suspicious user agents
     */
    private function isSuspiciousUserAgent(string $userAgent): bool
    {
        $suspiciousPatterns = [
            '/bot|crawler|spider|scraper/i',
            '/curl|wget|python|perl|ruby/i',
            '/scanner|exploit|hack|attack/i',
            '/sqlmap|nikto|nmap|masscan/i'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for suspicious request patterns
     */
    private function hasSuspiciousRequestPattern(): bool
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        
        // Check for common attack paths
        $suspiciousPaths = [
            '/wp-admin', '/wp-login', '/admin', '/phpmyadmin',
            '/.env', '/config', '/backup', '/test',
            '/shell', '/cmd', '/exec'
        ];

        foreach ($suspiciousPaths as $path) {
            if (strpos($uri, $path) !== false && !$this->isLegitimateAdminRequest()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if this is a legitimate admin request
     */
    private function isLegitimateAdminRequest(): bool
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($uri, '/admin') === 0; // Our legitimate admin interface
    }

    /**
     * Check if IP is known to be malicious
     */
    private function isKnownMaliciousIp(string $ip): bool
    {
        // This could be enhanced with external threat intelligence feeds
        // For now, just check against basic patterns
        
        // Block private/internal IPs from external access in production
        if ($this->isProductionEnvironment() && $this->isPrivateIp($ip)) {
            return false; // Allow private IPs in production (they're behind proxy)
        }

        return false; // No malicious IPs detected with current implementation
    }

    /**
     * Check if IP is private/internal
     */
    private function isPrivateIp(string $ip): bool
    {
        return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }

    /**
     * Check if running in production environment
     */
    private function isProductionEnvironment(): bool
    {
        return ($_ENV['APP_ENV'] ?? 'development') === 'production';
    }

    /**
     * Get client IP address
     */
    private function getClientIp(): string
    {
        $ipKeys = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR'
        ];

        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = trim($_SERVER[$key]);
                
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }

        return '127.0.0.1';
    }

    /**
     * Log security violation
     */
    private function logSecurityViolation(string $violation, array $context = []): void
    {
        $this->securityManager->logSecurityEvent($violation, array_merge($context, [
            'timestamp' => date('c'),
            'ip' => $this->getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
        ]));

        $this->logger->logError('Security violation: ' . $violation, $context);
    }

    /**
     * Get sanitized input data
     */
    public function getSanitizedInput(): array
    {
        $allInput = array_merge(
            $_GET ?? [],
            $_POST ?? [],
            $this->getJsonInput()
        );

        $sanitized = [];
        foreach ($allInput as $key => $value) {
            $sanitized[$key] = $this->securityManager->sanitizeInput($value);
        }

        return $sanitized;
    }

    /**
     * Generate and return CSRF token for forms
     */
    public function getCsrfToken(): string
    {
        return $this->securityManager->generateCsrfTokenWithBackup();
    }

    /**
     * Check if request passes all security checks
     */
    public function isRequestSecure(): bool
    {
        $securityResult = $this->applySecurityMeasures();
        return $securityResult['allowed'];
    }
}