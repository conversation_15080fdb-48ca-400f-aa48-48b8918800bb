<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Models\IpDataModel;
use Skpassegna\GuardgeoApi\Database\IpIntelligenceRepository;
use Skpassegna\GuardgeoApi\Utils\Logger;
use Skpassegna\GuardgeoApi\Config\Environment;
use DateTime;

/**
 * Cache Manager
 * 
 * Handles intelligent caching with configurable deprecation thresholds,
 * automatic refresh logic, and batch processing capabilities for IP data.
 */
class CacheManager
{
    private IpIntelligenceRepository $repository;
    private Logger $logger;
    
    // Configurable deprecation thresholds (in days)
    private int $locationDeprecationDays;
    private int $securityDeprecationDays;
    private int $connectionDeprecationDays;
    private int $companyDeprecationDays;
    
    // Cache configuration
    private int $maxCacheSize;
    private int $cleanupBatchSize;
    private int $refreshBatchSize;
    
    /**
     * Constructor
     */
    public function __construct(?IpIntelligenceRepository $repository = null)
    {
        $this->repository = $repository ?? new IpIntelligenceRepository();
        $this->logger = new Logger();
        
        // Load configuration from environment
        $this->locationDeprecationDays = (int) Environment::get('IP_CACHE_LOCATION_DAYS', 10);
        $this->securityDeprecationDays = (int) Environment::get('IP_CACHE_SECURITY_DAYS', 3);
        $this->connectionDeprecationDays = (int) Environment::get('IP_CACHE_CONNECTION_DAYS', 7);
        $this->companyDeprecationDays = (int) Environment::get('IP_CACHE_COMPANY_DAYS', 30);
        
        $this->maxCacheSize = (int) Environment::get('IP_CACHE_MAX_SIZE', 100000);
        $this->cleanupBatchSize = (int) Environment::get('IP_CACHE_CLEANUP_BATCH_SIZE', 1000);
        $this->refreshBatchSize = (int) Environment::get('IP_CACHE_REFRESH_BATCH_SIZE', 100);
    }
    
    /**
     * Check if IP data needs refresh based on deprecation rules
     */
    public function needsRefresh(IpDataModel $ipData): bool
    {
        $now = new DateTime();
        
        return $now > $ipData->location_expires_at ||
               $now > $ipData->security_expires_at ||
               $now > $ipData->connection_expires_at ||
               $now > $ipData->company_expires_at;
    }
    
    /**
     * Get specific expired data types for an IP
     */
    public function getExpiredDataTypes(IpDataModel $ipData): array
    {
        $now = new DateTime();
        $expired = [];
        
        if ($now > $ipData->location_expires_at) {
            $expired[] = 'location';
        }
        
        if ($now > $ipData->security_expires_at) {
            $expired[] = 'security';
        }
        
        if ($now > $ipData->connection_expires_at) {
            $expired[] = 'connection';
        }
        
        if ($now > $ipData->company_expires_at) {
            $expired[] = 'company';
        }
        
        return $expired;
    }
    
    /**
     * Calculate expiry dates for new IP data based on current configuration
     */
    public function calculateExpiryDates(): array
    {
        $now = new DateTime();
        
        return [
            'location_expires_at' => (clone $now)->modify("+{$this->locationDeprecationDays} days"),
            'security_expires_at' => (clone $now)->modify("+{$this->securityDeprecationDays} days"),
            'connection_expires_at' => (clone $now)->modify("+{$this->connectionDeprecationDays} days"),
            'company_expires_at' => (clone $now)->modify("+{$this->companyDeprecationDays} days")
        ];
    }
    
    /**
     * Update expiry dates for an IP data model
     */
    public function updateExpiryDates(IpDataModel $ipData): void
    {
        $expiryDates = $this->calculateExpiryDates();
        
        $ipData->location_expires_at = $expiryDates['location_expires_at'];
        $ipData->security_expires_at = $expiryDates['security_expires_at'];
        $ipData->connection_expires_at = $expiryDates['connection_expires_at'];
        $ipData->company_expires_at = $expiryDates['company_expires_at'];
        $ipData->cached_at = new DateTime();
    }
    
    /**
     * Get cache statistics
     */
    public function getCacheStatistics(): array
    {
        $stats = [
            'total_cached_ips' => $this->repository->getTotalCount(),
            'fresh_ips' => $this->repository->getFreshCount(),
            'expired_ips' => $this->repository->getExpiredCount(),
            'cache_utilization' => 0.0,
            'deprecation_config' => [
                'location_days' => $this->locationDeprecationDays,
                'security_days' => $this->securityDeprecationDays,
                'connection_days' => $this->connectionDeprecationDays,
                'company_days' => $this->companyDeprecationDays
            ]
        ];
        
        // Calculate cache utilization
        if ($this->maxCacheSize > 0) {
            $stats['cache_utilization'] = ($stats['total_cached_ips'] / $this->maxCacheSize) * 100;
        }
        
        return $stats;
    }
    
    /**
     * Get IPs that need refresh (expired data)
     */
    public function getIpsNeedingRefresh(int $limit = null): array
    {
        $limit = $limit ?? $this->refreshBatchSize;
        return $this->repository->findExpiredIps($limit);
    }
    
    /**
     * Perform automatic cache cleanup
     */
    public function performCleanup(): array
    {
        $this->logger->info("Starting automatic cache cleanup");
        
        $results = [
            'old_data_deleted' => 0,
            'cache_size_reduced' => false,
            'errors' => []
        ];
        
        try {
            // Delete old data beyond retention period
            $retentionDays = (int) Environment::get('IP_CACHE_RETENTION_DAYS', 90);
            $results['old_data_deleted'] = $this->repository->deleteOldData($retentionDays);
            
            $this->logger->info("Deleted old cache data", [
                'deleted_count' => $results['old_data_deleted'],
                'retention_days' => $retentionDays
            ]);
            
            // Check if cache size exceeds maximum
            $totalCount = $this->repository->getTotalCount();
            if ($totalCount > $this->maxCacheSize) {
                $this->logger->info("Cache size exceeds maximum, performing size reduction", [
                    'current_size' => $totalCount,
                    'max_size' => $this->maxCacheSize
                ]);
                
                $results['cache_size_reduced'] = $this->reduceCacheSize();
            }
            
        } catch (\Exception $e) {
            $error = "Cache cleanup failed: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        $this->logger->info("Cache cleanup completed", $results);
        
        return $results;
    }
    
    /**
     * Reduce cache size by removing oldest entries
     */
    private function reduceCacheSize(): bool
    {
        try {
            $targetSize = (int) ($this->maxCacheSize * 0.8); // Reduce to 80% of max
            $currentSize = $this->repository->getTotalCount();
            $toDelete = $currentSize - $targetSize;
            
            if ($toDelete <= 0) {
                return false;
            }
            
            $this->logger->info("Reducing cache size", [
                'current_size' => $currentSize,
                'target_size' => $targetSize,
                'to_delete' => $toDelete
            ]);
            
            // Delete oldest entries in batches
            $deleted = 0;
            while ($deleted < $toDelete) {
                $batchSize = min($this->cleanupBatchSize, $toDelete - $deleted);
                $batchDeleted = $this->deleteOldestEntries($batchSize);
                
                if ($batchDeleted === 0) {
                    break; // No more entries to delete
                }
                
                $deleted += $batchDeleted;
            }
            
            $this->logger->info("Cache size reduction completed", [
                'deleted_count' => $deleted
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to reduce cache size", [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Delete oldest cache entries
     */
    private function deleteOldestEntries(int $count): int
    {
        return $this->repository->deleteOldestEntries($count);
    }
    
    /**
     * Perform batch refresh of expired IPs
     */
    public function performBatchRefresh(IpRegistryApiClient $apiClient, int $batchSize = null): array
    {
        $batchSize = $batchSize ?? $this->refreshBatchSize;
        
        $this->logger->info("Starting batch refresh of expired IPs", [
            'batch_size' => $batchSize
        ]);
        
        $results = [
            'processed' => 0,
            'refreshed' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        try {
            $expiredIps = $this->getIpsNeedingRefresh($batchSize);
            $results['processed'] = count($expiredIps);
            
            if (empty($expiredIps)) {
                $this->logger->info("No expired IPs found for refresh");
                return $results;
            }
            
            // Extract IP addresses for batch API call
            $ipsToRefresh = array_column($expiredIps, 'ip');
            
            $this->logger->info("Refreshing expired IPs", [
                'ip_count' => count($ipsToRefresh),
                'ips' => $ipsToRefresh
            ]);
            
            // Fetch fresh data from API
            try {
                $batchApiData = $apiClient->getBatchIpData($ipsToRefresh);
                
                // Process batch response
                if (isset($batchApiData['results']) && is_array($batchApiData['results'])) {
                    foreach ($batchApiData['results'] as $apiData) {
                        if (isset($apiData['ip'])) {
                            $this->processSingleRefresh($apiData, $results);
                        }
                    }
                } else {
                    // Single IP response format (fallback)
                    if (count($ipsToRefresh) === 1 && isset($batchApiData['ip'])) {
                        $this->processSingleRefresh($batchApiData, $results);
                    }
                }
                
            } catch (IpRegistryApiException $e) {
                $error = "Batch API call failed: " . $e->getMessage();
                $results['errors'][] = $error;
                $results['failed'] = count($ipsToRefresh);
                
                $this->logger->error($error, [
                    'ips' => $ipsToRefresh
                ]);
            }
            
        } catch (\Exception $e) {
            $error = "Batch refresh failed: " . $e->getMessage();
            $results['errors'][] = $error;
            $this->logger->error($error, ['exception' => $e]);
        }
        
        $this->logger->info("Batch refresh completed", $results);
        
        return $results;
    }
    
    /**
     * Process single IP refresh from batch response
     */
    private function processSingleRefresh(array $apiData, array &$results): void
    {
        try {
            $ipModel = IpDataModel::fromApiResponse($apiData);
            
            if ($ipModel->isValid()) {
                // Update expiry dates with current configuration
                $this->updateExpiryDates($ipModel);
                
                // Update in database
                if ($this->repository->update($ipModel)) {
                    $results['refreshed']++;
                    $this->logger->debug("Successfully refreshed IP", ['ip' => $ipModel->ip]);
                } else {
                    $results['failed']++;
                    $this->logger->warning("Failed to update refreshed IP in database", ['ip' => $ipModel->ip]);
                }
            } else {
                $results['failed']++;
                $errors = implode(', ', $ipModel->validate());
                $this->logger->warning("Invalid IP data received from API", [
                    'ip' => $apiData['ip'] ?? 'unknown',
                    'errors' => $errors
                ]);
            }
            
        } catch (\Exception $e) {
            $results['failed']++;
            $this->logger->error("Failed to process single IP refresh", [
                'ip' => $apiData['ip'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Get cache health status
     */
    public function getCacheHealth(): array
    {
        $stats = $this->getCacheStatistics();
        
        $health = [
            'status' => 'healthy',
            'issues' => [],
            'recommendations' => []
        ];
        
        // Check cache utilization
        if ($stats['cache_utilization'] > 90) {
            $health['status'] = 'warning';
            $health['issues'][] = 'Cache utilization is very high (' . round($stats['cache_utilization'], 1) . '%)';
            $health['recommendations'][] = 'Consider increasing max cache size or reducing retention period';
        }
        
        // Check expired data ratio
        $expiredRatio = $stats['total_cached_ips'] > 0 
            ? ($stats['expired_ips'] / $stats['total_cached_ips']) * 100 
            : 0;
            
        if ($expiredRatio > 50) {
            $health['status'] = 'warning';
            $health['issues'][] = 'High percentage of expired data (' . round($expiredRatio, 1) . '%)';
            $health['recommendations'][] = 'Consider running batch refresh more frequently';
        }
        
        // Check if repository is accessible
        if (!$this->repository->healthCheck()) {
            $health['status'] = 'critical';
            $health['issues'][] = 'Database repository is not accessible';
            $health['recommendations'][] = 'Check database connection and table structure';
        }
        
        return array_merge($health, [
            'statistics' => $stats,
            'last_checked' => (new DateTime())->format('c')
        ]);
    }
    
    /**
     * Configure deprecation thresholds
     */
    public function configureDeprecation(array $config): void
    {
        if (isset($config['location_days'])) {
            $this->locationDeprecationDays = (int) $config['location_days'];
        }
        
        if (isset($config['security_days'])) {
            $this->securityDeprecationDays = (int) $config['security_days'];
        }
        
        if (isset($config['connection_days'])) {
            $this->connectionDeprecationDays = (int) $config['connection_days'];
        }
        
        if (isset($config['company_days'])) {
            $this->companyDeprecationDays = (int) $config['company_days'];
        }
        
        $this->logger->info("Updated deprecation configuration", [
            'location_days' => $this->locationDeprecationDays,
            'security_days' => $this->securityDeprecationDays,
            'connection_days' => $this->connectionDeprecationDays,
            'company_days' => $this->companyDeprecationDays
        ]);
    }
    
    /**
     * Get current deprecation configuration
     */
    public function getDeprecationConfig(): array
    {
        return [
            'location_days' => $this->locationDeprecationDays,
            'security_days' => $this->securityDeprecationDays,
            'connection_days' => $this->connectionDeprecationDays,
            'company_days' => $this->companyDeprecationDays
        ];
    }
    
    /**
     * Estimate time until next refresh needed for an IP
     */
    public function getTimeUntilRefresh(IpDataModel $ipData): array
    {
        $now = new DateTime();
        
        $timeUntil = [
            'location' => $this->getTimeDifference($now, $ipData->location_expires_at),
            'security' => $this->getTimeDifference($now, $ipData->security_expires_at),
            'connection' => $this->getTimeDifference($now, $ipData->connection_expires_at),
            'company' => $this->getTimeDifference($now, $ipData->company_expires_at)
        ];
        
        // Find the earliest expiry
        $earliestExpiry = min(
            $ipData->location_expires_at,
            $ipData->security_expires_at,
            $ipData->connection_expires_at,
            $ipData->company_expires_at
        );
        
        $timeUntil['next_refresh'] = $this->getTimeDifference($now, $earliestExpiry);
        
        return $timeUntil;
    }
    
    /**
     * Get human-readable time difference
     */
    private function getTimeDifference(DateTime $from, DateTime $to): array
    {
        $diff = $from->diff($to);
        
        return [
            'days' => $diff->days,
            'hours' => $diff->h,
            'minutes' => $diff->i,
            'seconds' => $diff->s,
            'is_past' => $from > $to,
            'formatted' => $this->formatTimeDifference($diff, $from > $to)
        ];
    }
    
    /**
     * Format time difference as human-readable string
     */
    private function formatTimeDifference(\DateInterval $diff, bool $isPast): string
    {
        $parts = [];
        
        if ($diff->days > 0) {
            $parts[] = $diff->days . ' day' . ($diff->days > 1 ? 's' : '');
        }
        
        if ($diff->h > 0) {
            $parts[] = $diff->h . ' hour' . ($diff->h > 1 ? 's' : '');
        }
        
        if ($diff->i > 0 && count($parts) < 2) {
            $parts[] = $diff->i . ' minute' . ($diff->i > 1 ? 's' : '');
        }
        
        if (empty($parts)) {
            return $isPast ? 'expired' : 'less than a minute';
        }
        
        $formatted = implode(', ', array_slice($parts, 0, 2));
        
        return $isPast ? $formatted . ' ago' : 'in ' . $formatted;
    }
    
    /**
     * Perform scheduled maintenance tasks
     */
    public function performScheduledMaintenance(): array
    {
        $this->logger->info("Starting scheduled cache maintenance");
        
        $results = [
            'tasks_completed' => [],
            'tasks_failed' => [],
            'total_duration' => 0,
            'summary' => []
        ];
        
        $startTime = microtime(true);
        
        // Task 1: Cleanup old data
        try {
            $cleanupStart = microtime(true);
            $cleanupResults = $this->performCleanup();
            $cleanupDuration = microtime(true) - $cleanupStart;
            
            $results['tasks_completed'][] = 'cleanup';
            $results['summary']['cleanup'] = [
                'old_data_deleted' => $cleanupResults['old_data_deleted'],
                'cache_size_reduced' => $cleanupResults['cache_size_reduced'],
                'duration' => round($cleanupDuration, 2)
            ];
            
        } catch (\Exception $e) {
            $results['tasks_failed'][] = 'cleanup';
            $this->logger->error("Scheduled cleanup failed", ['error' => $e->getMessage()]);
        }
        
        // Task 2: Refresh expired data
        try {
            $refreshStart = microtime(true);
            $refreshResults = $this->performBatchRefresh(new IpRegistryApiClient());
            $refreshDuration = microtime(true) - $refreshStart;
            
            $results['tasks_completed'][] = 'refresh';
            $results['summary']['refresh'] = [
                'processed' => $refreshResults['processed'],
                'refreshed' => $refreshResults['refreshed'],
                'failed' => $refreshResults['failed'],
                'duration' => round($refreshDuration, 2)
            ];
            
        } catch (\Exception $e) {
            $results['tasks_failed'][] = 'refresh';
            $this->logger->error("Scheduled refresh failed", ['error' => $e->getMessage()]);
        }
        
        // Task 3: Update cache statistics
        try {
            $statsStart = microtime(true);
            $stats = $this->getCacheStatistics();
            $statsDuration = microtime(true) - $statsStart;
            
            $results['tasks_completed'][] = 'statistics';
            $results['summary']['statistics'] = [
                'total_ips' => $stats['total_cached_ips'],
                'fresh_ips' => $stats['fresh_ips'],
                'expired_ips' => $stats['expired_ips'],
                'cache_utilization' => $stats['cache_utilization'],
                'duration' => round($statsDuration, 2)
            ];
            
        } catch (\Exception $e) {
            $results['tasks_failed'][] = 'statistics';
            $this->logger->error("Statistics update failed", ['error' => $e->getMessage()]);
        }
        
        $results['total_duration'] = round(microtime(true) - $startTime, 2);
        
        $this->logger->info("Scheduled cache maintenance completed", [
            'completed_tasks' => count($results['tasks_completed']),
            'failed_tasks' => count($results['tasks_failed']),
            'duration' => $results['total_duration']
        ]);
        
        return $results;
    }
    
    /**
     * Get maintenance schedule recommendations
     */
    public function getMaintenanceScheduleRecommendations(): array
    {
        $stats = $this->getCacheStatistics();
        $recommendations = [];
        
        // Cleanup frequency recommendation
        $utilizationPercent = $stats['cache_utilization'];
        if ($utilizationPercent > 80) {
            $recommendations['cleanup_frequency'] = 'daily';
        } elseif ($utilizationPercent > 50) {
            $recommendations['cleanup_frequency'] = 'weekly';
        } else {
            $recommendations['cleanup_frequency'] = 'monthly';
        }
        
        // Refresh frequency recommendation
        $expiredPercent = $stats['total_cached_ips'] > 0 
            ? ($stats['expired_ips'] / $stats['total_cached_ips']) * 100 
            : 0;
            
        if ($expiredPercent > 30) {
            $recommendations['refresh_frequency'] = 'hourly';
        } elseif ($expiredPercent > 15) {
            $recommendations['refresh_frequency'] = 'every_6_hours';
        } else {
            $recommendations['refresh_frequency'] = 'daily';
        }
        
        // Batch size recommendations
        if ($stats['total_cached_ips'] > 10000) {
            $recommendations['batch_size'] = 200;
        } elseif ($stats['total_cached_ips'] > 1000) {
            $recommendations['batch_size'] = 100;
        } else {
            $recommendations['batch_size'] = 50;
        }
        
        return $recommendations;
    }
    
    /**
     * Perform intelligent cache warming using CacheWarmingService
     */
    public function performIntelligentWarmup(): array
    {
        try {
            $warmingService = new \Skpassegna\GuardgeoApi\Services\CacheWarmingService(
                $this->repository,
                null,
                null
            );
            
            return $warmingService->performIntelligentWarmup();
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to perform intelligent warmup", [
                'error' => $e->getMessage()
            ]);
            return [
                'strategies_executed' => [],
                'total_ips_processed' => 0,
                'total_ips_warmed' => 0,
                'errors' => ['Failed to initialize warming service: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Get comprehensive cache statistics using CacheStatisticsService
     */
    public function getComprehensiveStatistics(): array
    {
        try {
            $statisticsService = new \Skpassegna\GuardgeoApi\Services\CacheStatisticsService(
                $this->repository,
                null
            );
            
            return $statisticsService->getComprehensiveStatistics();
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to get comprehensive statistics", [
                'error' => $e->getMessage()
            ]);
            return [
                'overview' => $this->getCacheStatistics(),
                'errors' => ['Failed to get comprehensive statistics: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Perform cache invalidation using CacheInvalidationService
     */
    public function performInvalidation(array $ips, string $reason = 'manual', array $dataTypes = []): array
    {
        try {
            $invalidationService = new \Skpassegna\GuardgeoApi\Services\CacheInvalidationService(
                $this->repository,
                null
            );
            
            if (count($ips) === 1) {
                return $invalidationService->invalidateIp($ips[0], $reason, $dataTypes);
            } else {
                return $invalidationService->invalidateMultipleIps($ips, $reason, $dataTypes);
            }
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to perform cache invalidation", [
                'ips' => $ips,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
            return [
                'total_ips' => count($ips),
                'invalidated_count' => 0,
                'failed_count' => count($ips),
                'errors' => ['Failed to initialize invalidation service: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Perform database optimization using DatabaseOptimizationService
     */
    public function performDatabaseOptimization(): array
    {
        try {
            $optimizationService = new \Skpassegna\GuardgeoApi\Services\DatabaseOptimizationService();
            
            return $optimizationService->performOptimization();
            
        } catch (\Exception $e) {
            $this->logger->error("Failed to perform database optimization", [
                'error' => $e->getMessage()
            ]);
            return [
                'optimizations_performed' => [],
                'performance_improvements' => [],
                'errors' => ['Failed to initialize optimization service: ' . $e->getMessage()]
            ];
        }
    }
}