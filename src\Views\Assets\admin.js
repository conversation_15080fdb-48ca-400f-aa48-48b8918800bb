/**
 * GuardGeo Admin Interface JavaScript
 * 
 * Comprehensive JavaScript utilities for the admin interface including
 * AJAX form handling, modal management, and dynamic content updates.
 */

class GuardGeoAdmin {
    constructor() {
        this.csrfToken = null;
        this.init();
    }

    /**
     * Initialize admin interface
     */
    init() {
        this.setupEventListeners();
        this.loadCsrfToken();
        this.initializeComponents();
    }

    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // Mobile menu toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('#openSidebar')) {
                this.openSidebar();
            } else if (e.target.matches('#closeSidebar')) {
                this.closeSidebar();
            } else if (e.target.matches('#mobileMenuOverlay')) {
                this.closeSidebar();
            }
        });

        // User menu toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('#userMenuBtn') || e.target.closest('#userMenuBtn')) {
                this.toggleUserMenu();
            } else if (!e.target.closest('#userMenu')) {
                this.closeUserMenu();
            }
        });

        // Logout buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('#logoutBtn, #logoutBtnTop')) {
                e.preventDefault();
                this.logout();
            }
        });

        // Modal close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });

        // Form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.ajax-form')) {
                e.preventDefault();
                this.handleAjaxForm(e.target);
            }
        });

        // Auto-refresh data
        this.setupAutoRefresh();
    }

    /**
     * Load CSRF token
     */
    async loadCsrfToken() {
        try {
            const response = await fetch('/admin/auth/csrf');
            const data = await response.json();
            if (data.success) {
                this.csrfToken = data.csrf_token;
            }
        } catch (error) {
            console.error('Failed to load CSRF token:', error);
        }
    }

    /**
     * Initialize components
     */
    initializeComponents() {
        // Initialize tooltips
        this.initializeTooltips();
        
        // Initialize data tables
        this.initializeDataTables();
        
        // Initialize charts
        this.initializeCharts();
        
        // Initialize search functionality
        this.initializeSearch();
    }

    /**
     * Sidebar management
     */
    openSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileMenuOverlay');
        
        if (sidebar) {
            sidebar.classList.remove('-translate-x-full');
        }
        if (overlay) {
            overlay.classList.remove('hidden');
        }
    }

    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileMenuOverlay');
        
        if (sidebar) {
            sidebar.classList.add('-translate-x-full');
        }
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    /**
     * User menu management
     */
    toggleUserMenu() {
        const menu = document.getElementById('userMenu');
        if (menu) {
            menu.classList.toggle('hidden');
        }
    }

    closeUserMenu() {
        const menu = document.getElementById('userMenu');
        if (menu) {
            menu.classList.add('hidden');
        }
    }

    /**
     * Modal management
     */
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    }

    closeAllModals() {
        const modals = document.querySelectorAll('[id$="Modal"]');
        modals.forEach(modal => {
            modal.classList.add('hidden');
        });
        document.body.classList.remove('overflow-hidden');
    }

    /**
     * AJAX form handling
     */
    async handleAjaxForm(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn ? submitBtn.textContent : '';
        
        try {
            // Show loading state
            this.setFormLoading(form, true);
            
            // Clear previous errors
            this.clearFormErrors(form);
            
            // Prepare form data
            const formData = new FormData(form);
            if (this.csrfToken) {
                formData.append('csrf_token', this.csrfToken);
            }
            
            // Submit form
            const response = await fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.handleFormSuccess(form, result);
            } else {
                this.handleFormError(form, result);
            }
            
        } catch (error) {
            console.error('Form submission error:', error);
            this.showNotification('An error occurred. Please try again.', 'error');
        } finally {
            this.setFormLoading(form, false);
        }
    }

    /**
     * Set form loading state
     */
    setFormLoading(form, loading) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const inputs = form.querySelectorAll('input, select, textarea, button');
        
        if (loading) {
            inputs.forEach(input => input.disabled = true);
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
            }
        } else {
            inputs.forEach(input => input.disabled = false);
            if (submitBtn) {
                const originalText = submitBtn.dataset.originalText || 'Submit';
                submitBtn.innerHTML = originalText;
            }
        }
    }

    /**
     * Clear form errors
     */
    clearFormErrors(form) {
        // Remove error messages
        form.querySelectorAll('.text-red-600, .text-red-700').forEach(el => {
            if (el.textContent.includes('required') || el.textContent.includes('invalid')) {
                el.remove();
            }
        });
        
        // Remove error styling
        form.querySelectorAll('.border-red-300').forEach(el => {
            el.classList.remove('border-red-300', 'text-red-900', 'placeholder-red-300');
            el.classList.add('border-gray-300');
        });
    }

    /**
     * Handle form success
     */
    handleFormSuccess(form, result) {
        if (result.redirect) {
            window.location.href = result.redirect;
        } else {
            if (result.message) {
                this.showNotification(result.message, 'success');
            }
            
            // Close modal if form is in a modal
            const modal = form.closest('[id$="Modal"]');
            if (modal) {
                this.closeModal(modal.id);
            }
            
            // Refresh data if needed
            if (result.refresh) {
                this.refreshPageData();
            }
            
            // Reset form
            form.reset();
        }
    }

    /**
     * Handle form error
     */
    handleFormError(form, result) {
        if (result.errors) {
            this.displayFormErrors(form, result.errors);
        } else if (result.error) {
            this.showNotification(result.error, 'error');
        }
    }

    /**
     * Display form errors
     */
    displayFormErrors(form, errors) {
        Object.keys(errors).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                // Add error styling
                field.classList.add('border-red-300', 'text-red-900', 'placeholder-red-300');
                field.classList.remove('border-gray-300');
                
                // Add error messages
                const fieldContainer = field.closest('.form-field') || field.parentElement;
                if (fieldContainer) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'mt-1';
                    
                    const fieldErrors = Array.isArray(errors[fieldName]) ? errors[fieldName] : [errors[fieldName]];
                    fieldErrors.forEach(error => {
                        const errorP = document.createElement('p');
                        errorP.className = 'text-sm text-red-600';
                        errorP.textContent = error;
                        errorDiv.appendChild(errorP);
                    });
                    
                    fieldContainer.appendChild(errorDiv);
                }
            }
        });
    }

    /**
     * Notification system
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${this.getNotificationClasses(type)}`;
        
        notification.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="${this.getNotificationIcon(type)}"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button class="inline-flex text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after duration
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }

    getNotificationClasses(type) {
        const classes = {
            success: 'bg-green-50 border border-green-200 text-green-800',
            error: 'bg-red-50 border border-red-200 text-red-800',
            warning: 'bg-yellow-50 border border-yellow-200 text-yellow-800',
            info: 'bg-blue-50 border border-blue-200 text-blue-800'
        };
        return classes[type] || classes.info;
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fas fa-check-circle text-green-400',
            error: 'fas fa-exclamation-circle text-red-400',
            warning: 'fas fa-exclamation-triangle text-yellow-400',
            info: 'fas fa-info-circle text-blue-400'
        };
        return icons[type] || icons.info;
    }

    /**
     * Logout functionality
     */
    async logout() {
        try {
            const response = await fetch('/admin/logout', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-Token': this.csrfToken
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                window.location.href = result.redirect || '/admin/login';
            } else {
                this.showNotification('Logout failed. Please try again.', 'error');
            }
        } catch (error) {
            console.error('Logout error:', error);
            // Force redirect on error
            window.location.href = '/admin/login';
        }
    }

    /**
     * Data refresh functionality
     */
    async refreshPageData() {
        // This method should be overridden by page-specific implementations
        console.log('Refreshing page data...');
    }

    /**
     * Setup auto-refresh for dashboard data
     */
    setupAutoRefresh() {
        // Only auto-refresh on dashboard
        if (window.location.pathname.includes('/dashboard')) {
            setInterval(() => {
                this.refreshDashboardData();
            }, 30000); // Refresh every 30 seconds
        }
    }

    /**
     * Refresh dashboard data
     */
    async refreshDashboardData() {
        try {
            // This would be implemented by the dashboard page
            if (typeof window.loadDashboardData === 'function') {
                window.loadDashboardData();
            }
        } catch (error) {
            console.error('Dashboard refresh error:', error);
        }
    }

    /**
     * Initialize tooltips
     */
    initializeTooltips() {
        // Simple tooltip implementation
        document.querySelectorAll('[title]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.getAttribute('title'));
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.id = 'tooltip';
        tooltip.className = 'absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg';
        tooltip.textContent = text;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    }

    hideTooltip() {
        const tooltip = document.getElementById('tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    /**
     * Initialize data tables
     */
    initializeDataTables() {
        // Basic table functionality - sorting, filtering
        document.querySelectorAll('.data-table').forEach(table => {
            this.enhanceTable(table);
        });
    }

    enhanceTable(table) {
        // Add sorting to headers
        const headers = table.querySelectorAll('th[data-sortable]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(table, header);
            });
        });
    }

    sortTable(table, header) {
        // Basic table sorting implementation
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentElement.children).indexOf(header);
        const isAscending = !header.classList.contains('sort-asc');
        
        // Clear previous sort indicators
        header.parentElement.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        // Add current sort indicator
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
        
        // Sort rows
        rows.sort((a, b) => {
            const aValue = a.children[columnIndex].textContent.trim();
            const bValue = b.children[columnIndex].textContent.trim();
            
            if (isAscending) {
                return aValue.localeCompare(bValue, undefined, { numeric: true });
            } else {
                return bValue.localeCompare(aValue, undefined, { numeric: true });
            }
        });
        
        // Reorder rows in DOM
        rows.forEach(row => tbody.appendChild(row));
    }

    /**
     * Initialize charts (placeholder for chart library integration)
     */
    initializeCharts() {
        // This would integrate with a chart library like Chart.js
        console.log('Charts initialized');
    }

    /**
     * Initialize search functionality
     */
    initializeSearch() {
        document.querySelectorAll('.search-input').forEach(input => {
            let timeout;
            input.addEventListener('input', (e) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.performSearch(e.target);
                }, 300);
            });
        });
    }

    performSearch(input) {
        const query = input.value.toLowerCase();
        const targetTable = document.querySelector(input.dataset.target);
        
        if (targetTable) {
            const rows = targetTable.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(query) ? '' : 'none';
            });
        }
    }

    /**
     * Utility methods
     */
    formatDate(date) {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }

    formatNumber(number) {
        return new Intl.NumberFormat('en-US').format(number);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize admin interface when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.guardGeoAdmin = new GuardGeoAdmin();
});

// Global utility functions for backward compatibility
function showModal(modalId) {
    window.guardGeoAdmin.showModal(modalId);
}

function closeModal(modalId) {
    window.guardGeoAdmin.closeModal(modalId);
}

function showNotification(message, type, duration) {
    window.guardGeoAdmin.showNotification(message, type, duration);
}