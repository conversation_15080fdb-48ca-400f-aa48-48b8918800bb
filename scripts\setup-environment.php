#!/usr/bin/env php
<?php

/**
 * Environment Setup Script
 * 
 * Interactive script to set up environment configuration for different
 * deployment environments (development, staging, production).
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Skpassegna\GuardgeoApi\Config\DeploymentManager;
use Skpassegna\GuardgeoApi\Utils\CredentialManager;

// ANSI color codes for output
const COLOR_GREEN = "\033[32m";
const COLOR_RED = "\033[31m";
const COLOR_YELLOW = "\033[33m";
const COLOR_BLUE = "\033[34m";
const COLOR_CYAN = "\033[36m";
const COLOR_RESET = "\033[0m";

function printHeader(string $title): void
{
    echo COLOR_BLUE . "\n" . str_repeat("=", 60) . "\n";
    echo " " . strtoupper($title) . "\n";
    echo str_repeat("=", 60) . COLOR_RESET . "\n\n";
}

function printSuccess(string $message): void
{
    echo COLOR_GREEN . "✓ " . $message . COLOR_RESET . "\n";
}

function printError(string $message): void
{
    echo COLOR_RED . "✗ " . $message . COLOR_RESET . "\n";
}

function printWarning(string $message): void
{
    echo COLOR_YELLOW . "⚠ " . $message . COLOR_RESET . "\n";
}

function printInfo(string $message): void
{
    echo COLOR_CYAN . "ℹ " . $message . COLOR_RESET . "\n";
}

function prompt(string $question, string $default = ''): string
{
    $defaultText = $default ? " [$default]" : '';
    echo COLOR_YELLOW . $question . $defaultText . ": " . COLOR_RESET;
    
    $input = trim(fgets(STDIN));
    return $input ?: $default;
}

function promptPassword(string $question): string
{
    echo COLOR_YELLOW . $question . ": " . COLOR_RESET;
    
    // Hide input for password
    system('stty -echo');
    $password = trim(fgets(STDIN));
    system('stty echo');
    echo "\n";
    
    return $password;
}

function promptChoice(string $question, array $choices, string $default = ''): string
{
    echo COLOR_YELLOW . $question . COLOR_RESET . "\n";
    
    foreach ($choices as $key => $choice) {
        $marker = ($choice === $default) ? '*' : ' ';
        echo "  $marker $key) $choice\n";
    }
    
    do {
        $input = prompt("Choose", array_search($default, $choices) ?: '');
        
        if (isset($choices[$input])) {
            return $choices[$input];
        }
        
        printError("Invalid choice. Please try again.");
    } while (true);
}

function generateSecureKey(int $length = 32): string
{
    return bin2hex(random_bytes($length));
}

function validateEmail(string $email): bool
{
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function validateUrl(string $url): bool
{
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

try {
    printHeader("GuardGeo Environment Setup");
    
    // Get environment
    $environment = promptChoice(
        "Select environment to configure:",
        ['1' => 'development', '2' => 'staging', '3' => 'production'],
        'development'
    );
    
    printInfo("Setting up $environment environment...");
    
    // Initialize deployment manager
    $deploymentManager = DeploymentManager::getInstance();
    
    // Collect configuration values
    $config = [];
    
    // Application Configuration
    printHeader("Application Configuration");
    
    $config['APP_ENV'] = $environment;
    $config['APP_DEBUG'] = ($environment === 'production') ? 'false' : 
                          prompt("Enable debug mode? (true/false)", $environment === 'development' ? 'true' : 'false');
    
    $defaultUrl = match($environment) {
        'production' => 'https://your-domain.com',
        'staging' => 'https://staging.your-domain.com',
        'development' => 'http://localhost:8000'
    };
    
    $config['APP_URL'] = prompt("Application URL", $defaultUrl);
    $config['APP_TIMEZONE'] = prompt("Timezone", 'UTC');
    
    // Generate encryption key
    $config['APP_ENCRYPTION_KEY'] = generateSecureKey(32);
    printSuccess("Generated secure encryption key");
    
    // Database Configuration
    printHeader("Database Configuration");
    
    $config['DB_HOST'] = prompt("Database host", 'localhost');
    $config['DB_PORT'] = prompt("Database port", '5432');
    $config['DB_NAME'] = prompt("Database name", "guardgeo_$environment");
    $config['DB_USERNAME'] = prompt("Database username", "guardgeo_user");
    $config['DB_PASSWORD'] = promptPassword("Database password");
    
    if ($environment !== 'development') {
        $useSSL = promptChoice("Use SSL for database connection?", ['1' => 'yes', '2' => 'no'], 'yes');
        if ($useSSL === 'yes') {
            $config['DB_SSL_CA'] = prompt("SSL CA certificate path (optional)");
            $config['DB_SSL_VERIFY'] = 'true';
        }
    }
    
    // API Configuration
    printHeader("External API Configuration");
    
    $config['FREEMIUS_API_TOKEN'] = prompt("Freemius API token");
    $config['FREEMIUS_API_BASE_URL'] = prompt("Freemius API base URL", 'https://api.freemius.com/v1');
    
    $config['IPREGISTRY_API_KEY'] = prompt("ipRegistry API key");
    $config['IPREGISTRY_API_BASE_URL'] = prompt("ipRegistry API base URL", 'https://api.ipregistry.co');
    
    // Security Configuration
    printHeader("Security Configuration");
    
    if ($environment === 'production' || $environment === 'staging') {
        $config['SESSION_SECURE'] = 'true';
        $config['SESSION_DOMAIN'] = prompt("Session domain", parse_url($config['APP_URL'], PHP_URL_HOST));
        
        // SSL Configuration
        $config['SSL_CERTIFICATE_PATH'] = prompt("SSL certificate path");
        $config['SSL_PRIVATE_KEY_PATH'] = prompt("SSL private key path");
        $config['SSL_CA_BUNDLE_PATH'] = prompt("SSL CA bundle path (optional)");
    } else {
        $config['SESSION_SECURE'] = 'false';
    }
    
    $config['CSRF_PROTECTION'] = 'true';
    
    // Admin Configuration
    printHeader("Admin Configuration");
    
    $allowedDomains = prompt("Allowed email domains (comma-separated)", 'your-company.com');
    $config['ADMIN_ALLOWED_EMAIL_DOMAINS'] = $allowedDomains;
    $config['EMAIL_DOMAIN_STRICT_MODE'] = 'true';
    
    // Logging Configuration
    printHeader("Logging Configuration");
    
    $logLevel = match($environment) {
        'production' => 'error',
        'staging' => 'info',
        'development' => 'debug'
    };
    
    $config['LOG_LEVEL'] = prompt("Log level", $logLevel);
    $config['LOG_FILE_PATH'] = prompt("Log file path", 'logs');
    
    // Cache Configuration
    printHeader("Cache Configuration");
    
    $config['CACHE_ENABLED'] = prompt("Enable caching? (true/false)", 'true');
    $config['CACHE_DRIVER'] = promptChoice("Cache driver:", ['1' => 'database', '2' => 'redis'], 'database');
    
    if ($config['CACHE_DRIVER'] === 'redis') {
        $config['REDIS_HOST'] = prompt("Redis host", 'localhost');
        $config['REDIS_PORT'] = prompt("Redis port", '6379');
        $config['REDIS_PASSWORD'] = promptPassword("Redis password (optional)");
        $config['REDIS_DB'] = prompt("Redis database", '0');
    }
    
    // Monitoring Configuration
    printHeader("Monitoring Configuration");
    
    $config['MONITORING_ENABLED'] = prompt("Enable monitoring? (true/false)", 'true');
    $config['PERFORMANCE_TRACKING'] = prompt("Enable performance tracking? (true/false)", 'true');
    $config['ERROR_REPORTING'] = prompt("Enable error reporting? (true/false)", 'true');
    
    // Email Configuration (optional)
    printHeader("Email Configuration (Optional)");
    
    $configureEmail = promptChoice("Configure email settings?", ['1' => 'yes', '2' => 'no'], 'no');
    
    if ($configureEmail === 'yes') {
        $config['MAIL_HOST'] = prompt("SMTP host");
        $config['MAIL_PORT'] = prompt("SMTP port", '587');
        $config['MAIL_USERNAME'] = prompt("SMTP username");
        $config['MAIL_PASSWORD'] = promptPassword("SMTP password");
        $config['MAIL_ENCRYPTION'] = promptChoice("SMTP encryption:", ['1' => 'tls', '2' => 'ssl'], 'tls');
        $config['MAIL_FROM_ADDRESS'] = prompt("From email address");
        $config['MAIL_FROM_NAME'] = prompt("From name", 'GuardGeo Admin Platform');
    }
    
    // Backup Configuration (for production/staging)
    if ($environment === 'production' || $environment === 'staging') {
        printHeader("Backup Configuration");
        
        $config['BACKUP_ENCRYPTION_KEY'] = generateSecureKey(32);
        printSuccess("Generated secure backup encryption key");
        
        $configureS3 = promptChoice("Configure S3 backup?", ['1' => 'yes', '2' => 'no'], 'no');
        
        if ($configureS3 === 'yes') {
            $config['BACKUP_S3_BUCKET'] = prompt("S3 bucket name");
            $config['BACKUP_S3_REGION'] = prompt("S3 region", 'us-east-1');
            $config['BACKUP_S3_KEY'] = prompt("S3 access key");
            $config['BACKUP_S3_SECRET'] = promptPassword("S3 secret key");
        }
    }
    
    // Generate .env file
    printHeader("Generating Configuration File");
    
    $envContent = "# GuardGeo Admin Platform - $environment Environment Configuration\n";
    $envContent .= "# Generated on " . date('Y-m-d H:i:s') . "\n\n";
    
    foreach ($config as $key => $value) {
        if (!empty($value)) {
            $envContent .= "$key=$value\n";
        }
    }
    
    // Add additional configuration based on environment
    $envContent .= "\n# Additional Configuration\n";
    $envContent .= "API_RATE_LIMIT_ENABLED=true\n";
    $envContent .= "API_RATE_LIMIT_REQUESTS=" . ($environment === 'production' ? '1000' : '2000') . "\n";
    $envContent .= "API_RATE_LIMIT_WINDOW=3600\n";
    
    $envContent .= "\n# IP Cache Configuration\n";
    $envContent .= "IP_CACHE_LOCATION_DAYS=10\n";
    $envContent .= "IP_CACHE_SECURITY_DAYS=3\n";
    $envContent .= "IP_CACHE_CONNECTION_DAYS=7\n";
    $envContent .= "IP_CACHE_COMPANY_DAYS=30\n";
    
    // Write .env file
    $envFile = __DIR__ . '/../.env';
    
    if (file_exists($envFile)) {
        $overwrite = promptChoice("Environment file already exists. Overwrite?", ['1' => 'yes', '2' => 'no'], 'no');
        
        if ($overwrite === 'no') {
            $envFile = __DIR__ . "/../.env.$environment." . date('Y-m-d-H-i-s');
            printInfo("Writing to alternative file: " . basename($envFile));
        } else {
            // Backup existing file
            $backupFile = $envFile . '.backup.' . date('Y-m-d-H-i-s');
            copy($envFile, $backupFile);
            printInfo("Backed up existing file to: " . basename($backupFile));
        }
    }
    
    file_put_contents($envFile, $envContent);
    chmod($envFile, 0600); // Secure permissions
    
    printSuccess("Environment configuration written to: " . basename($envFile));
    
    // Generate additional files
    printHeader("Additional Setup");
    
    // Create log directory if it doesn't exist
    $logDir = __DIR__ . '/../' . ($config['LOG_FILE_PATH'] ?? 'logs');
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
        printSuccess("Created log directory: " . basename($logDir));
    }
    
    // Set proper permissions
    if (is_dir($logDir)) {
        chmod($logDir, 0755);
        printSuccess("Set log directory permissions");
    }
    
    // Generate deployment checklist
    $generateChecklist = promptChoice("Generate deployment checklist?", ['1' => 'yes', '2' => 'no'], 'yes');
    
    if ($generateChecklist === 'yes') {
        $checklist = $deploymentManager->getDeploymentChecklist();
        
        if (!empty($checklist)) {
            $checklistFile = __DIR__ . "/../deployment-checklist-$environment.md";
            $checklistContent = "# Deployment Checklist - " . ucfirst($environment) . " Environment\n\n";
            $checklistContent .= "Generated on: " . date('Y-m-d H:i:s') . "\n\n";
            
            foreach ($checklist as $section => $tasks) {
                $checklistContent .= "## $section\n\n";
                foreach ($tasks as $task) {
                    $checklistContent .= "- [ ] " . $task['task'] . "\n";
                }
                $checklistContent .= "\n";
            }
            
            file_put_contents($checklistFile, $checklistContent);
            printSuccess("Generated deployment checklist: " . basename($checklistFile));
        }
    }
    
    // Final instructions
    printHeader("Setup Complete");
    
    printSuccess("Environment setup completed successfully!");
    
    echo "\n" . COLOR_CYAN . "Next steps:\n" . COLOR_RESET;
    echo "1. Review the generated .env file and update any values as needed\n";
    echo "2. Run the configuration validation script: php scripts/validate-configuration.php\n";
    echo "3. Set up your database using the SQL scripts in src/Database/\n";
    echo "4. Configure your web server to point to the application directory\n";
    
    if ($environment === 'production') {
        echo "5. Set up SSL certificates and configure HTTPS\n";
        echo "6. Configure backup and monitoring systems\n";
        echo "7. Review and complete the deployment checklist\n";
    }
    
    echo "\n" . COLOR_GREEN . "Environment configuration is ready!" . COLOR_RESET . "\n\n";
    
} catch (\Exception $e) {
    printError("Setup failed with error: " . $e->getMessage());
    
    if (isset($argv[1]) && $argv[1] === '--debug') {
        echo "\n" . COLOR_RED . "Stack trace:\n" . $e->getTraceAsString() . COLOR_RESET . "\n\n";
    }
    
    exit(1);
}