<?php

namespace Skpassegna\GuardgeoApi\Database;

use Skpassegna\GuardgeoApi\Models\AdminUserModel;

/**
 * Admin User Repository
 * 
 * Handles database operations for admin users with proper error handling
 * and logging.
 */
class AdminUserRepository extends BaseRepository
{
    protected string $table = 'admin_users';

    /**
     * Find user by email
     */
    public function findByEmail(string $email): ?AdminUserModel
    {
        try {
            $row = $this->findOneBy(['email' => $email]);
            
            if (!$row) {
                return null;
            }

            return AdminUserModel::fromArray($row);

        } catch (DatabaseException $e) {
            $this->logger->error('Failed to find user by email', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Find user by ID (override parent to return model)
     */
    public function findById(int|string $id): ?AdminUserModel
    {
        try {
            $row = parent::findById($id);
            
            if (!$row) {
                return null;
            }

            return AdminUserModel::fromArray($row);

        } catch (DatabaseException $e) {
            $this->logger->error('Failed to find user by ID', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Create new admin user
     */
    public function create(AdminUserModel $user): ?AdminUserModel
    {
        try {
            $data = [
                'email' => $user->getEmail(),
                'password_hash' => $user->getPasswordHash(),
                'role' => $user->getRole(),
                'is_active' => $user->isActive()
            ];

            $id = $this->insert($data);
            
            return $this->findById($id);

        } catch (DatabaseException $e) {
            $this->logger->error('Failed to create user', [
                'email' => $user->getEmail(),
                'role' => $user->getRole(),
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Update admin user
     */
    public function updateUser(AdminUserModel $user): bool
    {
        try {
            $data = [
                'email' => $user->getEmail(),
                'role' => $user->getRole(),
                'is_active' => $user->isActive()
            ];

            return $this->update($user->getId(), $data);

        } catch (DatabaseException $e) {
            $this->logger->error('Failed to update user', [
                'id' => $user->getId(),
                'email' => $user->getEmail(),
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Update user password
     */
    public function updatePassword(int $userId, string $passwordHash): bool
    {
        try {
            return $this->update($userId, ['password_hash' => $passwordHash]);

        } catch (DatabaseException $e) {
            $this->logger->error('Failed to update user password', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Update last login timestamp
     */
    public function updateLastLogin(int $userId): bool
    {
        try {
            return $this->update($userId, ['last_login' => date('Y-m-d H:i:s')]);

        } catch (DatabaseException $e) {
            $this->logger->error('Failed to update last login', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Deactivate user
     */
    public function deactivate(int $userId): bool
    {
        try {
            return $this->update($userId, ['is_active' => false]);

        } catch (DatabaseException $e) {
            $this->logger->error('Failed to deactivate user', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Activate user
     */
    public function activate(int $userId): bool
    {
        try {
            return $this->update($userId, ['is_active' => true]);

        } catch (DatabaseException $e) {
            $this->logger->error('Failed to activate user', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Get all users with pagination
     *
     * @param int $page
     * @param int $limit
     * @param string|null $role Filter by role
     * @param bool|null $active Filter by active status
     * @return array
     */
    public function getAll(int $page = 1, int $limit = 20, ?string $role = null, ?bool $active = null): array
    {
        try {
            $offset = ($page - 1) * $limit;
            $conditions = [];
            $params = [];

            if ($role !== null) {
                $conditions[] = "role = ?";
                $params[] = $role;
            }

            if ($active !== null) {
                $conditions[] = "is_active = ?";
                $params[] = $active;
            }

            $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);

            // Get total count
            $countQuery = "SELECT COUNT(*) FROM {$this->table} {$whereClause}";
            $countResult = $this->db->query($countQuery, $params);
            $totalCount = $countResult->fetchColumn();

            // Get users
            $query = "
                SELECT * FROM {$this->table} 
                {$whereClause}
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ";

            $params[] = $limit;
            $params[] = $offset;

            $result = $this->db->query($query, $params);
            $users = [];

            while ($userData = $result->fetch(\PDO::FETCH_ASSOC)) {
                $users[] = AdminUserModel::fromArray($userData);
            }

            return [
                'users' => $users,
                'total' => $totalCount,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($totalCount / $limit)
            ];

        } catch (\Exception $e) {
            $this->logError('Failed to get all users', [
                'page' => $page,
                'limit' => $limit,
                'role' => $role,
                'active' => $active,
                'error' => $e->getMessage()
            ]);
            
            return [
                'users' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit,
                'pages' => 0
            ];
        }
    }

    /**
     * Get users by role
     *
     * @param string $role
     * @return array
     */
    public function getByRole(string $role): array
    {
        try {
            $query = "SELECT * FROM {$this->table} WHERE role = ? AND is_active = true ORDER BY email";
            $result = $this->db->query($query, [$role]);
            
            $users = [];
            while ($userData = $result->fetch(\PDO::FETCH_ASSOC)) {
                $users[] = AdminUserModel::fromArray($userData);
            }

            return $users;

        } catch (\Exception $e) {
            $this->logError('Failed to get users by role', [
                'role' => $role,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * Check if email exists
     *
     * @param string $email
     * @param int|null $excludeId Exclude this user ID from check
     * @return bool
     */
    public function emailExists(string $email, ?int $excludeId = null): bool
    {
        try {
            $query = "SELECT COUNT(*) FROM {$this->table} WHERE email = ?";
            $params = [$email];

            if ($excludeId !== null) {
                $query .= " AND id != ?";
                $params[] = $excludeId;
            }

            $result = $this->db->query($query, $params);
            
            return $result->fetchColumn() > 0;

        } catch (\Exception $e) {
            $this->logError('Failed to check email existence', [
                'email' => $email,
                'exclude_id' => $excludeId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Get user statistics
     *
     * @return array
     */
    public function getStatistics(): array
    {
        try {
            $stats = [
                'total' => 0,
                'active' => 0,
                'inactive' => 0,
                'by_role' => []
            ];

            // Total and active counts
            $query = "
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN is_active = true THEN 1 END) as active,
                    COUNT(CASE WHEN is_active = false THEN 1 END) as inactive
                FROM {$this->table}
            ";
            
            $result = $this->db->query($query);
            $counts = $result->fetch(\PDO::FETCH_ASSOC);
            
            $stats['total'] = (int)$counts['total'];
            $stats['active'] = (int)$counts['active'];
            $stats['inactive'] = (int)$counts['inactive'];

            // By role
            $roleQuery = "
                SELECT 
                    role,
                    COUNT(*) as count,
                    COUNT(CASE WHEN is_active = true THEN 1 END) as active_count
                FROM {$this->table}
                GROUP BY role
                ORDER BY role
            ";
            
            $roleResult = $this->db->query($roleQuery);
            
            while ($roleData = $roleResult->fetch(\PDO::FETCH_ASSOC)) {
                $stats['by_role'][$roleData['role']] = [
                    'total' => (int)$roleData['count'],
                    'active' => (int)$roleData['active_count']
                ];
            }

            return $stats;

        } catch (\Exception $e) {
            $this->logError('Failed to get user statistics', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'total' => 0,
                'active' => 0,
                'inactive' => 0,
                'by_role' => []
            ];
        }
    }

    /**
     * Delete user (soft delete by deactivating)
     *
     * @param int $userId
     * @return bool
     */
    public function delete(int $userId): bool
    {
        // For security, we don't actually delete users, just deactivate them
        return $this->deactivate($userId);
    }
}