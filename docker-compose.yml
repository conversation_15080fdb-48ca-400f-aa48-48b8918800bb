version: '3.8'

services:
  web:
    image: php:8.2-apache
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html/public
    command: >
      bash -c "
        a2enmod rewrite &&
        echo 'DocumentRoot /var/www/html/public' > /etc/apache2/sites-available/000-default.conf &&
        echo '<Directory /var/www/html/public>' >> /etc/apache2/sites-available/000-default.conf &&
        echo '    AllowOverride All' >> /etc/apache2/sites-available/000-default.conf &&
        echo '    Require all granted' >> /etc/apache2/sites-available/000-default.conf &&
        echo '</Directory>' >> /etc/apache2/sites-available/000-default.conf &&
        docker-php-ext-install pdo pdo_pgsql &&
        apache2-foreground
      "
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: guardgeo
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data: