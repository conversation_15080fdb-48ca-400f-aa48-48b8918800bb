<?php

namespace Skpassegna\GuardgeoApi\Controllers;

use Skpassegna\GuardgeoApi\Utils\AuthMiddleware;
use Skpassegna\GuardgeoApi\Utils\RoleManager;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Services\MonitoringService;
use Skpassegna\GuardgeoApi\Services\AuthService;
use Skpassegna\GuardgeoApi\Utils\ResponseFormatter;
use Skpassegna\GuardgeoApi\Utils\RequestValidator;

/**
 * Admin Router
 * 
 * Handles routing for admin interface with proper authentication
 * and role-based access control.
 */
class AdminRouter
{
    private AuthController $authController;
    private AdminController $adminController;
    private DashboardApiController $dashboardApiController;
    private IpIntelligenceApiController $ipApiController;
    private FreemiusApiController $freemiusApiController;
    private LogsApiController $logsApiController;
    private UserManagementApiController $userApiController;
    private ?MaintenanceApiController $maintenanceApiController = null;
    private ?AdminFormController $formController = null;
    private LoggingService $logger;

    public function __construct()
    {
        // Initialize dependencies
        $this->logger = new LoggingService();
        
        // Initialize database and other dependencies
        $db = new \Skpassegna\GuardgeoApi\Database\DatabaseConnection();
        $sessionManager = new \Skpassegna\GuardgeoApi\Utils\SessionManager();
        $passwordValidator = new \Skpassegna\GuardgeoApi\Utils\PasswordValidator();
        $emailValidator = new \Skpassegna\GuardgeoApi\Utils\EmailDomainValidator();
        
        $authService = new \Skpassegna\GuardgeoApi\Services\AuthService(
            $db,
            $sessionManager,
            $passwordValidator,
            $emailValidator,
            $this->logger
        );
        
        $authMiddleware = new AuthMiddleware($authService);
        $roleManager = new RoleManager();
        $responseFormatter = new ResponseFormatter();
        $requestValidator = new RequestValidator();
        $monitoring = new MonitoringService($this->logger);

        // Initialize controllers
        $this->authController = new AuthController(
            $authService,
            $authMiddleware,
            $responseFormatter,
            $requestValidator,
            $this->logger
        );

        $viewRenderer = new \Skpassegna\GuardgeoApi\Services\ViewRenderer();
        
        $this->adminController = new AdminController(
            $authMiddleware,
            $roleManager,
            $this->logger,
            $monitoring,
            $viewRenderer
        );

        $this->dashboardApiController = DashboardApiController::create();
        $this->ipApiController = IpIntelligenceApiController::create();
        $this->freemiusApiController = FreemiusApiController::create();
        $this->logsApiController = LogsApiController::create();
        $this->userApiController = UserManagementApiController::create();
    }

    /**
     * Handle admin routes
     *
     * @param string $path
     * @param string $method
     * @return void
     */
    public function handleRequest(string $path, string $method = 'GET'): void
    {
        try {
            // Remove query string from path
            $path = strtok($path, '?');
            
            // Remove /admin prefix if present
            if (strpos($path, '/admin') === 0) {
                $path = substr($path, 6);
            }
            
            // Default to dashboard if empty
            if (empty($path) || $path === '/') {
                $path = '/dashboard';
            }

            // Route the request
            match ([$method, $path]) {
                // Authentication routes
                ['GET', '/login'] => $this->authController->showLoginForm(),
                ['POST', '/login'] => $this->authController->login(),
                ['POST', '/logout'] => $this->authController->logout(),
                ['GET', '/logout'] => $this->authController->logout(),
                ['GET', '/auth/check'] => $this->authController->checkAuth(),
                ['GET', '/auth/csrf'] => $this->authController->getCsrfToken(),

                // Dashboard routes
                ['GET', '/dashboard'] => $this->adminController->dashboard(),
                ['GET', '/'] => $this->adminController->dashboard(),

                // IP Intelligence routes
                ['GET', '/ip-intelligence'] => $this->adminController->ipIntelligence(),

                // Freemius routes
                ['GET', '/freemius'] => $this->adminController->freemius(),

                // Logs routes
                ['GET', '/logs'] => $this->adminController->logs(),

                // User management routes
                ['GET', '/users'] => $this->adminController->users(),

                // Settings routes
                ['GET', '/settings'] => $this->adminController->settings(),

                // Dashboard API routes
                ['GET', '/api/dashboard/overview'] => $this->dashboardApiController->getSystemOverview(),
                ['GET', '/api/dashboard/activity'] => $this->dashboardApiController->getRecentActivity(),
                ['GET', '/api/dashboard/api-usage'] => $this->dashboardApiController->getApiUsageStats(),
                ['GET', '/api/dashboard/ip-stats'] => $this->dashboardApiController->getIpIntelligenceStats(),

                // IP Intelligence API routes
                ['GET', '/api/ip-intelligence/records'] => $this->ipApiController->getIpRecords(),
                ['GET', '/api/ip-intelligence/details'] => $this->ipApiController->getIpDetails(),
                ['GET', '/api/ip-intelligence/statistics'] => $this->ipApiController->getStatistics(),
                ['POST', '/api/ip-intelligence/refresh'] => $this->ipApiController->refreshIpData(),
                ['DELETE', '/api/ip-intelligence/delete'] => $this->ipApiController->deleteIpRecord(),
                ['POST', '/api/ip-intelligence/bulk-refresh'] => $this->ipApiController->bulkRefreshStale(),

                // Freemius API routes
                ['GET', '/api/freemius/products'] => $this->freemiusApiController->getProducts(),
                ['GET', '/api/freemius/product-details'] => $this->freemiusApiController->getProductDetails(),
                ['GET', '/api/freemius/installations'] => $this->freemiusApiController->getInstallations(),
                ['GET', '/api/freemius/installation-details'] => $this->freemiusApiController->getInstallationDetails(),
                ['GET', '/api/freemius/statistics'] => $this->freemiusApiController->getStatistics(),
                ['POST', '/api/freemius/sync-product'] => $this->freemiusApiController->syncProductData(),
                ['POST', '/api/freemius/sync-installation'] => $this->freemiusApiController->syncInstallationData(),
                ['POST', '/api/freemius/validate-installation'] => $this->freemiusApiController->validateInstallation(),

                // Logs API routes
                ['GET', '/api/logs/system'] => $this->logsApiController->getSystemLogs(),
                ['GET', '/api/logs/api'] => $this->logsApiController->getApiLogs(),
                ['GET', '/api/logs/statistics'] => $this->logsApiController->getLogStatistics(),
                ['GET', '/api/logs/export'] => $this->logsApiController->exportLogs(),

                // User Management API routes (Super Admin only)
                ['GET', '/api/users'] => $this->userApiController->getUsers(),
                ['GET', '/api/users/details'] => $this->userApiController->getUserDetails(),
                ['GET', '/api/users/statistics'] => $this->userApiController->getUserStatistics(),
                ['GET', '/api/users/roles'] => $this->userApiController->getAvailableRoles(),
                ['POST', '/api/users/create'] => $this->userApiController->createUser(),
                ['POST', '/api/users/update'] => $this->userApiController->updateUser(),
                ['DELETE', '/api/users/delete'] => $this->userApiController->deleteUser(),

                // Enhanced Form Handling routes
                ['POST', '/api/forms/user/create'] => $this->getFormController()->handleUserCreation(),
                ['POST', '/api/forms/user/update'] => $this->getFormController()->handleUserUpdate(),
                ['POST', '/api/forms/settings/update'] => $this->getFormController()->handleSettingsUpdate(),
                ['POST', '/api/forms/file/upload'] => $this->getFormController()->handleFileUpload(),

                // System Maintenance API routes (Super Admin and Dev only)
                ['GET', '/api/maintenance/health'] => $this->getMaintenanceController()->healthCheck(),
                ['GET', '/api/maintenance/status'] => $this->getMaintenanceController()->getMigrationStatus(),
                ['POST', '/api/maintenance/migrate'] => $this->getMaintenanceController()->runMigrations(),
                ['POST', '/api/maintenance/rollback'] => $this->getMaintenanceController()->rollbackMigration(),
                ['GET', '/api/maintenance/validate'] => $this->getMaintenanceController()->validateMigrations(),
                ['POST', '/api/maintenance/backup'] => $this->getMaintenanceController()->createBackup(),
                ['GET', '/api/maintenance/backups'] => $this->getMaintenanceController()->listBackups(),
                ['POST', '/api/maintenance/restore'] => $this->getMaintenanceController()->restoreBackup(),
                ['DELETE', '/api/maintenance/backup'] => $this->getMaintenanceController()->deleteBackup(),
                ['POST', '/api/maintenance/cleanup-backups'] => $this->getMaintenanceController()->cleanupBackups(),
                ['GET', '/api/maintenance/system-info'] => $this->getMaintenanceController()->getSystemInfo(),
                ['POST', '/api/maintenance/clear-cache'] => $this->getMaintenanceController()->clearCaches(),
                ['POST', '/api/maintenance/optimize-db'] => $this->getMaintenanceController()->optimizeDatabase(),

                // Maintenance page routes
                ['GET', '/maintenance'] => $this->adminController->maintenance(),

                // Default case - 404
                default => $this->handleNotFound($path)
            };

        } catch (\Exception $e) {
            $this->logger->logError('Admin router error', [
                'path' => $path,
                'method' => $method,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->handleServerError($e);
        }
    }

    /**
     * Handle 404 Not Found
     *
     * @param string $path
     * @return void
     */
    private function handleNotFound(string $path): void
    {
        http_response_code(404);
        header('Content-Type: text/html; charset=utf-8');
        
        echo $this->getNotFoundHtml($path);
    }

    /**
     * Handle 500 Server Error
     *
     * @param \Exception $e
     * @return void
     */
    private function handleServerError(\Exception $e): void
    {
        http_response_code(500);
        header('Content-Type: text/html; charset=utf-8');
        
        echo $this->getServerErrorHtml();
    }

    /**
     * Get 404 Not Found HTML
     *
     * @param string $path
     * @return string
     */
    private function getNotFoundHtml(string $path): string
    {
        $safePath = htmlspecialchars($path, ENT_QUOTES, 'UTF-8');
        
        return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - GuardGeo Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full text-center">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-question text-gray-600 text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Page Not Found</h1>
            <p class="text-gray-600 mb-2">The page <code class="bg-gray-100 px-2 py-1 rounded text-sm">{$safePath}</code> could not be found.</p>
            <p class="text-gray-500 text-sm mb-6">Please check the URL or navigate back to the dashboard.</p>
            <div class="space-y-2">
                <a href="/admin/dashboard" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Go to Dashboard
                </a>
                <br>
                <a href="/admin/login" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Login
                </a>
            </div>
        </div>
    </div>
</body>
</html>
HTML;
    }

    /**
     * Get 500 Server Error HTML
     *
     * @return string
     */
    private function getServerErrorHtml(): string
    {
        return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Error - GuardGeo Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full text-center">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Server Error</h1>
            <p class="text-gray-600 mb-6">An unexpected error occurred. Please try again later.</p>
            <div class="space-y-2">
                <button onclick="window.location.reload()" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-redo mr-2"></i>
                    Try Again
                </button>
                <br>
                <a href="/admin/dashboard" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                    <i class="fas fa-home mr-2"></i>
                    Go Home
                </a>
            </div>
        </div>
    </div>
</body>
</html>
HTML;
    }

    /**
     * Get available admin routes for debugging
     *
     * @return array
     */
    public function getAvailableRoutes(): array
    {
        return [
            'Authentication' => [
                'GET /admin/login' => 'Show login form',
                'POST /admin/login' => 'Process login',
                'POST /admin/logout' => 'Process logout',
                'GET /admin/auth/check' => 'Check authentication status',
                'GET /admin/auth/csrf' => 'Get CSRF token'
            ],
            'Dashboard' => [
                'GET /admin/dashboard' => 'Main dashboard',
                'GET /admin/' => 'Dashboard (alias)'
            ],
            'IP Intelligence' => [
                'GET /admin/ip-intelligence' => 'IP intelligence management'
            ],
            'Freemius' => [
                'GET /admin/freemius' => 'Freemius integration management'
            ],
            'Logs' => [
                'GET /admin/logs' => 'System logs and monitoring'
            ],
            'User Management' => [
                'GET /admin/users' => 'User management (Super Admin only)'
            ],
            'Settings' => [
                'GET /admin/settings' => 'System settings (Super Admin only)'
            ],
            'Maintenance' => [
                'GET /admin/maintenance' => 'System maintenance and health monitoring (Super Admin/Dev only)'
            ]
        ];
    }
    
    /**
     * Get maintenance controller instance
     */
    private function getMaintenanceController(): MaintenanceApiController
    {
        if ($this->maintenanceApiController === null) {
            $this->maintenanceApiController = new MaintenanceApiController();
        }
        
        return $this->maintenanceApiController;
    }

    /**
     * Get form controller instance
     */
    private function getFormController(): AdminFormController
    {
        if ($this->formController === null) {
            $authMiddleware = new \Skpassegna\GuardgeoApi\Utils\AuthMiddleware(
                new \Skpassegna\GuardgeoApi\Services\AuthService(
                    new \Skpassegna\GuardgeoApi\Database\DatabaseConnection(),
                    new \Skpassegna\GuardgeoApi\Utils\SessionManager(),
                    new \Skpassegna\GuardgeoApi\Utils\PasswordValidator(),
                    new \Skpassegna\GuardgeoApi\Utils\EmailDomainValidator(),
                    $this->logger
                )
            );
            
            $formValidator = new \Skpassegna\GuardgeoApi\Utils\FormValidator();
            $ajaxHandler = new \Skpassegna\GuardgeoApi\Utils\AjaxFormHandler(
                $formValidator,
                $authMiddleware,
                $this->logger
            );
            
            $this->formController = new AdminFormController(
                $authMiddleware,
                $formValidator,
                $ajaxHandler,
                $this->logger
            );
        }
        
        return $this->formController;
    }
}