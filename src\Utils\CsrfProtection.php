<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * CSRF Protection
 * 
 * Advanced CSRF protection with token rotation, double-submit cookies,
 * and origin validation for comprehensive protection.
 */
class CsrfProtection
{
    private LoggingService $logger;
    private array $config;

    // Default configuration
    private const DEFAULT_CONFIG = [
        'token_length' => 32,
        'token_lifetime' => 3600,        // 1 hour
        'rotation_interval' => 300,      // 5 minutes
        'double_submit_cookies' => true,
        'origin_validation' => true,
        'referer_validation' => true,
        'same_site_validation' => true,
        'token_storage' => 'session',    // session, database, cache
        'max_tokens_per_session' => 5,
        'secure_cookies' => true,
        'httponly_cookies' => true,
        'samesite_policy' => 'Strict'
    ];

    // Token storage
    private array $tokenStorage = [];

    public function __construct(LoggingService $logger = null, array $config = [])
    {
        $this->logger = $logger ?? new LoggingService();
        $this->config = array_merge(self::DEFAULT_CONFIG, $config);
    }

    /**
     * Generate CSRF token with enhanced security
     *
     * @param string|null $action Specific action for token scoping
     * @return string Generated token
     */
    public function generateToken(?string $action = null): string
    {
        $this->initializeSession();
        
        // Generate cryptographically secure token
        $token = $this->createSecureToken();
        $timestamp = time();
        
        // Create token data
        $tokenData = [
            'token' => $token,
            'created_at' => $timestamp,
            'expires_at' => $timestamp + $this->config['token_lifetime'],
            'action' => $action,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        // Store token
        $this->storeToken($token, $tokenData);
        
        // Set double-submit cookie if enabled
        if ($this->config['double_submit_cookies']) {
            $this->setDoubleSubmitCookie($token);
        }
        
        // Clean up old tokens
        $this->cleanupExpiredTokens();
        
        $this->logger->logInfo('CSRF token generated', [
            'action' => $action,
            'token_id' => substr($token, 0, 8) . '...',
            'expires_at' => date('Y-m-d H:i:s', $tokenData['expires_at'])
        ]);
        
        return $token;
    }

    /**
     * Validate CSRF token with comprehensive checks
     *
     * @param string $token Token to validate
     * @param string|null $action Expected action
     * @return array Validation result
     */
    public function validateToken(string $token, ?string $action = null): array
    {
        $result = [
            'valid' => false,
            'errors' => [],
            'warnings' => []
        ];

        try {
            // Basic token format validation
            if (!$this->isValidTokenFormat($token)) {
                $result['errors'][] = 'Invalid token format';
                $this->logValidationFailure('invalid_format', $token, $action);
                return $result;
            }

            // Retrieve token data
            $tokenData = $this->getTokenData($token);
            if (!$tokenData) {
                $result['errors'][] = 'Token not found or expired';
                $this->logValidationFailure('token_not_found', $token, $action);
                return $result;
            }

            // Check token expiration
            if (time() > $tokenData['expires_at']) {
                $result['errors'][] = 'Token expired';
                $this->logValidationFailure('token_expired', $token, $action);
                $this->removeToken($token);
                return $result;
            }

            // Validate action scope
            if ($action && $tokenData['action'] && $tokenData['action'] !== $action) {
                $result['errors'][] = 'Token action mismatch';
                $this->logValidationFailure('action_mismatch', $token, $action);
                return $result;
            }

            // Origin validation
            if ($this->config['origin_validation'] && !$this->validateOrigin()) {
                $result['errors'][] = 'Origin validation failed';
                $this->logValidationFailure('origin_validation_failed', $token, $action);
                return $result;
            }

            // Referer validation
            if ($this->config['referer_validation'] && !$this->validateReferer()) {
                $result['warnings'][] = 'Referer validation failed';
                $this->logValidationFailure('referer_validation_failed', $token, $action, 'warning');
            }

            // Double-submit cookie validation
            if ($this->config['double_submit_cookies'] && !$this->validateDoubleSubmitCookie($token)) {
                $result['errors'][] = 'Double-submit cookie validation failed';
                $this->logValidationFailure('double_submit_failed', $token, $action);
                return $result;
            }

            // Same-site validation
            if ($this->config['same_site_validation'] && !$this->validateSameSite()) {
                $result['warnings'][] = 'Same-site validation failed';
                $this->logValidationFailure('same_site_failed', $token, $action, 'warning');
            }

            // IP and User-Agent consistency check
            $consistencyCheck = $this->validateTokenConsistency($tokenData);
            if (!$consistencyCheck['valid']) {
                $result['warnings'] = array_merge($result['warnings'], $consistencyCheck['warnings']);
            }

            // Token is valid
            $result['valid'] = true;
            
            // Rotate token if needed
            if ($this->shouldRotateToken($tokenData)) {
                $newToken = $this->rotateToken($token, $action);
                $result['new_token'] = $newToken;
            }

            $this->logger->logInfo('CSRF token validated successfully', [
                'action' => $action,
                'token_id' => substr($token, 0, 8) . '...',
                'warnings_count' => count($result['warnings'])
            ]);

        } catch (\Exception $e) {
            $result['errors'][] = 'Token validation error';
            $this->logger->logError('CSRF token validation error', [
                'error' => $e->getMessage(),
                'token_id' => substr($token, 0, 8) . '...',
                'action' => $action
            ]);
        }

        return $result;
    }

    /**
     * Get CSRF token for forms/AJAX
     *
     * @param string|null $action
     * @return string
     */
    public function getToken(?string $action = null): string
    {
        $this->initializeSession();
        
        // Check if we have a valid existing token
        $existingToken = $this->getExistingValidToken($action);
        if ($existingToken) {
            return $existingToken;
        }
        
        // Generate new token
        return $this->generateToken($action);
    }

    /**
     * Create secure token
     *
     * @return string
     */
    private function createSecureToken(): string
    {
        return bin2hex(random_bytes($this->config['token_length']));
    }

    /**
     * Initialize session if needed
     *
     * @return void
     */
    private function initializeSession(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_tokens'])) {
            $_SESSION['csrf_tokens'] = [];
        }
    }

    /**
     * Store token data
     *
     * @param string $token
     * @param array $tokenData
     * @return void
     */
    private function storeToken(string $token, array $tokenData): void
    {
        switch ($this->config['token_storage']) {
            case 'session':
                $_SESSION['csrf_tokens'][$token] = $tokenData;
                
                // Limit number of tokens per session
                if (count($_SESSION['csrf_tokens']) > $this->config['max_tokens_per_session']) {
                    $this->removeOldestToken();
                }
                break;
                
            case 'database':
                // Database storage implementation would go here
                break;
                
            case 'cache':
                // Cache storage implementation would go here
                break;
                
            default:
                $this->tokenStorage[$token] = $tokenData;
                break;
        }
    }

    /**
     * Get token data
     *
     * @param string $token
     * @return array|null
     */
    private function getTokenData(string $token): ?array
    {
        switch ($this->config['token_storage']) {
            case 'session':
                return $_SESSION['csrf_tokens'][$token] ?? null;
                
            case 'database':
                // Database retrieval implementation would go here
                return null;
                
            case 'cache':
                // Cache retrieval implementation would go here
                return null;
                
            default:
                return $this->tokenStorage[$token] ?? null;
        }
    }

    /**
     * Remove token
     *
     * @param string $token
     * @return void
     */
    private function removeToken(string $token): void
    {
        switch ($this->config['token_storage']) {
            case 'session':
                unset($_SESSION['csrf_tokens'][$token]);
                break;
                
            case 'database':
                // Database removal implementation would go here
                break;
                
            case 'cache':
                // Cache removal implementation would go here
                break;
                
            default:
                unset($this->tokenStorage[$token]);
                break;
        }
    }

    /**
     * Set double-submit cookie
     *
     * @param string $token
     * @return void
     */
    private function setDoubleSubmitCookie(string $token): void
    {
        $cookieName = 'csrf_token';
        $cookieValue = hash('sha256', $token); // Hash for additional security
        
        setcookie(
            $cookieName,
            $cookieValue,
            [
                'expires' => time() + $this->config['token_lifetime'],
                'path' => '/',
                'domain' => '',
                'secure' => $this->config['secure_cookies'] && $this->isHttps(),
                'httponly' => $this->config['httponly_cookies'],
                'samesite' => $this->config['samesite_policy']
            ]
        );
    }

    /**
     * Validate double-submit cookie
     *
     * @param string $token
     * @return bool
     */
    private function validateDoubleSubmitCookie(string $token): bool
    {
        $cookieValue = $_COOKIE['csrf_token'] ?? '';
        $expectedValue = hash('sha256', $token);
        
        return hash_equals($expectedValue, $cookieValue);
    }

    /**
     * Validate origin header
     *
     * @return bool
     */
    private function validateOrigin(): bool
    {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        
        if (empty($origin)) {
            return true; // Allow requests without origin (same-origin requests)
        }
        
        $allowedOrigins = $this->getAllowedOrigins();
        return in_array($origin, $allowedOrigins);
    }

    /**
     * Validate referer header
     *
     * @return bool
     */
    private function validateReferer(): bool
    {
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        
        if (empty($referer)) {
            return false; // Referer should be present for state-changing requests
        }
        
        $refererHost = parse_url($referer, PHP_URL_HOST);
        $currentHost = $_SERVER['HTTP_HOST'] ?? '';
        
        return $refererHost === $currentHost;
    }

    /**
     * Validate same-site request
     *
     * @return bool
     */
    private function validateSameSite(): bool
    {
        // Check for same-site indicators
        $secFetchSite = $_SERVER['HTTP_SEC_FETCH_SITE'] ?? '';
        
        if ($secFetchSite) {
            return in_array($secFetchSite, ['same-origin', 'same-site']);
        }
        
        // Fallback to referer check
        return $this->validateReferer();
    }

    /**
     * Validate token consistency
     *
     * @param array $tokenData
     * @return array
     */
    private function validateTokenConsistency(array $tokenData): array
    {
        $result = ['valid' => true, 'warnings' => []];
        
        $currentIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        if ($tokenData['ip'] !== $currentIp) {
            $result['warnings'][] = 'IP address changed since token creation';
        }
        
        if ($tokenData['user_agent'] !== $currentUserAgent) {
            $result['warnings'][] = 'User agent changed since token creation';
        }
        
        return $result;
    }

    /**
     * Check if token should be rotated
     *
     * @param array $tokenData
     * @return bool
     */
    private function shouldRotateToken(array $tokenData): bool
    {
        $age = time() - $tokenData['created_at'];
        return $age > $this->config['rotation_interval'];
    }

    /**
     * Rotate token
     *
     * @param string $oldToken
     * @param string|null $action
     * @return string New token
     */
    private function rotateToken(string $oldToken, ?string $action = null): string
    {
        // Remove old token
        $this->removeToken($oldToken);
        
        // Generate new token
        $newToken = $this->generateToken($action);
        
        $this->logger->logInfo('CSRF token rotated', [
            'old_token_id' => substr($oldToken, 0, 8) . '...',
            'new_token_id' => substr($newToken, 0, 8) . '...',
            'action' => $action
        ]);
        
        return $newToken;
    }

    /**
     * Get existing valid token
     *
     * @param string|null $action
     * @return string|null
     */
    private function getExistingValidToken(?string $action = null): ?string
    {
        $tokens = $_SESSION['csrf_tokens'] ?? [];
        
        foreach ($tokens as $token => $tokenData) {
            if (time() <= $tokenData['expires_at']) {
                if (!$action || $tokenData['action'] === $action) {
                    return $token;
                }
            }
        }
        
        return null;
    }

    /**
     * Clean up expired tokens
     *
     * @return void
     */
    private function cleanupExpiredTokens(): void
    {
        $now = time();
        $tokens = $_SESSION['csrf_tokens'] ?? [];
        
        foreach ($tokens as $token => $tokenData) {
            if ($now > $tokenData['expires_at']) {
                unset($_SESSION['csrf_tokens'][$token]);
            }
        }
    }

    /**
     * Remove oldest token
     *
     * @return void
     */
    private function removeOldestToken(): void
    {
        $tokens = $_SESSION['csrf_tokens'] ?? [];
        
        if (empty($tokens)) {
            return;
        }
        
        $oldestToken = null;
        $oldestTime = PHP_INT_MAX;
        
        foreach ($tokens as $token => $tokenData) {
            if ($tokenData['created_at'] < $oldestTime) {
                $oldestTime = $tokenData['created_at'];
                $oldestToken = $token;
            }
        }
        
        if ($oldestToken) {
            unset($_SESSION['csrf_tokens'][$oldestToken]);
        }
    }

    /**
     * Get allowed origins
     *
     * @return array
     */
    private function getAllowedOrigins(): array
    {
        $protocol = $this->isHttps() ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        
        return [
            $protocol . '://' . $host
        ];
    }

    /**
     * Check if connection is HTTPS
     *
     * @return bool
     */
    private function isHttps(): bool
    {
        return (
            (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
            $_SERVER['SERVER_PORT'] == 443 ||
            (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https')
        );
    }

    /**
     * Validate token format
     *
     * @param string $token
     * @return bool
     */
    private function isValidTokenFormat(string $token): bool
    {
        return preg_match('/^[a-f0-9]{' . ($this->config['token_length'] * 2) . '}$/', $token) === 1;
    }

    /**
     * Log validation failure
     *
     * @param string $reason
     * @param string $token
     * @param string|null $action
     * @param string $level
     * @return void
     */
    private function logValidationFailure(string $reason, string $token, ?string $action = null, string $level = 'error'): void
    {
        $logData = [
            'reason' => $reason,
            'token_id' => substr($token, 0, 8) . '...',
            'action' => $action,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ];

        if ($level === 'warning') {
            $this->logger->logWarning('CSRF validation warning: ' . $reason, $logData);
        } else {
            $this->logger->logError('CSRF validation failed: ' . $reason, $logData);
        }
    }

    /**
     * Get CSRF protection status
     *
     * @return array
     */
    public function getProtectionStatus(): array
    {
        $this->initializeSession();
        
        $tokens = $_SESSION['csrf_tokens'] ?? [];
        $validTokens = 0;
        $expiredTokens = 0;
        $now = time();
        
        foreach ($tokens as $tokenData) {
            if ($now <= $tokenData['expires_at']) {
                $validTokens++;
            } else {
                $expiredTokens++;
            }
        }
        
        return [
            'total_tokens' => count($tokens),
            'valid_tokens' => $validTokens,
            'expired_tokens' => $expiredTokens,
            'double_submit_enabled' => $this->config['double_submit_cookies'],
            'origin_validation_enabled' => $this->config['origin_validation'],
            'referer_validation_enabled' => $this->config['referer_validation'],
            'token_lifetime' => $this->config['token_lifetime'],
            'rotation_interval' => $this->config['rotation_interval']
        ];
    }

    /**
     * Update configuration
     *
     * @param array $newConfig
     * @return void
     */
    public function updateConfiguration(array $newConfig): void
    {
        $this->config = array_merge($this->config, $newConfig);
    }

    /**
     * Get current configuration
     *
     * @return array
     */
    public function getConfiguration(): array
    {
        return $this->config;
    }
}