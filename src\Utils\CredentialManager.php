<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Secure Credential Management Utility
 * 
 * Handles secure storage, retrieval, and validation of sensitive credentials
 * using environment variables and encrypted storage.
 */
class CredentialManager
{
    private static ?CredentialManager $instance = null;
    private LoggingService $logger;
    private array $credentials = [];
    private string $encryptionKey;
    private bool $loaded = false;
    
    /**
     * Private constructor for singleton pattern
     */
    private function __construct()
    {
        $this->logger = LoggingService::getInstance();
        $this->initializeEncryption();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance(): CredentialManager
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Initialize encryption for credential storage
     */
    private function initializeEncryption(): void
    {
        $this->encryptionKey = $_ENV['APP_ENCRYPTION_KEY'] ?? getenv('APP_ENCRYPTION_KEY') ?? '';
        
        if (empty($this->encryptionKey)) {
            throw new \RuntimeException('APP_ENCRYPTION_KEY environment variable is required for credential management');
        }
        
        if (strlen($this->encryptionKey) < 32) {
            throw new \RuntimeException('APP_ENCRYPTION_KEY must be at least 32 characters long');
        }
    }
    
    /**
     * Load credentials from environment variables
     */
    public function loadCredentials(): void
    {
        if ($this->loaded) {
            return;
        }
        
        try {
            // Load database credentials
            $this->credentials['database'] = [
                'host' => $this->getEnvVar('DB_HOST'),
                'port' => $this->getEnvVar('DB_PORT', '5432'),
                'database' => $this->getEnvVar('DB_NAME'),
                'username' => $this->getEnvVar('DB_USERNAME'),
                'password' => $this->getEnvVar('DB_PASSWORD'),
                'ssl_ca' => $this->getEnvVar('DB_SSL_CA'),
            ];
            
            // Load API credentials
            $this->credentials['apis'] = [
                'freemius' => [
                    'api_token' => $this->getEnvVar('FREEMIUS_API_TOKEN'),
                    'base_url' => $this->getEnvVar('FREEMIUS_API_BASE_URL', 'https://api.freemius.com/v1'),
                ],
                'ipregistry' => [
                    'api_key' => $this->getEnvVar('IPREGISTRY_API_KEY'),
                    'base_url' => $this->getEnvVar('IPREGISTRY_API_BASE_URL', 'https://api.ipregistry.co'),
                ]
            ];
            
            // Load security credentials
            $this->credentials['security'] = [
                'encryption_key' => $this->encryptionKey,
                'session_secret' => $this->getEnvVar('SESSION_SECRET', $this->generateSecureKey(32)),
                'csrf_secret' => $this->getEnvVar('CSRF_SECRET', $this->generateSecureKey(32)),
            ];
            
            // Load email credentials
            $this->credentials['email'] = [
                'host' => $this->getEnvVar('MAIL_HOST'),
                'port' => $this->getEnvVar('MAIL_PORT', '587'),
                'username' => $this->getEnvVar('MAIL_USERNAME'),
                'password' => $this->getEnvVar('MAIL_PASSWORD'),
                'encryption' => $this->getEnvVar('MAIL_ENCRYPTION', 'tls'),
            ];
            
            // Load backup credentials
            $this->credentials['backup'] = [
                'encryption_key' => $this->getEnvVar('BACKUP_ENCRYPTION_KEY'),
                's3' => [
                    'bucket' => $this->getEnvVar('BACKUP_S3_BUCKET'),
                    'region' => $this->getEnvVar('BACKUP_S3_REGION', 'us-east-1'),
                    'access_key' => $this->getEnvVar('BACKUP_S3_KEY'),
                    'secret_key' => $this->getEnvVar('BACKUP_S3_SECRET'),
                ]
            ];
            
            // Load SSL credentials
            $this->credentials['ssl'] = [
                'certificate_path' => $this->getEnvVar('SSL_CERTIFICATE_PATH'),
                'private_key_path' => $this->getEnvVar('SSL_PRIVATE_KEY_PATH'),
                'ca_bundle_path' => $this->getEnvVar('SSL_CA_BUNDLE_PATH'),
            ];
            
            // Validate loaded credentials
            $this->validateCredentials();
            
            $this->loaded = true;
            
            $this->logger->info('Credentials loaded successfully', [
                'credential_sections' => array_keys($this->credentials),
                'total_credentials' => $this->countCredentials()
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to load credentials', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
    
    /**
     * Get environment variable with fallback
     */
    private function getEnvVar(string $key, ?string $default = null): ?string
    {
        return $_ENV[$key] ?? getenv($key) ?: $default;
    }
    
    /**
     * Validate loaded credentials
     */
    private function validateCredentials(): void
    {
        $errors = [];
        
        // Validate database credentials
        if (empty($this->credentials['database']['host'])) {
            $errors[] = 'Database host is required';
        }
        if (empty($this->credentials['database']['database'])) {
            $errors[] = 'Database name is required';
        }
        if (empty($this->credentials['database']['username'])) {
            $errors[] = 'Database username is required';
        }
        if (empty($this->credentials['database']['password'])) {
            $errors[] = 'Database password is required';
        }
        
        // Validate API credentials
        if (empty($this->credentials['apis']['freemius']['api_token'])) {
            $errors[] = 'Freemius API token is required';
        }
        if (empty($this->credentials['apis']['ipregistry']['api_key'])) {
            $errors[] = 'ipRegistry API key is required';
        }
        
        // Validate credential formats
        $this->validateCredentialFormats($errors);
        
        if (!empty($errors)) {
            throw new \RuntimeException('Credential validation failed: ' . implode('; ', $errors));
        }
    }
    
    /**
     * Validate credential formats and strength
     */
    private function validateCredentialFormats(array &$errors): void
    {
        // Validate database password strength
        $dbPassword = $this->credentials['database']['password'];
        if (!empty($dbPassword) && strlen($dbPassword) < 8) {
            $errors[] = 'Database password is too weak (minimum 8 characters)';
        }
        
        // Validate API token formats
        $freemiusToken = $this->credentials['apis']['freemius']['api_token'];
        if (!empty($freemiusToken)) {
            if (strlen($freemiusToken) < 20) {
                $errors[] = 'Freemius API token appears to be too short';
            }
            if (in_array($freemiusToken, ['test', 'demo', 'example', 'changeme'])) {
                $errors[] = 'Freemius API token appears to be a placeholder value';
            }
        }
        
        $ipRegistryKey = $this->credentials['apis']['ipregistry']['api_key'];
        if (!empty($ipRegistryKey)) {
            if (strlen($ipRegistryKey) < 16) {
                $errors[] = 'ipRegistry API key appears to be too short';
            }
            if (in_array($ipRegistryKey, ['test', 'demo', 'example', 'changeme'])) {
                $errors[] = 'ipRegistry API key appears to be a placeholder value';
            }
        }
        
        // Validate encryption key
        if (strlen($this->encryptionKey) < 32) {
            $errors[] = 'Encryption key must be at least 32 characters long';
        }
    }
    
    /**
     * Get credential value
     */
    public function getCredential(string $path, ?string $default = null): ?string
    {
        if (!$this->loaded) {
            $this->loadCredentials();
        }
        
        $keys = explode('.', $path);
        $value = $this->credentials;
        
        foreach ($keys as $key) {
            if (!is_array($value) || !isset($value[$key])) {
                return $default;
            }
            $value = $value[$key];
        }
        
        return $value;
    }
    
    /**
     * Get database credentials
     */
    public function getDatabaseCredentials(): array
    {
        if (!$this->loaded) {
            $this->loadCredentials();
        }
        
        return $this->credentials['database'];
    }
    
    /**
     * Get API credentials
     */
    public function getAPICredentials(string $service): array
    {
        if (!$this->loaded) {
            $this->loadCredentials();
        }
        
        return $this->credentials['apis'][$service] ?? [];
    }
    
    /**
     * Get security credentials
     */
    public function getSecurityCredentials(): array
    {
        if (!$this->loaded) {
            $this->loadCredentials();
        }
        
        return $this->credentials['security'];
    }
    
    /**
     * Encrypt sensitive data
     */
    public function encrypt(string $data): string
    {
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $this->encryptionKey, 0, $iv);
        
        if ($encrypted === false) {
            throw new \RuntimeException('Failed to encrypt data');
        }
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Decrypt sensitive data
     */
    public function decrypt(string $encryptedData): string
    {
        $data = base64_decode($encryptedData);
        
        if ($data === false || strlen($data) < 16) {
            throw new \RuntimeException('Invalid encrypted data');
        }
        
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $this->encryptionKey, 0, $iv);
        
        if ($decrypted === false) {
            throw new \RuntimeException('Failed to decrypt data');
        }
        
        return $decrypted;
    }
    
    /**
     * Generate secure random key
     */
    public function generateSecureKey(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * Hash password securely
     */
    public function hashPassword(string $password): string
    {
        $options = [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3,         // 3 threads
        ];
        
        return password_hash($password, PASSWORD_ARGON2ID, $options);
    }
    
    /**
     * Verify password hash
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }
    
    /**
     * Mask credential for display
     */
    public function maskCredential(?string $credential): string
    {
        if (empty($credential)) {
            return 'Not configured';
        }
        
        if (strlen($credential) <= 8) {
            return str_repeat('*', strlen($credential));
        }
        
        return substr($credential, 0, 4) . str_repeat('*', strlen($credential) - 8) . substr($credential, -4);
    }
    
    /**
     * Get masked credentials for display
     */
    public function getMaskedCredentials(): array
    {
        if (!$this->loaded) {
            $this->loadCredentials();
        }
        
        return [
            'database' => [
                'host' => $this->credentials['database']['host'],
                'port' => $this->credentials['database']['port'],
                'database' => $this->credentials['database']['database'],
                'username' => $this->credentials['database']['username'],
                'password' => $this->maskCredential($this->credentials['database']['password']),
            ],
            'apis' => [
                'freemius' => [
                    'api_token' => $this->maskCredential($this->credentials['apis']['freemius']['api_token']),
                    'base_url' => $this->credentials['apis']['freemius']['base_url'],
                ],
                'ipregistry' => [
                    'api_key' => $this->maskCredential($this->credentials['apis']['ipregistry']['api_key']),
                    'base_url' => $this->credentials['apis']['ipregistry']['base_url'],
                ]
            ],
            'security' => [
                'encryption_key' => $this->maskCredential($this->credentials['security']['encryption_key']),
                'session_secret' => $this->maskCredential($this->credentials['security']['session_secret']),
                'csrf_secret' => $this->maskCredential($this->credentials['security']['csrf_secret']),
            ],
            'email' => [
                'host' => $this->credentials['email']['host'],
                'port' => $this->credentials['email']['port'],
                'username' => $this->credentials['email']['username'],
                'password' => $this->maskCredential($this->credentials['email']['password']),
                'encryption' => $this->credentials['email']['encryption'],
            ]
        ];
    }
    
    /**
     * Validate credential strength
     */
    public function validateCredentialStrength(string $credential, string $type): array
    {
        $result = [
            'valid' => true,
            'score' => 0,
            'issues' => []
        ];
        
        switch ($type) {
            case 'password':
                $result = $this->validatePasswordStrength($credential);
                break;
                
            case 'api_key':
                $result = $this->validateAPIKeyStrength($credential);
                break;
                
            case 'encryption_key':
                $result = $this->validateEncryptionKeyStrength($credential);
                break;
        }
        
        return $result;
    }
    
    /**
     * Validate password strength
     */
    private function validatePasswordStrength(string $password): array
    {
        $result = [
            'valid' => true,
            'score' => 0,
            'issues' => []
        ];
        
        // Length check
        if (strlen($password) < 8) {
            $result['valid'] = false;
            $result['issues'][] = 'Password must be at least 8 characters long';
        } else {
            $result['score'] += 20;
        }
        
        if (strlen($password) >= 12) {
            $result['score'] += 10;
        }
        
        // Character variety checks
        if (preg_match('/[a-z]/', $password)) {
            $result['score'] += 10;
        } else {
            $result['issues'][] = 'Password should contain lowercase letters';
        }
        
        if (preg_match('/[A-Z]/', $password)) {
            $result['score'] += 10;
        } else {
            $result['issues'][] = 'Password should contain uppercase letters';
        }
        
        if (preg_match('/[0-9]/', $password)) {
            $result['score'] += 10;
        } else {
            $result['issues'][] = 'Password should contain numbers';
        }
        
        if (preg_match('/[^a-zA-Z0-9]/', $password)) {
            $result['score'] += 15;
        } else {
            $result['issues'][] = 'Password should contain special characters';
        }
        
        // Common password check
        $commonPasswords = ['password', '123456', 'admin', 'changeme', 'default'];
        if (in_array(strtolower($password), $commonPasswords)) {
            $result['valid'] = false;
            $result['score'] = 0;
            $result['issues'][] = 'Password is too common';
        }
        
        return $result;
    }
    
    /**
     * Validate API key strength
     */
    private function validateAPIKeyStrength(string $apiKey): array
    {
        $result = [
            'valid' => true,
            'score' => 0,
            'issues' => []
        ];
        
        if (strlen($apiKey) < 16) {
            $result['valid'] = false;
            $result['issues'][] = 'API key is too short';
        } else {
            $result['score'] += 50;
        }
        
        if (strlen($apiKey) >= 32) {
            $result['score'] += 25;
        }
        
        // Check for placeholder values
        $placeholders = ['test', 'demo', 'example', 'changeme', 'your_key_here'];
        if (in_array(strtolower($apiKey), $placeholders)) {
            $result['valid'] = false;
            $result['score'] = 0;
            $result['issues'][] = 'API key appears to be a placeholder value';
        } else {
            $result['score'] += 25;
        }
        
        return $result;
    }
    
    /**
     * Validate encryption key strength
     */
    private function validateEncryptionKeyStrength(string $key): array
    {
        $result = [
            'valid' => true,
            'score' => 0,
            'issues' => []
        ];
        
        if (strlen($key) < 32) {
            $result['valid'] = false;
            $result['issues'][] = 'Encryption key must be at least 32 characters long';
        } else {
            $result['score'] += 50;
        }
        
        if (strlen($key) >= 64) {
            $result['score'] += 25;
        }
        
        // Check entropy
        $uniqueChars = count(array_unique(str_split($key)));
        if ($uniqueChars < 10) {
            $result['issues'][] = 'Encryption key has low entropy';
        } else {
            $result['score'] += 25;
        }
        
        return $result;
    }
    
    /**
     * Count total credentials
     */
    private function countCredentials(): int
    {
        $count = 0;
        
        foreach ($this->credentials as $section) {
            if (is_array($section)) {
                $count += $this->countArrayCredentials($section);
            }
        }
        
        return $count;
    }
    
    /**
     * Count credentials in array recursively
     */
    private function countArrayCredentials(array $array): int
    {
        $count = 0;
        
        foreach ($array as $value) {
            if (is_array($value)) {
                $count += $this->countArrayCredentials($value);
            } elseif (!empty($value)) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * Test credential connectivity
     */
    public function testCredentialConnectivity(): array
    {
        $results = [
            'database' => $this->testDatabaseConnectivity(),
            'freemius_api' => $this->testFreemiusAPIConnectivity(),
            'ipregistry_api' => $this->testIpRegistryAPIConnectivity(),
        ];
        
        return $results;
    }
    
    /**
     * Test database connectivity
     */
    private function testDatabaseConnectivity(): array
    {
        try {
            $dbCreds = $this->getDatabaseCredentials();
            $dsn = "pgsql:host={$dbCreds['host']};port={$dbCreds['port']};dbname={$dbCreds['database']}";
            
            $pdo = new \PDO($dsn, $dbCreds['username'], $dbCreds['password']);
            $pdo->query("SELECT 1");
            
            return ['status' => 'success', 'message' => 'Database connection successful'];
            
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Database connection failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Test Freemius API connectivity
     */
    private function testFreemiusAPIConnectivity(): array
    {
        try {
            $apiCreds = $this->getAPICredentials('freemius');
            
            if (empty($apiCreds['api_token'])) {
                return ['status' => 'error', 'message' => 'Freemius API token not configured'];
            }
            
            // Simple connectivity test (without making actual API call)
            return ['status' => 'success', 'message' => 'Freemius API credentials configured'];
            
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Freemius API test failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Test ipRegistry API connectivity
     */
    private function testIpRegistryAPIConnectivity(): array
    {
        try {
            $apiCreds = $this->getAPICredentials('ipregistry');
            
            if (empty($apiCreds['api_key'])) {
                return ['status' => 'error', 'message' => 'ipRegistry API key not configured'];
            }
            
            // Simple connectivity test (without making actual API call)
            return ['status' => 'success', 'message' => 'ipRegistry API credentials configured'];
            
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'ipRegistry API test failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get credential security summary
     */
    public function getSecuritySummary(): array
    {
        if (!$this->loaded) {
            $this->loadCredentials();
        }
        
        return [
            'total_credentials' => $this->countCredentials(),
            'encryption_enabled' => !empty($this->encryptionKey),
            'database_configured' => !empty($this->credentials['database']['password']),
            'apis_configured' => [
                'freemius' => !empty($this->credentials['apis']['freemius']['api_token']),
                'ipregistry' => !empty($this->credentials['apis']['ipregistry']['api_key']),
            ],
            'ssl_configured' => !empty($this->credentials['ssl']['certificate_path']),
            'backup_configured' => !empty($this->credentials['backup']['encryption_key']),
            'last_loaded' => date('Y-m-d H:i:s')
        ];
    }
}