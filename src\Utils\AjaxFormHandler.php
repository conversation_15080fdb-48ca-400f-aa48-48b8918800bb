<?php

namespace Skpassegna\GuardgeoApi\Utils;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * AJAX Form Handler
 * 
 * Utility for handling AJAX form submissions with proper validation,
 * CSRF protection, and standardized response formatting.
 */
class AjaxFormHandler
{
    private FormValidator $validator;
    private AuthMiddleware $authMiddleware;
    private LoggingService $logger;
    private array $errors = [];
    private array $data = [];

    public function __construct(
        FormValidator $validator,
        AuthMiddleware $authMiddleware,
        LoggingService $logger
    ) {
        $this->validator = $validator;
        $this->authMiddleware = $authMiddleware;
        $this->logger = $logger;
    }

    /**
     * Handle AJAX form submission
     *
     * @param array $validationRules Validation rules for the form
     * @param callable $processCallback Callback to process valid form data
     * @param array $options Additional options
     * @return void
     */
    public function handleSubmission(
        array $validationRules,
        callable $processCallback,
        array $options = []
    ): void {
        try {
            // Check if request is AJAX
            if (!$this->isAjaxRequest()) {
                $this->sendErrorResponse('Invalid request method', 400);
                return;
            }

            // Check authentication if required
            if ($options['requireAuth'] ?? true) {
                if (!$this->authMiddleware->isAuthenticated()) {
                    $this->sendErrorResponse('Authentication required', 401);
                    return;
                }
            }

            // Get form data
            $this->data = $this->getFormData();

            // Validate CSRF token if required
            if ($options['requireCsrf'] ?? true) {
                if (!$this->validateCsrfToken()) {
                    $this->sendErrorResponse('Invalid CSRF token', 403);
                    return;
                }
            }

            // Validate form data
            $validation = $this->validator->validate(
                $this->data,
                $validationRules,
                $options['customMessages'] ?? []
            );

            if (!$validation['valid']) {
                $this->sendValidationErrorResponse($validation);
                return;
            }

            // Process the form data
            $result = $processCallback($validation['validated_data'], $this->authMiddleware->getCurrentUser());

            // Send success response
            $this->sendSuccessResponse($result);

        } catch (\Exception $e) {
            $this->logger->logError('AJAX form handler error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'form_data' => $this->sanitizeLogData($this->data),
                'user' => $this->authMiddleware->getCurrentUser()['email'] ?? 'anonymous'
            ]);

            $this->sendErrorResponse('An error occurred while processing the form', 500);
        }
    }

    /**
     * Handle file upload with form data
     *
     * @param array $validationRules Validation rules
     * @param array $fileRules File validation rules
     * @param callable $processCallback Processing callback
     * @param array $options Additional options
     * @return void
     */
    public function handleFileUpload(
        array $validationRules,
        array $fileRules,
        callable $processCallback,
        array $options = []
    ): void {
        try {
            // Check if request is AJAX
            if (!$this->isAjaxRequest()) {
                $this->sendErrorResponse('Invalid request method', 400);
                return;
            }

            // Check authentication
            if (!$this->authMiddleware->isAuthenticated()) {
                $this->sendErrorResponse('Authentication required', 401);
                return;
            }

            // Get form data and files
            $this->data = $this->getFormData();
            $files = $_FILES;

            // Validate CSRF token
            if ($options['requireCsrf'] ?? true) {
                if (!$this->validateCsrfToken()) {
                    $this->sendErrorResponse('Invalid CSRF token', 403);
                    return;
                }
            }

            // Validate form data
            $validation = $this->validator->validate($this->data, $validationRules);
            if (!$validation['valid']) {
                $this->sendValidationErrorResponse($validation);
                return;
            }

            // Validate files
            $fileValidation = $this->validateFiles($files, $fileRules);
            if (!$fileValidation['valid']) {
                $this->sendValidationErrorResponse($fileValidation);
                return;
            }

            // Process the form data and files
            $result = $processCallback(
                $validation['validated_data'],
                $fileValidation['validated_files'],
                $this->authMiddleware->getCurrentUser()
            );

            $this->sendSuccessResponse($result);

        } catch (\Exception $e) {
            $this->logger->logError('AJAX file upload error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'form_data' => $this->sanitizeLogData($this->data),
                'user' => $this->authMiddleware->getCurrentUser()['email'] ?? 'anonymous'
            ]);

            $this->sendErrorResponse('An error occurred while processing the upload', 500);
        }
    }

    /**
     * Get form data from request
     *
     * @return array
     */
    private function getFormData(): array
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';

        if (strpos($contentType, 'application/json') !== false) {
            $json = file_get_contents('php://input');
            return json_decode($json, true) ?: [];
        }

        return $_POST;
    }

    /**
     * Check if request is AJAX
     *
     * @return bool
     */
    private function isAjaxRequest(): bool
    {
        return (
            !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest'
        ) || (
            !empty($_SERVER['HTTP_ACCEPT']) &&
            strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false
        );
    }

    /**
     * Validate CSRF token
     *
     * @return bool
     */
    private function validateCsrfToken(): bool
    {
        $token = $this->data['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? null;
        
        if (!$token) {
            return false;
        }

        return $this->authMiddleware->validateCsrfToken($token);
    }

    /**
     * Validate uploaded files
     *
     * @param array $files Uploaded files
     * @param array $rules File validation rules
     * @return array
     */
    private function validateFiles(array $files, array $rules): array
    {
        $errors = [];
        $validatedFiles = [];

        foreach ($rules as $fieldName => $fieldRules) {
            if (!isset($files[$fieldName])) {
                if (in_array('required', $fieldRules)) {
                    $errors[$fieldName][] = 'File is required';
                }
                continue;
            }

            $file = $files[$fieldName];
            
            // Check for upload errors
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $errors[$fieldName][] = $this->getUploadErrorMessage($file['error']);
                continue;
            }

            // Validate file rules
            foreach ($fieldRules as $rule) {
                if (is_string($rule)) {
                    $this->validateFileRule($fieldName, $file, $rule, $errors);
                }
            }

            if (!isset($errors[$fieldName])) {
                $validatedFiles[$fieldName] = $file;
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'validated_files' => $validatedFiles
        ];
    }

    /**
     * Validate individual file rule
     *
     * @param string $fieldName
     * @param array $file
     * @param string $rule
     * @param array &$errors
     * @return void
     */
    private function validateFileRule(string $fieldName, array $file, string $rule, array &$errors): void
    {
        $ruleParts = explode(':', $rule, 2);
        $ruleName = $ruleParts[0];
        $parameters = isset($ruleParts[1]) ? explode(',', $ruleParts[1]) : [];

        switch ($ruleName) {
            case 'max_size':
                $maxSize = (int)$parameters[0] * 1024; // Convert KB to bytes
                if ($file['size'] > $maxSize) {
                    $errors[$fieldName][] = "File size must not exceed {$parameters[0]}KB";
                }
                break;

            case 'mime_types':
                $allowedTypes = $parameters;
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mimeType = finfo_file($finfo, $file['tmp_name']);
                finfo_close($finfo);

                if (!in_array($mimeType, $allowedTypes)) {
                    $errors[$fieldName][] = 'File type not allowed';
                }
                break;

            case 'extensions':
                $allowedExtensions = $parameters;
                $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                
                if (!in_array($extension, $allowedExtensions)) {
                    $errors[$fieldName][] = 'File extension not allowed';
                }
                break;
        }
    }

    /**
     * Get upload error message
     *
     * @param int $errorCode
     * @return string
     */
    private function getUploadErrorMessage(int $errorCode): string
    {
        return match ($errorCode) {
            UPLOAD_ERR_INI_SIZE => 'File is too large (exceeds server limit)',
            UPLOAD_ERR_FORM_SIZE => 'File is too large (exceeds form limit)',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension',
            default => 'Unknown upload error'
        };
    }

    /**
     * Send success response
     *
     * @param mixed $data
     * @return void
     */
    private function sendSuccessResponse($data = null): void
    {
        $response = [
            'success' => true,
            'message' => 'Operation completed successfully'
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        $this->sendJsonResponse($response);
    }

    /**
     * Send error response
     *
     * @param string $message
     * @param int $statusCode
     * @return void
     */
    private function sendErrorResponse(string $message, int $statusCode = 400): void
    {
        $this->sendJsonResponse([
            'success' => false,
            'error' => $message
        ], $statusCode);
    }

    /**
     * Send validation error response
     *
     * @param array $validation
     * @return void
     */
    private function sendValidationErrorResponse(array $validation): void
    {
        $this->sendJsonResponse([
            'success' => false,
            'error' => 'Validation failed',
            'errors' => $validation['errors'],
            'warnings' => $validation['warnings'] ?? []
        ], 422);
    }

    /**
     * Send JSON response
     *
     * @param array $data
     * @param int $statusCode
     * @return void
     */
    private function sendJsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Sanitize data for logging (remove sensitive information)
     *
     * @param array $data
     * @return array
     */
    private function sanitizeLogData(array $data): array
    {
        $sensitiveFields = ['password', 'password_confirmation', 'csrf_token', 'api_key', 'secret'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }
}