<?php

namespace Skpassegna\GuardgeoApi\Services;

/**
 * Error Logger
 * 
 * Specialized logger for system errors, exceptions, and critical failures.
 * Provides detailed error tracking with stack traces and context information.
 */
class ErrorLogger implements LoggerInterface
{
    private LoggingService $loggingService;
    
    public function __construct(LoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }
    
    /**
     * Log debug message
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_DEBUG, $message, $context);
    }
    
    /**
     * Log info message
     */
    public function info(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_INFO, $message, $context);
    }
    
    /**
     * Log warning message
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_WARNING, $message, $context);
    }
    
    /**
     * Log error message
     */
    public function error(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_ERROR, $message, $context);
    }
    
    /**
     * Log critical message
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log(LoggingService::LEVEL_CRITICAL, $message, $context);
    }
    
    /**
     * Log exception with full details
     */
    public function logException(\Throwable $exception, array $additionalContext = []): void
    {
        $message = sprintf(
            'Exception: %s in %s:%d',
            $exception->getMessage(),
            basename($exception->getFile()),
            $exception->getLine()
        );
        
        $context = array_merge($additionalContext, [
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'code' => $exception->getCode(),
            'trace' => $this->formatStackTrace($exception->getTrace()),
            'previous' => $exception->getPrevious() ? [
                'class' => get_class($exception->getPrevious()),
                'message' => $exception->getPrevious()->getMessage(),
                'file' => $exception->getPrevious()->getFile(),
                'line' => $exception->getPrevious()->getLine()
            ] : null
        ]);
        
        $this->error($message, $context);
    }
    
    /**
     * Log database error
     */
    public function logDatabaseError(string $operation, string $sql, array $params, \Throwable $exception): void
    {
        $message = sprintf(
            'Database error during %s: %s',
            $operation,
            $exception->getMessage()
        );
        
        $context = [
            'operation' => $operation,
            'sql' => $sql,
            'param_count' => count($params),
            'error_code' => $exception->getCode(),
            'error_message' => $exception->getMessage(),
            'exception_class' => get_class($exception)
        ];
        
        $this->error($message, $context);
    }
    
    /**
     * Log API error
     */
    public function logApiError(string $endpoint, array $requestData, \Throwable $exception): void
    {
        $message = sprintf(
            'API error on %s: %s',
            $endpoint,
            $exception->getMessage()
        );
        
        $context = [
            'endpoint' => $endpoint,
            'request_data' => $this->sanitizeRequestData($requestData),
            'exception_class' => get_class($exception),
            'error_message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine()
        ];
        
        $this->error($message, $context);
    }
    
    /**
     * Log external service error
     */
    public function logExternalServiceError(string $service, string $operation, int $httpCode, string $response, \Throwable $exception = null): void
    {
        $message = sprintf(
            'External service error: %s %s returned %d',
            $service,
            $operation,
            $httpCode
        );
        
        $context = [
            'service' => $service,
            'operation' => $operation,
            'http_code' => $httpCode,
            'response' => $this->truncateResponse($response),
            'response_length' => strlen($response)
        ];
        
        if ($exception) {
            $context['exception'] = [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine()
            ];
        }
        
        $this->error($message, $context);
    }
    
    /**
     * Log validation error
     */
    public function logValidationError(string $field, string $value, string $rule, string $message): void
    {
        $logMessage = sprintf(
            'Validation error: %s failed %s rule',
            $field,
            $rule
        );
        
        $context = [
            'field' => $field,
            'value' => $this->sanitizeValue($value),
            'rule' => $rule,
            'validation_message' => $message
        ];
        
        $this->warning($logMessage, $context);
    }
    
    /**
     * Log authentication error
     */
    public function logAuthenticationError(string $email, string $reason, array $details = []): void
    {
        $message = sprintf(
            'Authentication error for %s: %s',
            $email,
            $reason
        );
        
        $context = array_merge($details, [
            'email' => $email,
            'reason' => $reason,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
        $this->warning($message, $context);
    }
    
    /**
     * Log authorization error
     */
    public function logAuthorizationError(int $userId, string $action, string $resource, string $reason): void
    {
        $message = sprintf(
            'Authorization error: User %d denied %s on %s - %s',
            $userId,
            $action,
            $resource,
            $reason
        );
        
        $context = [
            'user_id' => $userId,
            'action' => $action,
            'resource' => $resource,
            'reason' => $reason,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null
        ];
        
        $this->warning($message, $context);
    }
    
    /**
     * Log configuration error
     */
    public function logConfigurationError(string $setting, string $issue, array $details = []): void
    {
        $message = sprintf(
            'Configuration error: %s - %s',
            $setting,
            $issue
        );
        
        $context = array_merge($details, [
            'setting' => $setting,
            'issue' => $issue
        ]);
        
        $this->error($message, $context);
    }
    
    /**
     * Log file system error
     */
    public function logFileSystemError(string $operation, string $path, \Throwable $exception): void
    {
        $message = sprintf(
            'File system error during %s on %s: %s',
            $operation,
            $path,
            $exception->getMessage()
        );
        
        $context = [
            'operation' => $operation,
            'path' => $path,
            'error_message' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'exception_class' => get_class($exception)
        ];
        
        $this->error($message, $context);
    }
    
    /**
     * Log memory or resource error
     */
    public function logResourceError(string $resource, string $issue, array $metrics = []): void
    {
        $message = sprintf(
            'Resource error: %s - %s',
            $resource,
            $issue
        );
        
        $context = array_merge($metrics, [
            'resource' => $resource,
            'issue' => $issue,
            'current_memory' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'memory_limit' => ini_get('memory_limit')
        ]);
        
        $this->critical($message, $context);
    }
    
    /**
     * Log security incident
     */
    public function logSecurityIncident(string $incident, string $severity, array $details = []): void
    {
        $message = sprintf(
            'Security incident: %s (severity: %s)',
            $incident,
            $severity
        );
        
        $context = array_merge($details, [
            'incident' => $incident,
            'severity' => $severity,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'timestamp' => date('c')
        ]);
        
        $level = match($severity) {
            'critical' => LoggingService::LEVEL_CRITICAL,
            'high' => LoggingService::LEVEL_ERROR,
            'medium' => LoggingService::LEVEL_WARNING,
            default => LoggingService::LEVEL_INFO
        };
        
        $this->log($level, $message, $context);
    }
    
    /**
     * Format stack trace for logging
     */
    private function formatStackTrace(array $trace): array
    {
        $formatted = [];
        
        foreach (array_slice($trace, 0, 10) as $index => $frame) {
            $formatted[] = [
                'index' => $index,
                'file' => $frame['file'] ?? 'unknown',
                'line' => $frame['line'] ?? 0,
                'function' => $frame['function'] ?? 'unknown',
                'class' => $frame['class'] ?? null,
                'type' => $frame['type'] ?? null
            ];
        }
        
        return $formatted;
    }
    
    /**
     * Sanitize request data for logging
     */
    private function sanitizeRequestData(array $data): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (in_array(strtolower($key), ['password', 'token', 'key', 'secret'])) {
                $sanitized[$key] = '[REDACTED]';
            } else {
                $sanitized[$key] = is_string($value) && strlen($value) > 100 
                    ? substr($value, 0, 97) . '...' 
                    : $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize sensitive values
     */
    private function sanitizeValue(string $value): string
    {
        if (strlen($value) > 100) {
            return substr($value, 0, 97) . '...';
        }
        
        return $value;
    }
    
    /**
     * Truncate long responses
     */
    private function truncateResponse(string $response): string
    {
        if (strlen($response) > 1000) {
            return substr($response, 0, 997) . '...';
        }
        
        return $response;
    }
    
    /**
     * Internal log method
     */
    private function log(string $level, string $message, array $context): void
    {
        $this->loggingService->log(LoggingService::TYPE_ERROR, $level, $message, $context);
    }
}