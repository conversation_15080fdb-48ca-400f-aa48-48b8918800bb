<?php

namespace Skpassegna\GuardgeoApi\Services;

use Skpassegna\GuardgeoApi\Database\DatabaseConnection;
use DateTime;

/**
 * Dashboard Service
 * 
 * Provides data and metrics for the admin dashboard interface
 * with role-based data filtering and real-time statistics.
 */
class DashboardService
{
    private DatabaseConnection $db;
    private LoggingService $logger;

    public function __construct(DatabaseConnection $db, LoggingService $logger)
    {
        $this->db = $db;
        $this->logger = $logger;
    }

    /**
     * Get system overview metrics
     *
     * @return array
     */
    public function getSystemOverview(): array
    {
        try {
            $pdo = $this->db->getConnection();
            
            // Get basic counts
            $apiRequestsToday = $this->getApiRequestsCount('today');
            $apiRequestsWeek = $this->getApiRequestsCount('week');
            $totalIpRecords = $this->getTotalIpRecords();
            $totalFreemiusInstallations = $this->getTotalFreemiusInstallations();
            $systemStatus = $this->getSystemStatus();

            return [
                'system_status' => $systemStatus,
                'api_requests' => [
                    'today' => $apiRequestsToday,
                    'week' => $apiRequestsWeek,
                    'status' => $apiRequestsToday > 0 ? 'active' : 'idle'
                ],
                'ip_intelligence' => [
                    'total_records' => $totalIpRecords,
                    'cache_status' => $this->getCacheStatus()
                ],
                'freemius' => [
                    'total_installations' => $totalFreemiusInstallations,
                    'sync_status' => 'operational'
                ],
                'last_updated' => (new DateTime())->format('Y-m-d H:i:s')
            ];

        } catch (\Exception $e) {
            $this->logger->logError('Dashboard overview error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'system_status' => 'error',
                'error' => 'Unable to load system metrics',
                'last_updated' => (new DateTime())->format('Y-m-d H:i:s')
            ];
        }
    }

    /**
     * Get recent activity for dashboard
     *
     * @param int $limit
     * @return array
     */
    public function getRecentActivity(int $limit = 10): array
    {
        try {
            $pdo = $this->db->getConnection();
            
            $stmt = $pdo->prepare("
                SELECT 
                    type,
                    level,
                    message,
                    context,
                    ip,
                    created_at
                FROM system_logs 
                WHERE type IN ('api', 'admin')
                ORDER BY created_at DESC 
                LIMIT :limit
            ");
            
            $stmt->bindValue(':limit', $limit, \PDO::PARAM_INT);
            $stmt->execute();
            
            $activities = [];
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                $activities[] = [
                    'type' => $row['type'],
                    'level' => $row['level'],
                    'message' => $row['message'],
                    'context' => $row['context'] ? json_decode($row['context'], true) : null,
                    'ip' => $row['ip'],
                    'timestamp' => $row['created_at'],
                    'formatted_time' => $this->formatRelativeTime($row['created_at'])
                ];
            }

            return $activities;

        } catch (\Exception $e) {
            $this->logger->logError('Recent activity error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [];
        }
    }

    /**
     * Get API usage statistics
     *
     * @param string $period
     * @return array
     */
    public function getApiUsageStats(string $period = 'week'): array
    {
        try {
            $pdo = $this->db->getConnection();
            
            $dateCondition = match ($period) {
                'today' => "DATE(created_at) = CURRENT_DATE",
                'week' => "created_at >= NOW() - INTERVAL '7 days'",
                'month' => "created_at >= NOW() - INTERVAL '30 days'",
                default => "created_at >= NOW() - INTERVAL '7 days'"
            };

            // Get request counts by status
            $stmt = $pdo->prepare("
                SELECT 
                    response_status,
                    COUNT(*) as count,
                    AVG(response_time_ms) as avg_response_time
                FROM api_requests 
                WHERE {$dateCondition}
                GROUP BY response_status
                ORDER BY response_status
            ");
            
            $stmt->execute();
            
            $stats = [
                'total_requests' => 0,
                'success_requests' => 0,
                'error_requests' => 0,
                'avg_response_time' => 0,
                'status_breakdown' => []
            ];

            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                $status = (int)$row['response_status'];
                $count = (int)$row['count'];
                
                $stats['total_requests'] += $count;
                $stats['status_breakdown'][$status] = [
                    'count' => $count,
                    'avg_response_time' => round((float)$row['avg_response_time'], 2)
                ];

                if ($status >= 200 && $status < 300) {
                    $stats['success_requests'] += $count;
                } else {
                    $stats['error_requests'] += $count;
                }
            }

            // Calculate overall average response time
            if ($stats['total_requests'] > 0) {
                $stmt = $pdo->prepare("
                    SELECT AVG(response_time_ms) as avg_response_time
                    FROM api_requests 
                    WHERE {$dateCondition}
                ");
                $stmt->execute();
                $result = $stmt->fetch(\PDO::FETCH_ASSOC);
                $stats['avg_response_time'] = round((float)$result['avg_response_time'], 2);
            }

            return $stats;

        } catch (\Exception $e) {
            $this->logger->logError('API usage stats error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'total_requests' => 0,
                'success_requests' => 0,
                'error_requests' => 0,
                'avg_response_time' => 0,
                'status_breakdown' => [],
                'error' => 'Unable to load API statistics'
            ];
        }
    }

    /**
     * Get IP intelligence statistics
     *
     * @return array
     */
    public function getIpIntelligenceStats(): array
    {
        try {
            $pdo = $this->db->getConnection();
            
            // Get total IP records
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM ip_intelligence");
            $stmt->execute();
            $total = $stmt->fetch(\PDO::FETCH_ASSOC)['total'];

            // Get fresh vs stale records
            $stmt = $pdo->prepare("
                SELECT 
                    COUNT(*) as fresh_count
                FROM ip_intelligence 
                WHERE security_expires_at > NOW()
            ");
            $stmt->execute();
            $fresh = $stmt->fetch(\PDO::FETCH_ASSOC)['fresh_count'];

            $stale = $total - $fresh;

            // Get recent additions
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as recent_count
                FROM ip_intelligence 
                WHERE cached_at >= NOW() - INTERVAL '24 hours'
            ");
            $stmt->execute();
            $recent = $stmt->fetch(\PDO::FETCH_ASSOC)['recent_count'];

            return [
                'total_records' => (int)$total,
                'fresh_records' => (int)$fresh,
                'stale_records' => (int)$stale,
                'recent_additions' => (int)$recent,
                'cache_efficiency' => $total > 0 ? round(($fresh / $total) * 100, 1) : 0
            ];

        } catch (\Exception $e) {
            $this->logger->logError('IP intelligence stats error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'total_records' => 0,
                'fresh_records' => 0,
                'stale_records' => 0,
                'recent_additions' => 0,
                'cache_efficiency' => 0,
                'error' => 'Unable to load IP intelligence statistics'
            ];
        }
    }

    /**
     * Get API requests count for a period
     *
     * @param string $period
     * @return int
     */
    private function getApiRequestsCount(string $period): int
    {
        try {
            $pdo = $this->db->getConnection();
            
            $dateCondition = match ($period) {
                'today' => "DATE(created_at) = CURRENT_DATE",
                'week' => "created_at >= NOW() - INTERVAL '7 days'",
                'month' => "created_at >= NOW() - INTERVAL '30 days'",
                default => "created_at >= NOW() - INTERVAL '7 days'"
            };

            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM api_requests WHERE {$dateCondition}");
            $stmt->execute();
            
            return (int)$stmt->fetch(\PDO::FETCH_ASSOC)['count'];

        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get total IP records count
     *
     * @return int
     */
    private function getTotalIpRecords(): int
    {
        try {
            $pdo = $this->db->getConnection();
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM ip_intelligence");
            $stmt->execute();
            
            return (int)$stmt->fetch(\PDO::FETCH_ASSOC)['count'];

        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get total Freemius installations count
     *
     * @return int
     */
    private function getTotalFreemiusInstallations(): int
    {
        try {
            $pdo = $this->db->getConnection();
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM freemius_installations");
            $stmt->execute();
            
            return (int)$stmt->fetch(\PDO::FETCH_ASSOC)['count'];

        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get system status
     *
     * @return string
     */
    private function getSystemStatus(): string
    {
        try {
            // Check database connection
            $pdo = $this->db->getConnection();
            $stmt = $pdo->prepare("SELECT 1");
            $stmt->execute();
            
            return 'operational';

        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * Get cache status
     *
     * @return string
     */
    private function getCacheStatus(): string
    {
        try {
            $pdo = $this->db->getConnection();
            
            // Check if we have recent cache activity
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM ip_intelligence 
                WHERE cached_at >= NOW() - INTERVAL '1 hour'
            ");
            $stmt->execute();
            
            $recentActivity = (int)$stmt->fetch(\PDO::FETCH_ASSOC)['count'];
            
            return $recentActivity > 0 ? 'active' : 'idle';

        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * Format relative time
     *
     * @param string $timestamp
     * @return string
     */
    private function formatRelativeTime(string $timestamp): string
    {
        $time = new DateTime($timestamp);
        $now = new DateTime();
        $diff = $now->diff($time);

        if ($diff->days > 0) {
            return $diff->days . ' day' . ($diff->days > 1 ? 's' : '') . ' ago';
        } elseif ($diff->h > 0) {
            return $diff->h . ' hour' . ($diff->h > 1 ? 's' : '') . ' ago';
        } elseif ($diff->i > 0) {
            return $diff->i . ' minute' . ($diff->i > 1 ? 's' : '') . ' ago';
        } else {
            return 'Just now';
        }
    }
}