# **GuardGeo: Comprehensive User Flows**

This document outlines the various interaction paths and scenarios for the two primary user types of the GuardGeo system: the **Website Owner** (the administrator) and the **Website Visitor** (the end-user).

---

## **Part 1: The Website Owner User Flows**

These flows describe how a website owner installs, configures, and manages the GuardGeo plugin.

### **Website Owner Flow 1: First-Time Setup and Basic Configuration**

**User Goal:** To install GuardGeo and activate a basic level of protection quickly.

**Text Description:**
1.  The owner navigates to the WordPress Plugin repository, searches for "GuardGeo," and clicks "Install Now."
2.  After installation, they click "Activate."
3.  Upon activation, they are prompted to subscribe from Freemius to start their plan (either free or paid). They complete the Freemius onboarding (Freemius is embed in the Plugin, they are not redirected outside their website).
4.  They are redirected to the GuardGeo dashboard within WordPress.
5.  They navigate to the "Geo-Blocking" settings.
6.  They select two countries known for causing them issues from a dropdown list.
7.  They click "Save Settings." A confirmation message appears, and the rules are now active.

**Graph:**
```mermaid
---
config:
  layout: fixed
---
flowchart TD
 subgraph subGraph0["In WordPress Dashboard"]
        B["Activate Plugin"]
        A["Search & Install GuardGeo"]
        C{"Connect to Freemius<br> (Create Account/Login)"}
        D["Navigate to GuardGeo Dashboard"]
        E["Go to Geo-Blocking Settings"]
        F@{ label: "Select 'Country A' & 'Country B'" }
        G["Save Settings"]
  end
 subgraph subGraph1["GuardGeo Backend"]
        H{"Receive &amp; Store<br>New Geo-Blocking Rule"}
  end
 subgraph subGraph2["Confirmation"]
        I@{ label: "Display 'Settings Saved!'<br>in WP Dashboard" }
  end
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G -- Sends Config --> H
    H -- Sends Success --> I
    F@{ shape: rect}
    I@{ shape: rect}

```

### **Website Owner Flow 2: Advanced Anti-Fraud Configuration**

**User Goal:** To protect an e-commerce store by configuring the proactive Anti-Fraud engine.

**Text Description:**
1.  The owner, who has a premium GuardGeo plan, navigates to the GuardGeo dashboard.
2.  They go to the "E-commerce Protection" or "Anti-Fraud" tab.
3.  They enable the main "Anti-Fraud Engine" toggle.
4.  A set of conditions appears. They check the boxes for: "Block payments if visitor is using a VPN/Proxy" and "Block payments if visitor's IP risk score is High."
5.  They choose the action: "Remove refundable payment methods (Credit Card, PayPal)."
6.  They click "Save Settings." The advanced protection is now active on their checkout page.

**Graph:**
```mermaid
---
config:
  layout: fixed
---
flowchart TD
 subgraph subGraph0["In WordPress Dashboard"]
        B@{ label: "Select 'Anti-Fraud' Tab" }
        A["Navigate to GuardGeo Dashboard"]
        C["Enable Anti-Fraud Engine"]
        D@{ label: "Configure Conditions<br>- Check 'VPN/Proxy'<br>- Check 'High Risk Score'" }
        E@{ label: "Select Action<br>'Remove Refundable Payments'" }
        F["Save Settings"]
  end
 subgraph subGraph1["GuardGeo Backend"]
        G{"Store Anti-Fraud Logic"}
        H["Confirmation Sent"]
  end
 subgraph subGraph2["Confirmation"]
        I@{ label: "Display 'Anti-Fraud Rules Updated!'" }
  end
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F -- Sends Config --> G
    G --> H
    H --> I
    B@{ shape: rect}
    D@{ shape: diamond}
    E@{ shape: diamond}
    I@{ shape: rect}

```

### **Website Owner Flow 3: Monitoring and Responding to a Blocked User**

**User Goal:** To investigate why a legitimate customer was blocked and grant them access.

**Text Description:**
1.  The owner receives an email from a customer who says they cannot access the website.
2.  The owner logs into their WordPress dashboard and navigates to the GuardGeo "Activity Log."
3.  They filter the log by the customer's IP address (which the customer provided).
4.  They find the log entry. It shows the customer was blocked because their IP was flagged as "High Risk."
5.  The owner determines this is a "false positive." They click the "Whitelist IP" button next to the log entry.
6.  A confirmation appears. The customer's IP is now on the whitelist and will bypass all future GuardGeo checks.
7.  The owner emails the customer back to inform them they can now access the site.

**Graph:**
```mermaid
---
config:
  layout: fixed
---
flowchart TD
    A["Receive Complaint from<br>Blocked Customer"] --> B["Login to WordPress"]
    B --> C["Navigate to GuardGeo<br>Activity Log"]
    C --> D@{ label: "Filter Log by<br>Customer's IP Address" }
    D --> E@{ label: "Find Block Event<br>Reason: 'High Risk'" }
    E --> F{"Decision: False Positive?"}
    F -- Yes --> G@{ label: "Click 'Whitelist IP'" }
    G --> H["IP added to Whitelist<br>in GuardGeo Backend"]
    H --> I["Notify Customer<br>They Can Access Site"]
    F -- No --> J["Decide to Keep Block<br>(Inform Customer)"]
    D@{ shape: diamond}
    E@{ shape: rect}
    G@{ shape: rect}

```

---

## **Part 2: The Website Visitor User Flows**

These flows describe the experience of an end-user browsing a site protected by GuardGeo.

### **Website Visitor Flow 1: The Ideal Path (Legitimate Visitor)**

**User Goal:** To browse the website and make a purchase without interruption.

**Text Description:**
1.  A legitimate visitor from an allowed country, with a clean IP and not using a VPN, types the website URL into their browser.
2.  The request is sent to the website.
3.  GuardGeo's backend instantly analyzes the request and finds no risks.
4.  GuardGeo returns an "Allow" decision.
5.  The website loads normally for the visitor. They browse products, add one to their cart, and proceed to checkout.
6.  At checkout, GuardGeo re-confirms the visitor is low-risk.
7.  The full, unmodified checkout page is displayed with all payment options. The visitor completes their purchase. GuardGeo was completely invisible to them.

**Graph:**
```mermaid
---
config:
  layout: fixed
---
flowchart TD
    A["Visitor Requests Website"] --> B{"GuardGeo Analysis<br>Backend Check"}
    B --> C{"Risk Assessment<br>- Country OK?<br>- IP Clean?<br>- No VPN?"}
    C -- All Checks Pass --> D["Decision: Allow"]
    D --> E["Website Loads Normally"]
    E --> F["Visitor Proceeds to Checkout"]
    F --> G{"GuardGeo Re-confirms<br>Visitor is Low-Risk"}
    G --> H["Full Checkout Page is Displayed"]
    H --> I["Visitor Completes Purchase"]
```

### **Website Visitor Flow 2: The Hard Block Path (Malicious Visitor)**

**User Goal:** To scan the website for vulnerabilities (but is blocked).

**Text Description:**
1.  An automated bot from a known malicious IP address attempts to connect to the website.
2.  The request is sent to the website.
3.  GuardGeo's backend analyzes the request. The IP Reputation check immediately flags the IP as a known threat.
4.  GuardGeo returns a "Block" decision.
5.  Instead of the website loading, the visitor is shown a simple "Access Denied" page (or the connection is dropped). The flow ends here.

**Graph:**
```mermaid
---
config:
  layout: fixed
---
flowchart TD
    A["Malicious Bot<br>Requests Website"] --> B{"GuardGeo Analysis<br>Backend Check"}
    B --> C{"Risk Assessment<br>IP Reputation Check"}
    C -- IP Flagged as Malicious --> D["Decision: Block"]
    D --> E["'Access Denied' Page is Shown"]
    E --> F(["End of Session"])
```

### **Website Visitor Flow 3: The "Soft Block" Path (High-Risk E-commerce Visitor)**

**User Goal:** To make a purchase while using a VPN for privacy.

**Text Description:**
1.  A visitor from an allowed country is using a commercial VPN. They browse the site and proceed to checkout.
2.  At the checkout page, GuardGeo analyzes the visitor. It sees their country is okay, but the Anti-Anonymizer check flags them for using a VPN.
3.  The site owner has configured the Anti-Fraud engine to act on VPN usage.
4.  GuardGeo's decision is not to block the visitor entirely, but to "Modify" the page.
5.  The checkout page loads, but the Credit Card and PayPal payment options are missing. Only the "Direct Bank Transfer" option is visible.
6.  The visitor can either proceed with the safer payment method or abandon the cart.

**Graph:**
```mermaid
---
config:
  layout: fixed
---
flowchart TD
    A["Visitor Using VPN<br>Proceeds to Checkout"] --> B{"GuardGeo Analysis<br>Backend Check"}
    B --> C{"Risk Assessment<br>- Country OK?<br>- Anonymizer Flag: YES"}
    C --> D{"Anti-Fraud Engine Decision<br>(Based on Owner's Rules)"}
    D --> E["Decision: Modify Page"]
    E --> F["Checkout Page Loads<br>with *Only* Bank Transfer Option"]
    F --> G{"Visitor Decides<br>to Pay or Leave"}
```

### **Website Visitor Flow 4: The Block and Appeal Path**

**User Goal:** To access a website but is blocked, and believes the block is a mistake.

**Text Description:**
1.  A visitor is on a corporate network whose IP address has been mistakenly flagged as high-risk by an intelligence database.
2.  They try to access the website. GuardGeo analyzes the IP and blocks them.
3.  The "Access Denied" page is shown, but it contains a message: "If you believe this is an error, please click here to appeal." It also shows them their unique ID.
4.  The visitor clicks the link and is taken to a simple appeal form.
5.  They enter their email and a brief message explaining their situation.
6.  The appeal is submitted to the GuardGeo system, which notifies the website owner.
7.  The visitor must now wait for the website owner to review the appeal (as seen in Owner Flow 3).

**Graph:**
```mermaid
---
config:
  layout: fixed
---
flowchart TD
    A["Visitor Requests Website"] --> B{"GuardGeo Analysis<br>IP Flagged as High-Risk"}
    B --> C["Decision: Block"]
    C --> D["'Access Denied' Page is Shown<br>with Appeal Link & ID"]
    D --> E{"Visitor Clicks<br>'Appeal this Block'"}
    E --> F["Visitor Fills Out<br>Appeal Form"]
    F --> G["Submit Appeal"]
    G --> H["System Notifies<br>Website Owner"]
    H --> I(["Visitor Waits for Response"])
```