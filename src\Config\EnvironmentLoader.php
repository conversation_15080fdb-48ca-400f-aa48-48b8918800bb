<?php

namespace Skpassegna\GuardgeoApi\Config;

use Skpassegna\GuardgeoApi\Utils\Logger;

/**
 * Environment Configuration Loader
 * 
 * Handles loading and validation of environment-specific configuration
 * for development, staging, and production environments.
 */
class EnvironmentLoader
{
    private static Logger $logger;
    private static array $loadedConfigs = [];
    private static bool $initialized = false;
    
    /**
     * Initialize the environment loader
     */
    public static function initialize(): void
    {
        if (self::$initialized) {
            return;
        }
        
        self::$logger = new Logger();
        self::$initialized = true;
    }
    
    /**
     * Load configuration for specific environment
     */
    public static function loadEnvironment(string $environment): array
    {
        self::initialize();
        
        if (isset(self::$loadedConfigs[$environment])) {
            return self::$loadedConfigs[$environment];
        }
        
        $config = [];
        
        try {
            // Load base environment configuration
            $config = self::loadBaseConfig();
            
            // Load environment-specific overrides
            $envConfig = self::loadEnvironmentConfig($environment);
            $config = array_merge_recursive($config, $envConfig);
            
            // Load local overrides if they exist
            $localConfig = self::loadLocalConfig($environment);
            if (!empty($localConfig)) {
                $config = array_merge_recursive($config, $localConfig);
            }
            
            // Validate required configuration
            self::validateConfiguration($config, $environment);
            
            // Cache the loaded configuration
            self::$loadedConfigs[$environment] = $config;
            
            self::$logger->info("Environment configuration loaded", [
                'environment' => $environment,
                'config_sections' => array_keys($config)
            ]);
            
            return $config;
            
        } catch (\Exception $e) {
            self::$logger->error("Failed to load environment configuration", [
                'environment' => $environment,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Load base configuration common to all environments
     */
    private static function loadBaseConfig(): array
    {
        return [
            'app' => [
                'name' => 'GuardGeo Admin Platform',
                'version' => '1.0.0',
                'timezone' => 'UTC',
            ],
            
            'database' => [
                'driver' => 'pgsql',
                'charset' => 'utf8',
                'options' => [
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                    \PDO::ATTR_EMULATE_PREPARES => false,
                ]
            ],
            
            'logging' => [
                'channels' => ['file', 'database'],
                'max_file_size' => 10485760, // 10MB
                'max_files' => 5,
                'date_format' => 'Y-m-d H:i:s',
            ],
            
            'security' => [
                'hash_algorithm' => 'sha256',
                'session_httponly' => true,
                'csrf_protection' => true,
            ],
            
            'api' => [
                'version' => 'v1',
                'max_request_size' => 1048576, // 1MB
            ],
            
            'cache' => [
                'driver' => 'database',
                'ip_cache' => [
                    'location_days' => 10,
                    'security_days' => 3,
                    'connection_days' => 7,
                    'company_days' => 30,
                ]
            ]
        ];
    }
    
    /**
     * Load environment-specific configuration
     */
    private static function loadEnvironmentConfig(string $environment): array
    {
        $configFile = __DIR__ . "/../../config/{$environment}.php";
        
        if (!file_exists($configFile)) {
            self::$logger->warning("Environment config file not found", [
                'environment' => $environment,
                'file' => $configFile
            ]);
            return [];
        }
        
        $config = require $configFile;
        
        if (!is_array($config)) {
            throw new \RuntimeException("Environment config file must return an array: {$configFile}");
        }
        
        return $config;
    }
    
    /**
     * Load local configuration overrides
     */
    private static function loadLocalConfig(string $environment): array
    {
        $localConfigFile = __DIR__ . "/../../config/{$environment}.local.php";
        
        if (!file_exists($localConfigFile)) {
            return [];
        }
        
        $config = require $localConfigFile;
        
        if (!is_array($config)) {
            self::$logger->warning("Local config file must return an array", [
                'file' => $localConfigFile
            ]);
            return [];
        }
        
        self::$logger->info("Local configuration overrides loaded", [
            'environment' => $environment,
            'file' => $localConfigFile
        ]);
        
        return $config;
    }
    
    /**
     * Validate configuration for environment
     */
    private static function validateConfiguration(array $config, string $environment): void
    {
        $requiredKeys = [
            'database.host',
            'database.database',
            'database.username',
        ];
        
        // Add environment-specific required keys
        switch ($environment) {
            case 'production':
                $requiredKeys = array_merge($requiredKeys, [
                    'security.encryption_key',
                    'logging.level',
                ]);
                break;
                
            case 'staging':
                $requiredKeys = array_merge($requiredKeys, [
                    'security.encryption_key',
                ]);
                break;
        }
        
        $missing = [];
        foreach ($requiredKeys as $key) {
            if (!self::hasNestedKey($config, $key)) {
                $missing[] = $key;
            }
        }
        
        if (!empty($missing)) {
            throw new \RuntimeException(
                "Missing required configuration keys for {$environment}: " . implode(', ', $missing)
            );
        }
        
        // Validate specific configuration values
        self::validateSpecificConfig($config, $environment);
    }
    
    /**
     * Validate specific configuration values
     */
    private static function validateSpecificConfig(array $config, string $environment): void
    {
        // Validate database configuration
        if (isset($config['database']['port']) && 
            ($config['database']['port'] < 1 || $config['database']['port'] > 65535)) {
            throw new \RuntimeException("Invalid database port: {$config['database']['port']}");
        }
        
        // Validate logging level
        if (isset($config['logging']['level'])) {
            $validLevels = ['debug', 'info', 'warning', 'error', 'critical'];
            if (!in_array($config['logging']['level'], $validLevels)) {
                throw new \RuntimeException("Invalid logging level: {$config['logging']['level']}");
            }
        }
        
        // Production-specific validations
        if ($environment === 'production') {
            // Ensure debug is disabled in production
            if (isset($config['app']['debug']) && $config['app']['debug']) {
                throw new \RuntimeException("Debug mode must be disabled in production");
            }
            
            // Ensure HTTPS is enforced
            if (isset($config['security']['force_https']) && !$config['security']['force_https']) {
                self::$logger->warning("HTTPS is not enforced in production environment");
            }
        }
    }
    
    /**
     * Check if nested key exists in array
     */
    private static function hasNestedKey(array $array, string $key): bool
    {
        $keys = explode('.', $key);
        $current = $array;
        
        foreach ($keys as $k) {
            if (!is_array($current) || !isset($current[$k])) {
                return false;
            }
            $current = $current[$k];
        }
        
        return true;
    }
    
    /**
     * Get configuration value using dot notation
     */
    public static function getConfigValue(array $config, string $key, $default = null)
    {
        $keys = explode('.', $key);
        $current = $config;
        
        foreach ($keys as $k) {
            if (!is_array($current) || !isset($current[$k])) {
                return $default;
            }
            $current = $current[$k];
        }
        
        return $current;
    }
    
    /**
     * Merge environment variables into configuration
     */
    public static function mergeEnvironmentVariables(array $config): array
    {
        // Database configuration from environment
        if (getenv('DB_HOST')) {
            $config['database']['host'] = getenv('DB_HOST');
        }
        if (getenv('DB_PORT')) {
            $config['database']['port'] = (int) getenv('DB_PORT');
        }
        if (getenv('DB_NAME')) {
            $config['database']['database'] = getenv('DB_NAME');
        }
        if (getenv('DB_USER')) {
            $config['database']['username'] = getenv('DB_USER');
        }
        if (getenv('DB_PASSWORD')) {
            $config['database']['password'] = getenv('DB_PASSWORD');
        }
        
        // API configuration from environment
        if (getenv('FREEMIUS_API_TOKEN')) {
            $config['freemius']['api_token'] = getenv('FREEMIUS_API_TOKEN');
        }
        if (getenv('IPREGISTRY_API_KEY')) {
            $config['ipregistry']['api_key'] = getenv('IPREGISTRY_API_KEY');
        }
        
        // Security configuration from environment
        if (getenv('APP_ENCRYPTION_KEY')) {
            $config['security']['encryption_key'] = getenv('APP_ENCRYPTION_KEY');
        }
        
        // Logging configuration from environment
        if (getenv('LOG_LEVEL')) {
            $config['logging']['level'] = getenv('LOG_LEVEL');
        }
        
        return $config;
    }
    
    /**
     * Export configuration for debugging (without sensitive data)
     */
    public static function exportSafeConfig(array $config): array
    {
        $safeConfig = $config;
        
        // Remove sensitive data
        if (isset($safeConfig['database']['password'])) {
            $safeConfig['database']['password'] = '***HIDDEN***';
        }
        if (isset($safeConfig['freemius']['api_token'])) {
            $safeConfig['freemius']['api_token'] = '***HIDDEN***';
        }
        if (isset($safeConfig['ipregistry']['api_key'])) {
            $safeConfig['ipregistry']['api_key'] = '***HIDDEN***';
        }
        if (isset($safeConfig['security']['encryption_key'])) {
            $safeConfig['security']['encryption_key'] = '***HIDDEN***';
        }
        
        return $safeConfig;
    }
    
    /**
     * Clear cached configurations
     */
    public static function clearCache(): void
    {
        self::$loadedConfigs = [];
        self::$logger->info("Environment configuration cache cleared");
    }
    
    /**
     * Get all loaded environments
     */
    public static function getLoadedEnvironments(): array
    {
        return array_keys(self::$loadedConfigs);
    }
}