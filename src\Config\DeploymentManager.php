<?php

namespace Skpassegna\GuardgeoApi\Config;

use Skpassegna\GuardgeoApi\Services\LoggingService;

/**
 * Deployment Configuration Manager
 * 
 * Manages deployment-specific configuration for different environments,
 * handles deployment validation, and provides deployment utilities.
 */
class DeploymentManager
{
    private static ?DeploymentManager $instance = null;
    private LoggingService $logger;
    private EnvironmentManager $envManager;
    private array $deploymentConfig = [];
    private bool $loaded = false;
    
    /**
     * Private constructor for singleton pattern
     */
    private function __construct()
    {
        $this->logger = LoggingService::getInstance();
        $this->envManager = EnvironmentManager::getInstance();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance(): DeploymentManager
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Load deployment configuration
     */
    public function loadDeploymentConfiguration(): array
    {
        if ($this->loaded) {
            return $this->deploymentConfig;
        }
        
        try {
            $environment = $this->envManager->getEnvironment();
            
            // Load base deployment configuration
            $this->deploymentConfig = $this->getBaseDeploymentConfig();
            
            // Load environment-specific deployment configuration
            $this->loadEnvironmentDeploymentConfig($environment);
            
            // Load deployment templates and validation rules
            $this->loadDeploymentTemplates($environment);
            
            // Validate deployment configuration
            $this->validateDeploymentConfiguration($environment);
            
            $this->loaded = true;
            
            $this->logger->info('Deployment configuration loaded', [
                'environment' => $environment,
                'config_sections' => array_keys($this->deploymentConfig)
            ]);
            
            return $this->deploymentConfig;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to load deployment configuration', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
    
    /**
     * Get base deployment configuration
     */
    private function getBaseDeploymentConfig(): array
    {
        return [
            'application' => [
                'name' => 'GuardGeo Admin Platform',
                'version' => '1.0.0',
                'php_version_required' => '8.1',
                'extensions_required' => [
                    'pdo', 'pdo_pgsql', 'json', 'curl', 'openssl', 
                    'mbstring', 'zip', 'gd', 'intl'
                ],
                'memory_limit_min' => '256M',
                'max_execution_time' => 60,
            ],
            
            'database' => [
                'engine' => 'PostgreSQL',
                'version_required' => '13.0',
                'charset' => 'UTF8',
                'collation' => 'en_US.UTF-8',
                'connection_pool' => [
                    'min_connections' => 2,
                    'max_connections' => 10,
                    'connection_timeout' => 30,
                    'idle_timeout' => 300,
                ]
            ],
            
            'web_server' => [
                'supported' => ['nginx', 'apache'],
                'document_root' => '/var/www/guardgeo',
                'index_files' => ['api.php', 'admin.php'],
                'ssl_required' => false, // Will be overridden by environment
            ],
            
            'security' => [
                'file_permissions' => [
                    'directories' => '755',
                    'files' => '644',
                    'config_files' => '600',
                    'log_files' => '644',
                ],
                'required_headers' => [
                    'X-Content-Type-Options',
                    'X-Frame-Options',
                    'X-XSS-Protection',
                    'Referrer-Policy'
                ],
                'blocked_extensions' => [
                    '.env', '.log', '.sql', '.bak', '.old', '.tmp'
                ]
            ],
            
            'monitoring' => [
                'health_check_endpoints' => [
                    '/health',
                    '/api/health',
                    '/admin/health'
                ],
                'log_files' => [
                    'combined' => 'logs/combined.log',
                    'error' => 'logs/error.log',
                    'security' => 'logs/security.log',
                    'audit' => 'logs/audit.log'
                ],
                'metrics' => [
                    'response_time',
                    'memory_usage',
                    'database_connections',
                    'cache_hit_ratio',
                    'error_rate'
                ]
            ],
            
            'backup' => [
                'enabled' => true,
                'schedule' => '0 2 * * *', // Daily at 2 AM
                'retention' => [
                    'daily' => 7,
                    'weekly' => 4,
                    'monthly' => 12
                ],
                'components' => [
                    'database',
                    'configuration',
                    'logs'
                ]
            ],
            
            'maintenance' => [
                'log_rotation' => [
                    'enabled' => true,
                    'schedule' => '0 1 * * *', // Daily at 1 AM
                    'max_size' => '100M',
                    'max_files' => 30
                ],
                'cache_cleanup' => [
                    'enabled' => true,
                    'schedule' => '0 3 * * *', // Daily at 3 AM
                    'max_age_days' => 30
                ],
                'database_maintenance' => [
                    'enabled' => true,
                    'schedule' => '0 4 * * 0', // Weekly on Sunday at 4 AM
                    'operations' => ['vacuum', 'analyze', 'reindex']
                ]
            ]
        ];
    }
    
    /**
     * Load environment-specific deployment configuration
     */
    private function loadEnvironmentDeploymentConfig(string $environment): void
    {
        $overrides = [];
        
        switch ($environment) {
            case 'production':
                $overrides = [
                    'web_server' => [
                        'ssl_required' => true,
                        'document_root' => '/var/www/guardgeo/production',
                    ],
                    'database' => [
                        'connection_pool' => [
                            'min_connections' => 5,
                            'max_connections' => 20,
                        ]
                    ],
                    'security' => [
                        'required_headers' => [
                            'X-Content-Type-Options',
                            'X-Frame-Options',
                            'X-XSS-Protection',
                            'Referrer-Policy',
                            'Strict-Transport-Security',
                            'Content-Security-Policy'
                        ]
                    ],
                    'monitoring' => [
                        'alert_thresholds' => [
                            'response_time_ms' => 2000,
                            'memory_usage_percent' => 80,
                            'error_rate_percent' => 5,
                            'disk_usage_percent' => 85
                        ]
                    ],
                    'backup' => [
                        'destinations' => ['local', 's3'],
                        'encryption' => true,
                        'compression' => true
                    ]
                ];
                break;
                
            case 'staging':
                $overrides = [
                    'web_server' => [
                        'ssl_required' => true,
                        'document_root' => '/var/www/guardgeo/staging',
                    ],
                    'database' => [
                        'connection_pool' => [
                            'min_connections' => 3,
                            'max_connections' => 10,
                        ]
                    ],
                    'monitoring' => [
                        'alert_thresholds' => [
                            'response_time_ms' => 3000,
                            'memory_usage_percent' => 85,
                            'error_rate_percent' => 10,
                            'disk_usage_percent' => 90
                        ]
                    ],
                    'backup' => [
                        'retention' => [
                            'daily' => 3,
                            'weekly' => 2,
                            'monthly' => 3
                        ]
                    ]
                ];
                break;
                
            case 'development':
                $overrides = [
                    'web_server' => [
                        'ssl_required' => false,
                        'document_root' => '/var/www/guardgeo/development',
                    ],
                    'database' => [
                        'connection_pool' => [
                            'min_connections' => 1,
                            'max_connections' => 5,
                        ]
                    ],
                    'monitoring' => [
                        'alert_thresholds' => [
                            'response_time_ms' => 5000,
                            'memory_usage_percent' => 90,
                            'error_rate_percent' => 20,
                            'disk_usage_percent' => 95
                        ]
                    ],
                    'backup' => [
                        'enabled' => false
                    ]
                ];
                break;
        }
        
        $this->deploymentConfig = array_merge_recursive($this->deploymentConfig, $overrides);
    }
    
    /**
     * Load deployment templates
     */
    private function loadDeploymentTemplates(string $environment): void
    {
        $templatesPath = __DIR__ . '/../../config/deployment';
        
        $this->deploymentConfig['templates'] = [
            'env_template' => "{$templatesPath}/{$environment}.env.template",
            'nginx_config' => "{$templatesPath}/nginx.{$environment}.conf",
            'docker_compose' => "{$templatesPath}/docker-compose.{$environment}.yml",
            'crontab' => "{$templatesPath}/crontab.{$environment}",
            'deployment_checklist' => "{$templatesPath}/deployment-checklist.md"
        ];
        
        // Check which templates exist
        $this->deploymentConfig['available_templates'] = [];
        foreach ($this->deploymentConfig['templates'] as $name => $path) {
            if (file_exists($path)) {
                $this->deploymentConfig['available_templates'][$name] = $path;
            }
        }
    }
    
    /**
     * Validate deployment configuration
     */
    private function validateDeploymentConfiguration(string $environment): void
    {
        $errors = [];
        $warnings = [];
        
        // Validate PHP requirements
        if (version_compare(PHP_VERSION, $this->deploymentConfig['application']['php_version_required'], '<')) {
            $errors[] = "PHP version {$this->deploymentConfig['application']['php_version_required']} or higher required. Current: " . PHP_VERSION;
        }
        
        // Validate required PHP extensions
        foreach ($this->deploymentConfig['application']['extensions_required'] as $extension) {
            if (!extension_loaded($extension)) {
                $errors[] = "Required PHP extension '{$extension}' is not loaded";
            }
        }
        
        // Validate memory limit
        $memoryLimit = ini_get('memory_limit');
        $requiredMemory = $this->deploymentConfig['application']['memory_limit_min'];
        if ($this->parseMemoryLimit($memoryLimit) < $this->parseMemoryLimit($requiredMemory)) {
            $warnings[] = "Memory limit ({$memoryLimit}) is below recommended minimum ({$requiredMemory})";
        }
        
        // Validate SSL requirements
        if ($this->deploymentConfig['web_server']['ssl_required'] && !$this->envManager->isSSLEnforced()) {
            $errors[] = "SSL is required for {$environment} environment but not enforced";
        }
        
        // Validate file permissions
        $this->validateFilePermissions($warnings);
        
        // Validate log directories
        $this->validateLogDirectories($errors, $warnings);
        
        if (!empty($errors)) {
            throw new \RuntimeException(
                "Deployment validation failed: " . implode('; ', $errors)
            );
        }
        
        if (!empty($warnings)) {
            foreach ($warnings as $warning) {
                $this->logger->warning('Deployment validation warning', [
                    'warning' => $warning,
                    'environment' => $environment
                ]);
            }
        }
        
        $this->logger->info('Deployment configuration validated', [
            'environment' => $environment,
            'warnings_count' => count($warnings)
        ]);
    }
    
    /**
     * Parse memory limit string to bytes
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
    
    /**
     * Validate file permissions
     */
    private function validateFilePermissions(array &$warnings): void
    {
        $basePath = __DIR__ . '/../..';
        $checkPaths = [
            'logs' => $basePath . '/logs',
            'config' => $basePath . '/config',
            'src' => $basePath . '/src'
        ];
        
        foreach ($checkPaths as $name => $path) {
            if (!is_dir($path)) {
                continue;
            }
            
            $perms = fileperms($path) & 0777;
            $expectedPerms = octdec($this->deploymentConfig['security']['file_permissions']['directories']);
            
            if ($perms !== $expectedPerms) {
                $warnings[] = "Directory {$name} has permissions " . decoct($perms) . 
                             ", expected " . decoct($expectedPerms);
            }
        }
    }
    
    /**
     * Validate log directories
     */
    private function validateLogDirectories(array &$errors, array &$warnings): void
    {
        foreach ($this->deploymentConfig['monitoring']['log_files'] as $name => $path) {
            $fullPath = __DIR__ . '/../../' . $path;
            $dir = dirname($fullPath);
            
            if (!is_dir($dir)) {
                $errors[] = "Log directory does not exist: {$dir}";
            } elseif (!is_writable($dir)) {
                $errors[] = "Log directory is not writable: {$dir}";
            }
        }
    }
    
    /**
     * Get deployment configuration
     */
    public function getDeploymentConfiguration(): array
    {
        if (!$this->loaded) {
            $this->loadDeploymentConfiguration();
        }
        
        return $this->deploymentConfig;
    }
    
    /**
     * Get configuration for specific section
     */
    public function getSection(string $section): array
    {
        $config = $this->getDeploymentConfiguration();
        return $config[$section] ?? [];
    }
    
    /**
     * Generate environment file from template
     */
    public function generateEnvironmentFile(string $environment, array $values = []): string
    {
        $templatePath = __DIR__ . "/../../config/deployment/{$environment}.env.template";
        
        if (!file_exists($templatePath)) {
            throw new \RuntimeException("Environment template not found: {$templatePath}");
        }
        
        $template = file_get_contents($templatePath);
        
        // Replace placeholders with actual values
        foreach ($values as $key => $value) {
            $template = str_replace("{{$key}}", $value, $template);
        }
        
        return $template;
    }
    
    /**
     * Get deployment checklist
     */
    public function getDeploymentChecklist(): array
    {
        $checklistPath = __DIR__ . '/../../config/deployment/deployment-checklist.md';
        
        if (!file_exists($checklistPath)) {
            return [];
        }
        
        $content = file_get_contents($checklistPath);
        
        // Parse checklist items
        $checklist = [];
        $lines = explode("\n", $content);
        $currentSection = '';
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (preg_match('/^##\s+(.+)$/', $line, $matches)) {
                $currentSection = $matches[1];
                $checklist[$currentSection] = [];
            } elseif (preg_match('/^-\s+\[\s*\]\s+(.+)$/', $line, $matches)) {
                if ($currentSection) {
                    $checklist[$currentSection][] = [
                        'task' => $matches[1],
                        'completed' => false
                    ];
                }
            }
        }
        
        return $checklist;
    }
    
    /**
     * Validate deployment readiness
     */
    public function validateDeploymentReadiness(string $environment): array
    {
        $results = [
            'ready' => true,
            'checks' => [],
            'errors' => [],
            'warnings' => []
        ];
        
        try {
            // Check system requirements
            $this->checkSystemRequirements($results);
            
            // Check configuration completeness
            $this->checkConfigurationCompleteness($results, $environment);
            
            // Check security configuration
            $this->checkSecurityConfiguration($results, $environment);
            
            // Check database connectivity
            $this->checkDatabaseConnectivity($results);
            
            // Check external API connectivity
            $this->checkExternalAPIConnectivity($results);
            
            // Check file permissions
            $this->checkFilePermissions($results);
            
            // Check SSL configuration (if required)
            if ($this->deploymentConfig['web_server']['ssl_required']) {
                $this->checkSSLConfiguration($results);
            }
            
        } catch (\Exception $e) {
            $results['ready'] = false;
            $results['errors'][] = "Deployment readiness check failed: " . $e->getMessage();
        }
        
        $results['ready'] = empty($results['errors']);
        
        return $results;
    }
    
    /**
     * Check system requirements
     */
    private function checkSystemRequirements(array &$results): void
    {
        $results['checks']['system_requirements'] = [];
        
        // PHP version
        $phpVersion = PHP_VERSION;
        $requiredVersion = $this->deploymentConfig['application']['php_version_required'];
        
        if (version_compare($phpVersion, $requiredVersion, '>=')) {
            $results['checks']['system_requirements']['php_version'] = 'OK';
        } else {
            $results['errors'][] = "PHP version {$requiredVersion} required, found {$phpVersion}";
            $results['checks']['system_requirements']['php_version'] = 'FAIL';
        }
        
        // PHP extensions
        $missingExtensions = [];
        foreach ($this->deploymentConfig['application']['extensions_required'] as $extension) {
            if (extension_loaded($extension)) {
                $results['checks']['system_requirements']['ext_' . $extension] = 'OK';
            } else {
                $missingExtensions[] = $extension;
                $results['checks']['system_requirements']['ext_' . $extension] = 'FAIL';
            }
        }
        
        if (!empty($missingExtensions)) {
            $results['errors'][] = "Missing PHP extensions: " . implode(', ', $missingExtensions);
        }
    }
    
    /**
     * Check configuration completeness
     */
    private function checkConfigurationCompleteness(array &$results, string $environment): void
    {
        $results['checks']['configuration'] = [];
        
        // Check if environment configuration is loaded
        try {
            $envConfig = $this->envManager->getAllConfiguration();
            $results['checks']['configuration']['environment_loaded'] = 'OK';
        } catch (\Exception $e) {
            $results['errors'][] = "Environment configuration not loaded: " . $e->getMessage();
            $results['checks']['configuration']['environment_loaded'] = 'FAIL';
            return;
        }
        
        // Check required configuration sections
        $requiredSections = ['app', 'database', 'security', 'apis'];
        foreach ($requiredSections as $section) {
            if (isset($envConfig[$section]) && !empty($envConfig[$section])) {
                $results['checks']['configuration'][$section] = 'OK';
            } else {
                $results['errors'][] = "Configuration section '{$section}' is missing or empty";
                $results['checks']['configuration'][$section] = 'FAIL';
            }
        }
    }
    
    /**
     * Check security configuration
     */
    private function checkSecurityConfiguration(array &$results, string $environment): void
    {
        $results['checks']['security'] = [];
        
        // Check encryption key
        $encryptionKey = $this->envManager->get('security.encryption_key');
        if (!empty($encryptionKey) && strlen($encryptionKey) >= 32) {
            $results['checks']['security']['encryption_key'] = 'OK';
        } else {
            $results['errors'][] = "Encryption key is missing or too short";
            $results['checks']['security']['encryption_key'] = 'FAIL';
        }
        
        // Check debug mode in production
        if ($environment === 'production' && $this->envManager->get('app.debug')) {
            $results['errors'][] = "Debug mode must be disabled in production";
            $results['checks']['security']['debug_disabled'] = 'FAIL';
        } else {
            $results['checks']['security']['debug_disabled'] = 'OK';
        }
    }
    
    /**
     * Check database connectivity
     */
    private function checkDatabaseConnectivity(array &$results): void
    {
        $results['checks']['database'] = [];
        
        try {
            $dbConfig = $this->envManager->getSection('database');
            $dsn = "pgsql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
            
            $pdo = new \PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
            $pdo->query("SELECT 1");
            
            $results['checks']['database']['connectivity'] = 'OK';
            
        } catch (\Exception $e) {
            $results['errors'][] = "Database connectivity failed: " . $e->getMessage();
            $results['checks']['database']['connectivity'] = 'FAIL';
        }
    }
    
    /**
     * Check external API connectivity
     */
    private function checkExternalAPIConnectivity(array &$results): void
    {
        $results['checks']['external_apis'] = [];
        
        // Check Freemius API
        $freemiusToken = $this->envManager->get('apis.freemius.api_token');
        if (!empty($freemiusToken)) {
            $results['checks']['external_apis']['freemius_token'] = 'OK';
        } else {
            $results['errors'][] = "Freemius API token is missing";
            $results['checks']['external_apis']['freemius_token'] = 'FAIL';
        }
        
        // Check ipRegistry API
        $ipRegistryKey = $this->envManager->get('apis.ipregistry.api_key');
        if (!empty($ipRegistryKey)) {
            $results['checks']['external_apis']['ipregistry_key'] = 'OK';
        } else {
            $results['errors'][] = "ipRegistry API key is missing";
            $results['checks']['external_apis']['ipregistry_key'] = 'FAIL';
        }
    }
    
    /**
     * Check file permissions
     */
    private function checkFilePermissions(array &$results): void
    {
        $results['checks']['file_permissions'] = [];
        
        $basePath = __DIR__ . '/../..';
        $checkPaths = [
            'logs' => $basePath . '/logs',
            'config' => $basePath . '/config'
        ];
        
        foreach ($checkPaths as $name => $path) {
            if (is_dir($path) && is_writable($path)) {
                $results['checks']['file_permissions'][$name] = 'OK';
            } else {
                $results['warnings'][] = "Directory {$name} may not have correct permissions";
                $results['checks']['file_permissions'][$name] = 'WARNING';
            }
        }
    }
    
    /**
     * Check SSL configuration
     */
    private function checkSSLConfiguration(array &$results): void
    {
        $results['checks']['ssl'] = [];
        
        $sslMiddleware = new \Skpassegna\GuardgeoApi\Utils\SSLEnforcementMiddleware();
        $sslStatus = $sslMiddleware->getSSLStatus();
        
        if ($sslStatus['ssl_enforced']) {
            $results['checks']['ssl']['enforcement'] = 'OK';
        } else {
            $results['errors'][] = "SSL enforcement is not enabled";
            $results['checks']['ssl']['enforcement'] = 'FAIL';
        }
        
        $certInfo = $sslStatus['certificate_info'];
        if ($certInfo['configured'] && $certInfo['valid'] && !($certInfo['is_expired'] ?? false)) {
            $results['checks']['ssl']['certificate'] = 'OK';
        } else {
            $results['warnings'][] = "SSL certificate may not be properly configured";
            $results['checks']['ssl']['certificate'] = 'WARNING';
        }
    }
    
    /**
     * Get deployment summary
     */
    public function getDeploymentSummary(): array
    {
        $config = $this->getDeploymentConfiguration();
        $envSummary = $this->envManager->getDeploymentSummary();
        
        return [
            'environment' => $this->envManager->getEnvironment(),
            'application' => $config['application'],
            'ssl_required' => $config['web_server']['ssl_required'],
            'backup_enabled' => $config['backup']['enabled'],
            'monitoring_enabled' => $envSummary['monitoring_enabled'],
            'available_templates' => array_keys($config['available_templates'] ?? []),
            'deployment_ready' => $this->validateDeploymentReadiness($this->envManager->getEnvironment())['ready']
        ];
    }
}