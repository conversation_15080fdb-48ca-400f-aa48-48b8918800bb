<?php

namespace Skpassegna\GuardgeoApi\Config;

/**
 * Security Configuration Manager
 * 
 * Centralized security configuration with environment-specific settings
 * and production-ready security policies.
 */
class SecurityConfig
{
    private static ?SecurityConfig $instance = null;
    private array $config;

    private function __construct()
    {
        $this->loadConfiguration();
    }

    public static function getInstance(): SecurityConfig
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Load security configuration based on environment
     */
    private function loadConfiguration(): void
    {
        $environment = $_ENV['APP_ENV'] ?? 'production';
        
        $this->config = [
            // Input Sanitization
            'input_sanitization' => [
                'max_string_length' => 10000,
                'max_array_depth' => 10,
                'max_array_elements' => 1000,
                'strip_tags' => true,
                'encode_html' => true,
                'normalize_unicode' => true,
                'remove_null_bytes' => true,
                'trim_whitespace' => true,
                'log_sanitization' => $environment !== 'production'
            ],

            // CSRF Protection
            'csrf_protection' => [
                'token_length' => 32,
                'token_lifetime' => 3600,
                'rotation_interval' => 300,
                'double_submit_cookies' => true,
                'origin_validation' => true,
                'referer_validation' => true,
                'same_site_validation' => true,
                'max_tokens_per_session' => 5,
                'secure_cookies' => $this->isHttps(),
                'httponly_cookies' => true,
                'samesite_policy' => 'Strict'
            ],

            // Security Headers
            'security_headers' => [
                'environment' => $environment,
                'force_https' => $environment === 'production',
                'hsts_max_age' => 31536000,
                'hsts_include_subdomains' => true,
                'hsts_preload' => true,
                'csp_report_uri' => null,
                'csp_report_only' => $environment === 'development',
                'frame_options' => 'DENY',
                'content_type_options' => 'nosniff',
                'xss_protection' => '1; mode=block',
                'referrer_policy' => 'strict-origin-when-cross-origin',
                'permissions_policy_enabled' => true,
                'cross_origin_policies_enabled' => true,
                'cache_control_sensitive' => 'no-store, no-cache, must-revalidate, max-age=0'
            ],

            // Session Security
            'session_security' => [
                'session_timeout' => 3600,
                'idle_timeout' => 1800,
                'regenerate_interval' => 300,
                'max_concurrent_sessions' => 3,
                'enable_fingerprinting' => true,
                'strict_ip_check' => false,
                'secure_cookies' => $this->isHttps(),
                'httponly_cookies' => true,
                'samesite_policy' => 'Strict'
            ],

            // Rate Limiting
            'rate_limiting' => [
                'api_requests_per_minute' => 60,
                'login_attempts_per_hour' => 5,
                'admin_requests_per_minute' => 120,
                'global_requests_per_minute' => 1000,
                'block_duration_minutes' => 15,
                'progressive_blocking' => true
            ],

            // Threat Detection
            'threat_detection' => [
                'enable_pattern_matching' => true,
                'enable_behavioral_analysis' => true,
                'risk_score_threshold' => 50,
                'auto_block_threshold' => 75,
                'log_all_threats' => true,
                'alert_on_critical' => true
            ],

            // Audit Logging
            'audit_logging' => [
                'store_in_database' => true,
                'log_to_file' => true,
                'alert_on_critical' => true,
                'retention_days' => 90,
                'log_admin_actions' => true,
                'log_api_requests' => true,
                'log_security_events' => true
            ],

            // IP Security
            'ip_security' => [
                'enable_geolocation_blocking' => false,
                'blocked_countries' => [],
                'allowed_ip_ranges' => [],
                'blocked_ip_ranges' => [],
                'enable_tor_blocking' => false,
                'enable_proxy_detection' => false
            ],

            // File Upload Security
            'file_upload' => [
                'max_file_size' => 5242880, // 5MB
                'allowed_mime_types' => [
                    'image/jpeg',
                    'image/png',
                    'image/gif',
                    'application/pdf',
                    'text/plain'
                ],
                'scan_for_malware' => false,
                'quarantine_suspicious' => true
            ],

            // Database Security
            'database_security' => [
                'enable_query_logging' => $environment !== 'production',
                'log_slow_queries' => true,
                'slow_query_threshold' => 2.0,
                'enable_prepared_statements' => true,
                'validate_all_parameters' => true
            ],

            // API Security
            'api_security' => [
                'require_https' => $environment === 'production',
                'validate_content_type' => true,
                'max_request_size' => 10485760, // 10MB
                'enable_cors' => false,
                'allowed_origins' => [],
                'require_user_agent' => true
            ],

            // Admin Security
            'admin_security' => [
                'require_2fa' => false,
                'password_min_length' => 12,
                'password_require_special' => true,
                'password_require_numbers' => true,
                'password_require_uppercase' => true,
                'password_require_lowercase' => true,
                'account_lockout_attempts' => 5,
                'account_lockout_duration' => 1800,
                'session_concurrent_limit' => 3
            ]
        ];

        // Environment-specific overrides
        $this->applyEnvironmentOverrides($environment);
    }

    /**
     * Apply environment-specific configuration overrides
     */
    private function applyEnvironmentOverrides(string $environment): void
    {
        switch ($environment) {
            case 'development':
                $this->config['security_headers']['force_https'] = false;
                $this->config['security_headers']['csp_report_only'] = true;
                $this->config['csrf_protection']['secure_cookies'] = false;
                $this->config['session_security']['secure_cookies'] = false;
                $this->config['rate_limiting']['api_requests_per_minute'] = 1000;
                $this->config['threat_detection']['auto_block_threshold'] = 90;
                break;

            case 'staging':
                $this->config['security_headers']['force_https'] = true;
                $this->config['security_headers']['csp_report_only'] = false;
                $this->config['rate_limiting']['api_requests_per_minute'] = 120;
                break;

            case 'production':
                $this->config['security_headers']['force_https'] = true;
                $this->config['security_headers']['csp_report_only'] = false;
                $this->config['input_sanitization']['log_sanitization'] = false;
                $this->config['database_security']['enable_query_logging'] = false;
                $this->config['threat_detection']['auto_block_threshold'] = 75;
                break;
        }
    }

    /**
     * Get configuration value
     */
    public function get(string $key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Get all configuration
     */
    public function getAll(): array
    {
        return $this->config;
    }

    /**
     * Check if HTTPS is enabled
     */
    private function isHttps(): bool
    {
        return (
            (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
            $_SERVER['SERVER_PORT'] == 443 ||
            (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
            (!empty($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on')
        );
    }

    /**
     * Update configuration value
     */
    public function set(string $key, $value): void
    {
        $keys = explode('.', $key);
        $config = &$this->config;

        foreach ($keys as $k) {
            if (!isset($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }

        $config = $value;
    }

    /**
     * Get security policy for specific component
     */
    public function getSecurityPolicy(string $component): array
    {
        return $this->config[$component] ?? [];
    }

    /**
     * Check if security feature is enabled
     */
    public function isEnabled(string $feature): bool
    {
        $value = $this->get($feature, false);
        return is_bool($value) ? $value : (bool)$value;
    }

    /**
     * Get environment-specific security level
     */
    public function getSecurityLevel(): string
    {
        $environment = $_ENV['APP_ENV'] ?? 'production';
        
        return match ($environment) {
            'development' => 'relaxed',
            'staging' => 'moderate',
            'production' => 'strict',
            default => 'strict'
        };
    }

    /**
     * Validate security configuration
     */
    public function validateConfiguration(): array
    {
        $issues = [];

        // Check HTTPS enforcement in production
        if ($this->get('security_headers.environment') === 'production' && !$this->get('security_headers.force_https')) {
            $issues[] = 'HTTPS enforcement should be enabled in production';
        }

        // Check CSRF protection
        if (!$this->get('csrf_protection.double_submit_cookies')) {
            $issues[] = 'Double-submit cookies should be enabled for CSRF protection';
        }

        // Check session security
        if ($this->get('session_security.session_timeout') > 7200) {
            $issues[] = 'Session timeout should not exceed 2 hours for security';
        }

        // Check rate limiting
        if ($this->get('rate_limiting.login_attempts_per_hour') > 10) {
            $issues[] = 'Login rate limiting should be more restrictive';
        }

        return $issues;
    }

    /**
     * Export configuration for debugging (sanitized)
     */
    public function exportSanitized(): array
    {
        $config = $this->config;
        
        // Remove sensitive information
        unset($config['database_security']);
        unset($config['api_security']['allowed_origins']);
        
        return $config;
    }
}