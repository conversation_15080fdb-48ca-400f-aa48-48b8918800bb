<?php

namespace Skpassegna\GuardgeoApi\Database;

/**
 * System Log Repository
 * 
 * Handles database operations for system logs with filtering and pagination.
 */
class SystemLogRepository extends BaseRepository
{
    protected string $table = 'system_logs';
    
    /**
     * Create a new log entry
     */
    public function createLog(string $type, string $level, string $message, array $context = [], ?string $ip = null, ?int $userId = null): bool
    {
        try {
            $data = [
                'type' => $type,
                'level' => $level,
                'message' => $message,
                'context' => json_encode($context),
                'ip' => $ip,
                'user_id' => $userId
            ];
            
            $this->insert($data);
            return true;
            
        } catch (DatabaseException $e) {
            // Don't log errors about logging to avoid infinite loops
            return false;
        }
    }
    
    /**
     * Get logs with pagination and filtering
     */
    public function getLogsPaginated(int $page, int $limit, array $filters = [], string $sortBy = 'created_at', string $sortOrder = 'desc'): array
    {
        $offset = ($page - 1) * $limit;
        
        $whereConditions = [];
        $params = [];
        
        // Apply filters
        if (!empty($filters['type'])) {
            $whereConditions[] = 'type = :type';
            $params['type'] = $filters['type'];
        }
        
        if (!empty($filters['level'])) {
            $whereConditions[] = 'level = :level';
            $params['level'] = $filters['level'];
        }
        
        if (!empty($filters['user_id'])) {
            $whereConditions[] = 'user_id = :user_id';
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['search'])) {
            $whereConditions[] = 'message ILIKE :search';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= :date_from';
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= :date_to';
            $params['date_to'] = $filters['date_to'];
        }
        
        // Build WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        // Apply sorting
        $validSortColumns = ['id', 'type', 'level', 'message', 'created_at', 'user_id'];
        if (!in_array($sortBy, $validSortColumns)) {
            $sortBy = 'created_at';
        }
        
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql = "
            SELECT l.*, u.email as user_email 
            FROM {$this->table} l
            LEFT JOIN admin_users u ON l.user_id = u.id
            {$whereClause}
            ORDER BY {$sortBy} {$sortOrder}
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        try {
            $statement = $this->executeQuery($sql, $params);
            $logs = $statement->fetchAll();
            
            // Decode JSON context for each log
            foreach ($logs as &$log) {
                $log['context'] = json_decode($log['context'] ?? '{}', true);
            }
            
            return [
                'logs' => $logs,
                'total' => $this->getFilteredCount($filters)
            ];
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get paginated logs", [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [
                'logs' => [],
                'total' => 0
            ];
        }
    }
    
    /**
     * Get log statistics
     */
    public function getStatistics(): array
    {
        try {
            $sql = "
                SELECT 
                    COUNT(*) as total_logs,
                    COUNT(CASE WHEN level = 'error' THEN 1 END) as error_count,
                    COUNT(CASE WHEN level = 'warning' THEN 1 END) as warning_count,
                    COUNT(CASE WHEN level = 'info' THEN 1 END) as info_count,
                    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as last_24h,
                    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as last_7d
                FROM {$this->table}
            ";
            
            $statement = $this->executeQuery($sql);
            return $statement->fetch();
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to get log statistics", [
                'error' => $e->getMessage()
            ]);
            return [
                'total_logs' => 0,
                'error_count' => 0,
                'warning_count' => 0,
                'info_count' => 0,
                'last_24h' => 0,
                'last_7d' => 0
            ];
        }
    }
    
    /**
     * Clean up old logs
     */
    public function cleanupOldLogs(int $retentionDays): int
    {
        try {
            $cutoffDate = date('Y-m-d H:i:s', time() - ($retentionDays * 24 * 3600));
            
            $sql = "DELETE FROM {$this->table} WHERE created_at < :cutoff_date";
            $statement = $this->executeQuery($sql, ['cutoff_date' => $cutoffDate]);
            
            return $statement->rowCount();
            
        } catch (DatabaseException $e) {
            $this->logger->error("Failed to cleanup old logs", [
                'retention_days' => $retentionDays,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * Get filtered count for pagination
     */
    private function getFilteredCount(array $filters): int
    {
        $whereConditions = [];
        $params = [];
        
        // Apply same filters as main query
        if (!empty($filters['type'])) {
            $whereConditions[] = 'type = :type';
            $params['type'] = $filters['type'];
        }
        
        if (!empty($filters['level'])) {
            $whereConditions[] = 'level = :level';
            $params['level'] = $filters['level'];
        }
        
        if (!empty($filters['user_id'])) {
            $whereConditions[] = 'user_id = :user_id';
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['search'])) {
            $whereConditions[] = 'message ILIKE :search';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= :date_from';
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= :date_to';
            $params['date_to'] = $filters['date_to'];
        }
        
        // Build WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }
        
        $sql = "SELECT COUNT(*) as count FROM {$this->table} {$whereClause}";
        
        try {
            $statement = $this->executeQuery($sql, $params);
            $row = $statement->fetch();
            return (int)($row['count'] ?? 0);
        } catch (DatabaseException $e) {
            return 0;
        }
    }
}