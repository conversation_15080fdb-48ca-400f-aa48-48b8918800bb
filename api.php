<?php

/**
 * GuardGeo API Entry Point
 * 
 * This file serves as the main entry point for the GuardGeo REST API.
 * It handles routing for the /api/analyze endpoint used by WordPress plugins.
 */

// Load environment configuration
require_once __DIR__ . '/vendor/autoload.php';

use Skpassegna\GuardgeoApi\Controllers\ApiRouter;
use Skpassegna\GuardgeoApi\Utils\SecurityMiddleware;
use Skpassegna\GuardgeoApi\Utils\SSLEnforcementMiddleware;
use Skpassegna\GuardgeoApi\Services\LoggingService;
use Skpassegna\GuardgeoApi\Config\EnvironmentManager;

// Initialize environment manager
$envManager = EnvironmentManager::getInstance();
$envManager->loadConfiguration();

// Configure error reporting based on environment
if ($envManager->isProduction()) {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Set timezone
date_default_timezone_set($envManager->get('app.timezone', 'UTC'));

try {
    // Initialize SSL enforcement middleware
    $sslMiddleware = new SSLEnforcementMiddleware();
    $sslMiddleware->process();
    
    // Initialize security middleware
    $logger = LoggingService::getInstance();
    $securityMiddleware = new SecurityMiddleware($logger);
    
    // Apply comprehensive security measures
    $securityResult = $securityMiddleware->applySecurityMeasures();
    
    if (!$securityResult['allowed']) {
        // Security check failed
        http_response_code(403);
        header('Content-Type: application/json');
        
        echo json_encode([
            'success' => false,
            'error' => [
                'code' => 'SECURITY_VIOLATION',
                'message' => 'Request blocked by security measures'
            ],
            'timestamp' => date('c')
        ], JSON_PRETTY_PRINT);
        exit;
    }
    
    // Create and execute router with security context
    $router = new ApiRouter();
    $router->route();
    
} catch (\Exception $e) {
    // Log critical errors
    error_log("Critical API Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    
    // Return generic error response
    http_response_code(500);
    header('Content-Type: application/json');
    
    echo json_encode([
        'success' => false,
        'error' => [
            'code' => 'CRITICAL_ERROR',
            'message' => 'A critical system error occurred'
        ],
        'timestamp' => date('c')
    ], JSON_PRETTY_PRINT);
}