<?php

namespace Skpassegna\GuardgeoApi\Models;

use DateTime;
use JsonSerializable;

/**
 * Base Model
 * 
 * Provides common functionality for all data models
 */
abstract class BaseModel implements JsonSerializable
{
    /**
     * Convert DateTime to database format
     */
    protected function dateTimeToDb(?DateTime $dateTime): ?string
    {
        return $dateTime ? $dateTime->format('Y-m-d H:i:s') : null;
    }
    
    /**
     * Convert database date string to DateTime
     */
    protected function dbToDateTime(?string $dateString): ?DateTime
    {
        return $dateString ? new DateTime($dateString) : null;
    }
    
    /**
     * Convert DateTime to ISO 8601 format for JSON
     */
    protected function dateTimeToJson(?DateTime $dateTime): ?string
    {
        return $dateTime ? $dateTime->format('c') : null;
    }
    
    /**
     * Safely convert value to integer
     */
    protected function toInt(mixed $value): int
    {
        return (int) $value;
    }
    
    /**
     * Safely convert value to nullable integer
     */
    protected function toNullableInt(mixed $value): ?int
    {
        return $value !== null ? (int) $value : null;
    }
    
    /**
     * Safely convert value to float
     */
    protected function toFloat(mixed $value): float
    {
        return (float) $value;
    }
    
    /**
     * Safely convert value to boolean
     */
    protected function toBool(mixed $value): bool
    {
        return (bool) $value;
    }
    
    /**
     * Safely convert value to string
     */
    protected function toString(mixed $value): string
    {
        return (string) $value;
    }
    
    /**
     * Safely convert value to nullable string
     */
    protected function toNullableString(mixed $value): ?string
    {
        return $value !== null ? (string) $value : null;
    }
    
    /**
     * Validate required field
     */
    protected function validateRequired(mixed $value, string $fieldName): ?string
    {
        if (empty($value)) {
            return "$fieldName is required";
        }
        return null;
    }
    
    /**
     * Validate email format
     */
    protected function validateEmail(?string $email, string $fieldName): ?string
    {
        if ($email !== null && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return "$fieldName must be a valid email address";
        }
        return null;
    }
    
    /**
     * Validate URL format
     */
    protected function validateUrl(?string $url, string $fieldName): ?string
    {
        if ($url !== null && !filter_var($url, FILTER_VALIDATE_URL)) {
            return "$fieldName must be a valid URL";
        }
        return null;
    }
    
    /**
     * Validate enum value
     */
    protected function validateEnum(mixed $value, array $allowedValues, string $fieldName): ?string
    {
        if (!in_array($value, $allowedValues)) {
            $allowed = implode(', ', $allowedValues);
            return "$fieldName must be one of: $allowed";
        }
        return null;
    }
    
    /**
     * Validate positive integer
     */
    protected function validatePositiveInt(mixed $value, string $fieldName): ?string
    {
        if (!is_numeric($value) || (int) $value <= 0) {
            return "$fieldName must be a positive integer";
        }
        return null;
    }
    
    /**
     * Validate non-negative integer
     */
    protected function validateNonNegativeInt(mixed $value, string $fieldName): ?string
    {
        if (!is_numeric($value) || (int) $value < 0) {
            return "$fieldName must be a non-negative integer";
        }
        return null;
    }
    
    /**
     * Validate non-negative float
     */
    protected function validateNonNegativeFloat(mixed $value, string $fieldName): ?string
    {
        if (!is_numeric($value) || (float) $value < 0) {
            return "$fieldName must be a non-negative number";
        }
        return null;
    }
    
    /**
     * Validate string length
     */
    protected function validateStringLength(?string $value, int $maxLength, string $fieldName): ?string
    {
        if ($value !== null && strlen($value) > $maxLength) {
            return "$fieldName must not exceed $maxLength characters";
        }
        return null;
    }
    
    /**
     * Validate exact string length
     */
    protected function validateExactStringLength(?string $value, int $exactLength, string $fieldName): ?string
    {
        if ($value !== null && strlen($value) !== $exactLength) {
            return "$fieldName must be exactly $exactLength characters";
        }
        return null;
    }
    
    /**
     * Get all validation errors
     */
    abstract public function validate(): array;
    
    /**
     * Check if model is valid
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }
    
    /**
     * Get first validation error
     */
    public function getFirstError(): ?string
    {
        $errors = $this->validate();
        return $errors[0] ?? null;
    }
    
    /**
     * Convert model to array for database operations
     */
    abstract public function toDatabaseArray(): array;
    
    /**
     * JSON serialization
     */
    abstract public function jsonSerialize(): array;
    
    /**
     * Convert model to JSON string
     */
    public function toJson(): string
    {
        return json_encode($this->jsonSerialize(), JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * Get model class name without namespace
     */
    public function getModelName(): string
    {
        $className = get_class($this);
        return substr($className, strrpos($className, '\\') + 1);
    }
    
    /**
     * Create a copy of the model
     */
    public function copy(): static
    {
        return clone $this;
    }
}