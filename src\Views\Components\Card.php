<?php

namespace Skpassegna\GuardgeoApi\Views\Components;

/**
 * Card Component
 * 
 * Reusable card component for displaying content in a consistent container
 * with optional header, footer, and various styling options.
 */
class Card extends BaseComponent
{
    protected function getDefaultClasses(): string
    {
        return 'bg-white rounded-lg shadow';
    }

    public function render(): string
    {
        $title = $this->prop('title');
        $subtitle = $this->prop('subtitle');
        $content = $this->prop('content', '');
        $footer = $this->prop('footer');
        $padding = $this->prop('padding', 'p-6');
        $headerPadding = $this->prop('headerPadding', 'px-6 py-4');
        $footerPadding = $this->prop('footerPadding', 'px-6 py-4');

        $html = '<div class="' . $this->getClasses() . '"';
        
        if ($this->attributes) {
            $html .= ' ' . $this->renderAttributes();
        }
        
        $html .= '>';

        // Header
        if ($title || $subtitle) {
            $html .= '<div class="' . $headerPadding . ' border-b border-gray-200">';
            
            if ($title) {
                $html .= '<h3 class="text-lg font-medium text-gray-900">' . $this->escape($title) . '</h3>';
            }
            
            if ($subtitle) {
                $html .= '<p class="text-sm text-gray-600 mt-1">' . $this->escape($subtitle) . '</p>';
            }
            
            $html .= '</div>';
        }

        // Content
        if ($content) {
            $contentPadding = ($title || $subtitle) ? $padding : $padding;
            $html .= '<div class="' . $contentPadding . '">' . $content . '</div>';
        }

        // Footer
        if ($footer) {
            $html .= '<div class="' . $footerPadding . ' border-t border-gray-200">' . $footer . '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Create a stat card variant
     *
     * @param array $props
     * @return self
     */
    public static function stat(array $props): self
    {
        $icon = $props['icon'] ?? '';
        $iconColor = $props['iconColor'] ?? 'bg-blue-500';
        $title = $props['title'] ?? '';
        $value = $props['value'] ?? '';
        $subtitle = $props['subtitle'] ?? '';
        $trend = $props['trend'] ?? null;

        $content = '<div class="flex items-center">';
        
        if ($icon) {
            $content .= '<div class="flex-shrink-0">';
            $content .= '<div class="w-8 h-8 ' . $iconColor . ' rounded-full flex items-center justify-center">';
            $content .= '<i class="' . $icon . ' text-white text-sm"></i>';
            $content .= '</div>';
            $content .= '</div>';
        }
        
        $content .= '<div class="ml-4 flex-1">';
        $content .= '<h3 class="text-lg font-medium text-gray-900">' . htmlspecialchars($title, ENT_QUOTES, 'UTF-8') . '</h3>';
        $content .= '<p class="text-2xl font-bold text-gray-900">' . htmlspecialchars($value, ENT_QUOTES, 'UTF-8') . '</p>';
        
        if ($subtitle) {
            $content .= '<p class="text-sm text-gray-600">' . htmlspecialchars($subtitle, ENT_QUOTES, 'UTF-8') . '</p>';
        }
        
        if ($trend) {
            $trendColor = $trend['type'] === 'up' ? 'text-green-600' : ($trend['type'] === 'down' ? 'text-red-600' : 'text-gray-600');
            $trendIcon = $trend['type'] === 'up' ? 'fas fa-arrow-up' : ($trend['type'] === 'down' ? 'fas fa-arrow-down' : 'fas fa-minus');
            $content .= '<div class="flex items-center mt-2">';
            $content .= '<i class="' . $trendIcon . ' ' . $trendColor . ' text-xs mr-1"></i>';
            $content .= '<span class="text-sm ' . $trendColor . '">' . htmlspecialchars($trend['value'], ENT_QUOTES, 'UTF-8') . '</span>';
            $content .= '</div>';
        }
        
        $content .= '</div>';
        $content .= '</div>';

        return new self(['content' => $content]);
    }
}